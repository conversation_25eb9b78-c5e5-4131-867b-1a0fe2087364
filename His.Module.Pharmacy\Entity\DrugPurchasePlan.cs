﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 采购计划表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_purchase_plan", "采购计划表")]
public class DrugPurchasePlan : EntityTenant
{
    /// <summary>
    /// 采购计划号
    /// </summary>
    [SugarColumn(ColumnName = "plan_no", ColumnDescription = "采购计划号", Length = 100)]
    public virtual string? PlanNo { get; set; }
    
    /// <summary>
    /// 采购计划时间
    /// </summary>
    [SugarColumn(ColumnName = "plan_time", ColumnDescription = "采购计划时间")]
    public virtual DateTime? PlanTime { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态（0 未处理 1 处理中 2 已完成等）")]
    public virtual int? Status { get; set; }
    
}
