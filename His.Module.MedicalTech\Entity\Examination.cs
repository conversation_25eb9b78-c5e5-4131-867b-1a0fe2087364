﻿namespace His.Module.MedicalTech.Entity;

/// <summary>
/// 检查表
/// </summary>
[Tenant("1300000000009")]
[SugarTable("examination", "检查表")]
public class Examination : EntityTenantBaseData
{
    /// <summary>
    /// 申请单号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "apply_no", ColumnDescription = "申请单号", Length = 64)]
    public virtual string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "就诊Id")]
    public virtual long RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊流水号", Length = 64)]
    public virtual string VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 64)]
    public virtual string OutpatientNo { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "就诊卡号", Length = 64)]
    public virtual string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 64)]
    public virtual string PatientName { get; set; }

    /// <summary>
    /// 检查类别Id
    /// </summary>
    [SugarColumn(ColumnName = "check_category_id", ColumnDescription = "检查类别Id")]
    public virtual long? CheckCategoryId { get; set; }

    /// <summary>
    /// 检查类别名称
    /// </summary>
    [SugarColumn(ColumnName = "check_category_name", ColumnDescription = "检查类别名称", Length = 64)]
    public virtual string? CheckCategoryName { get; set; }

    /// <summary>
    /// 检查部位Id
    /// </summary>
    [SugarColumn(ColumnName = "check_point_id", ColumnDescription = "检查部位Id")]
    public virtual long? CheckPointId { get; set; }

    /// <summary>
    /// 检查部位名称
    /// </summary>
    [SugarColumn(ColumnName = "check_point_name", ColumnDescription = "检查部位名称", Length = 64)]
    public virtual string? CheckPointName { get; set; }

    /// <summary>
    /// 检查目的
    /// </summary>
    [SugarColumn(ColumnName = "check_objective", ColumnDescription = "检查目的", Length = 200)]
    public virtual string? CheckObjective { get; set; }

    /// <summary>
    /// 临床诊断
    /// </summary>
    [SugarColumn(ColumnName = "clinical_diagnosis", ColumnDescription = "临床诊断", Length = 200)]
    public virtual string? ClinicalDiagnosis { get; set; }

    /// <summary>
    /// 病历摘要
    /// </summary>
    [SugarColumn(ColumnName = "medical_record_summary", ColumnDescription = "病历摘要", Length = 500)]
    public virtual string? MedicalRecordSummary { get; set; }

    /// <summary>
    /// 状态 字典 ApplyStatus
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 字典 ApplyStatus")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    [SugarColumn(ColumnName = "flag", ColumnDescription = "门诊住院标识 0门诊 1住院")]
    public virtual int? Flag { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    [SugarColumn(ColumnName = "billing_time", ColumnDescription = "开单时间")]
    public virtual DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_id", ColumnDescription = "开单科室Id")]
    public virtual long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_name", ColumnDescription = "开单科室名称", Length = 64)]
    public virtual string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_id", ColumnDescription = "开单医生Id")]
    public virtual long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_name", ColumnDescription = "开单医生名称", Length = 64)]
    public virtual string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [SugarColumn(ColumnName = "execute_time", ColumnDescription = "执行时间")]
    public virtual DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_id", ColumnDescription = "执行科室Id")]
    public virtual long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_name", ColumnDescription = "执行科室名称", Length = 64)]
    public virtual string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_address", ColumnDescription = "执行科室地址", Length = 100)]
    public virtual string? ExecuteDeptAddress { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_doctor_id", ColumnDescription = "执行医生Id")]
    public virtual long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    [SugarColumn(ColumnName = "execute_doctor_name", ColumnDescription = "执行医生名称", Length = 64)]
    public virtual string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_staff_id", ColumnDescription = "收费人员Id")]
    public virtual long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费人员名称
    /// </summary>
    [SugarColumn(ColumnName = "charge_staff_name", ColumnDescription = "收费人员名称", Length = 64)]
    public virtual string? ChargeStaffName { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    [SugarColumn(ColumnName = "charge_time", ColumnDescription = "收费时间")]
    public virtual DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 医生签名
    /// </summary>
    [SugarColumn(ColumnName = "doctor_sign", ColumnDescription = "医生签名", Length = 0)]
    public virtual string? DoctorSign { get; set; }

    /// <summary>
    /// 医嘱Id
    /// </summary>
    [SugarColumn(ColumnName = "medical_advice_id", ColumnDescription = "医嘱Id")]
    public virtual long? MedicalAdviceId { get; set; }

    /// <summary>
    /// 检查明细
    /// </summary>

    [Navigate(NavigateType.OneToMany, nameof(Entity.ExaminationDetails.ExaminationId))]
    public virtual List<ExaminationDetails> ExaminationDetails { get; set; }
}