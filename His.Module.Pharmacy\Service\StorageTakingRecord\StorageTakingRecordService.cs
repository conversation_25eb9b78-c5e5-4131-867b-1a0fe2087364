﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品盘点服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class StorageTakingRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<StorageTakingRecord> _storageTakingRecordRep;
    private readonly SqlSugarRepository<StorageTakingDetail> _storageTakingDetailRep;
    private readonly SqlSugarRepository<DrugInventory> _drugInventoryRep;
    private readonly SqlSugarRepository<DrugStorage> _drugStorageRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;
    private readonly InventoryService _inventoryService;

    public StorageTakingRecordService(SqlSugarRepository<StorageTakingRecord> storageTakingRecordRep,
        SqlSugarRepository<StorageTakingDetail> storageTakingDetailRep,
        SqlSugarRepository<DrugInventory> drugInventoryRep,
        SqlSugarRepository<DrugStorage> drugStorageRep,
        ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService,
        InventoryService inventoryService
    )
    {
        _storageTakingRecordRep = storageTakingRecordRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
        _storageTakingDetailRep = storageTakingDetailRep;
        _drugStorageRep = drugStorageRep;
        _drugInventoryRep = drugInventoryRep;
        _inventoryService = inventoryService;
    }

    /// <summary>
    /// 分页查询药品盘点 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品盘点")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<StorageTakingRecordOutput>> Page(PageStorageTakingRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _storageTakingRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.TakingNo.Contains(input.Keyword) || u.StorageCode.Contains(input.Keyword) ||
                     u.StorageName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TakingNo), u => u.TakingNo.Contains(input.TakingNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageCode),
                u => u.StorageCode.Contains(input.StorageCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageName),
                u => u.StorageName.Contains(input.StorageName.Trim()))
            .WhereIF(input.TakingTimeRange?.Length == 2,
                u => u.TakingTime >= input.TakingTimeRange[0] && u.TakingTime <= input.TakingTimeRange[1])
            .WhereIF(input.TakingResult != null, u => u.TakingResult == input.TakingResult)
            .WhereIF(input.StorageId != null, u => u.StorageId == input.StorageId)
            .WhereIF(input.CurrentQuantity != null, u => u.CurrentQuantity == input.CurrentQuantity)
            .WhereIF(input.TakingQuantity != null, u => u.TakingQuantity == input.TakingQuantity)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<DrugStorage>((u, storage) => u.StorageId == storage.Id)
            .Select((u, storage) => new StorageTakingRecordOutput
            {
                Id = u.Id,
                TakingNo = u.TakingNo,
                TakingTime = u.TakingTime,
                TakingResult = u.TakingResult,
                StorageId = u.StorageId,
                StorageFkDisplayName = $"{storage.StorageName}",
                StorageCode = u.StorageCode,
                StorageName = u.StorageName,
                CurrentQuantity = u.CurrentQuantity,
                TakingQuantity = u.TakingQuantity,
                CurrentSalePrice = u.CurrentSalePrice,
                TakingSalePrice = u.TakingSalePrice,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品盘点详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品盘点详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<StorageTakingRecord> Detail([FromQuery] QueryByIdStorageTakingRecordInput input)
    {
        return await _storageTakingRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品盘点 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品盘点")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<long> Add(AddStorageTakingRecordInput input)
    {
        // var orgName =    App.User.FindFirst(ClaimConst.OrgName);

        // 获取当前科室 
        // 获取药房
        var storage = await _drugStorageRep.GetFirstAsync(u => u.StorageCode == "0033");
        List<DrugInventory> drugInventoryList = await _drugInventoryRep.AsQueryable()
            // .Where(u => u.StorageId == storage.Id)
            .Where(u => u.Quantity > 0)
            .ToListAsync();

        var entity = new StorageTakingRecord();
        entity.Status = 0;
        entity.TakingResult = 0;
        entity.TakingNo = await _storageTakingRecordRep.Context.Ado.GetStringAsync(
            "SELECT LPAD(CAST(NEXTVAL('storage_taking_no_seq')As varchar),7,'0')");
        entity.StorageId = storage.Id;
        entity.StorageCode = storage.StorageCode;
        entity.StorageName = storage.StorageName;
        entity.CurrentQuantity = drugInventoryList.Sum(u => u.Quantity);
        entity.CurrentSalePrice = drugInventoryList.Sum(u => u.Quantity * u.SalePrice);
        entity.TakingQuantity = entity.CurrentQuantity;
        entity.TakingSalePrice = entity.CurrentSalePrice;
        var recordId = await _storageTakingRecordRep.InsertAsync(entity) ? entity.Id : 0;
        var details = new List<StorageTakingDetail>();
        foreach (var item in drugInventoryList)
        {
            var detail = item.Adapt<StorageTakingDetail>();
            detail.InventoryId = item.Id;
            detail.TakingRecordId = recordId;
            detail.CurrentQuantity = item.Quantity;
            detail.TakingQuantity = item.Quantity;
            detail.CurrentSalePrice = item.SalePrice;
            detail.TakingSalePrice = item.SalePrice;
            detail.TotalCurrentSalePrice = item.Quantity * item.SalePrice;
            detail.TotalTakingSalePrice = item.Quantity * item.SalePrice;
            detail.TakingDifference = 0;
            detail.TakingDifferenceSalePrice = 0;
            detail.Status = 0;
            detail.Id = 0;

            details.Add(detail);
        }

        await _storageTakingDetailRep.InsertRangeAsync(details);
        return recordId;
    }

    /// <summary>
    /// 提交 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("提交")]
    [ApiDescriptionSettings(Name = "Submit"), HttpPost]
    [UnitOfWork]
    public async Task<bool> Submit(SubmitStorageTakingRecordInput input)
    {
        var result = await _storageTakingRecordRep.GetFirstAsync(u => u.Id == input.Id);
        if (result.Status == 0)
        {
            // 是否需要审核
            //  var storage = await _drugStorageRep.GetFirstAsync(u => u.Id == result.ApplyDeptId);
            var details = await _storageTakingDetailRep.GetListAsync(u =>
                u.TakingRecordId == input.Id);

            foreach (var detail in details)
            {
                await _inventoryService.UpdateInventoryAsync(detail, result);
                await _storageTakingDetailRep.UpdateAsync(
                    u => new StorageTakingDetail()
                    {
                        Status = 1
                    },
                    u => u.Id == detail.Id
                );
            }

            var takingQuantity = details.Sum(u => u.TakingQuantity);
            var currentQuantity = details.Sum(u => u.CurrentQuantity);
            var totalTakingSalePrice = details.Sum(u => u.TakingQuantity * u.TakingSalePrice);
            return await _storageTakingRecordRep.UpdateAsync(u
                => new StorageTakingRecord()
                {
                    Status = 1,
                    TakingQuantity = takingQuantity,
                    TakingSalePrice = totalTakingSalePrice,
                    TakingTime = DateTime.Now,
                    TakingResult = takingQuantity > currentQuantity ? 1 : 2
                }, u => u.Id == input.Id);
        }
        else
            throw Oops.Oh("当前状态禁止提交");
    }

    /// <summary>
    /// 更新药品盘点 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品盘点")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [UnitOfWork]
    public async Task Update(UpdateStorageTakingRecordInput input)
    {
        var record = await _storageTakingRecordRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        if (record.Status == 0)
        {
            var entity = input.Adapt<StorageTakingRecord>();
            var details = input.Details.Adapt<List<StorageTakingDetail>>();
            foreach (var detail in details)
            {
                // 判断是否存在重复 
                // 先过滤detail
                if (detail.Id.Equals(0L))
                {
                    // 判断是否存在重复 
                    // 先过滤detail
                    var exist = details.Where(u =>
                        (u.DrugId == detail.DrugId && u.BatchNo == detail.BatchNo && u.Id != detail.Id)
                    ).ToList();
                    if (exist.Count > 0)
                        throw Oops.Oh("当前列表存在重复数据[" + detail.DrugCode + "]" + exist.First().Id);
                    // 插入前验证一遍
                    var inventory = await _storageTakingDetailRep.GetFirstAsync(u =>
                        u.DrugId == detail.DrugId && u.BatchNo == detail.BatchNo && u.TakingRecordId==record.Id);
                    if (inventory != null)
                    {
                        throw Oops.Oh("与已保存数据中存在重复数据[" + detail.DrugCode + "]");
                    }
                    detail.TakingRecordId= record.Id;
                    detail.TakingQuantity = detail.TakingQuantity;
                    detail.TotalTakingSalePrice = detail.TakingQuantity * detail.TakingSalePrice;
                    detail.TakingDifference = detail.TakingQuantity - detail.CurrentQuantity;
                    detail.TakingDifferenceSalePrice =
                        (detail.TakingQuantity * detail.TakingSalePrice) -
                        (detail.CurrentQuantity * detail.CurrentSalePrice);
                    await _storageTakingDetailRep.InsertAsync(detail);
                }
            }

            foreach (var detail in details)
            {
                var takingDifferenceSalePrice = (detail.TakingQuantity * detail.TakingSalePrice) -
                                                (detail.CurrentQuantity * detail.CurrentSalePrice);
                await _storageTakingDetailRep.UpdateAsync(u =>
                        new StorageTakingDetail()
                        {
                            TakingQuantity = detail.TakingQuantity,
                            TotalTakingSalePrice = detail.TakingQuantity * u.TakingSalePrice,
                            TakingDifference = detail.TakingQuantity - detail.CurrentQuantity,
                            TakingDifferenceSalePrice =takingDifferenceSalePrice
                        },
                    u => u.Id == detail.Id);
            }

            entity.TakingSalePrice = details.Sum(u => u.TakingQuantity * u.TakingSalePrice);
            await _storageTakingRecordRep.UpdateAsync(
                u => new StorageTakingRecord()
                {
                    TakingQuantity = details.Sum(d => d.TakingQuantity),
                    TakingSalePrice = entity.TakingSalePrice,
                    TakingResult = 9
                }, u => u.Id == input.Id
            );
        }
        else
        {
            throw Oops.Oh("当前状态禁止修改");
        }
    }

    /// <summary>
    /// 删除药品盘点 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品盘点")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeleteStorageTakingRecordInput input)
    {
        var entity = await _storageTakingRecordRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status == 0)
        {
            await _storageTakingRecordRep.FakeDeleteAsync(entity); //假删除

            var details = await _storageTakingDetailRep.GetListAsync(u => u.TakingRecordId == input.Id);
            await _storageTakingDetailRep.FakeDeleteAsync(details);
        }
        else
        {
            throw Oops.Oh("当前状态禁止删除");
        }


        //await _storageTakingRecordRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品盘点 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品盘点")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteStorageTakingRecordInput> input)
    {
        var exp = Expressionable.Create<StorageTakingRecord>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _storageTakingRecordRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _storageTakingRecordRep.FakeDeleteAsync(list); //假删除
        //return await _storageTakingRecordRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataStorageTakingRecordInput input)
    {
        var storageIdData = await _storageTakingRecordRep.Context.Queryable<DrugStorage>()
            .InnerJoinIF<StorageTakingRecord>(input.FromPage, (u, r) => u.Id == r.StorageId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.StorageName}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "storageId", storageIdData },
        };
    }

    /// <summary>
    /// 导出药品盘点记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品盘点记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageStorageTakingRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportStorageTakingRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var statusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" })
            .Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e =>
        {
            e.StatusDictLabel = statusDictMap.GetValueOrDefault(e.Status == null ? "" : e.Status.ToString());
        });
        return ExcelHelper.ExportTemplate(list, "药品盘点导出记录");
    }

    /// <summary>
    /// 下载药品盘点数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品盘点数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportStorageTakingRecordOutput>(), "药品盘点导入模板", (_, info) =>
        {
            if (nameof(ExportStorageTakingRecordOutput.StorageFkDisplayName) == info.Name)
                return _storageTakingRecordRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}")
                    .Distinct().ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入药品盘点记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品盘点记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var statusDictMap = _sysDictTypeService
                .GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportStorageTakingRecordInput, StorageTakingRecord>(file,
                (list, markerErrorAction) =>
                {
                    _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                    {
                        // 链接 库房
                        var storageIdLabelList = pageItems.Where(x => x.StorageFkDisplayName != null)
                            .Select(x => x.StorageFkDisplayName).Distinct().ToList();
                        if (storageIdLabelList.Any())
                        {
                            var storageIdLinkMap = _storageTakingRecordRep.Context.Queryable<DrugStorage>()
                                .Where(u => storageIdLabelList.Contains($"{u.StorageName}")).ToList()
                                .ToDictionary(u => $"{u.StorageName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.StorageId = storageIdLinkMap.GetValueOrDefault(e.StorageFkDisplayName ?? "");
                                if (e.StorageId == null) e.Error = "库房链接失败";
                            });
                        }

                        // 映射字典值
                        // foreach(var item in pageItems) {
                        //     if (string.IsNullOrWhiteSpace(item.StatusDictLabel)) continue;
                        //     item.Status = statusDictMap.GetValueOrDefault(item.StatusDictLabel);
                        //     if (item.Status == null) item.Error = "状态字典映射失败";
                        // }

                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x => { return true; }).Adapt<List<StorageTakingRecord>>();

                        var storageable = _storageTakingRecordRep.Context.Storageable(rows)
                            .SplitError(it => it.Item.TakingNo?.Length > 100, "盘点单号长度不能超过100个字符")
                            .SplitError(it => it.Item.StorageCode?.Length > 100, "库房编码长度不能超过100个字符")
                            .SplitError(it => it.Item.StorageName?.Length > 100, "库房名称长度不能超过100个字符")
                            .SplitInsert(_ => true)
                            .ToStorage();

                        storageable.BulkCopy();
                        storageable.BulkUpdate();

                        // 标记错误信息
                        markerErrorAction.Invoke(storageable, pageItems, rows);
                    });
                });

            return stream;
        }
    }
}