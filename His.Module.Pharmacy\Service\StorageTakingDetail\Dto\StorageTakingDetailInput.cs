﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品盘点明细表基础输入参数
/// </summary>
public class StorageTakingDetailBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    public   long? InventoryId { get; set; }
    /// <summary>
    /// 药品ID
    /// </summary>
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    public virtual int? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    public virtual decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 现有零售总价
    /// </summary>
    public virtual decimal? TotalCurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    public virtual int? TakingQuantity { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    public virtual decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售总价
    /// </summary>
    public virtual decimal? TotalTakingSalePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public virtual string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    public virtual DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    public virtual DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂商
    /// </summary>
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public virtual string? DrugType { get; set; }
    
}

/// <summary>
/// 药品盘点明细表分页查询输入参数
/// </summary>
public class PageStorageTakingDetailInput : BasePageInput
{
    /// <summary>
    /// 盘点记录ID
    /// </summary>
    public long? TakingRecordId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    public int? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 现有零售总价
    /// </summary>
    public decimal? TotalCurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    public int? TakingQuantity { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售总价
    /// </summary>
    public decimal? TotalTakingSalePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期范围
    /// </summary>
     public DateTime?[] ProductionDateRange { get; set; }
    
    /// <summary>
    /// 有效期范围
    /// </summary>
     public DateTime?[] ExpirationDateRange { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂商
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品盘点明细表增加输入参数
/// </summary>
public class AddStorageTakingDetailInput
{
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    public   long? InventoryId { get; set; }
    /// <summary>
    /// 药品编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    public int? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 现有零售总价
    /// </summary>
    public decimal? TotalCurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    public int? TakingQuantity { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售总价
    /// </summary>
    public decimal? TotalTakingSalePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂商
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "生产厂商名称字符长度不能超过100")]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(20, ErrorMessage = "药品类型字符长度不能超过20")]
    public string? DrugType { get; set; }
    
}

/// <summary>
/// 药品盘点明细表删除输入参数
/// </summary>
public class DeleteStorageTakingDetailInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品盘点明细表更新输入参数
/// </summary>
public class UpdateStorageTakingDetailInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    public   long? InventoryId { get; set; }
    /// <summary>
    /// 药品ID
    /// </summary>    
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>    
    public int? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>    
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 现有零售总价
    /// </summary>    
    public decimal? TotalCurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>    
    public int? TakingQuantity { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>    
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售总价
    /// </summary>    
    public decimal? TotalTakingSalePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>    
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>    
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂商
    /// </summary>    
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "生产厂商名称字符长度不能超过100")]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>    
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(20, ErrorMessage = "药品类型字符长度不能超过20")]
    public string? DrugType { get; set; }
    
}

/// <summary>
/// 药品盘点明细表主键查询输入参数
/// </summary>
public class QueryByIdStorageTakingDetailInput : DeleteStorageTakingDetailInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataStorageTakingDetailInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 药品盘点明细表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportStorageTakingDetailInput : BaseImportInput
{
    /// <summary>
    /// 药品ID
    /// </summary>
    [ImporterHeader(Name = "药品ID")]
    [ExporterHeader("药品ID", Format = "", Width = 25, IsBold = true)]
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [ImporterHeader(Name = "药品编码")]
    [ExporterHeader("药品编码", Format = "", Width = 25, IsBold = true)]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [ImporterHeader(Name = "药品名称")]
    [ExporterHeader("药品名称", Format = "", Width = 25, IsBold = true)]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [ImporterHeader(Name = "规格")]
    [ExporterHeader("规格", Format = "", Width = 25, IsBold = true)]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [ImporterHeader(Name = "单位")]
    [ExporterHeader("单位", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    [ImporterHeader(Name = "现有数量")]
    [ExporterHeader("现有数量", Format = "", Width = 25, IsBold = true)]
    public int? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    [ImporterHeader(Name = "现有零售价")]
    [ExporterHeader("现有零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 现有零售总价
    /// </summary>
    [ImporterHeader(Name = "现有零售总价")]
    [ExporterHeader("现有零售总价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalCurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    [ImporterHeader(Name = "盘点数量")]
    [ExporterHeader("盘点数量", Format = "", Width = 25, IsBold = true)]
    public int? TakingQuantity { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    [ImporterHeader(Name = "盘点零售价")]
    [ExporterHeader("盘点零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售总价
    /// </summary>
    [ImporterHeader(Name = "盘点零售总价")]
    [ExporterHeader("盘点零售总价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalTakingSalePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [ImporterHeader(Name = "批号")]
    [ExporterHeader("批号", Format = "", Width = 25, IsBold = true)]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    [ImporterHeader(Name = "生产日期")]
    [ExporterHeader("生产日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    [ImporterHeader(Name = "有效期")]
    [ExporterHeader("有效期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [ImporterHeader(Name = "批准文号")]
    [ExporterHeader("批准文号", Format = "", Width = 25, IsBold = true)]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [ImporterHeader(Name = "国家医保编码")]
    [ExporterHeader("国家医保编码", Format = "", Width = 25, IsBold = true)]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂商 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂商 文本
    /// </summary>
    [ImporterHeader(Name = "生产厂商")]
    [ExporterHeader("生产厂商", Format = "", Width = 25, IsBold = true)]
    public string ManufacturerFkDisplayName { get; set; }
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>
    [ImporterHeader(Name = "生产厂商名称")]
    [ExporterHeader("生产厂商名称", Format = "", Width = 25, IsBold = true)]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 药品类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类型 文本
    /// </summary>
    [Dict("DrugType")]
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string DrugTypeDictLabel { get; set; }
    
}
