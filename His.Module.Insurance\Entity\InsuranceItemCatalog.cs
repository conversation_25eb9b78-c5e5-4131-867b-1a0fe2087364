﻿namespace His.Module.Insurance.Entity;

/// <summary>
/// 医保项目目录
/// </summary>
[Tenant("1300000000013")]
[SugarTable("insurance_item_catalog", "医保项目目录")]
public class InsuranceItemCatalog : EntityTenant
{
    /// <summary>
    /// 医疗项目编码
    /// </summary>
    [SugarColumn(ColumnName = "ylxm_bm", ColumnDescription = "医疗项目编码", Length = 50)]
    public virtual string YlxmBm { get; set; }
    
    /// <summary>
    /// 医疗项目标准名称
    /// </summary>
    [SugarColumn(ColumnName = "ylxm_bzmc", ColumnDescription = "医疗项目标准名称", Length = 200)]
    public virtual string YlxmBzmc { get; set; }
    
    /// <summary>
    /// 拼音
    /// </summary>
    [SugarColumn(ColumnName = "py", ColumnDescription = "拼音", Length = 100)]
    public virtual string? Py { get; set; }
    
    /// <summary>
    /// 适用症
    /// </summary>
    [SugarColumn(ColumnName = "syz", ColumnDescription = "适用症", Length = 500)]
    public virtual string? Syz { get; set; }
    
    /// <summary>
    /// 禁忌
    /// </summary>
    [SugarColumn(ColumnName = "jj", ColumnDescription = "禁忌", Length = 500)]
    public virtual string? Jj { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "gg", ColumnDescription = "规格", Length = 100)]
    public virtual string? Gg { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "dw", ColumnDescription = "单位", Length = 20)]
    public virtual string? Dw { get; set; }
    
    /// <summary>
    /// 参考价
    /// </summary>
    [SugarColumn(ColumnName = "ckj", ColumnDescription = "参考价")]
    public virtual decimal? Ckj { get; set; }
    
    /// <summary>
    /// 剂型码
    /// </summary>
    [SugarColumn(ColumnName = "jxm", ColumnDescription = "剂型码", Length = 20)]
    public virtual string? Jxm { get; set; }
    
    /// <summary>
    /// 注销标志
    /// </summary>
    [SugarColumn(ColumnName = "zx_bz", ColumnDescription = "注销标志", Length = 10)]
    public virtual string? ZxBz { get; set; }
    
    /// <summary>
    /// 生产企业
    /// </summary>
    [SugarColumn(ColumnName = "scqy", ColumnDescription = "生产企业", Length = 200)]
    public virtual string? Scqy { get; set; }
    
    /// <summary>
    /// 产地码
    /// </summary>
    [SugarColumn(ColumnName = "cdm", ColumnDescription = "产地码", Length = 10)]
    public virtual string? Cdm { get; set; }
    
    /// <summary>
    /// 处方药标志
    /// </summary>
    [SugarColumn(ColumnName = "cfy_bz", ColumnDescription = "处方药标志", Length = 10)]
    public virtual string? CfyBz { get; set; }
    
    /// <summary>
    /// GMP标志
    /// </summary>
    [SugarColumn(ColumnName = "gmp_bz", ColumnDescription = "GMP标志", Length = 10)]
    public virtual string? GmpBz { get; set; }

    /// <summary>
    /// 最小规格
    /// </summary>
    [SugarColumn(ColumnName = "zx_gg", ColumnDescription = "最小规格", Length = 100)]
    public virtual string? ZxGg { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(ColumnName = "gxsj", ColumnDescription = "更新时间", Length = 50)]
    public virtual string? Gxsj { get; set; }

    /// <summary>
    /// 药品标志
    /// </summary>
    [SugarColumn(ColumnName = "yp_bz", ColumnDescription = "药品标志", Length = 10)]
    public virtual string? YpBz { get; set; }

    /// <summary>
    /// 结算项目编号
    /// </summary>
    [SugarColumn(ColumnName = "js_xmbh", ColumnDescription = "结算项目编号", Length = 50)]
    public virtual string? JsXmbh { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    [SugarColumn(ColumnName = "zczh", ColumnDescription = "注册证号", Length = 100)]
    public virtual string? Zczh { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>
    [SugarColumn(ColumnName = "pzwh", ColumnDescription = "批准文号", Length = 100)]
    public virtual string? Pzwh { get; set; }

    /// <summary>
    /// 包装规格
    /// </summary>
    [SugarColumn(ColumnName = "bzgg", ColumnDescription = "包装规格", Length = 100)]
    public virtual string? Bzgg { get; set; }

    /// <summary>
    /// 上市备案号
    /// </summary>
    [SugarColumn(ColumnName = "lstd_fil_no", ColumnDescription = "上市备案号", Length = 100)]
    public virtual string? LstdFilNo { get; set; }

    /// <summary>
    /// 同步序号
    /// </summary>
    [SugarColumn(ColumnName = "sxh", ColumnDescription = "同步序号")]
    public virtual long? Sxh { get; set; }

    /// <summary>
    /// 最后同步时间
    /// </summary>
    [SugarColumn(ColumnName = "last_sync_time", ColumnDescription = "最后同步时间")]
    public virtual DateTime? LastSyncTime { get; set; }
}
