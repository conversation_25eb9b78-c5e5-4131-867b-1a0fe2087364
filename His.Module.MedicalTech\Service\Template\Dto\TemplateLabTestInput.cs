namespace His.Module.OutpatientDoctor.Service.Dto;

public class TemplateLabTestInput
{
    
    
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// `模板名称
    /// </summary> 
    public virtual string? TemplateName { get; set; }
    
     
    
    /// <summary>
    /// 1 全院 2 科室模板/ 3 个人模板
    /// </summary> 
    public virtual int? TemplateScope { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary> 
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary> 
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 中药付数
    /// </summary> 
    public virtual int? HerbsQuantity { get; set; }
    
  
    
    /// <summary>
    /// 创建者部门Id
    /// </summary> 
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary> 
    public virtual string? CreateOrgName { get; set; }
    
    public List<TemplateLabTestDetail> Details { get; set; }
}

 
 