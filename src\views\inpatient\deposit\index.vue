<template>
	<div class="deposit-container">
		<!-- 账户查询 -->
		<el-card shadow="hover" style="margin-bottom: 10px">
			<template #header>
				<span>账户查询</span>
			</template>
			<el-form :model="state.queryParams" ref="queryFormRef" :inline="true" label-width="80px">
				<el-row :gutter="16">
					<el-col :span="8">
						<el-form-item label="住院号" prop="inpatientNo">
							<el-input v-model="state.queryParams.inpatientNo" placeholder="请输入住院号" clearable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="住院流水号" prop="inpatientSerialNo">
							<el-input v-model="state.queryParams.inpatientSerialNo" placeholder="请输入住院流水号" clearable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item>
							<el-button-group>
								<el-button type="primary" icon="ele-Search" @click="handleSearchAccount">查询账户</el-button>
								<el-button icon="ele-Refresh" @click="resetQuery">重置</el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>

		<!-- 账户信息 -->
		<el-card shadow="hover" style="margin-bottom: 10px">
			<template #header>
				<span>账户信息</span>
			</template>
			<el-descriptions :column="4" border>
				<el-descriptions-item label="住院号">{{ state.currentAccount.inpatientNo }}</el-descriptions-item>
				<el-descriptions-item label="患者ID">{{ state.currentAccount.patientId }}</el-descriptions-item>
				<el-descriptions-item label="账户状态">
					<el-tag :type="getStatusType(state.currentAccount.status)">
						{{ getStatusText(state.currentAccount.status) }}
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="创建时间">{{ state.currentAccount.createTime }}</el-descriptions-item>
				<el-descriptions-item label="当前余额">
					<span :class="state.currentAccount.currentBalance > 0 ? 'text-success' : 'text-danger'" style="font-size: 18px; font-weight: bold">
						¥{{ (state.currentAccount.currentBalance || 0).toFixed(2) }}
					</span>
				</el-descriptions-item>
				<el-descriptions-item label="总缴费金额">
					<span class="text-success">¥{{ (state.currentAccount.totalPaidAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="总退款金额">
					<span class="text-warning">¥{{ (state.currentAccount.totalRefundedAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="总使用金额">
					<span class="text-info">¥{{ (state.currentAccount.totalUsedAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
			</el-descriptions>
		</el-card>

		<!-- 缴费功能 -->
		<el-card shadow="hover" style="margin-bottom: 10px">
			<template #header>
				<span>押金缴费</span>
			</template>
			<el-form :model="state.paymentForm" :rules="state.paymentRules" ref="paymentFormRef" :inline="true" label-width="100px">
				<el-row :gutter="20">
					<el-col :span="6">
						<el-form-item label="缴费金额" prop="amount">
							<el-input-number v-model="state.paymentForm.amount" placeholder="请输入缴费金额" :min="0.01" :precision="2" style="width: 100%" />
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="支付渠道" prop="channel">
							<el-select v-model="state.paymentForm.channel" placeholder="请选择支付渠道">
								<el-option label="现金" value="CASH" />
								<el-option label="银行卡" value="BANK_CARD" />
								<el-option label="微信支付" value="WECHAT_PAY" />
								<el-option label="支付宝" value="ALIPAY" />
								<el-option label="医保卡" value="MEDICAL_CARD" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="支付方式" prop="payType">
							<el-select v-model="state.paymentForm.payType" placeholder="请选择支付方式">
								<el-option label="现金支付" value="CASH_PAY" />
								<el-option label="刷卡支付" value="CARD_PAY" />
								<el-option label="扫码支付" value="QR_PAY" />
								<el-option label="转账支付" value="TRANSFER_PAY" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="6">
						<el-form-item label="外部凭证号" prop="receiptNo">
							<el-input v-model="state.paymentForm.receiptNo" placeholder="请输入外部凭证号（可选）" clearable />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.paymentForm.remark" placeholder="请输入备注信息（可选）" clearable />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item>
							<el-button type="success" icon="ele-Money" @click="handlePayment" :disabled="state.currentAccount.status === 3" :loading="state.paymentLoading"> 确认缴费 </el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>

		<!-- 交易明细记录 -->
		<el-card shadow="hover">
			<template #header>
				<span>交易明细记录</span>
			</template>
			<el-table :data="state.transactions" v-loading="state.loading" border stripe height="400px">
				<el-table-column prop="id" label="交易ID" width="80" />
				<el-table-column prop="transactionType" label="交易类型" width="100" align="center">
					<template #default="scope">
						<el-tag :type="getTransactionTypeColor(scope.row.transactionType)">
							{{ getTransactionTypeText(scope.row.transactionType) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="amount" label="交易金额" width="120" align="right">
					<template #default="scope">
						<span :class="getAmountClass(scope.row.transactionType)"> {{ getAmountPrefix(scope.row.transactionType) }}¥{{ (scope.row.amount || 0).toFixed(2) }} </span>
					</template>
				</el-table-column>
				<el-table-column prop="refundableAmount" label="可退金额" width="120" align="right">
					<template #default="scope">
						<span class="text-info">¥{{ (scope.row.refundableAmount || 0).toFixed(2) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="channel" label="支付渠道" width="100" />
				<el-table-column prop="paymentMethod" label="支付方式" width="100" />
				<el-table-column prop="receiptNo" label="外部凭证号" width="120" show-overflow-tooltip />
				<el-table-column prop="invoiceNo" label="发票号" width="120" show-overflow-tooltip />
				<el-table-column prop="status" label="状态" width="80" align="center">
					<template #default="scope">
						<el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
							{{ scope.row.status === '1' ? '成功' : '失败' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="createTime" label="交易时间" width="160" show-overflow-tooltip />
				<el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
				<el-table-column label="操作" width="150" align="center" fixed="right">
					<template #default="scope">
						<el-button-group size="small">
							<el-button v-if="scope.row.transactionType === '0' && scope.row.refundableAmount > 0" type="warning" @click="handleRefundTransaction(scope.row)"> 退款 </el-button>
							<el-button v-if="scope.row.transactionType === '0' && scope.row.status === '1'" type="danger" @click="handleCorrect(scope.row)"> 红冲 </el-button>
						</el-button-group>
					</template>
				</el-table-column>
			</el-table>
		</el-card>

		<!-- 对话框组件 -->
		<RefundDialog ref="refundDialogRef" @reload-data="loadAccountDetail" />
		<CorrectDialog ref="correctDialogRef" @reload-data="loadAccountDetail" />
	</div>
</template>

<script setup lang="ts" name="DepositManagement">
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useDepositApi, type DepositAccount, type DepositTransaction } from '/@/api/inpatient/deposit';
import { useInpatientRegisterApi } from '/@/api/inpatient/inpatientRegister';

// 引入对话框组件
import RefundDialog from './components/RefundDialog.vue';
import CorrectDialog from './components/CorrectDialog.vue';

// API实例
const depositApi = useDepositApi();
const inpatientRegisterApi = useInpatientRegisterApi();

// 响应式数据
const state = reactive({
	loading: false,
	paymentLoading: false,
	currentAccount: {} as DepositAccount,
	transactions: [] as DepositTransaction[],
	queryParams: {
		inpatientNo: '',
		inpatientSerialNo: '',
	},
	paymentForm: {
		amount: undefined as number | undefined,
		channel: '',
		payType: '',
		receiptNo: '',
		remark: '',
	},
	paymentRules: {
		amount: [
			{ required: true, message: '请输入缴费金额', trigger: 'blur' },
			{ type: 'number', min: 0.01, message: '缴费金额必须大于0', trigger: 'blur' },
		],
		channel: [{ required: true, message: '请选择支付渠道', trigger: 'change' }],
		payType: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
	},
});

// 引用
const queryFormRef = ref();
const paymentFormRef = ref();
const refundDialogRef = ref();
const correctDialogRef = ref();

// 获取状态类型
const getStatusType = (status: number) => {
	const types = ['success', 'warning', 'info', 'danger'];
	return types[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: number) => {
	const texts = ['正常', '冻结', '结算中', '已关闭'];
	return texts[status] || '未知';
};

// 获取交易类型颜色
const getTransactionTypeColor = (type: string) => {
	const colors: Record<string, string> = {
		'0': 'success', // 缴费
		'1': 'warning', // 退款
		'2': 'info', // 使用
		'3': 'danger', // 冲正
	};
	return colors[type] || 'info';
};

// 获取交易类型文本
const getTransactionTypeText = (type: string) => {
	const texts: Record<string, string> = {
		'0': '缴费',
		'1': '退款',
		'2': '使用',
		'3': '冲正',
	};
	return texts[type] || '未知';
};

// 获取金额样式类
const getAmountClass = (type: string) => {
	if (type === '0') return 'text-success'; // 缴费 - 绿色
	if (type === '1') return 'text-warning'; // 退款 - 橙色
	if (type === '2') return 'text-info'; // 使用 - 灰色
	if (type === '3') return 'text-danger'; // 冲正 - 红色
	return '';
};

// 获取金额前缀
const getAmountPrefix = (type: string) => {
	if (type === '0') return '+'; // 缴费
	if (type === '1' || type === '2' || type === '3') return '-'; // 退款、使用、冲正
	return '';
};

// 查询账户
const handleSearchAccount = async () => {
	if (!state.queryParams.inpatientNo && !state.queryParams.inpatientNo) {
		ElMessage.warning('请输入住院号或住院流水号');
		return;
	}

	state.loading = true;
	try {
		const response = await inpatientRegisterApi.detail(state.queryParams);
		if (response.data) {
			// 如果找到账户，加载详细信息
			await loadAccountDetail(response.data.result.id);
			ElMessage.success('账户查询成功');
		} else {
			ElMessage.warning('未找到对应的押金账户');
			state.currentAccount = {};
			state.transactions = [];
		}
	} catch (error) {
		console.error('查询账户失败:', error);
		ElMessage.error('查询账户失败');
		state.currentAccount = {};
		state.transactions = [];
	} finally {
		state.loading = false;
	}
};

// 加载账户详情
const loadAccountDetail = async (accountId: number) => {
	state.loading = true;
	try {
		const response = await depositApi.detail({ inpatientRegisterId: accountId });
		if (response.data.result) {
			state.currentAccount = response.data.result;
			state.transactions = response.data.result.transactions || [];
		}
	} catch (error) {
		console.error('加载账户详情失败:', error);
	} finally {
		state.loading = false;
	}
};

// 重置查询
const resetQuery = () => {
	state.queryParams = {
		inpatientNo: '',
		inpatientSerialNo: '',
	};
	state.currentAccount = {};
	state.transactions = [];
};

// 押金缴费
const handlePayment = async () => {
	if (!paymentFormRef.value) return;

	try {
		await paymentFormRef.value.validate();
	} catch (error) {
		ElMessage.warning('请完善缴费信息');
		return;
	}

	state.paymentLoading = true;
	try {
		const paymentData = {
			accountId: state.currentAccount.id,
			amount: state.paymentForm.amount,
			channel: state.paymentForm.channel,
			payType: state.paymentForm.payType,
			receiptNo: state.paymentForm.receiptNo,
			remark: state.paymentForm.remark,
		};

		await depositApi.payment(paymentData);
		ElMessage.success('缴费成功');

		// 重置表单
		state.paymentForm = {
			amount: undefined,
			channel: '',
			payType: '',
			receiptNo: '',
			remark: '',
		};
		paymentFormRef.value.resetFields();

		// 重新加载账户详情
		await loadAccountDetail(state.currentAccount.id!);
	} catch (error) {
		console.error('缴费失败:', error);
		ElMessage.error('缴费失败');
	} finally {
		state.paymentLoading = false;
	}
};

// 交易退款
const handleRefundTransaction = (transaction: DepositTransaction) => {
	refundDialogRef.value?.openDialog(state.currentAccount, transaction);
};

// 红冲操作
const handleCorrect = (transaction: DepositTransaction) => {
	correctDialogRef.value?.openDialog(transaction);
};
</script>

<style lang="scss" scoped>
.deposit-container {
	padding: 10px;
}

.text-success {
	color: #67c23a;
	font-weight: bold;
}

.text-warning {
	color: #e6a23c;
	font-weight: bold;
}

.text-info {
	color: #909399;
	font-weight: bold;
}

.text-danger {
	color: #f56c6c;
	font-weight: bold;
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
