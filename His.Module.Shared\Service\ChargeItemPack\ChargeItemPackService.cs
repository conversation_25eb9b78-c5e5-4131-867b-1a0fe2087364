﻿namespace His.Module.Shared.Service;

/// <summary>
/// 收费项目套餐服务
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class ChargeItemPackService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ChargeItemPack> _ChargeItemPackRep;
    private readonly SqlSugarRepository<ChargeItem> _ChargeItemRep;

    public ChargeItemPackService(SqlSugarRepository<ChargeItemPack> ChargeItemPackRep,
        SqlSugarRepository<ChargeItem> ChargeItemRep)
    {
        _ChargeItemPackRep = ChargeItemPackRep;
        _ChargeItemRep = ChargeItemRep;
    }

    /// <summary>
    /// 分页查询收费项目套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    [DisplayName("分页查询收费项目套餐")]
    public async Task<SqlSugarPagedList<ChargeItemPackOutput>> Page(PageChargeItemPackInput input)
    {
        return await _ChargeItemPackRep.AsQueryable()
            .LeftJoin<ChargeItem>((u, a) => u.ChargeItemId == a.Id)
            .Where(u => u.PackId == input.PackId)
            .OrderBy(u => new { u.Id })
            .Select((u, a) => new ChargeItemPackOutput()
            {
                Code = a.Code,
                Name = a.Name,
                Price = a.Price,
                Unit = a.Unit,
            }, true)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加收费项目套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    [DisplayName("增加收费项目套餐")]
    [UnitOfWork]
    public async Task Add(List<AddChargeItemPackInput> input)
    {
        var packId = input.Select(a => a.PackId).First();
        //先删除套餐下的收费项目再进行添加
        await _ChargeItemPackRep.AsDeleteable().Where(u => u.PackId == packId).ExecuteCommandAsync();
        var entity = input.Adapt<List<ChargeItemPack>>();
        await _ChargeItemPackRep.InsertRangeAsync(entity);
        var singles = await _ChargeItemRep.AsQueryable()
            .Where(u => input.Select(a => a.ChargeItemId).ToList().Contains(u.Id))
            .ToListAsync();
        var totalPrice = decimal.Zero;
        foreach (var item in input)
        {
            var single = singles.First(u => u.Id == item.ChargeItemId);
            totalPrice += (decimal)(single.Price * item.ChargeItemQuantity);
        }
        await _ChargeItemRep.AsUpdateable()
            .SetColumns(u => u.Price == totalPrice)
            .Where(u => u.Id == packId).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除收费项目套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    [DisplayName("删除收费项目套餐")]
    [UnitOfWork]
    public async Task Delete(DeleteChargeItemPackInput input)
    {
        var entity = await _ChargeItemPackRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _ChargeItemPackRep.DeleteAsync(entity);
        var single = await _ChargeItemRep.GetFirstAsync(u => u.Id == entity.ChargeItemId);
        var price = single.Price * entity.ChargeItemQuantity;
        await _ChargeItemRep.AsUpdateable()
            .SetColumns(u => u.Price == u.Price - price)
            .Where(u => u.Id == entity.PackId).ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新收费项目套餐
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    [DisplayName("更新收费项目套餐")]
    public async Task Update(UpdateChargeItemPackInput input)
    {
        var entity = input.Adapt<ChargeItemPack>();
        await _ChargeItemPackRep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取收费项目套餐列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    [DisplayName("获取收费项目套餐列表")]
    public async Task<List<ChargeItemPackOutput>> List([FromQuery] PageChargeItemPackInput input)
    {
        return await _ChargeItemPackRep.AsQueryable()
            .LeftJoin<ChargeItem>((u, a) => u.ChargeItemId == a.Id)
            .Where(u => u.PackId == input.PackId)
            .OrderBy(u => new { u.Id })
            .Select((u, a) => new ChargeItemPackOutput()
            {
                Name = a.Name,
                Price = a.Price,
                Unit = a.Unit,
            }, true)
            .ToListAsync();
    }
}