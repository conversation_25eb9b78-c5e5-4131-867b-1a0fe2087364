﻿namespace His.Module.OutpatientDoctor.Enum;

/// <summary>
/// 挂号状态枚举
/// </summary>
[Description("挂号状态枚举")]
public enum RegStatusEnum
{
    /// <summary>
	/// 挂号
	/// </summary>
	[Description("挂号")]
    Register = 0,

    /// <summary>
	/// 就诊
	/// </summary>
	[Description("就诊")]
    Visit = 1,

    /// <summary>
    /// 结束就诊
    /// </summary>
    [Description("结束就诊")]
    End = 2,

    /// <summary>
    /// 转诊
    /// </summary>
    [Description("转诊")]
    Referral = 3,

    /// <summary>
    /// 退号
    /// </summary>
    [Description("退号")]
    Refund = 4,

    /// <summary>
    /// 医保登记失败
    /// </summary>
    [Description("医保登记失败")]
    MedInsRegFailed = 5,
    
    /// <summary>
    /// 医保登记失败
    /// </summary>
    [Description("未收费")]
    NotCharged = 9,
}