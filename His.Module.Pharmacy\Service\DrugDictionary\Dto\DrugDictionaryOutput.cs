﻿namespace His.Module.Pharmacy;

/// <summary>
/// 药品字典维护输出参数
/// </summary>
public class DrugDictionaryOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string DrugCode { get; set; }    
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string DrugName { get; set; }    
    
    /// <summary>
    /// 药品名称拼音
    /// </summary>
    public string? DrugNamePinyin { get; set; }    
    
    /// <summary>
    /// 通用名称
    /// </summary>
    public string? GenericName { get; set; }    
    
    /// <summary>
    /// 通用名称拼音
    /// </summary>
    public string? GenericNamePinyin { get; set; }    
    
    /// <summary>
    /// 产品名称
    /// </summary>
    public string? ProductName { get; set; }    
    
    /// <summary>
    /// 产品名称拼音
    /// </summary>
    public string? ProductNamePinyin { get; set; }    
    
    /// <summary>
    /// 药品类型
    /// </summary>
    public string? DrugType { get; set; }    
    
    /// <summary>
    /// 药品分类
    /// </summary>
    public string? DrugCategory { get; set; }    
    
    /// <summary>
    /// 药品分类 描述
    /// </summary>
    public string DrugCategoryFkDisplayName { get; set; } 
    
    /// <summary>
    /// 药理分类
    /// </summary>
    public string? PharmacologicalClass { get; set; }    
    
    /// <summary>
    /// 药理分类 描述
    /// </summary>
    public string PharmacologicalClassFkDisplayName { get; set; } 
    
    /// <summary>
    /// 抗生素级别
    /// </summary>
    public string? AntibacterialLevel { get; set; }    
    
    /// <summary>
    /// 剂型
    /// </summary>
    public string? DrugForm { get; set; }    
    
    /// <summary>
    /// 剂型 描述
    /// </summary>
    public string DrugFormFkDisplayName { get; set; } 
    
    /// <summary>
    /// 用药途径
    /// </summary>
    public string? DrugRoute { get; set; }    
    
    /// <summary>
    /// 用药频次
    /// </summary>
    public string? DrugFreq { get; set; }    
    
    /// <summary>
    /// 用药频次 描述
    /// </summary>
    public string DrugFreqFkDisplayName { get; set; } 
    
    /// <summary>
    /// ICD10编码
    /// </summary>
     [SugarColumn( 
      
    IsJson = true  )]
    public List<String> ? Icd10 { get; set; }    
    
    /// <summary>
    /// 生产企业
    /// </summary>
    public long? ManufacturerId { get; set; }    
    
    /// <summary>
    /// 生产企业 描述
    /// </summary>
    public string ManufacturerFkDisplayName { get; set; } 
    
    /// <summary>
    /// 生产企业名称
    /// </summary>
    public string? ManufacturerName { get; set; }    
    
    /// <summary>
    /// 产地
    /// </summary>
    public string? PlaceOfOrigin { get; set; }    
    
    /// <summary>
    /// 产地 描述
    /// </summary>
    public string PlaceOfOriginFkDisplayName { get; set; } 
    
    /// <summary>
    /// 入库单位
    /// </summary>
    public string? StorageUnit { get; set; }    
    
    /// <summary>
    /// 入库单位 描述
    /// </summary>
    public string StorageUnitFkDisplayName { get; set; } 
    
    /// <summary>
    /// 包装规格
    /// </summary>
    public string? PackageSpec { get; set; }    
    
    /// <summary>
    /// 包装数量
    /// </summary>
    public int? PackageQuantity { get; set; }    
    
    /// <summary>
    /// 最小包装单位
    /// </summary>
    public string? MinPackageUnit { get; set; }    
    
    /// <summary>
    /// 剂量单位
    /// </summary>
    public string? DosageUnit { get; set; }    
    
    /// <summary>
    /// 剂量单位 描述
    /// </summary>
    public string DosageUnitFkDisplayName { get; set; } 
    
    /// <summary>
    /// 剂量值
    /// </summary>
    public decimal? DosageValue { get; set; }    
    
    /// <summary>
    /// 含量
    /// </summary>
    public decimal? ContentValue { get; set; }    
    
    /// <summary>
    /// 含量 描述
    /// </summary>
    public string ContentValueFkDisplayName { get; set; } 
    
    /// <summary>
    /// 含量单位
    /// </summary>
    public string? ContentUnit { get; set; }    
    
    /// <summary>
    /// 门诊规格
    /// </summary>
    public string? OutpatientSpec { get; set; }    
    
    /// <summary>
    /// 门诊单位
    /// </summary>
    public string? OutpatientUnit { get; set; }    
    
    /// <summary>
    /// 门诊单位 描述
    /// </summary>
    public string OutpatientUnitFkDisplayName { get; set; } 
    
    /// <summary>
    /// 门诊包装数量
    /// </summary>
    public int? OutpatientPackageQuantity { get; set; }    
    
    /// <summary>
    /// 住院规格
    /// </summary>
    public string? InpatientSpec { get; set; }    
    
    /// <summary>
    /// 住院单位
    /// </summary>
    public string? InpatientUnit { get; set; }    
    
    /// <summary>
    /// 住院单位 描述
    /// </summary>
    public string InpatientUnitFkDisplayName { get; set; } 
    
    /// <summary>
    /// 住院包装数量
    /// </summary>
    public int? InpatientPackageQuantity { get; set; }    
    
    /// <summary>
    /// 采购类型
    /// </summary>
    public string? PurchaseType { get; set; }    
    
    /// <summary>
    /// 上市许可持有人
    /// </summary>
    public string? Holder { get; set; }    
    
    /// <summary>
    /// 上市许可持有人 描述
    /// </summary>
    public string HolderFkDisplayName { get; set; } 
    
    /// <summary>
    /// 进价
    /// </summary>
    public decimal? PurchasePrice { get; set; }    
    
    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? SalePrice { get; set; }    
    
    /// <summary>
    /// 每公斤进价
    /// </summary>
    public decimal? PurchasePriceOfKg { get; set; }    
    
    /// <summary>
    /// 每公斤零售价
    /// </summary>
    public decimal? SalePriceOfKg { get; set; }    
    
    /// <summary>
    /// 电子监管码
    /// </summary>
    public string? RegulationCode { get; set; }    
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public string? ApprovalNumber { get; set; }    
    
    /// <summary>
    /// 优先使用
    /// </summary>
    public string? PriorityUse { get; set; }    
    
    /// <summary>
    /// 药房货位
    /// </summary>
    public string? PharmacyLocation { get; set; }    
    
    /// <summary>
    /// 药库货位
    /// </summary>
    public string? StorehouseLocation { get; set; }    
    
    /// <summary>
    /// YPID
    /// </summary>
    public string? Ypid { get; set; }    
    
    /// <summary>
    /// 是否拆零
    /// </summary>
    public YesNoEnum IsSplit { get; set; }    
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }    
    
    /// <summary>
    /// 是否医保药品
    /// </summary>
    public YesNoEnum IsMedicare { get; set; }    
    
    /// <summary>
    /// 是否自制药
    /// </summary>
    public YesNoEnum IsSelf { get; set; }    
    
    /// <summary>
    /// 是否基本药物
    /// </summary>
    public YesNoEnum IsBasic { get; set; }    
    
    /// <summary>
    /// 是否皮试药品
    /// </summary>
    public YesNoEnum IsSkinTest { get; set; }    
    
    /// <summary>
    /// 是否国谈药
    /// </summary>
    public YesNoEnum IsCountry { get; set; }    
    
    /// <summary>
    /// 是否辅助药品
    /// </summary>
    public YesNoEnum IsAssist { get; set; }    
    
    /// <summary>
    /// 是否临采药品
    /// </summary>
    public YesNoEnum IsTemporary { get; set; }    
    
    /// <summary>
    /// 是否溶媒
    /// </summary>
    public YesNoEnum IsSolvent { get; set; }    
    
    /// <summary>
    /// 是否新冠门诊药品
    /// </summary>
    public YesNoEnum IsCovid { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 药品字典维护数据导入模板实体
/// </summary>
public class ExportDrugDictionaryOutput : ImportDrugDictionaryInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
