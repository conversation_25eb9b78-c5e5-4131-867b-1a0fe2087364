--
drop table if exists pharmacy.drug_dictionary;
drop table if exists pharmacy.drug_attribute;
create table pharmacy.drug_dictionary(
    id bigserial primary key,
    drug_code varchar(50) not null,
    drug_name varchar(200) not null,
    drug_name_pinyin varchar(200),
    
    -- 规格
    --通用名称
    generic_name varchar(200),
    generic_name_pinyin varchar(200),
    --产品名称 
    product_name varchar(200),
    product_name_pinyin varchar(200),
    -- “西药”、“中成药”、“中药饮片”。
    drug_type varchar(100)  ,
     -- 存储药品的分类，如“普通药品”、“精神类药品”、“抗菌药物”、“生物制药”、“疫苗”。
    drug_category bigint,
    -- 药理分类
    pharmacological_class bigint,
    -- 抗生素级别
    antibacterial_level varchar(100),
    -- 剂型
    drug_form bigint,

    --用药途径
    drug_route bigint,
    -- 用药频次
    drug_freq bigint,
    icd10 jsonb,
    --生产企业
    manufacturer_id bigint,
    manufacturer_name varchar(200),
    --产地
    place_of_origin bigint,
    --入库单位
    storage_unit varchar(100),
    --包装规格
    package_spec varchar(100),
    --包装数量 
    package_quantity int,
    --最小包装单位
    min_package_unit varchar(100) ,

    dosage_unit varchar(100),
    dosage_value decimal,
    -- 含量
    content_value decimal,
    content_unit varchar(100),
    --门诊规格 
    outpatient_spec varchar(100),
    --门诊单位
    outpatient_unit varchar(100),
     --门诊包装数量 
    outpatient_package_quantity int,
    --住院规格
    inpatient_spec varchar(100),
    --住院单位
    inpatient_unit varchar(100),
    --住院包装数量
    inpatient_package_quantity int,
    -- 采购类型
    purchase_type varchar(100),
    --上市许可持有人 
    holder bigint,--
    -- 进价
    purchase_price decimal,
    -- 零售价
    sale_price decimal,
 
    purchase_price_of_kg decimal,

    sale_price_of_kg decimal,
    -- 电子监管码 
    regulation_code varchar(100),
    -- 批准文号 
    approval_number varchar(100),
    --优先使用
    priority_use varchar(100),
    --药房货位
    pharmacy_location varchar(100),
    --药库货位
    storehouse_location varchar(100),
    --ypid
    ypid varchar(100),
    --拆零 
    is_split int default 0,
    -- 国家医保编码
     medicine_code varchar(100),
    -- 医保药品

    is_medicare int default 2,
    --自制药
    is_self int default 2,
    --基本药物
    is_basic int default 2,
    -- 皮试
    is_skin_test int default 2,
    -- 国谈药
    is_country int default 2,
    -- 辅助 
    is_assist int default 2,
    -- 临采
    is_temporary int default 2,
    -- 溶媒
    is_solvent int default 2,
    -- 新冠门诊
    is_covid int default 2,

 
    
    -- 是否停用
    status int default 1,
    create_org_id bigint,
    create_org_name varchar(100),
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint 
);

-- create sequence pharmacy.drug_dictionary_code_seq;
-- COMMENT ON SEQUENCE pharmacy.drug_dictionary_code_seq IS '药品编码序列';
 
-- 表注释
COMMENT ON TABLE pharmacy.drug_dictionary IS '药品字典表';
        

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_dictionary.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_dictionary.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.drug_dictionary.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.drug_dictionary.drug_name_pinyin IS '药品名称拼音';
COMMENT ON COLUMN pharmacy.drug_dictionary.generic_name IS '通用名称';
COMMENT ON COLUMN pharmacy.drug_dictionary.generic_name_pinyin IS '通用名称拼音';
COMMENT ON COLUMN pharmacy.drug_dictionary.product_name IS '产品名称';
COMMENT ON COLUMN pharmacy.drug_dictionary.product_name_pinyin IS '产品名称拼音';
COMMENT ON COLUMN pharmacy.drug_dictionary.drug_type IS '药品类型（西药、中成药、中药饮片等）';
COMMENT ON COLUMN pharmacy.drug_dictionary.drug_category IS '药品分类（普通药品、精神类药品、抗菌药物等）';
COMMENT ON COLUMN pharmacy.drug_dictionary.pharmacological_class IS '药理分类';
COMMENT ON COLUMN pharmacy.drug_dictionary.antibacterial_level IS '抗生素级别';
COMMENT ON COLUMN pharmacy.drug_dictionary.drug_form IS '剂型';
COMMENT ON COLUMN pharmacy.drug_dictionary.drug_route IS '用药途径';
COMMENT ON COLUMN pharmacy.drug_dictionary.drug_freq IS '用药频次';
COMMENT ON COLUMN pharmacy.drug_dictionary.icd10 IS 'ICD10编码';
COMMENT ON COLUMN pharmacy.drug_dictionary.manufacturer_id IS '生产企业ID';
COMMENT ON COLUMN pharmacy.drug_dictionary.manufacturer_name IS '生产企业名称';
COMMENT ON COLUMN pharmacy.drug_dictionary.place_of_origin IS '产地';
COMMENT ON COLUMN pharmacy.drug_dictionary.storage_unit IS '入库单位';
COMMENT ON COLUMN pharmacy.drug_dictionary.package_spec IS '包装规格';
COMMENT ON COLUMN pharmacy.drug_dictionary.package_quantity IS '包装数量';
COMMENT ON COLUMN pharmacy.drug_dictionary.min_package_unit IS '最小包装单位';
COMMENT ON COLUMN pharmacy.drug_dictionary.dosage_unit IS '剂量单位';
COMMENT ON COLUMN pharmacy.drug_dictionary.dosage_value IS '剂量值';
COMMENT ON COLUMN pharmacy.drug_dictionary.content_value IS '含量';
COMMENT ON COLUMN pharmacy.drug_dictionary.content_unit IS '含量单位';
COMMENT ON COLUMN pharmacy.drug_dictionary.outpatient_spec IS '门诊规格';
COMMENT ON COLUMN pharmacy.drug_dictionary.outpatient_unit IS '门诊单位';
COMMENT ON COLUMN pharmacy.drug_dictionary.outpatient_package_quantity IS '门诊包装数量';
COMMENT ON COLUMN pharmacy.drug_dictionary.inpatient_spec IS '住院规格';
COMMENT ON COLUMN pharmacy.drug_dictionary.inpatient_unit IS '住院单位';
COMMENT ON COLUMN pharmacy.drug_dictionary.inpatient_package_quantity IS '住院包装数量';
COMMENT ON COLUMN pharmacy.drug_dictionary.purchase_type IS '采购类型';
COMMENT ON COLUMN pharmacy.drug_dictionary.holder IS '上市许可持有人';
COMMENT ON COLUMN pharmacy.drug_dictionary.purchase_price IS '进价';
COMMENT ON COLUMN pharmacy.drug_dictionary.sale_price IS '零售价';
COMMENT ON COLUMN pharmacy.drug_dictionary.purchase_price_of_kg IS '每公斤进价';
COMMENT ON COLUMN pharmacy.drug_dictionary.sale_price_of_kg IS '每公斤零售价';
COMMENT ON COLUMN pharmacy.drug_dictionary.regulation_code IS '电子监管码';
COMMENT ON COLUMN pharmacy.drug_dictionary.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.drug_dictionary.priority_use IS '优先使用';
COMMENT ON COLUMN pharmacy.drug_dictionary.pharmacy_location IS '药房货位';
COMMENT ON COLUMN pharmacy.drug_dictionary.storehouse_location IS '药库货位';
COMMENT ON COLUMN pharmacy.drug_dictionary.ypid IS 'YPID';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_split IS '是否拆零';
COMMENT ON COLUMN pharmacy.drug_dictionary.medicine_code IS '国家医保编码';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_medicare IS '是否医保药品';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_self IS '是否自制药';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_basic IS '是否基本药物';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_skin_test IS '是否皮试药品';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_country IS '是否国谈药';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_assist IS '是否辅助药品';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_temporary IS '是否临采药品';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_solvent IS '是否溶媒';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_covid IS '是否新冠门诊药品';
COMMENT ON COLUMN pharmacy.drug_dictionary.status IS '1 启用 2 停用';
COMMENT ON COLUMN pharmacy.drug_dictionary.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_dictionary.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_dictionary.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_dictionary.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_dictionary.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_dictionary.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_dictionary.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_dictionary.tenant_id IS '租户ID';


alter table pharmacy.drug_dictionary
-- 库存上限 
add column upper_limit int default 0,
-- 库存下限
add column lower_limit int default 0;
comment on column pharmacy.drug_dictionary.upper_limit IS '库存上限';
comment on column pharmacy.drug_dictionary.lower_limit IS '库存下限';
-- 煎药方式
-- 药品煎制方式
--  
-- create table pharmacy.drug_decocted_mode(
--     id bigserial primary key,
--     mode_name varchar(100),
--     mode_name_pinyin varchar(100),
--     -- 是否停用
--     status int default 1,
--     create_time timestamp,
--     create_user_id bigint,
--     create_user_name varchar(100),
--     update_time timestamp,
--     update_user_id bigint,
--     update_user_name varchar(100),
--     is_delete bool default false,
--     tenant_id bigint
-- )
-- -- 表注释
-- COMMENT ON TABLE pharmacy.drug_decocted_mode IS '煎药方式表';
-- 
-- -- 字段注释
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.id IS '主键ID';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.mode_name IS '煎药方式名称';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.mode_name_pinyin IS '煎药方式名称拼音';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.status IS '1 启用 2 停用';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.create_time IS '创建时间';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.create_user_id IS '创建用户ID';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.create_user_name IS '创建用户名';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.update_time IS '更新时间';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.update_user_id IS '更新用户ID';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.update_user_name IS '更新用户名';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.is_delete IS '是否删除';
-- COMMENT ON COLUMN pharmacy.drug_decocted_mode.tenant_id IS '租户ID';

-- 药品持有人
create table pharmacy.drug_holder(
    id bigserial primary key,
    holder_name varchar(100),
    holder_name_pinyin varchar(100),
    remark varchar(255),
    -- 是否停用
    status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.drug_holder IS '药品持有人表';

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_holder.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_holder.holder_name IS '持有人名称';
COMMENT ON COLUMN pharmacy.drug_holder.holder_name_pinyin IS '持有人名称拼音';
COMMENT ON COLUMN pharmacy.drug_holder.remark IS '备注';
COMMENT ON COLUMN pharmacy.drug_holder.status IS '1 启用 2 停用';
COMMENT ON COLUMN pharmacy.drug_holder.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_holder.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_holder.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_holder.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_holder.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_holder.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_holder.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_holder.tenant_id IS '租户ID';
-- 药品产地
create table pharmacy.drug_place(
    id bigserial primary key,
    place_name varchar(100),
    place_name_pinyin varchar(100),
    remark varchar(255),
    status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.drug_place IS '药品产地表';

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_place.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_place.place_name IS '产地名称';
COMMENT ON COLUMN pharmacy.drug_place.place_name_pinyin IS '产地名称拼音';
COMMENT ON COLUMN pharmacy.drug_place.remark IS '备注';
COMMENT ON COLUMN pharmacy.drug_place.status IS '1 启用 2 停用';
COMMENT ON COLUMN pharmacy.drug_place.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_place.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_place.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_place.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_place.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_place.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_place.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_place.tenant_id IS '租户ID';

create table pharmacy.drug_form(
    id bigserial primary key,
    form_name varchar(100),
    form_name_pinyin varchar(100),
    big_form_id int,
    big_form_name varchar(100),
    status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.drug_form IS '药品剂型表';

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_form.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_form.form_name IS '剂型名称';
COMMENT ON COLUMN pharmacy.drug_form.form_name_pinyin IS '剂型名称拼音';
COMMENT ON COLUMN pharmacy.drug_form.big_form_id IS '大剂型ID';
COMMENT ON COLUMN pharmacy.drug_form.big_form_name IS '大剂型名称';
COMMENT ON COLUMN pharmacy.drug_form.status IS '1 启用 2 停用';
COMMENT ON COLUMN pharmacy.drug_form.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_form.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_form.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_form.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_form.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_form.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_form.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_form.tenant_id IS '租户ID';

create table pharmacy.drug_big_form(
       id bigserial primary key,
       big_form_name varchar(100),
       big_form_name_pinyin varchar(100),
       status int default 1,
       create_time timestamp,
       create_user_id bigint,
       create_user_name varchar(100),
       update_time timestamp,
       update_user_id bigint,
       update_user_name varchar(100),
       is_delete bool default false,
       tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.drug_big_form IS '药品大剂型表';

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_big_form.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_big_form.big_form_name IS '大剂型名称';
COMMENT ON COLUMN pharmacy.drug_big_form.big_form_name_pinyin IS '大剂型名称拼音';
COMMENT ON COLUMN pharmacy.drug_big_form.status IS '1 启用 2 停用';
COMMENT ON COLUMN pharmacy.drug_big_form.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_big_form.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_big_form.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_big_form.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_big_form.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_big_form.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_big_form.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_big_form.tenant_id IS '租户ID';
-- 
-- -- 剂量单位
create table pharmacy.drug_dosage_unit(
    id bigserial primary key,
    unit_name varchar(100),
    unit_pinyin varchar(100),
    status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.drug_dosage_unit IS '药品剂量单位表';

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_dosage_unit.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.unit_name IS '剂量单位名称';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.unit_pinyin IS '剂量单位名称拼音';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.status IS '1 启用 2 停用';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_dosage_unit.tenant_id IS '租户ID';

-- 企业字典
create table pharmacy.enterprise_dictionary(
    id bigserial primary key,
    enterprise_code varchar(100),
    enterprise_name varchar(100),
    enterprise_name_pinyin varchar(100),
    -- supplier or manufacturer
    enterprise_type varchar(50),
    status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.enterprise_dictionary IS '企业字典表，包含供应商和生产厂家信息';

-- 字段注释
COMMENT ON COLUMN pharmacy.enterprise_dictionary.id IS '主键ID';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.enterprise_code IS '企业编码';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.enterprise_name IS '企业名称';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.enterprise_name_pinyin IS '企业名称拼音';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.enterprise_type IS '企业类型（supplier 或 manufacturer）';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.status IS '1 启用 2 停用';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.enterprise_dictionary.tenant_id IS '租户ID';
-- 药房药库科室

create table pharmacy.drug_storage(
    id bigserial primary key,
    storage_code varchar(100),
    storage_name varchar(100),
    storage_drug_type jsonb,
    --库存金额上限
    storage_amount_limit decimal(20,4) default 0,
    -- 父级库房id 
    parent_id bigint default 0,
    parent_code varchar(100) default '',
 
--     is_allow_minus_out int default 2, -- 是否允许负库存出库 1 允许 2 不允许
--     order_no_prefix varchar(100),
    service_object int, -- 门诊，急诊、住院、
    -- 采购入库审核
    purchase_audit int default 2,
    -- 采购退货审核
    purchase_return_audit int default 2,
    -- 药房申领审核
    apply_audit int default 2,
    -- 药房退药审核
    apply_return_audit int default 2,
    -- 出库审核
    out_audit int default 2,
    --特殊处理审核
    special_audit int default 2,
    -- 按批号盘点
    batch_check int default 2,
        status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
    
);
-- 表注释
COMMENT ON TABLE pharmacy.drug_storage IS '药品库房表';

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_storage.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_storage.storage_code IS '库房编码';
COMMENT ON COLUMN pharmacy.drug_storage.storage_name IS '库房名称';
COMMENT ON COLUMN pharmacy.drug_storage.storage_drug_type IS '存储药品类型（JSONB格式）';
COMMENT ON COLUMN pharmacy.drug_storage.storage_amount_limit IS '库存金额上限';
COMMENT ON COLUMN pharmacy.drug_storage.parent_id IS '父级库房ID';
COMMENT ON COLUMN pharmacy.drug_storage.parent_code IS '父级库房编码';
-- COMMENT ON COLUMN pharmacy.drug_storage.is_allow_minus_out IS '是否允许负库存出库（1 允许 2 不允许）';
-- COMMENT ON COLUMN pharmacy.drug_storage.order_no_prefix IS '订单号前缀';
COMMENT ON COLUMN pharmacy.drug_storage.service_object IS '服务对象（门诊，急诊、住院）';
COMMENT ON COLUMN pharmacy.drug_storage.purchase_audit IS '采购入库审核（1 审核 2 不审核）';
COMMENT ON COLUMN pharmacy.drug_storage.purchase_return_audit IS '采购退货审核（1 审核 2 不审核）';
COMMENT ON COLUMN pharmacy.drug_storage.apply_audit IS '药店申领审核（1 审核 2 不审核）';
COMMENT ON COLUMN pharmacy.drug_storage.apply_return_audit IS '药店退药审核（1 审核 2 不审核）';
COMMENT ON COLUMN pharmacy.drug_storage.out_audit IS '出库审核（1 审核 2 不审核）';
COMMENT ON COLUMN pharmacy.drug_storage.special_audit IS '特殊处理审核（1 审核 2 不审核）';
COMMENT ON COLUMN pharmacy.drug_storage.batch_check IS '按批号盘点（1 按批号 2 不按批号）';
COMMENT ON COLUMN pharmacy.drug_storage.status IS '状态（1 启用 2 停用）';
COMMENT ON COLUMN pharmacy.drug_storage.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_storage.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_storage.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_storage.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_storage.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_storage.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_storage.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_storage.tenant_id IS '租户ID';

drop table if exists pharmacy.drug_unit;
create table pharmacy.drug_unit(
    id bigserial primary key,
    unit varchar(100),
    convert_unit varchar(100),
    convert_ratio decimal(20,4),
    unit_pinyin varchar(100),
    status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
    COMMENT ON TABLE pharmacy.drug_unit IS '药品单位表';
-- 字段注释
COMMENT ON COLUMN pharmacy.drug_unit.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_unit.unit IS '单位名称';
COMMENT ON COLUMN pharmacy.drug_unit.convert_unit IS '转换单位';
COMMENT ON COLUMN pharmacy.drug_unit.convert_ratio IS '转换比率';
COMMENT ON COLUMN pharmacy.drug_unit.unit_pinyin IS '单位拼音';
COMMENT ON COLUMN pharmacy.drug_unit.status IS '状态（1 启用 2 停用）';
COMMENT ON COLUMN pharmacy.drug_unit.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_unit.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_unit.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_unit.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_unit.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_unit.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_unit.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_unit.tenant_id IS '租户ID';

create table pharmacy.drug_category(
    id bigserial primary key,
    category_code varchar(100),
    category_name varchar(100),
    status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.drug_category IS '药品分类表';

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_category.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_category.category_code IS '分类编码';
COMMENT ON COLUMN pharmacy.drug_category.category_name IS '分类名称';
COMMENT ON COLUMN pharmacy.drug_category.status IS '状态（1 启用 2 停用）';
COMMENT ON COLUMN pharmacy.drug_category.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_category.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_category.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_category.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_category.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_category.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_category.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_category.tenant_id IS '租户ID';



--     drug_category  
--     -- 药理分类
--  
--     pharmacological_class  
create table pharmacy.pharmacological_class(
    id bigserial primary key,
    class_code varchar(100),
    class_name varchar(100),
    parent_id bigint default 0,
    status int default 1,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.pharmacological_class IS '药理分类表';

-- 字段注释
COMMENT ON COLUMN pharmacy.pharmacological_class.id IS '主键ID';
COMMENT ON COLUMN pharmacy.pharmacological_class.class_code IS '药理分类编码';
COMMENT ON COLUMN pharmacy.pharmacological_class.class_name IS '药理分类名称';
COMMENT ON COLUMN pharmacy.pharmacological_class.parent_id IS '父级分类ID';
COMMENT ON COLUMN pharmacy.pharmacological_class.status IS '状态（1 启用 2 停用）';
COMMENT ON COLUMN pharmacy.pharmacological_class.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.pharmacological_class.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.pharmacological_class.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.pharmacological_class.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.pharmacological_class.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.pharmacological_class.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.pharmacological_class.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.pharmacological_class.tenant_id IS '租户ID';

-- 入库 

create table pharmacy.storage_in_record(
    id bigserial primary key,
    storage_id bigint,
    storage_code varchar(100),
    drug_type varchar(100),
    storage_in_no varchar(100),
    supplier_id bigint,
    supplier_code varchar(100),
    supplier_name varchar(100),
    storage_in_type varchar(100),
    storage_in_time timestamp,
    --总进价
    total_purchase_price  decimal(20,4),
    --总零售价
    total_sale_price   decimal(20,4),
    -- 发票号
    invoice_no varchar(100),
    remark varchar(100),
    status int default 0,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.storage_in_record IS '药品入库记录表';



-- 字段注释
COMMENT ON COLUMN pharmacy.storage_in_record.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_in_record.storage_id IS '库房ID';
COMMENT ON COLUMN pharmacy.storage_in_record.storage_code IS '库房编码';
COMMENT ON COLUMN pharmacy.storage_in_record.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.storage_in_record.storage_in_no IS '入库单号';
COMMENT ON COLUMN pharmacy.storage_in_record.supplier_id IS '供应商ID';
COMMENT ON COLUMN pharmacy.storage_in_record.supplier_code IS '供应商编码';
COMMENT ON COLUMN pharmacy.storage_in_record.supplier_name IS '供应商名称';
COMMENT ON COLUMN pharmacy.storage_in_record.storage_in_type IS '入库类型';
COMMENT ON COLUMN pharmacy.storage_in_record.storage_in_time IS '入库日期';
COMMENT ON COLUMN pharmacy.storage_in_record.total_purchase_price IS '总进价';
COMMENT ON COLUMN pharmacy.storage_in_record.total_sale_price IS '总零售价';
COMMENT ON COLUMN pharmacy.storage_in_record.invoice_no IS '发票号';
COMMENT ON COLUMN pharmacy.storage_in_record.remark IS '备注';
COMMENT ON COLUMN pharmacy.storage_in_record.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_in_record.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_in_record.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_in_record.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_in_record.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_in_record.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_in_record.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_in_record.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_in_record.tenant_id IS '租户ID';

create sequence  pharmacy.storage_in_record_no_seq;
comment on sequence  pharmacy.storage_in_record_no_seq is '入库单号序列';
drop table if exists pharmacy.storage_in_detail;
create table pharmacy.storage_in_detail(
    id bigserial primary key,
    drug_type varchar(100),
    storage_in_no varchar(100),
    storage_in_id bigint,
    drug_id bigint,
    inventory_id bigint,
    drug_code varchar(100),
    drug_name varchar(100),
    spec varchar(100),
    unit varchar(100),

    purchase_price  decimal(20,4),
    sale_price   decimal(20,4),
    total_purchase_price  decimal(20,4),
    total_sale_price   decimal(20,4),
    quantity  int default 0,
    batch_no varchar(100),
    production_date timestamp,
    expiration_date timestamp,
    -- 准字号
    approval_number varchar(100),
    medicine_code varchar(100),
    -- 质量状况
    quality_status varchar(100),
    -- 验收状态
    acceptance_status varchar(100),
     manufacturer_id bigint,
  manufacturer_name varchar(100),
    status int default 0,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
COMMENT ON TABLE pharmacy.storage_in_detail IS '药品入库明细表';


COMMENT ON COLUMN pharmacy.storage_in_detail.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_in_detail.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.storage_in_detail.storage_in_no IS '入库单号';
COMMENT ON COLUMN pharmacy.storage_in_detail.storage_in_id IS '入库ID';
COMMENT ON COLUMN pharmacy.storage_in_detail.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.storage_in_detail.inventory_id IS '库存ID';
COMMENT ON COLUMN pharmacy.storage_in_detail.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.storage_in_detail.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.storage_in_detail.spec IS '规格';
COMMENT ON COLUMN pharmacy.storage_in_detail.unit IS '单位';
COMMENT ON COLUMN pharmacy.storage_in_detail.purchase_price IS '进价';
COMMENT ON COLUMN pharmacy.storage_in_detail.sale_price IS '零售价';
COMMENT ON COLUMN pharmacy.storage_in_detail.total_purchase_price IS '总进价';
COMMENT ON COLUMN pharmacy.storage_in_detail.total_sale_price IS '总零售价';
COMMENT ON COLUMN pharmacy.storage_in_detail.quantity IS '数量';
COMMENT ON COLUMN pharmacy.storage_in_detail.batch_no IS '批号';
COMMENT ON COLUMN pharmacy.storage_in_detail.production_date IS '生产日期';
COMMENT ON COLUMN pharmacy.storage_in_detail.expiration_date IS '过期日期';
COMMENT ON COLUMN pharmacy.storage_in_detail.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.storage_in_detail.medicine_code IS '国家医保编码';
COMMENT ON COLUMN pharmacy.storage_in_detail.quality_status IS '质量状况';
COMMENT ON COLUMN pharmacy.storage_in_detail.acceptance_status IS '验收状态';
COMMENT ON COLUMN pharmacy.storage_in_detail.manufacturer_id IS '生产厂商ID';
COMMENT ON COLUMN pharmacy.storage_in_detail.manufacturer_name IS '生产厂商名称';
COMMENT ON COLUMN pharmacy.storage_in_detail.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_in_detail.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_in_detail.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_in_detail.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_in_detail.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_in_detail.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_in_detail.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_in_detail.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_in_detail.tenant_id IS '租户ID';
-- 生产厂家 

alter table if exists pharmacy.storage_in_detail add column   inventory_id bigint;  
comment on column pharmacy.storage_in_detail.inventory_id IS '库存id'; 

drop table if exists pharmacy.storage_out_record;
create table pharmacy.storage_out_record(
                                           id bigserial primary key,
                                           storage_id bigint,
                                           storage_code varchar(100),
                                           drug_type varchar(100),
                                           storage_out_no varchar(100),
                                           supplier_id bigint,
                                           supplier_code varchar(100),
                                           supplier_name varchar(100),
                                           storage_out_type varchar(100),
                                           storage_out_time timestamp,
    -- 目标科室
                                            target_dept_id bigint,
                                            target_dept_code varchar(100),
                                            target_dept_name varchar(100),
    -- 领用人
                                           target_user_id bigint,
                                           target_user_code varchar(100),
                                           target_user_name varchar(100),
    
    
                                    
    --总进价
                                           total_purchase_price  decimal(20,4),
    --总零售价
                                           total_sale_price   decimal(20,4),
    -- 发票号
                                           invoice_no varchar(100),
                                           remark varchar(100),
                                           status int default 0,
                                           create_time timestamp,
                                           create_user_id bigint,
                                           create_user_name varchar(100),
                                           update_time timestamp,
                                           update_user_id bigint,
                                           update_user_name varchar(100),
                                           is_delete bool default false,
                                           tenant_id bigint
);
COMMENT ON TABLE pharmacy.storage_out_record IS '药品出库记录表';



COMMENT ON COLUMN pharmacy.storage_out_record.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_out_record.storage_id IS '库房ID';
COMMENT ON COLUMN pharmacy.storage_out_record.storage_code IS '库房编码';
COMMENT ON COLUMN pharmacy.storage_out_record.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.storage_out_record.storage_out_no IS '出库单号';
COMMENT ON COLUMN pharmacy.storage_out_record.supplier_id IS '供应商ID';
COMMENT ON COLUMN pharmacy.storage_out_record.supplier_code IS '供应商编码';
COMMENT ON COLUMN pharmacy.storage_out_record.supplier_name IS '供应商名称';
COMMENT ON COLUMN pharmacy.storage_out_record.storage_out_type IS '出库类型';
COMMENT ON COLUMN pharmacy.storage_out_record.storage_out_time IS '出库日期';
COMMENT ON COLUMN pharmacy.storage_out_record.target_dept_id IS '目标科室ID';
COMMENT ON COLUMN pharmacy.storage_out_record.target_dept_code IS '目标科室编码';
COMMENT ON COLUMN pharmacy.storage_out_record.target_dept_name IS '目标科室名称';
COMMENT ON COLUMN pharmacy.storage_out_record.target_user_id IS '目标用户ID';
COMMENT ON COLUMN pharmacy.storage_out_record.target_user_code IS '目标用户编码';
COMMENT ON COLUMN pharmacy.storage_out_record.target_user_name IS '目标用户名称';
COMMENT ON COLUMN pharmacy.storage_out_record.total_purchase_price IS '总进价';
COMMENT ON COLUMN pharmacy.storage_out_record.total_sale_price IS '总零售价';
COMMENT ON COLUMN pharmacy.storage_out_record.invoice_no IS '发票号';
COMMENT ON COLUMN pharmacy.storage_out_record.remark IS '备注';
COMMENT ON COLUMN pharmacy.storage_out_record.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_out_record.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_out_record.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_out_record.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_out_record.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_out_record.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_out_record.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_out_record.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_out_record.tenant_id IS '租户ID';

create sequence  pharmacy.storage_out_record_no_seq;
comment on sequence  pharmacy.storage_out_record_no_seq is '出库单号序列';
drop table if exists pharmacy.storage_out_detail;
create table pharmacy.storage_out_detail(
                                           id bigserial primary key,
                                           drug_type varchar(100),
                                           storage_out_no varchar(100),
                                           storage_out_id bigint,
                                           drug_id bigint,
                                           inventory_id bigint,
                                           drug_code varchar(100),
                                           drug_name varchar(100),
                                           spec varchar(100),
                                           unit varchar(100),

                                           purchase_price  decimal(20,4),
                                           sale_price   decimal(20,4),
                                           total_purchase_price  decimal(20,4),
                                           total_sale_price   decimal(20,4),
                                           quantity  int default 0,
                                           batch_no varchar(100),
                                           production_date timestamp,
                                           expiration_date timestamp,
    -- 准字号
                                           approval_number varchar(100),
                                           medicine_code varchar(100),
                                           manufacturer_id bigint,
                                           manufacturer_name varchar(100),
    
                                           status int default 0,
                                           create_time timestamp,
                                           create_user_id bigint,
                                           create_user_name varchar(100),
                                           update_time timestamp,
                                           update_user_id bigint,
                                           update_user_name varchar(100),
                                           is_delete bool default false,
                                           tenant_id bigint
);

COMMENT ON TABLE pharmacy.storage_out_detail IS '药品出库明细表';


COMMENT ON COLUMN pharmacy.storage_out_detail.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_out_detail.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.storage_out_detail.storage_out_no IS '出库单号';
COMMENT ON COLUMN pharmacy.storage_out_detail.storage_out_id IS '出库单ID';
COMMENT ON COLUMN pharmacy.storage_out_detail.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.storage_out_detail.inventory_id IS '库存ID';
COMMENT ON COLUMN pharmacy.storage_out_detail.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.storage_out_detail.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.storage_out_detail.spec IS '规格';
COMMENT ON COLUMN pharmacy.storage_out_detail.unit IS '单位';
COMMENT ON COLUMN pharmacy.storage_out_detail.purchase_price IS '进价';
COMMENT ON COLUMN pharmacy.storage_out_detail.sale_price IS '零售价';
COMMENT ON COLUMN pharmacy.storage_out_detail.total_purchase_price IS '总进价';
COMMENT ON COLUMN pharmacy.storage_out_detail.total_sale_price IS '总零售价';
COMMENT ON COLUMN pharmacy.storage_out_detail.quantity IS '数量';
COMMENT ON COLUMN pharmacy.storage_out_detail.batch_no IS '批号';
COMMENT ON COLUMN pharmacy.storage_out_detail.production_date IS '生产日期';
COMMENT ON COLUMN pharmacy.storage_out_detail.expiration_date IS '过期日期';
COMMENT ON COLUMN pharmacy.storage_out_detail.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.storage_out_detail.medicine_code IS '国家医保编码';
COMMENT ON COLUMN pharmacy.storage_out_detail.manufacturer_id IS '生产厂商ID';
COMMENT ON COLUMN pharmacy.storage_out_detail.manufacturer_name IS '生产厂商名称';
COMMENT ON COLUMN pharmacy.storage_out_detail.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_out_detail.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_out_detail.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_out_detail.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_out_detail.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_out_detail.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_out_detail.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_out_detail.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_out_detail.tenant_id IS '租户ID';

alter table if exists pharmacy.storage_out_detail add column   inventory_id bigint;
comment on column pharmacy.storage_out_detail.inventory_id IS '库存id';

alter table if exists pharmacy.storage_out_detail add column   manufacturer_id bigint;
alter table if exists pharmacy.storage_out_detail add column   manufacturer_name varchar(100);
comment on column pharmacy.storage_out_detail.manufacturer_id IS '生产厂商ID';
comment on column pharmacy.storage_out_detail.manufacturer_name IS '生产厂商名称';
-- 药品编码	药品名称	规格	厂家标识	数量	单位	待发药数量	零售价	零售金额	采购价
-- 采购金额	批号	有效期	最后一次供应商	国家医保编码

-- 库存表
create table pharmacy.drug_inventory
(
    id                   bigserial primary key,
    drug_id              bigint,
    drug_code            varchar(100),
    drug_name            varchar(100),
    drug_type            varchar(100),
    -- 药房药库
    storage_id           bigint,
    storage_code         varchar(100),
    storage_name         varchar(100),
    spec                 varchar(100),
    -- 厂家
    manufacturer_id      bigint,
    manufacturer_name    varchar(100),
    --数量
    quantity             int  default 0,
    unit                 varchar(100),
    -- 代发药数量
    pending_quantity     int  default 0,
    -- 零售价
    sale_price           decimal(20, 4),
    total_sale_price     decimal(20, 4),
    -- 进价
    purchase_price       decimal(20, 4),
    total_purchase_price decimal(20, 4),
    batch_no             varchar(100),
    production_date      timestamp,
    expiration_date      timestamp,
    -- 医保编码
    approval_number      varchar(100),
    medicine_code        varchar(100),
    last_supplier_id     bigint,
    last_supplier_name   varchar(100),
    status               int  default 0,
    create_time          timestamp,
    create_user_id       bigint,
    create_user_name     varchar(100),
    update_time          timestamp,
    update_user_id       bigint,
    update_user_name     varchar(100),
    is_delete            bool default false,
    tenant_id            bigint
)
-- 表注释
COMMENT ON TABLE pharmacy.drug_inventory IS '药品库存表';

-- 字段注释
COMMENT ON COLUMN pharmacy.drug_inventory.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_inventory.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.drug_inventory.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.drug_inventory.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.drug_inventory.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.drug_inventory.storage_id IS '药房ID';
COMMENT ON COLUMN pharmacy.drug_inventory.storage_code IS '药房编码';
COMMENT ON COLUMN pharmacy.drug_inventory.storage_name IS '药房名称';
COMMENT ON COLUMN pharmacy.drug_inventory.spec IS '规格';
COMMENT ON COLUMN pharmacy.drug_inventory.manufacturer_id IS '生产厂家ID';
COMMENT ON COLUMN pharmacy.drug_inventory.manufacturer_name IS '生产厂家名称';
COMMENT ON COLUMN pharmacy.drug_inventory.quantity IS '数量';
COMMENT ON COLUMN pharmacy.drug_inventory.unit IS '单位';
COMMENT ON COLUMN pharmacy.drug_inventory.pending_quantity IS '待发药数量';
COMMENT ON COLUMN pharmacy.drug_inventory.sale_price IS '零售价';
COMMENT ON COLUMN pharmacy.drug_inventory.total_sale_price IS '零售金额';
COMMENT ON COLUMN pharmacy.drug_inventory.purchase_price IS '进价';
COMMENT ON COLUMN pharmacy.drug_inventory.total_purchase_price IS '采购金额';
COMMENT ON COLUMN pharmacy.drug_inventory.batch_no IS '批号';
COMMENT ON COLUMN pharmacy.drug_inventory.production_date IS '生产日期';
COMMENT ON COLUMN pharmacy.drug_inventory.expiration_date IS '有效期';
COMMENT ON COLUMN pharmacy.drug_inventory.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.drug_inventory.medicine_code IS '国家医保编码';
COMMENT ON COLUMN pharmacy.drug_inventory.last_supplier_id IS '最后一次供应商ID';
COMMENT ON COLUMN pharmacy.drug_inventory.last_supplier_name IS '最后一次供应商名称';
COMMENT ON COLUMN pharmacy.drug_inventory.status IS '状态';
COMMENT ON COLUMN pharmacy.drug_inventory.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_inventory.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_inventory.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_inventory.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_inventory.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_inventory.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_inventory.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_inventory.tenant_id IS '租户ID';
-- 可用库存  ，  available integer GENERATED ALWAYS AS (stock - pre_lock) STORED,
ALTER TABLE pharmacy.drug_inventory
  ADD COLUMN available_quantity numeric(16,4) GENERATED ALWAYS AS (quantity - pending_quantity) STORED;
comment on column pharmacy.drug_inventory.available_quantity IS '可用库存';



-- 特殊处理表

create table pharmacy.storage_special_record(
                                            id bigserial primary key,
                                            storage_id bigint,
                                            storage_code varchar(100),
                                            storage_name varchar(100),
                                            handle_no varchar(100),
    
                                            drug_type varchar(100),
                                            supplier_id bigint,
                                            supplier_code varchar(100),
                                            supplier_name varchar(100),
                                            handle_type varchar(100),
                                            handle_time timestamp,
                                            total_purchase_price  decimal(20,4),
                                            total_sale_price   decimal(20,4),
                                            remark varchar(100),
                                            status int default 0,
                                            create_time timestamp,
                                            create_user_id bigint,
                                            create_user_name varchar(100),
                                            update_time timestamp,
                                            update_user_id bigint,
                                            update_user_name varchar(100),
                                            is_delete bool default false,
                                            tenant_id bigint
);

-- 表注释
COMMENT ON TABLE pharmacy.storage_special_record IS '存储特殊处理记录表';
-- 字段注释
COMMENT ON COLUMN pharmacy.storage_special_record.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_special_record.storage_id IS '库房ID';
COMMENT ON COLUMN pharmacy.storage_special_record.storage_code IS '库房编码';
COMMENT ON COLUMN pharmacy.storage_special_record.storage_name IS '库房名称';
COMMENT ON COLUMN pharmacy.storage_special_record.handle_no IS '特殊处理单号';
COMMENT ON COLUMN pharmacy.storage_special_record.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.storage_special_record.supplier_id IS '供应商ID';
COMMENT ON COLUMN pharmacy.storage_special_record.supplier_code IS '供应商编码';
COMMENT ON COLUMN pharmacy.storage_special_record.supplier_name IS '供应商名称';
COMMENT ON COLUMN pharmacy.storage_special_record.handle_type IS '特殊处理类型';
COMMENT ON COLUMN pharmacy.storage_special_record.handle_time IS '特殊处理时间';
COMMENT ON COLUMN pharmacy.storage_special_record.total_purchase_price IS '总进价';
COMMENT ON COLUMN pharmacy.storage_special_record.total_sale_price IS '总零售价';
COMMENT ON COLUMN pharmacy.storage_special_record.remark IS '备注';
COMMENT ON COLUMN pharmacy.storage_special_record.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_special_record.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_special_record.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_special_record.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_special_record.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_special_record.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_special_record.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_special_record.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_special_record.tenant_id IS '租户ID';



create sequence  pharmacy.storage_handle_no_seq;
comment on sequence  pharmacy.storage_handle_no_seq is '存储特殊记录号';
 
create table pharmacy.storage_special_detail(
                                            id bigserial primary key,
                                            drug_type varchar(100),
                                            handle_no varchar(100),
                                            handle_id bigint,
                                            drug_id bigint,
                                            inventory_id bigint,
                                            drug_code varchar(100),
                                            drug_name varchar(100),
                                            spec varchar(100),
                                            unit varchar(100),
                                            purchase_price  decimal(20,4),
                                            sale_price   decimal(20,4),
                                            total_purchase_price  decimal(20,4),
                                            total_sale_price   decimal(20,4),
                                            quantity  int default 0,
                                            batch_no varchar(100),
                                            production_date timestamp,
                                            expiration_date timestamp,
                                            approval_number varchar(100),
                                            medicine_code varchar(100),
                                            manufacturer_id bigint,
                                            manufacturer_name varchar(100),
                                            status int default 0,
                                            create_time timestamp,
                                            create_user_id bigint,
                                            create_user_name varchar(100),
                                            update_time timestamp,
                                            update_user_id bigint,
                                            update_user_name varchar(100),
                                            is_delete bool default false,
                                            tenant_id bigint
);


COMMENT ON TABLE pharmacy.storage_special_detail IS '存储特殊处理明细表';
-- 字段注释
COMMENT ON COLUMN pharmacy.storage_special_detail.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_special_detail.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.storage_special_detail.handle_no IS '特殊处理单号';
COMMENT ON COLUMN pharmacy.storage_special_detail.handle_id IS '特殊处理ID';
COMMENT ON COLUMN pharmacy.storage_special_detail.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.storage_special_detail.inventory_id IS '库存ID';
COMMENT ON COLUMN pharmacy.storage_special_detail.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.storage_special_detail.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.storage_special_detail.spec IS '规格';
COMMENT ON COLUMN pharmacy.storage_special_detail.unit IS '单位';
COMMENT ON COLUMN pharmacy.storage_special_detail.purchase_price IS '进价';
COMMENT ON COLUMN pharmacy.storage_special_detail.sale_price IS '零售价';
COMMENT ON COLUMN pharmacy.storage_special_detail.total_purchase_price IS '总进价';
COMMENT ON COLUMN pharmacy.storage_special_detail.total_sale_price IS '总零售价';
COMMENT ON COLUMN pharmacy.storage_special_detail.quantity IS '数量';
COMMENT ON COLUMN pharmacy.storage_special_detail.batch_no IS '批号';
COMMENT ON COLUMN pharmacy.storage_special_detail.production_date IS '生产日期';
COMMENT ON COLUMN pharmacy.storage_special_detail.expiration_date IS '有效期';
COMMENT ON COLUMN pharmacy.storage_special_detail.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.storage_special_detail.medicine_code IS '国家医保编码';
COMMENT ON COLUMN pharmacy.storage_special_detail.manufacturer_id IS '生产厂商ID';
COMMENT ON COLUMN pharmacy.storage_special_detail.manufacturer_name IS '生产厂商名称';
COMMENT ON COLUMN pharmacy.storage_special_detail.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_special_detail.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_special_detail.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_special_detail.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_special_detail.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_special_detail.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_special_detail.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_special_detail.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_special_detail.tenant_id IS '租户ID';

-- 调价记录表
drop table if exists pharmacy.adjust_price_record;

create table pharmacy.adjust_price_record(
     id bigserial primary key,
     drug_id bigint,
     drug_code varchar(100),
     drug_name varchar(100),
     spec varchar(100),
     unit varchar(100),
     quantity  int default 0,
     old_sale_price  decimal(20,4),
     new_sale_price decimal(20,4),
     total_old_sale_price   decimal(20,4),
     total_new_sale_price   decimal(20,4),
     adjust_price decimal(20,4),
     batch_no varchar(100),
     production_date timestamp,
     expiration_date timestamp,
     approval_number varchar(100),
     medicine_code varchar(100),
     manufacturer_id bigint,
     manufacturer_name varchar(100),
     adjust_time timestamp,
     status int default 0,
     create_time timestamp,
     create_user_id bigint,
     create_user_name varchar(100),
     update_time timestamp,
     update_user_id bigint,
     update_user_name varchar(100),
     is_delete bool default false,
     tenant_id bigint
);
-- 表注释
COMMENT ON TABLE pharmacy.adjust_price_record IS '药品调价记录表';

-- 字段注释
COMMENT ON COLUMN pharmacy.adjust_price_record.id IS '主键ID';
COMMENT ON COLUMN pharmacy.adjust_price_record.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.adjust_price_record.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.adjust_price_record.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.adjust_price_record.spec IS '规格';
COMMENT ON COLUMN pharmacy.adjust_price_record.unit IS '单位';
COMMENT ON COLUMN pharmacy.adjust_price_record.quantity IS '数量';
COMMENT ON COLUMN pharmacy.adjust_price_record.old_sale_price IS '旧零售价';
COMMENT ON COLUMN pharmacy.adjust_price_record.new_sale_price IS '新零售价'; 
COMMENT ON COLUMN pharmacy.adjust_price_record.total_old_sale_price IS '旧总价';
COMMENT ON COLUMN pharmacy.adjust_price_record.total_new_sale_price IS '新总价';
COMMENT ON COLUMN pharmacy.adjust_price_record.adjust_price IS '调价金额';
COMMENT ON COLUMN pharmacy.adjust_price_record.batch_no IS '批号';
COMMENT ON COLUMN pharmacy.adjust_price_record.production_date IS '生产日期';
COMMENT ON COLUMN pharmacy.adjust_price_record.expiration_date IS '有效期';
COMMENT ON COLUMN pharmacy.adjust_price_record.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.adjust_price_record.medicine_code IS '国家医保编码';
COMMENT ON COLUMN pharmacy.adjust_price_record.manufacturer_id IS '生产厂商ID';
COMMENT ON COLUMN pharmacy.adjust_price_record.manufacturer_name IS '生产厂商名称';
COMMENT ON COLUMN pharmacy.adjust_price_record.adjust_time IS '调价时间';
COMMENT ON COLUMN pharmacy.adjust_price_record.status IS '状态';
COMMENT ON COLUMN pharmacy.adjust_price_record.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.adjust_price_record.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.adjust_price_record.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.adjust_price_record.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.adjust_price_record.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.adjust_price_record.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.adjust_price_record.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.adjust_price_record.tenant_id IS '租户ID';

select * from pharmacy.drug_inventory

update pharmacy.drug_inventory set  total_sale_price = sale_price * quantity,
                                    total_purchase_price = purchase_price * quantity


-- 盘点表
drop table if exists pharmacy.storage_taking_record;
create table pharmacy.storage_taking_record
(
    id                   bigserial primary key,
    taking_no            varchar(100),
    taking_time          timestamp,
    taking_result  int default 0,
    storage_id           bigint,
    storage_code         varchar(100),
    storage_name         varchar(100),
    -- 现有数量
    current_quantity      int default 0,
    -- 盘点数量
    taking_quantity      int default 0,
    current_sale_price    decimal(20,4) default 0,
    taking_sale_price    decimal(20,4) default 0,
    status int default 0,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- create sequence pharmacy.storage_taking_no_seq ; 
-- comment on sequence pharmacy.storage_taking_no_seq IS '药品盘点单号';
-- 表注释
COMMENT ON TABLE pharmacy.storage_taking_record IS '药品盘点记录表';
-- 字段注释 for storage_taking_record
COMMENT ON COLUMN pharmacy.storage_taking_record.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_taking_record.taking_no IS '盘点单号';
COMMENT ON COLUMN pharmacy.storage_taking_record.taking_time IS '盘点时间';
COMMENT ON COLUMN pharmacy.storage_taking_record.taking_result IS '盘点结果';
COMMENT ON COLUMN pharmacy.storage_taking_record.storage_id IS '库房ID';
COMMENT ON COLUMN pharmacy.storage_taking_record.storage_code IS '库房编码';
COMMENT ON COLUMN pharmacy.storage_taking_record.storage_name IS '库房名称';
COMMENT ON COLUMN pharmacy.storage_taking_record.current_quantity IS '现有数量';
COMMENT ON COLUMN pharmacy.storage_taking_record.taking_quantity IS '盘点数量';
COMMENT ON COLUMN pharmacy.storage_taking_record.current_sale_price IS '现有零售价';
COMMENT ON COLUMN pharmacy.storage_taking_record.taking_sale_price IS '盘点零售价';
COMMENT ON COLUMN pharmacy.storage_taking_record.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_taking_record.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_taking_record.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_taking_record.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_taking_record.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_taking_record.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_taking_record.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_taking_record.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_taking_record.tenant_id IS '租户ID';

drop table if exists pharmacy.storage_taking_detail;


create table pharmacy.storage_taking_detail(
    id                   bigserial primary key,
    taking_record_id     bigint,
    inventory_id         bigint,
    drug_id bigint,
    drug_code varchar(100),
    drug_name varchar(100),
    drug_type varchar(100),
    spec varchar(100),
    unit varchar(100),
    current_quantity  int default 0,
    current_sale_price  decimal(20,4),
    total_current_sale_price   decimal(20,4),
    taking_quantity  int default 0,
    taking_sale_price  decimal(20,4),
    -- 盘点差额
    taking_difference  int default 0,
    taking_difference_sale_price  decimal(20,4),
    total_taking_sale_price   decimal(20,4),
    batch_no varchar(100),
    production_date timestamp,
    expiration_date timestamp,
    approval_number varchar(100),
    medicine_code varchar(100),
    manufacturer_id bigint,
    manufacturer_name varchar(100),
    status int default 0,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
COMMENT ON TABLE pharmacy.storage_taking_detail IS '药品盘点明细表';


-- 字段注释 for storage_taking_detail
COMMENT ON COLUMN pharmacy.storage_taking_detail.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_taking_detail.taking_record_id IS '盘点记录ID';
COMMENT ON COLUMN pharmacy.storage_taking_detail.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.storage_taking_detail.inventory_id IS '库存ID';
COMMENT ON COLUMN pharmacy.storage_taking_detail.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.storage_taking_detail.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.storage_taking_detail.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.storage_taking_detail.spec IS '规格';
COMMENT ON COLUMN pharmacy.storage_taking_detail.unit IS '单位';
COMMENT ON COLUMN pharmacy.storage_taking_detail.current_quantity IS '现有数量';
COMMENT ON COLUMN pharmacy.storage_taking_detail.current_sale_price IS '现有零售价';
COMMENT ON COLUMN pharmacy.storage_taking_detail.total_current_sale_price IS '现有零售总价';
COMMENT ON COLUMN pharmacy.storage_taking_detail.taking_quantity IS '盘点数量';
COMMENT ON COLUMN pharmacy.storage_taking_detail.taking_sale_price IS '盘点零售价';
COMMENT ON COLUMN pharmacy.storage_taking_detail.taking_difference IS '盘点差额';
COMMENT ON COLUMN pharmacy.storage_taking_detail.taking_difference_sale_price IS '盘点差额零售价';
COMMENT ON COLUMN pharmacy.storage_taking_detail.total_taking_sale_price IS '盘点零售总价';
COMMENT ON COLUMN pharmacy.storage_taking_detail.batch_no IS '批号';
COMMENT ON COLUMN pharmacy.storage_taking_detail.production_date IS '生产日期';
COMMENT ON COLUMN pharmacy.storage_taking_detail.expiration_date IS '有效期';
COMMENT ON COLUMN pharmacy.storage_taking_detail.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.storage_taking_detail.medicine_code IS '国家医保编码';
COMMENT ON COLUMN pharmacy.storage_taking_detail.manufacturer_id IS '生产厂商ID';
COMMENT ON COLUMN pharmacy.storage_taking_detail.manufacturer_name IS '生产厂商名称';
COMMENT ON COLUMN pharmacy.storage_taking_detail.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_taking_detail.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_taking_detail.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_taking_detail.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_taking_detail.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_taking_detail.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_taking_detail.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_taking_detail.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_taking_detail.tenant_id IS '租户ID';

-- 退货表
create table pharmacy.storage_refund_record(
    id                   bigserial primary key,
    refund_no            varchar(100),
    refund_time          timestamp,
    refund_type          varchar(100),
    refund_reason        varchar(100),
    apply_dept_id           bigint,
    apply_dept_code           varchar(100),
    apply_dept_name           varchar(100),
    target_storage_id           bigint,
    target_storage_code           varchar(100),
    target_storage_name           varchar(100),
    total_sale_price   decimal(20,4),
    status int default 0,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);

alter table pharmacy.storage_refund_record

-- 表注释
COMMENT ON TABLE pharmacy.storage_refund_record IS '药品退货记录表';
-- 字段注释 for storage_refund_record
COMMENT ON COLUMN pharmacy.storage_refund_record.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_refund_record.refund_no IS '退货单号';
COMMENT ON COLUMN pharmacy.storage_refund_record.refund_time IS '退货时间';
COMMENT ON COLUMN pharmacy.storage_refund_record.refund_type IS '退货类型';
COMMENT ON COLUMN pharmacy.storage_refund_record.refund_reason IS '退货原因';
COMMENT ON COLUMN pharmacy.storage_refund_record.apply_dept_id IS '申请部门ID';
COMMENT ON COLUMN pharmacy.storage_refund_record.apply_dept_code IS '申请部门编码';
COMMENT ON COLUMN pharmacy.storage_refund_record.apply_dept_name IS '申请部门名称';
COMMENT ON COLUMN pharmacy.storage_refund_record.target_storage_id IS '目标库房ID';
COMMENT ON COLUMN pharmacy.storage_refund_record.target_storage_code IS '目标库房编码';
COMMENT ON COLUMN pharmacy.storage_refund_record.target_storage_name IS '目标库房名称';
COMMENT ON COLUMN pharmacy.storage_refund_record.total_sale_price IS '总零售价';
COMMENT ON COLUMN pharmacy.storage_refund_record.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_refund_record.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_refund_record.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_refund_record.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_refund_record.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_refund_record.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_refund_record.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_refund_record.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_refund_record.tenant_id IS '租户ID';
 
create sequence pharmacy.storage_refund_no_seq start with 1 increment by 1;
comment on sequence pharmacy.storage_refund_no_seq is '退货单号';

drop table pharmacy.storage_refund_detail;

create table pharmacy.storage_refund_detail(
    id                   bigserial primary key,
    refund_record_id     bigint,
    drug_id bigint,
    inventory_id bigint,
    drug_type varchar(100),
    drug_code varchar(100),
    drug_name varchar(100),
    spec varchar(100),
    unit varchar(100),
    quantity  int default 0,
    sale_price  decimal(20,4),
    total_sale_price  decimal(20,4),
    batch_no varchar(100),
    production_date timestamp,
    expiration_date timestamp,
    approval_number varchar(100),
    medicine_code varchar(100),
    manufacturer_id bigint,
    manufacturer_name varchar(100),
    status int default 0,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
COMMENT ON TABLE pharmacy.storage_refund_detail IS '药品退货明细表';

-- 字段注释 for storage_refund_detail
COMMENT ON COLUMN pharmacy.storage_refund_detail.id IS '主键ID';
COMMENT ON COLUMN pharmacy.storage_refund_detail.refund_record_id IS '退货记录ID';
COMMENT ON COLUMN pharmacy.storage_refund_detail.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.storage_refund_detail.inventory_id IS '库存ID';
COMMENT ON COLUMN pharmacy.storage_refund_detail.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.storage_refund_detail.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.storage_refund_detail.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.storage_refund_detail.spec IS '规格';
COMMENT ON COLUMN pharmacy.storage_refund_detail.unit IS '单位';
COMMENT ON COLUMN pharmacy.storage_refund_detail.quantity IS '数量';
COMMENT ON COLUMN pharmacy.storage_refund_detail.sale_price IS '零售价';
COMMENT ON COLUMN pharmacy.storage_refund_detail.total_sale_price IS '总零售价';
COMMENT ON COLUMN pharmacy.storage_refund_detail.batch_no IS '批号';
COMMENT ON COLUMN pharmacy.storage_refund_detail.production_date IS '生产日期';
COMMENT ON COLUMN pharmacy.storage_refund_detail.expiration_date IS '有效期';
COMMENT ON COLUMN pharmacy.storage_refund_detail.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.storage_refund_detail.medicine_code IS '国家医保编码';
COMMENT ON COLUMN pharmacy.storage_refund_detail.manufacturer_id IS '生产厂商ID';
COMMENT ON COLUMN pharmacy.storage_refund_detail.manufacturer_name IS '生产厂商名称';
COMMENT ON COLUMN pharmacy.storage_refund_detail.status IS '状态';
COMMENT ON COLUMN pharmacy.storage_refund_detail.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.storage_refund_detail.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.storage_refund_detail.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.storage_refund_detail.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.storage_refund_detail.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.storage_refund_detail.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.storage_refund_detail.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.storage_refund_detail.tenant_id IS '租户ID';

-- 采购计划
create table pharmacy.drug_purchase_plan(
    id                   bigserial primary key,
    plan_no              varchar(100),
    plan_time            timestamp,
    status int default 0,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
COMMENT ON TABLE pharmacy.drug_purchase_plan IS '采购计划表';
create sequence pharmacy.drug_purchase_plan_no_seq start with 1 increment by 1;
comment on sequence pharmacy.drug_purchase_plan_no_seq is '采购计划号';
COMMENT ON TABLE pharmacy.drug_purchase_plan IS '采购计划表';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.plan_no IS '采购计划号';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.plan_time IS '采购计划时间';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.status IS '状态（0 未处理 1 处理中 2 已完成等）';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_purchase_plan.tenant_id IS '租户ID';

drop table pharmacy.drug_purchase_plan_detail;
create table pharmacy.drug_purchase_plan_detail(
    id                   bigserial primary key,
    plan_id              bigint,
    drug_id              bigint,
    drug_code            varchar(100),
    drug_name            varchar(100),
    drug_type            varchar(100),
    spec                 varchar(100),
    unit                 varchar(100),
    -- 药房库存
    pharmacy_quantity     int  default 0,
    -- 药库库存
    storage_quantity     int  default 0,
    -- 销售数量
    current_sale_quantity        int  default 0,
    last_sale_quantity    int  default 0,
    -- 平均销售数量
    average_sale_quantity       int  default 0,
    -- 计划数量
    quantity             int  default 0,
    purchase_price           decimal(20, 4),
    total_purchase_price     decimal(20, 4),
    -- 生产厂家
    manufacturer_id      bigint,
    manufacturer_name    varchar(100),
    -- 供应商
    supplier_id          bigint,
    supplier_name        varchar(100),
    status int default 0,
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
comment on table pharmacy.drug_purchase_plan_detail is '采购计划明细表';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.id IS '主键ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.plan_id IS '采购计划ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.spec IS '规格';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.unit IS '单位';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.pharmacy_quantity IS '药店库存数量';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.storage_quantity IS '药库库存数量';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.current_sale_quantity IS '当前销售数量';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.last_sale_quantity IS '上次销售数量';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.average_sale_quantity IS '平均销售数量';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.quantity IS '计划采购数量';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.purchase_price IS '采购单价';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.total_purchase_price IS '总采购价';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.manufacturer_id IS '生产厂家ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.manufacturer_name IS '生产厂家名称';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.supplier_id IS '供应商ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.supplier_name IS '供应商名称';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.status IS '状态（0 未处理 1 处理中 2 已完成等）';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.drug_purchase_plan_detail.tenant_id IS '租户ID';

drop table pharmacy.outpatient_drug_send_record  ;
-- 药房门诊发药表
create table pharmacy.outpatient_drug_send_record
(
    id                bigserial primary key,
    send_no          varchar(100),
    -- 发药
    send_user_id      bigint,
    send_user_name   varchar(100),
    send_time        timestamp,
    -- 审核
    audit_user_id     bigint,
    audit_user_name varchar(100),
    audit_time        timestamp,
    -- 调配人员
    pick_user_id        bigint,
    pick_user_name   varchar(100),
    pick_time        timestamp,
    -- 何对人员
    check_user_id     bigint,
    check_user_name varchar(100),
    check_time        timestamp,
    
    storage_id        bigint,
    storage_code      varchar(100),
    patient_id        bigint,
    patient_name      varchar(100),
    register_id       bigint,
    visit_no          varchar(100),
    prescription_id   bigint,
    prescription_detail_id   bigint,
    prescription_no   varchar(100),
    prescription_time timestamp,
    prescription_type varchar(100),
    -- 库存id
    inventory_id      bigint,
    dept_id           bigint,
    dept_name         varchar(100),
    doctor_id         bigint,
    doctor_name       varchar(100),
    invoice_number    varchar(100),
    charge_staff_id   bigint,
    charge_id         bigint,
    charge_time       timestamp,
    drug_id           bigint,
    drug_code         varchar(100),
    drug_name         varchar(100),
    drug_type         varchar(100),
    spec              varchar(100),
    unit              varchar(100),
    quantity          int default 0,
    single_dose                 numeric(16, 4),
    single_dose_unit            varchar(16),
    medication_routes_id        bigint,
    frequency_id                bigint,
    medication_days             smallint,
    price                       numeric(16, 4),
    amount                      numeric(16, 4),
 -- 草药付数
    herbs_quantity    integer,
    -- 解药方法
    decoction_method   varchar(128),
    -- 是否代煎
    is_decoction                 integer,
    refund_quantity    int default 0,
    refund_amount       decimal(20, 4),
    batch_no          varchar(100),
    production_date   timestamp,
    expiration_date   timestamp,
    approval_number   varchar(100),
    -- 国家医保编码
    medicine_code varchar(100),
    manufacturer_id      bigint,
    manufacturer_name    varchar(100),
    status int default 0,
    create_org_id bigint,
    create_org_name varchar(100),
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);

--create sequence pharmacy.outpatient_drug_send_no_seq start with 1 increment by 1;

-- 表注释
COMMENT ON TABLE pharmacy.outpatient_drug_send_record IS '门诊发药记录表';

-- 字段注释
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.id IS '主键ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.send_no IS '发药单号';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.send_user_id IS '发药人ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.send_user_name IS '发药人名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.send_time IS '发药时间';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.audit_user_id IS '审核人ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.audit_user_name IS '审核人名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.audit_time IS '审核时间';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.pick_user_id IS '调配人员ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.pick_user_name IS '调配人员名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.pick_time IS '调配时间';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.check_user_id IS '核对人员ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.check_user_name IS '核对人员名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.check_time IS '核对时间';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.storage_id IS '药房ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.storage_code IS '药房编码';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.patient_id IS '患者ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.patient_name IS '患者名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.register_id IS '挂号ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.visit_no IS '就诊号';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.prescription_id IS '处方ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.prescription_detail_id IS '处方明细ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.prescription_no IS '处方号';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.prescription_time IS '处方时间';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.prescription_type IS '处方类型';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.inventory_id IS '库存ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.dept_id IS '科室ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.dept_name IS '科室名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.doctor_id IS '医生ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.doctor_name IS '医生名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.invoice_number IS '发票号';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.charge_staff_id IS '收费人员ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.charge_id IS '收费ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.charge_time IS '收费时间';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.drug_type IS '药品类型';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.spec IS '药品规格';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.unit IS '药品单位';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.quantity IS '发药数量';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.single_dose IS '单次剂量';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.single_dose_unit IS '单次剂量单位';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.medication_routes_id IS '用药途径ID'; 
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.frequency_id IS '用药频次ID'; 
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.medication_days IS '用药天数';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.price IS '零售价';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.amount IS '总零售价';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.herbs_quantity IS '草药付数';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.decoction_method IS '煎药方法';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.is_decoction IS '是否代煎';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.refund_quantity IS '退药数量';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.refund_amount IS '总退药金额';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.batch_no IS '批号';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.production_date IS '生产日期';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.expiration_date IS '有效期';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.approval_number IS '批准文号';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.medicine_code IS '药品通用名编码';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.manufacturer_id IS '生产厂家ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.manufacturer_name IS '生产厂家名称';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.status IS '状态';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.outpatient_drug_send_record.tenant_id IS '租户ID';

//      *处方批次分配表 (prescription_batch_allocation)
        //         id (主键)
        //         prescription_detail_id (处方明细 ID)
        //         inventory_id (库存 ID)
        //         batch_number (批次号)
        //         allocated_quantity (分配数量)
        //                      *
drop table if exists pharmacy.outpatient_prescription_inventory_allocation;
create table pharmacy.outpatient_prescription_inventory_allocation(
    id bigserial primary key,
    storage_id bigint,
    inventory_id bigint,
    drug_id bigint,
    prescription_id bigint,
    prescription_detail_id bigint,
    drug_code varchar(100),
    drug_name varchar(100),
    spec varchar(100),
    allocated_quantity int,
    unit varchar(100),
    batch_no varchar(100),
    status int default 0,
    create_org_id bigint,
    create_org_name varchar(100),
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
comment on table pharmacy.outpatient_prescription_inventory_allocation is '门诊处方库存分配表，锁定库存时保存分配信息';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.id IS '主键';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.storage_id IS '药房id';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.inventory_id IS '库存ID';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.drug_id IS '药品ID';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.prescription_id IS '处方ID';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.prescription_detail_id IS '处方明细ID';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.drug_code IS '药品编码';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.drug_name IS '药品名称';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.spec IS '药品规格';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.unit IS '药品单位';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.batch_no IS '批次号';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.allocated_quantity IS '分配数量';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.status IS '状态';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.create_org_id IS '创建机构ID';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.create_org_name IS '创建机构名称';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.create_time IS '创建时间';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.create_user_id IS '创建用户ID';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.create_user_name IS '创建用户名';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.update_time IS '更新时间';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.update_user_id IS '更新用户ID';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.update_user_name IS '更新用户名';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.is_delete IS '是否删除';
COMMENT ON COLUMN pharmacy.outpatient_prescription_inventory_allocation.tenant_id IS '租户ID';
SELECT 'ALTER TABLE pharmacy.'||table_name||' ADD COLUMN create_org_id BIGINT;' 
FROM information_schema.tables
WHERE table_schema = 'pharmacy'
union all
SELECT 'ALTER TABLE pharmacy.'||table_name||' ADD COLUMN create_org_name VARCHAR(100);'
FROM information_schema.tables
WHERE table_schema = 'pharmacy'
 
drop table if exists inpatient.appointment_record;
-- 预约记录
create table inpatient.appointment_record(
                                             id bigserial primary key,
                                             patient_id bigint,
                                             patient_name varchar(100),
                                             appointment_time timestamp,
                                             medical_card_id bigint,
                                             medical_card_no varchar(100),
                                             outpatient_no varchar(100),
    visit_no varchar(100),
                                             id_card_type varchar(100),
                                             id_card_no varchar(100),
                                             insurance_no varchar(100),
    -- 预约科室
                                             dept_id bigint,
                                             dept_code varchar(100),
                                             dept_name varchar(100),
    -- 预约医生
                                             doctor_id bigint,
                                             doctor_code varchar(100),
                                             doctor_name varchar(100),

                                             diagnostic_code varchar(100),
                                             diagnostic_name varchar(100),
    -- 预交金
    -- advance_amount decimal(20,4),
                                             remark varchar(500),
                                             status int default 0,
                                             create_org_id bigint,
                                             create_org_name varchar(100),
                                             create_time timestamp,
                                             create_user_id bigint,
                                             create_user_name varchar(100),
                                             update_time timestamp,
                                             update_user_id bigint,
                                             update_user_name varchar(100),
                                             is_delete bool default false,
                                             tenant_id bigint
)
-- 表注释
COMMENT ON TABLE inpatient.appointment_record IS '住院预约表';

-- 列注释
COMMENT ON COLUMN inpatient.appointment_record.id IS '主键ID';
COMMENT ON COLUMN inpatient.appointment_record.patient_id IS '患者ID';
COMMENT ON COLUMN inpatient.appointment_record.patient_name IS '患者姓名';
COMMENT ON COLUMN inpatient.appointment_record.appointment_time IS '预约时间';
COMMENT ON COLUMN inpatient.appointment_record.medical_card_id IS '就诊卡ID';
COMMENT ON COLUMN inpatient.appointment_record.medical_card_no IS '就诊卡号';
COMMENT ON COLUMN inpatient.appointment_record.outpatient_no IS '门诊号';
COMMENT ON COLUMN inpatient.appointment_record.visit_no IS '就诊号';
COMMENT ON COLUMN inpatient.appointment_record.id_card_type IS '证件类型';
COMMENT ON COLUMN inpatient.appointment_record.id_card_no IS '证件号码';
COMMENT ON COLUMN inpatient.appointment_record.insurance_no IS '保险号';
COMMENT ON COLUMN inpatient.appointment_record.dept_id IS '预约科室ID';
COMMENT ON COLUMN inpatient.appointment_record.dept_code IS '预约科室代码';
COMMENT ON COLUMN inpatient.appointment_record.dept_name IS '预约科室名称';
COMMENT ON COLUMN inpatient.appointment_record.doctor_id IS '预约医生ID';
COMMENT ON COLUMN inpatient.appointment_record.doctor_code IS '预约医生代码';
COMMENT ON COLUMN inpatient.appointment_record.doctor_name IS '预约医生姓名';
COMMENT ON COLUMN inpatient.appointment_record.diagnostic_code IS '诊断代码';
COMMENT ON COLUMN inpatient.appointment_record.diagnostic_name IS '诊断名称';
COMMENT ON COLUMN inpatient.appointment_record.remark IS '备注';
COMMENT ON COLUMN inpatient.appointment_record.status IS '状态';
COMMENT ON COLUMN inpatient.appointment_record.create_org_id IS '创建组织ID';
COMMENT ON COLUMN inpatient.appointment_record.create_org_name IS '创建组织名称';
COMMENT ON COLUMN inpatient.appointment_record.create_time IS '创建时间';
COMMENT ON COLUMN inpatient.appointment_record.create_user_id IS '创建用户ID';
COMMENT ON COLUMN inpatient.appointment_record.create_user_name IS '创建用户名称';
COMMENT ON COLUMN inpatient.appointment_record.update_time IS '更新时间';
COMMENT ON COLUMN inpatient.appointment_record.update_user_id IS '更新用户ID';
COMMENT ON COLUMN inpatient.appointment_record.update_user_name IS '更新用户名称';
COMMENT ON COLUMN inpatient.appointment_record.is_delete IS '是否删除';
COMMENT ON COLUMN inpatient.appointment_record.tenant_id IS '租户ID';
drop table if exists outpatient_doctor.refund_apply;

-- 退费申请表
create table outpatient_doctor . refund_apply(
    id bigserial primary key,
    -- 计费id
    charge_id bigint,
    charge_type varchar(100),
    apply_no varchar(100),

    apply_time timestamp,
    apply_dept_id bigint,
    apply_dept_code varchar(100),
    apply_dept_name varchar(100),
    apply_user_id bigint,
    apply_user_code varchar(100),
    apply_user_name varchar(100),
    -- 就诊卡号
    card_no varchar(100),
    -- 就诊号
    visit_no varchar(100),
    -- 门诊号
    outpatient_no varchar(100),
    -- 挂号id 
    register_id bigint,
    patient_id bigint,
    patient_name varchar(100),

    apply_reason varchar(200),
 
    status int default 0,
    audit_status int default 0,
    create_org_id bigint,
    create_org_name varchar(100),
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
COMMENT ON TABLE outpatient_doctor.refund_apply IS '退费申请表';
-- 字段注释 for refund_apply
COMMENT ON COLUMN outpatient_doctor.refund_apply.id IS '主键ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_no IS '退费申请单号';
COMMENT ON COLUMN outpatient_doctor.refund_apply.charge_id IS '退费申请ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.charge_type IS '退费申请类型';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_time IS '退费申请时间';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_dept_id IS '申请部门ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_dept_code IS '申请部门编码';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_dept_name IS '申请部门名称';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_user_id IS '申请人ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_user_code IS '申请人编码';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_user_name IS '申请人名称';
COMMENT ON COLUMN outpatient_doctor.refund_apply.card_no IS '就诊卡号';
COMMENT ON COLUMN outpatient_doctor.refund_apply.visit_no IS '就诊号';
COMMENT ON COLUMN outpatient_doctor.refund_apply.outpatient_no IS '门诊号';
COMMENT ON COLUMN outpatient_doctor.refund_apply.register_id IS '挂号ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.patient_id IS '患者ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.patient_name IS '患者名称';
COMMENT ON COLUMN outpatient_doctor.refund_apply.apply_reason IS '退费原因'; 
COMMENT ON COLUMN outpatient_doctor.refund_apply.status IS '状态 0 新增待审核 1 审核中 2 审核完成';
COMMENT ON COLUMN outpatient_doctor.refund_apply.audit_status IS '审核状态 表refund_audit 状态';
COMMENT ON COLUMN outpatient_doctor.refund_apply.create_org_id IS '创建机构ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.create_org_name IS '创建机构名称';
COMMENT ON COLUMN outpatient_doctor.refund_apply.create_time IS '创建时间';
COMMENT ON COLUMN outpatient_doctor.refund_apply.create_user_id IS '创建用户ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.create_user_name IS '创建用户名';
COMMENT ON COLUMN outpatient_doctor.refund_apply.update_time IS '更新时间';
COMMENT ON COLUMN outpatient_doctor.refund_apply.update_user_id IS '更新用户ID';
COMMENT ON COLUMN outpatient_doctor.refund_apply.update_user_name IS '更新用户名';
COMMENT ON COLUMN outpatient_doctor.refund_apply.is_delete IS '是否删除';
COMMENT ON COLUMN outpatient_doctor.refund_apply.tenant_id IS '租户ID';
create table outpatient_doctor.refund_audit(
    id bigserial primary key,
    apply_id bigint,
    audit_time timestamp,
    audit_dept_id bigint,
    audit_dept_code varchar(100),
    audit_dept_name varchar(100),
    audit_user_id bigint,
    audit_user_code varchar(100),
    audit_user_name varchar(100),
    audit_reason varchar(200),
    audit_status int default 0,
    flow_status int default 0,
    create_org_id bigint,
    create_org_name varchar(100),
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);

COMMENT ON TABLE outpatient_doctor.refund_audit IS '退费审核表';

-- 字段注释 for refund_audit
COMMENT ON COLUMN outpatient_doctor.refund_audit.id IS '主键ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit.apply_id IS '退费申请ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_time IS '审核时间';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_dept_id IS '审核部门ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_dept_code IS '审核部门编码';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_dept_name IS '审核部门名称';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_user_id IS '审核人ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_user_code IS '审核人编码';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_user_name IS '审核人名称';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_reason IS '审核原因';
COMMENT ON COLUMN outpatient_doctor.refund_audit.audit_status IS '审核状态';
COMMENT ON COLUMN outpatient_doctor.refund_audit.create_org_id IS '创建机构ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit.create_org_name IS '创建机构名称';
COMMENT ON COLUMN outpatient_doctor.refund_audit.create_time IS '创建时间';
COMMENT ON COLUMN outpatient_doctor.refund_audit.create_user_id IS '创建用户ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit.create_user_name IS '创建用户名';
COMMENT ON COLUMN outpatient_doctor.refund_audit.update_time IS '更新时间';
COMMENT ON COLUMN outpatient_doctor.refund_audit.update_user_id IS '更新用户ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit.update_user_name IS '更新用户名';
COMMENT ON COLUMN outpatient_doctor.refund_audit.is_delete IS '是否删除';
COMMENT ON COLUMN outpatient_doctor.refund_audit.tenant_id IS '租户ID';
drop table outpatient_doctor.refund_audit_flow;
create table outpatient_doctor.refund_audit_flow(
           id bigserial primary key,
           code varchar(100),
           name varchar(200),
           flow_status int default 0,
           flow_sort int,
           role_id bigint,
           role_name varchar(100),         
           user_id bigint,
           user_name varchar(100),
           status int default 0,
           create_org_id bigint,
           create_org_name varchar(100),
           create_time timestamp,
           create_user_id bigint,
           create_user_name varchar(100),
           update_time timestamp,
           update_user_id bigint,
           update_user_name varchar(100),
           is_delete bool default false,
           tenant_id bigint       
);
-- 表注释

COMMENT ON TABLE outpatient_doctor.refund_audit_flow IS '退费审核流程表';


-- 字段注释 for refund_audit_flow
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.id IS '主键ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.code IS '编码';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.name IS '名称';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.flow_status IS '流程状态';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.flow_sort IS '流程排序'; 
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.role_id IS '角色ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.role_name IS '角色名称';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.user_id IS '用户ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.user_name IS '用户名';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.status IS '状态';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.create_org_id IS '创建机构ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.create_org_name IS '创建机构名称';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.create_time IS '创建时间';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.create_user_id IS '创建用户ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.create_user_name IS '创建用户名';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.update_time IS '更新时间';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.update_user_id IS '更新用户ID';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.update_user_name IS '更新用户名';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.is_delete IS '是否删除';
COMMENT ON COLUMN outpatient_doctor.refund_audit_flow.tenant_id IS '租户ID';

//
ALTER TABLE outpatient_doctor.charge_main
    ADD COLUMN card_no VARCHAR(32),
    ADD COLUMN visit_no VARCHAR(32),
    ADD COLUMN outpatient_doctor_no VARCHAR(32),
    ADD COLUMN billing_dept_id BIGINT,
    ADD COLUMN billing_doctor_id BIGINT,
    ADD COLUMN billing_type VARCHAR(32),
    ADD COLUMN billing_time TIMESTAMP,
    ADD COLUMN execute_dept_id BIGINT,
    ADD COLUMN execute_doctor_id BIGINT,
    ADD COLUMN execute_status SMALLINT,
    ADD COLUMN execute_time TIMESTAMP;

COMMENT ON COLUMN outpatient_doctor.charge_main.billing_time IS '开单时间';
COMMENT ON COLUMN outpatient_doctor.charge_main.billing_type IS '开单类型  处方，处置，检验，检查';
COMMENT ON COLUMN outpatient_doctor.charge_main.billing_doctor_id IS '开单医生Id';
COMMENT ON COLUMN outpatient_doctor.charge_main.billing_dept_id IS '开单科室Id';
COMMENT ON COLUMN outpatient_doctor.charge_main.outpatient_doctor_no IS '门诊号';
COMMENT ON COLUMN outpatient_doctor.charge_main.visit_no IS '就诊号';
COMMENT ON COLUMN outpatient_doctor.charge_main.card_no IS '卡号';
COMMENT ON COLUMN outpatient_doctor.charge_main.execute_dept_id IS '执行科室';
COMMENT ON COLUMN outpatient_doctor.charge_main.execute_doctor_id IS '执行医生';
COMMENT ON COLUMN outpatient_doctor.charge_main.execute_status IS '执行状态';
COMMENT ON COLUMN outpatient_doctor.charge_main.execute_time IS '执行时间';

create table if not exists outpatient_doctor.charge_main
(
    id                    bigserial
        primary key,
    patient_id            bigint   not null,
    register_id           bigint   not null,
    invoice_number        varchar(32),
    total_amount          numeric(16, 4),
    pay_channel           integer,
    pay_method1_id        bigint,
    pay_amount1           numeric(16, 4),
    pay_method2_id        bigint,
    pay_amount2           numeric(16, 4),
    "print_number "       smallint    default 0,
    print_time            timestamp(6),
    print_user_id         bigint,
    status                smallint not null,
    daily_settle          smallint,
    daily_settle_id       bigint,
    type                  smallint,
    refund_invoice_number varchar(32),
    create_org_id         bigint,
    create_org_name       varchar(64) default NULL::character varying,
    create_time           timestamp(6),
    update_time           timestamp(6),
    create_user_id        bigint,
    create_user_name      varchar(64) default NULL::character varying,
    update_user_id        bigint,
    update_user_name      varchar(64) default NULL::character varying,
    is_delete             boolean  not null,
    tenant_id             bigint,
    card_no               varchar(32),
    visit_no              varchar(32),
    outpatient_doctor_no  varchar(32),
    billing_dept_id       bigint,
    billing_doctor_id     bigint,
    billing_type          varchar(32),
    billing_time          timestamp,
    execute_dept_id       bigint,
    execute_doctor_id     bigint,
    execute_status        smallint,
    execute_time          timestamp
);

comment on table outpatient_doctor.charge_main is '收费主表';

comment on column outpatient_doctor.charge_main.id is '主键Id';

comment on column outpatient_doctor.charge_main.patient_id is '患者Id';

comment on column outpatient_doctor.charge_main.register_id is '就诊Id';

comment on column outpatient_doctor.charge_main.invoice_number is '发票号';

comment on column outpatient_doctor.charge_main.total_amount is '总金额';

comment on column outpatient_doctor.charge_main.pay_channel is '支付渠道';

comment on column outpatient_doctor.charge_main.pay_method1_id is '支付方式1Id';

comment on column outpatient_doctor.charge_main.pay_amount1 is '支付方式1金额';

comment on column outpatient_doctor.charge_main.pay_method2_id is '支付方式2Id';

comment on column outpatient_doctor.charge_main.pay_amount2 is '支付方式2金额';

comment on column outpatient_doctor.charge_main."print_number " is '打印次数';

comment on column outpatient_doctor.charge_main.print_time is '打印时间';

comment on column outpatient_doctor.charge_main.print_user_id is '打印者Id';

comment on column outpatient_doctor.charge_main.status is '状态 0未收费 1已收费 2取药 3退药 4退费 5红冲';

comment on column outpatient_doctor.charge_main.daily_settle is '是否日结';

comment on column outpatient_doctor.charge_main.daily_settle_id is '日结Id';

comment on column outpatient_doctor.charge_main.type is '收费类型 1收费 0挂号';

comment on column outpatient_doctor.charge_main.refund_invoice_number is '退费发票号';

comment on column outpatient_doctor.charge_main.create_org_id is '创建者部门Id';

comment on column outpatient_doctor.charge_main.create_org_name is '创建者部门名称';

comment on column outpatient_doctor.charge_main.create_time is '创建时间';

comment on column outpatient_doctor.charge_main.update_time is '更新时间';

comment on column outpatient_doctor.charge_main.create_user_id is '创建者Id';

comment on column outpatient_doctor.charge_main.create_user_name is '创建者姓名';

comment on column outpatient_doctor.charge_main.update_user_id is '修改者Id';

comment on column outpatient_doctor.charge_main.update_user_name is '修改者姓名';

comment on column outpatient_doctor.charge_main.is_delete is '软删除';

comment on column outpatient_doctor.charge_main.tenant_id is '租户Id';

comment on column outpatient_doctor.charge_main.card_no is '卡号';

comment on column outpatient_doctor.charge_main.visit_no is '就诊号';

comment on column outpatient_doctor.charge_main.outpatient_doctor_no is '门诊号';

comment on column outpatient_doctor.charge_main.billing_dept_id is '开单科室Id';

comment on column outpatient_doctor.charge_main.billing_doctor_id is '开单医生Id';

comment on column outpatient_doctor.charge_main.billing_type is '开单类型  处方，处置，检验，检查';

comment on column outpatient_doctor.charge_main.billing_time is '开单时间';

comment on column outpatient_doctor.charge_main.execute_dept_id is '执行科室';

comment on column outpatient_doctor.charge_main.execute_doctor_id is '执行医生';

comment on column outpatient_doctor.charge_main.execute_status is '执行状态';

comment on column outpatient_doctor.charge_main.execute_time is '执行时间';
 
create sequence outpatient_doctor.refund_apply_no_seq start with 1 increment by 1;
comment on sequence outpatient_doctor.refund_apply_no_seq is '退费申请号';

create sequence outpatient_doctor.test_no_seq start with 1 increment by 1;

SELECT LPAD(CAST(NEXTVAL(' outpatient_doctor.test_no_seq')As varchar),2,'0')

    select   NEXTVAL('outpatient_doctor.test_no_seq');
    
    drop table if exists outpatient_doctor.refund_audit;

create table if not exists outpatient_doctor.refund_audit
(
    id               bigserial
        primary key,
    apply_id         bigint,
    audit_time       timestamp,
    audit_dept_id    bigint,
    audit_dept_code  varchar(100),
    audit_dept_name  varchar(100),
    audit_user_id    bigint,
    audit_user_code  varchar(100),
    audit_user_name  varchar(100),
    audit_reason     varchar(200),
    audit_status     integer ,
    flow_id       bigint,
    flow_sort   integer default 0,
    flow_role_id bigint,
    flow_role_code varchar(100),
    flow_role_name varchar(100),
    flow_user_id bigint,
    flow_user_name varchar(100),

    create_org_id    bigint,
    create_org_name  varchar(100),
    create_time      timestamp,
    create_user_id   bigint,
    create_user_name varchar(100),
    update_time      timestamp,
    update_user_id   bigint,
    update_user_name varchar(100),
    is_delete        boolean default false,
    tenant_id        bigint
);

comment on table outpatient_doctor.refund_audit is '退费审核表';

comment on column outpatient_doctor.refund_audit.id is '主键ID';
comment on column outpatient_doctor.refund_audit.apply_id is '退费申请ID';
comment on column outpatient_doctor.refund_audit.audit_time is '审核时间';
comment on column outpatient_doctor.refund_audit.audit_dept_id is '审核部门ID';
comment on column outpatient_doctor.refund_audit.audit_dept_code is '审核部门编码';
comment on column outpatient_doctor.refund_audit.audit_dept_name is '审核部门名称';
comment on column outpatient_doctor.refund_audit.audit_user_id is '审核人ID';
comment on column outpatient_doctor.refund_audit.audit_user_code is '审核人编码';
comment on column outpatient_doctor.refund_audit.audit_user_name is '审核人名称';
comment on column outpatient_doctor.refund_audit.audit_reason is '审核原因';
comment on column outpatient_doctor.refund_audit.audit_status is '审核状态 0 待审核  null 暂不需审核';
comment on column outpatient_doctor.refund_audit.flow_id is '审核流程ID';
comment on column outpatient_doctor.refund_audit.flow_sort is '审核流程排序';
comment on column outpatient_doctor.refund_audit.flow_role_id is '审核流程角色ID';
comment on column outpatient_doctor.refund_audit.flow_role_name is '生活流程角色名称';    
comment on column outpatient_doctor.refund_audit.flow_user_id is '审核流程审核人ID';
comment on column outpatient_doctor.refund_audit.flow_user_name is '审核流程审核人名称';
comment on column outpatient_doctor.refund_audit.create_org_id is '创建机构ID';
comment on column outpatient_doctor.refund_audit.create_org_name is '创建机构名称';
comment on column outpatient_doctor.refund_audit.create_time is '创建时间';
comment on column outpatient_doctor.refund_audit.create_user_id is '创建用户ID';
comment on column outpatient_doctor.refund_audit.create_user_name is '创建用户名';
comment on column outpatient_doctor.refund_audit.update_time is '更新时间';
comment on column outpatient_doctor.refund_audit.update_user_id is '更新用户ID';
comment on column outpatient_doctor.refund_audit.update_user_name is '更新用户名';
comment on column outpatient_doctor.refund_audit.is_delete is '是否删除';
comment on column outpatient_doctor.refund_audit.tenant_id is '租户ID';

alter table outpatient_doctor.refund_audit
    owner to his_user;


create table if not exists basic.sys_user_role_org
(
    id      bigint not null primary key,
    role_id bigint not null,
    role_code varchar(32) not null,
    role_name varchar(32) not null,
    org_id  bigint not null,
    org_code varchar(32) not null,
    org_name varchar(32) not null,
    user_id bigint,
    user_name varchar(32) not null,
    create_org_id    bigint,
    create_org_name  varchar(100),
    create_time      timestamp,
    create_user_id   bigint,
    create_user_name varchar(100),
    update_time      timestamp,
    update_user_id   bigint,
    update_user_name varchar(100),
    is_delete        boolean default false,
    tenant_id        bigint
);

comment on table basic.sys_user_role_org is '用户角色机构表';
comment on column basic.sys_user_role_org.id is '主键Id';
comment on column basic.sys_user_role_org.role_id is '角色Id';
comment on column basic.sys_user_role_org.role_code is '角色编码';
comment on column basic.sys_user_role_org.role_name is '角色名称';
comment on column basic.sys_user_role_org.org_id is '机构Id';
comment on column basic.sys_user_role_org.org_code is '机构编码';
comment on column basic.sys_user_role_org.org_name is '机构名称';
comment on column basic.sys_user_role_org.user_id is '用户id';
comment on column basic.sys_user_role_org.user_name is '用户名称';
comment on column basic.sys_user_role_org.create_org_id is '创建机构Id';
comment on column basic.sys_user_role_org.create_org_name is '创建机构名称';
comment on column basic.sys_user_role_org.create_time is '创建时间';
comment on column basic.sys_user_role_org.create_user_id is '创建用户Id';
comment on column basic.sys_user_role_org.create_user_name is '创建用户姓名';
comment on column basic.sys_user_role_org.update_time is '更新时间';
comment on column basic.sys_user_role_org.update_user_id is '更新用户Id';
comment on column basic.sys_user_role_org.update_user_name is '更新用户姓名';
comment on column basic.sys_user_role_org.is_delete is '软删除';
comment on column basic.sys_user_role_org.tenant_id is '租户Id';


-- 抗生素权限
alter table basic.sys_user
    add column antibacterial_permission int default 0,
    add column narcotic_permission int default 0,
    add column psychotropic_permission int default 0,
    add column herbs_permission int default 0,
    add column prescription_permission int default 0,
    add column administrative_dept int default 0,
    -- add column has_staffing int default 0,
    add column leave_date timestamp,
    add column medicine_code varchar(100),
    add column certificate_code varchar(100);
comment on column basic.sys_user.antibacterial_permission is '抗生素处方权';
comment on column basic.sys_user.narcotic_permission is '麻醉处方权';
comment on column basic.sys_user.psychotropic_permission is '精神药品处方权';
comment on column basic.sys_user.herbs_permission is '草药处方权';
comment on column basic.sys_user.prescription_permission is '处方权限';
comment on column basic.sys_user.administrative_dept is '行政科室';
comment on column basic.sys_user.leave_date is '离职日期';
comment on column basic.sys_user.medicine_code is '医保编号';
comment on column basic.sys_user.certificate_code is '职业资格证编号';


antibacterial_permission 
-- 毒麻权限 
narcotic_permission
-- 精二权限

psychotropic_permission
--草药处方
herbs_permission
-- 行政科室
administrative_dept
-- 是否在编
--has_staffing
-- 离职日期
leave_date
-- 医保编号
medicine_code
-- 职业资格证编号
certificate_code

create table basic.sys_org_storage(
    id bigserial primary key,
    org_id bigint,
    org_code varchar(100),
    org_name varchar(100),
    storage_id bigint,
    storage_code varchar(100),
    storage_name varchar(100),
    create_org_id    bigint,
    create_org_name  varchar(100),
    create_time      timestamp,
    create_user_id   bigint,
    create_user_name varchar(100),
    update_time      timestamp,
    update_user_id   bigint,
    update_user_name varchar(100),
    is_delete        boolean default false,
    tenant_id        bigint
)
comment on table basic.sys_org_storage is '科室药房表';
comment on column basic.sys_org_storage.id is '主键Id';
comment on column basic.sys_org_storage.org_id is '科室Id';
comment on column basic.sys_org_storage.org_code is '科室编码';
comment on column basic.sys_org_storage.org_name is '科室名称';
comment on column basic.sys_org_storage.storage_id is '药房Id';
comment on column basic.sys_org_storage.storage_code is '药房编码';
comment on column basic.sys_org_storage.storage_name is '药房名称';
comment on column basic.sys_org_storage.create_org_id is '创建机构Id';
comment on column basic.sys_org_storage.create_org_name is '创建机构名称';
comment on column basic.sys_org_storage.create_time is '创建时间';
comment on column basic.sys_org_storage.create_user_id is '创建用户Id';
comment on column basic.sys_org_storage.create_user_name is '创建用户姓名';
comment on column basic.sys_org_storage.update_time is '更新时间';
comment on column basic.sys_org_storage.update_user_id is '更新用户Id';
comment on column basic.sys_org_storage.update_user_name is '更新用户姓名';
comment on column basic.sys_org_storage.is_delete is '软删除';
comment on column basic.sys_org_storage.tenant_id is '租户Id';


alter table pharmacy.enterprise_dictionary
  --  drop column contact_address
        
--  联系人
   -- add column contact_name varchar(100),
   -- add column contact_phone varchar(100),
    add column enterprise_address varchar(100);
comment on column pharmacy.enterprise_dictionary.contact_name is '联系人';
comment on column pharmacy.enterprise_dictionary.contact_phone is '联系人电话';
comment on column pharmacy.enterprise_dictionary.enterprise_address is '企业地址';

drop table if exists pharmacy.outpatient_drug_refund_record;
create table if not exists pharmacy.outpatient_drug_refund_record
(
    id                     bigserial primary key,
    refund_no              varchar(100),                    -- 退药单号
    send_record_id         bigint,                          -- 关联的发药记录ID（外键）
    refund_user_id         bigint,                          -- 退药人ID
    refund_user_name       varchar(100),                    -- 退药人名称
    refund_time            timestamp,                       -- 退药时间
    refund_apply_id          bigint,                        -- 退药申请id
    audit_time             timestamp,                       -- 审核完成时间
    reason                 text,                            -- 退药原因
    storage_id             bigint,                          -- 药房ID 
    storage_name      varchar(100),                    -- 药房名称
    patient_id             bigint,                          -- 患者ID
    patient_name           varchar(100),                    -- 患者名称
    visit_no               varchar(100),                    -- 就诊号
    visit_id               bigint,                          -- 就诊id
    card_no                varchar(100),                    -- 卡号
    card_id                bigint,                          -- 卡id
    prescription_id        bigint,                          -- 处方ID
    prescription_detail_id bigint,                          -- 处方明细ID
    drug_id                bigint,                          -- 药品ID
    drug_code              varchar(100),                    -- 药品编码
    drug_name              varchar(100),                    -- 药品名称
    spec                   varchar(100),                    -- 药品规格
    unit                   varchar(100),                    -- 药品单位
    quantity              numeric(16,4),               -- 发药数量
    refund_quantity        numeric(16,4),               -- 退药数量

    inventory_id           bigint,                          -- 库存ID
    price                  numeric(16,4),                   -- 单价
    refund_amount          numeric(20,4),                   -- 总金额
    batch_no               varchar(100),                    -- 批号
    create_time            timestamp,                       -- 创建时间
    create_user_id         bigint,                          -- 创建用户ID
    create_user_name       varchar(100),                    -- 创建用户名
    update_time            timestamp,                       -- 更新时间
    update_user_id         bigint,                          -- 更新用户ID
    update_user_name       varchar(100),                    -- 更新用户名
    is_delete              boolean default false,           -- 是否删除
    tenant_id              bigint                           -- 租户ID
);

comment on table pharmacy.outpatient_drug_refund_record is '门诊退药记录表';
comment on column pharmacy.outpatient_drug_refund_record.id is '主键ID';
comment on column pharmacy.outpatient_drug_refund_record.refund_no is '退药单号';
comment on column pharmacy.outpatient_drug_refund_record.send_record_id is '关联的发药记录ID';
comment on column pharmacy.outpatient_drug_refund_record.refund_user_id is '退药人ID';
comment on column pharmacy.outpatient_drug_refund_record.refund_user_name is '退药人名称';
comment on column pharmacy.outpatient_drug_refund_record.refund_time is '退药时间';
comment on column pharmacy.outpatient_drug_refund_record.refund_apply_id is '退药申请id';
comment on column pharmacy.outpatient_drug_refund_record.audit_time is '审核完成时间'; 
comment on column pharmacy.outpatient_drug_refund_record.reason is '退药原因';
comment on column pharmacy.outpatient_drug_refund_record.storage_id is '药房ID';
comment on column pharmacy.outpatient_drug_refund_record.storage_name is '药房名称';
comment on column pharmacy.outpatient_drug_refund_record.patient_id is '患者ID';
comment on column pharmacy.outpatient_drug_refund_record.patient_name is '患者名称';
comment on column pharmacy.outpatient_drug_refund_record.visit_no is '就诊号';
comment on column pharmacy.outpatient_drug_refund_record.visit_id is '就诊id';
comment on column pharmacy.outpatient_drug_refund_record.card_no is '卡号';
comment on column pharmacy.outpatient_drug_refund_record.card_id is '卡id';
comment on column pharmacy.outpatient_drug_refund_record.prescription_id is '处方ID';
comment on column pharmacy.outpatient_drug_refund_record.prescription_detail_id is '处方明细ID';
comment on column pharmacy.outpatient_drug_refund_record.drug_id is '药品ID';
comment on column pharmacy.outpatient_drug_refund_record.drug_code is '药品编码';
comment on column pharmacy.outpatient_drug_refund_record.drug_name is '药品名称';
comment on column pharmacy.outpatient_drug_refund_record.spec is '药品规格';
comment on column pharmacy.outpatient_drug_refund_record.unit is '药品单位';
comment on column pharmacy.outpatient_drug_refund_record.refund_quantity is '退药数量';
comment on column pharmacy.outpatient_drug_refund_record.inventory_id is '库存ID';
comment on column pharmacy.outpatient_drug_refund_record.price is '零售价';
comment on column pharmacy.outpatient_drug_refund_record.quantity is '发药数量';

comment on column pharmacy.outpatient_drug_refund_record.refund_amount is '总退药金额';
comment on column pharmacy.outpatient_drug_refund_record.batch_no is '批号';
comment on column pharmacy.outpatient_drug_refund_record.create_time is '创建时间';
comment on column pharmacy.outpatient_drug_refund_record.create_user_id is '创建用户ID';
comment on column pharmacy.outpatient_drug_refund_record.create_user_name is '创建用户名';
comment on column pharmacy.outpatient_drug_refund_record.update_time is '更新时间';
comment on column pharmacy.outpatient_drug_refund_record.update_user_id is '更新用户ID';
comment on column pharmacy.outpatient_drug_refund_record.update_user_name is '更新用户名';
comment on column pharmacy.outpatient_drug_refund_record.is_delete is '是否删除';
comment on column pharmacy.outpatient_drug_refund_record.tenant_id is '租户ID';



alter table outpatient_doctor.prescription_main
    add refund_user_id bigint,
    add refund_reason varchar(255);
comment on column outpatient_doctor.prescription_main.refund_user_id is '退费人id';
comment on column outpatient_doctor.prescription_main.refund_reason is '退费原因';

drop table if exists registration.scheduling_plan;

create table if not exists registration.scheduling_plan
(
    id                bigserial
        primary key,
    doctor_id         bigint,
    doctor_name       varchar(64),
    time_period_id    bigint,
    time_period_code  varchar(64),
    time_period_name  varchar(64),
    reg_category_id   bigint,
    reg_category_name varchar(200),
    reg_limit         integer,
    app_limit         integer,
    reg_number        integer,
    app_number        integer,
    outpatient_date   date,
    start_time        time(6),
    end_time          time(6),
    dept_id           bigint,
    dept_name         varchar(64),
    week_day          varchar(32),
    room_id           bigint,
    room_name         varchar(64),
    ip_address        varchar(64),
    
    remark            varchar(256),
    create_time       timestamp(6),
    update_time       timestamp(6),
    create_user_id    bigint,
    create_user_name  varchar(64) default NULL::character varying,
    update_user_id    bigint,
    update_user_name  varchar(64) default NULL::character varying,
    is_delete         boolean not null,
    tenant_id         bigint
);

comment on table registration.scheduling_plan is '排班计划表';

comment on column registration.scheduling_plan.id is '主键Id';

comment on column registration.scheduling_plan.doctor_id is '医生id';

comment on column registration.scheduling_plan.doctor_name is '医生姓名';

comment on column registration.scheduling_plan.time_period_id is '时间段id';

comment on column registration.scheduling_plan.time_period_code is '时间段编码';

comment on column registration.scheduling_plan.time_period_name is '时间段名称';

comment on column registration.scheduling_plan.reg_category_id is '号别id';
comment on column registration.scheduling_plan.reg_category_name is '挂号类别名称';
comment on column registration.scheduling_plan.reg_limit is '限号数';

comment on column registration.scheduling_plan.app_limit is '限预约号数';

comment on column registration.scheduling_plan.reg_number is '已挂号数';

comment on column registration.scheduling_plan.app_number is '已预约号数';

comment on column registration.scheduling_plan.outpatient_date is '门诊日期';

comment on column registration.scheduling_plan.start_time is '开始时间';

comment on column registration.scheduling_plan.end_time is '结束时间';

comment on column registration.scheduling_plan.dept_id is '科室id';

comment on column registration.scheduling_plan.dept_name is '科室名称';

comment on column registration.scheduling_plan.week_day is '星期几';
comment on column registration.scheduling_plan.room_id is '诊室id';
comment on column registration.scheduling_plan.room_name is '诊室名称';
comment on column registration.scheduling_plan.ip_address is 'ip地址';

comment on column registration.scheduling_plan.remark is '备注';

comment on column registration.scheduling_plan.create_time is '创建时间';

comment on column registration.scheduling_plan.update_time is '更新时间';

comment on column registration.scheduling_plan.create_user_id is '创建者Id';

comment on column registration.scheduling_plan.create_user_name is '创建者姓名';

comment on column registration.scheduling_plan.update_user_id is '修改者Id';

comment on column registration.scheduling_plan.update_user_name is '修改者姓名';

comment on column registration.scheduling_plan.is_delete is '软删除';

comment on column registration.scheduling_plan.tenant_id is '租户Id';

drop table if exists shared.scheduling_template;
create table if not exists shared.scheduling_template
(
    id                bigserial    primary key,
    template_name       varchar(200),
    dept_id           bigint,
    dept_name         varchar(64),
    remark            varchar(256),
    create_time       timestamp(6),
    update_time       timestamp(6),
    create_user_id    bigint,
    create_user_name  varchar(64) default NULL::character varying,
    update_user_id    bigint,
    update_user_name  varchar(64) default NULL::character varying,
    is_delete         boolean not null,
    tenant_id         bigint
);
comment on table shared.scheduling_template is '排班模板表';
comment on column shared.scheduling_template.id is '主键Id';
comment on column shared.scheduling_template.template_name is '模板名称';
comment on column shared.scheduling_template.dept_id is '科室id';
comment on column shared.scheduling_template.dept_name is '科室名称';
comment on column shared.scheduling_template.remark is '备注';
comment on column shared.scheduling_template.create_time is '创建时间';
comment on column shared.scheduling_template.update_time is '更新时间';
comment on column shared.scheduling_template.create_user_id is '创建者Id';
comment on column shared.scheduling_template.create_user_name is '创建者姓名';
comment on column shared.scheduling_template.update_user_id is '修改者Id';
comment on column shared.scheduling_template.update_user_name is '修改者姓名';
comment on column shared.scheduling_template.is_delete is '软删除';
comment on column shared.scheduling_template.tenant_id is '租户Id';


drop table if exists shared.scheduling_template_detail;

create table if not exists shared.scheduling_template_detail
(
    id                bigserial
        primary key,
    template_id                bigint,
    doctor_id         bigint,
    doctor_name       varchar(64),
    time_period_id    bigint,
    time_period_code  varchar(64),
    time_period_name  varchar(64),
    reg_category_id   bigint,
    reg_category_name varchar(200),
    reg_limit         integer,
    app_limit         integer,
    reg_number        integer,
    app_number        integer,
    start_time        time(6),
    end_time          time(6),
    dept_id           bigint,
    dept_name         varchar(64),
    week_day          varchar(32),
    room_id           bigint,
    room_name         varchar(64),
    ip_address        varchar(64),
    remark            varchar(256),
    create_time       timestamp(6),
    update_time       timestamp(6),
    create_user_id    bigint,
    create_user_name  varchar(64) default NULL::character varying,
    update_user_id    bigint,
    update_user_name  varchar(64) default NULL::character varying,
    is_delete         boolean not null,
    tenant_id         bigint
);

comment on table shared.scheduling_template_detail is '排班计划模板表明细';

comment on column shared.scheduling_template_detail.id is '主键Id';
comment on column shared.scheduling_template_detail.template_id is '模板id';

comment on column shared.scheduling_template_detail.doctor_id is '医生id';

comment on column shared.scheduling_template_detail.doctor_name is '医生姓名';

comment on column shared.scheduling_template_detail.time_period_id is '时间段id';

comment on column shared.scheduling_template_detail.time_period_code is '时间段编码';

comment on column shared.scheduling_template_detail.time_period_name is '时间段名称';

comment on column shared.scheduling_template_detail.reg_category_id is '号别id';

comment on column shared.scheduling_template_detail.reg_category_name is '挂号类别名称';

comment on column shared.scheduling_template_detail.reg_limit is '限号数';

comment on column shared.scheduling_template_detail.app_limit is '限预约号数';

comment on column shared.scheduling_template_detail.reg_number is '已挂号数';

comment on column shared.scheduling_template_detail.app_number is '已预约号数';


comment on column shared.scheduling_template_detail.start_time is '开始时间';

comment on column shared.scheduling_template_detail.end_time is '结束时间';

comment on column shared.scheduling_template_detail.dept_id is '科室id';

comment on column shared.scheduling_template_detail.dept_name is '科室名称';

comment on column shared.scheduling_template_detail.week_day is '星期几';
comment on column shared.scheduling_template_detail.room_id is '诊室id';
comment on column shared.scheduling_template_detail.room_name is '诊室名称';
comment on column shared.scheduling_template_detail.ip_address is 'ip地址';

comment on column shared.scheduling_template_detail.remark is '备注';

comment on column shared.scheduling_template_detail.create_time is '创建时间';

comment on column shared.scheduling_template_detail.update_time is '更新时间';

comment on column shared.scheduling_template_detail.create_user_id is '创建者Id';

comment on column shared.scheduling_template_detail.create_user_name is '创建者姓名';

comment on column shared.scheduling_template_detail.update_user_id is '修改者Id';

comment on column shared.scheduling_template_detail.update_user_name is '修改者姓名';

comment on column shared.scheduling_template_detail.is_delete is '软删除';

comment on column shared.scheduling_template_detail.tenant_id is '租户Id';



-- 诊室表
drop table if exists  registration.clinic_room;
create table   registration.clinic_room(
    id bigserial primary key,
    name varchar(255),
    code varchar(255),
    dept_id bigint,
    dept_name varchar(255),
    ip_address varchar(255),
    status int default 0,
    remark varchar(255),
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(255),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(255),
    is_delete boolean default false,
    tenant_id bigint
);
comment on table  registration.clinic_room is '诊室表';
comment on column  registration.clinic_room.id is '主键Id';
comment on column  registration.clinic_room.name is '诊室名称';
comment on column  registration.clinic_room.code is '诊室编码';
comment on column  registration.clinic_room.dept_id is '科室id';
comment on column  registration.clinic_room.dept_name is '科室名称';
comment on column  registration.clinic_room.ip_address is 'ip地址';
comment on column  registration.clinic_room.status is '状态';
comment on column  registration.clinic_room.remark is '备注';
comment on column  registration.clinic_room.create_time is '创建时间';
comment on column  registration.clinic_room.create_user_id is '创建者Id';
comment on column  registration.clinic_room.create_user_name is '创建者姓名';
comment on column  registration.clinic_room.update_time is '更新时间';
comment on column  registration.clinic_room.update_user_id is '修改者Id';
comment on column  registration.clinic_room.update_user_name is '修改者姓名';
comment on column  registration.clinic_room.is_delete is '软删除';
comment on column  registration.clinic_room.tenant_id is '租户Id';
-- 诊台 
drop table if exists  registration.clinic_console;
create table   registration.clinic_console(
  id bigserial primary key,
  name varchar(255),
  code varchar(255),
  room_id bigint,
  room_name varchar(255),
  current_count int default 0, 
  status int default 0,
  remark varchar(255),
  create_time timestamp,
  create_user_id bigint,
  create_user_name varchar(255),
  update_time timestamp,
  update_user_id bigint,
  update_user_name varchar(255),
  is_delete boolean default false,
  tenant_id bigint
);
comment on table  registration.clinic_console is '诊台表';
comment on column  registration.clinic_console.id is '主键Id';
comment on column  registration.clinic_console.name is '诊台名称';
comment on column  registration.clinic_console.code is '诊台编码';
comment on column  registration.clinic_console.room_id is '诊室id';
comment on column  registration.clinic_console.room_name is '诊室名称';
comment on column  registration.clinic_console.current_count is '当前人数';
comment on column  registration.clinic_console.status is '状态';
comment on column  registration.clinic_console.remark is '备注';
comment on column  registration.clinic_console.create_time is '创建时间';
comment on column  registration.clinic_console.create_user_id is '创建者Id';
comment on column  registration.clinic_console.create_user_name is '创建者姓名';
comment on column  registration.clinic_console.update_time is '更新时间';
comment on column  registration.clinic_console.update_user_id is '修改者Id';
comment on column  registration.clinic_console.update_user_name is '修改者姓名';
comment on column  registration.clinic_console.is_delete is '软删除';
comment on column  registration.clinic_console.tenant_id is '租户Id';


create table if not exists registration.scheduling_plan
(
    id                bigserial
        primary key,
    doctor_id         bigint,
    doctor_name       varchar(64),
    time_period_id    bigint,
    time_period_code  varchar(64),
    time_period_name  varchar(64),
    reg_category_id   bigint,
    reg_category_name varchar(200),
    reg_limit         integer,
    app_limit         integer,
    reg_number        integer,
    app_number        integer,
    outpatient_date   date,
    start_time        time(6),
    end_time          time(6),
    dept_id           bigint,
    dept_name         varchar(64),
    week_day          varchar(32),
    room_id           bigint,
    room_name         varchar(64),
    ip_address        varchar(64),

    remark            varchar(256),
    create_time       timestamp(6),
    update_time       timestamp(6),
    create_user_id    bigint,
    create_user_name  varchar(64) default NULL::character varying,
    update_user_id    bigint,
    update_user_name  varchar(64) default NULL::character varying,
    is_delete         boolean not null,
    tenant_id         bigint
);

comment on table registration.scheduling_plan is '排班计划表';

comment on column registration.scheduling_plan.id is '主键Id';

comment on column registration.scheduling_plan.doctor_id is '医生id';

comment on column registration.scheduling_plan.doctor_name is '医生姓名';

comment on column registration.scheduling_plan.time_period_id is '时间段id';

comment on column registration.scheduling_plan.time_period_code is '时间段编码';

comment on column registration.scheduling_plan.time_period_name is '时间段名称';

comment on column registration.scheduling_plan.reg_category_id is '号别id';
comment on column registration.scheduling_plan.reg_category_name is '挂号类别名称';
comment on column registration.scheduling_plan.reg_limit is '限号数';

comment on column registration.scheduling_plan.app_limit is '限预约号数';

comment on column registration.scheduling_plan.reg_number is '已挂号数';

comment on column registration.scheduling_plan.app_number is '已预约号数';

comment on column registration.scheduling_plan.outpatient_date is '门诊日期';

comment on column registration.scheduling_plan.start_time is '开始时间';

comment on column registration.scheduling_plan.end_time is '结束时间';

comment on column registration.scheduling_plan.dept_id is '科室id';

comment on column registration.scheduling_plan.dept_name is '科室名称';

comment on column registration.scheduling_plan.week_day is '星期几';
comment on column registration.scheduling_plan.room_id is '诊室id';
comment on column registration.scheduling_plan.room_name is '诊室名称';
comment on column registration.scheduling_plan.ip_address is 'ip地址';

comment on column registration.scheduling_plan.remark is '备注';

comment on column registration.scheduling_plan.create_time is '创建时间';

comment on column registration.scheduling_plan.update_time is '更新时间';

comment on column registration.scheduling_plan.create_user_id is '创建者Id';

comment on column registration.scheduling_plan.create_user_name is '创建者姓名';

comment on column registration.scheduling_plan.update_user_id is '修改者Id';

comment on column registration.scheduling_plan.update_user_name is '修改者姓名';

comment on column registration.scheduling_plan.is_delete is '软删除';

comment on column registration.scheduling_plan.tenant_id is '租户Id';



 drop table if exists  shared.sys_org_struct;
 create table if not exists shared.sys_org_struct
(
    id               bigserial
        primary key,
    code             varchar(255),
    name             varchar(255),
    parent_id        bigint,
    parent_name      varchar(255),
    level            integer default 0,
    status           integer default 0,
    remark           varchar(255),
    create_time      timestamp,
    create_user_id   bigint,
    create_user_name varchar(255),
    update_time      timestamp,
    update_user_id   bigint,
    update_user_name varchar(255),
    is_delete        boolean default false,
    tenant_id        bigint
);

comment on table shared.sys_org_struct is '科室结构维护';

comment on column shared.sys_org_struct.id is '主键Id';

comment on column shared.sys_org_struct.name is '科室名称';

comment on column shared.sys_org_struct.parent_id is '父级id';

comment on column shared.sys_org_struct.parent_name is '父级名称';

comment on column shared.sys_org_struct.level is '层级';

comment on column shared.sys_org_struct.status is '状态';

comment on column shared.sys_org_struct.remark is '备注';

comment on column shared.sys_org_struct.create_time is '创建时间';

comment on column shared.sys_org_struct.create_user_id is '创建者Id';

comment on column shared.sys_org_struct.create_user_name is '创建者姓名';

comment on column shared.sys_org_struct.update_time is '更新时间';

comment on column shared.sys_org_struct.update_user_id is '修改者Id';

comment on column shared.sys_org_struct.update_user_name is '修改者姓名';

comment on column shared.sys_org_struct.is_delete is '软删除';

comment on column shared.sys_org_struct.tenant_id is '租户Id';

comment on column shared.sys_org_struct.code is '编号';
 

drop table if exists  shared.sys_org_struct_detail;

create table    shared.sys_org_struct_detail(
         id bigserial primary key,
         struct_id bigint,
    struct_code varchar(255),
         struct_name varchar(255),
     dept_id bigint,
    dept_name varchar(255),
    status int default 0,
    remark varchar(255),
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(255),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(255),
    is_delete boolean default false,
    tenant_id bigint
);
comment on table  shared.sys_org_struct_detail is '科室结构维护详情';
comment on column  shared.sys_org_struct_detail.id is '主键Id';
comment on column  shared.sys_org_struct_detail.struct_id is '结构id';
comment on column  shared.sys_org_struct_detail.struct_code is '结构编码';
comment on column  shared.sys_org_struct_detail.struct_name is '结构名称';
comment on column  shared.sys_org_struct_detail.dept_id is '科室id';
comment on column  shared.sys_org_struct_detail.dept_name is '科室名称';
comment on column  shared.sys_org_struct_detail.status is '状态';
comment on column  shared.sys_org_struct_detail.remark is '备注';
comment on column  shared.sys_org_struct_detail.create_time is '创建时间';
comment on column  shared.sys_org_struct_detail.create_user_id is '创建者Id';
comment on column  shared.sys_org_struct_detail.create_user_name is '创建者姓名';
comment on column  shared.sys_org_struct_detail.update_time is '更新时间';
comment on column  shared.sys_org_struct_detail.update_user_id is '修改者Id';
comment on column  shared.sys_org_struct_detail.update_user_name is '修改者姓名';
comment on column  shared.sys_org_struct_detail.is_delete is '软删除';
comment on column  shared.sys_org_struct_detail.tenant_id is '租户Id';

drop table if exists registration.triage_queue;

create table registration.triage_queue (
           id bigserial primary key,
           scheduling_plan_id bigint,
           room_id bigint,
           room_name varchar(255),
           console_id bigint,
           console_name varchar(255),
           queue_number int,
           status int,
           time_period_id    bigint,
           time_period_code  varchar(64),
           time_period_name  varchar(64),
           time_period_start_time  timestamp,
           time_period_end_time timestamp,
           remark varchar(255),
           create_time timestamp,
           create_user_id bigint,
           create_user_name varchar(255),
           update_time timestamp,
           update_user_id bigint,
           update_user_name varchar(255),
           is_delete boolean default false,
           tenant_id bigint
);
-- 表注释
COMMENT ON TABLE registration.triage_queue IS '分诊队列表，用于记录患者排队信息';

-- 字段注释
COMMENT ON COLUMN registration.triage_queue.id IS '主键ID';
COMMENT ON COLUMN registration.triage_queue.scheduling_plan_id IS '排班计划ID';
COMMENT ON COLUMN registration.triage_queue.room_id IS '诊室ID';
COMMENT ON COLUMN registration.triage_queue.room_name IS '诊室名称';
COMMENT ON COLUMN registration.triage_queue.console_id IS '分诊台ID';
COMMENT ON COLUMN registration.triage_queue.console_name IS '分诊台名称';
COMMENT ON COLUMN registration.triage_queue.queue_number IS '排队号';
COMMENT ON COLUMN registration.triage_queue.status IS '状态（如排队中、已就诊等）';
COMMENT ON COLUMN registration.triage_queue.time_period_id IS '时间段ID';
COMMENT ON COLUMN registration.triage_queue.time_period_code IS '时间段编码';
COMMENT ON COLUMN registration.triage_queue.time_period_name IS '时间段名称';
COMMENT ON COLUMN registration.triage_queue.time_period_start_time IS '时间段开始时间';
COMMENT ON COLUMN registration.triage_queue.time_period_end_time IS '时间段结束时间';
COMMENT ON COLUMN registration.triage_queue.remark IS '备注';
COMMENT ON COLUMN registration.triage_queue.create_time IS '创建时间';
COMMENT ON COLUMN registration.triage_queue.create_user_id IS '创建用户ID';
COMMENT ON COLUMN registration.triage_queue.create_user_name IS '创建用户名';
COMMENT ON COLUMN registration.triage_queue.update_time IS '更新时间';
COMMENT ON COLUMN registration.triage_queue.update_user_id IS '更新用户ID';
COMMENT ON COLUMN registration.triage_queue.update_user_name IS '更新用户名';
COMMENT ON COLUMN registration.triage_queue.is_delete IS '是否删除';
COMMENT ON COLUMN registration.triage_queue.tenant_id IS '租户ID';

drop table if exists registration.triage_queue_detail;

create table registration.triage_queue_detail (
                                                  id bigserial primary key,
                                                  triage_queue_detail_id bigint,
                                                  register_id bigint,
                                                  patient_id bigint,
                                                  patient_name varchar(255),
                                                  room_id bigint,
                                                  room_name varchar(255),
                                                  console_id bigint,
                                                  console_name varchar(255),
                                                  queue_number int,
                                                  status int,
                                                  priority_level int  default 0,--'优先级(0:普通,1:复诊,2:老年人,3:军人等)',
                                                  time_period_id    bigint,
                                                  time_period_code  varchar(64),
                                                  time_period_name  varchar(64),
                                                  triage_time timestamp,
                                                  start_time  timestamp,
                                                  end_time timestamp,
                                                  remark varchar(255),
                                                  create_time timestamp,
                                                  create_user_id bigint,
                                                  create_user_name varchar(255),
                                                  update_time timestamp,
                                                  update_user_id bigint,
                                                  update_user_name varchar(255),
                                                  is_delete boolean default false,
                                                  tenant_id bigint
);
-- 表注释
COMMENT ON TABLE registration.triage_queue_detail IS '分诊队列表，用于记录患者排队信息';

-- 字段注释
COMMENT ON COLUMN registration.triage_queue_detail.id IS '主键ID';
COMMENT ON COLUMN registration.triage_queue_detail.triage_queue_detail_id IS '分诊队列表ID';
COMMENT ON COLUMN registration.triage_queue_detail.register_id IS '挂号ID';
COMMENT ON COLUMN registration.triage_queue_detail.patient_id IS '患者ID';
COMMENT ON COLUMN registration.triage_queue_detail.patient_name IS '患者姓名';
COMMENT ON COLUMN registration.triage_queue_detail.room_id IS '诊室ID';
COMMENT ON COLUMN registration.triage_queue_detail.room_name IS '诊室名称';
COMMENT ON COLUMN registration.triage_queue_detail.console_id IS '分诊台ID';
COMMENT ON COLUMN registration.triage_queue_detail.console_name IS '分诊台名称';
COMMENT ON COLUMN registration.triage_queue_detail.queue_number IS '排队号';
COMMENT ON COLUMN registration.triage_queue_detail.status IS '状态（0 未分诊 1 已分诊 2 已就诊）';
COMMENT ON COLUMN registration.triage_queue_detail.priority_level IS '优先级(0:普通,1:复诊,2:老年人,3:军人等)';
COMMENT ON COLUMN registration.triage_queue_detail.time_period_id IS '时间段ID';
COMMENT ON COLUMN registration.triage_queue_detail.time_period_code IS '时间段编码';
COMMENT ON COLUMN registration.triage_queue_detail.time_period_name IS '时间段名称';
COMMENT ON COLUMN registration.triage_queue_detail.triage_time IS '分诊时间';
COMMENT ON COLUMN registration.triage_queue_detail.start_time IS '开始时间';
COMMENT ON COLUMN registration.triage_queue_detail.end_time IS '结束时间';
COMMENT ON COLUMN registration.triage_queue_detail.remark IS '备注';
COMMENT ON COLUMN registration.triage_queue_detail.create_time IS '创建时间';
COMMENT ON COLUMN registration.triage_queue_detail.create_user_id IS '创建用户ID';
COMMENT ON COLUMN registration.triage_queue_detail.create_user_name IS '创建用户名';
COMMENT ON COLUMN registration.triage_queue_detail.update_time IS '更新时间';
COMMENT ON COLUMN registration.triage_queue_detail.update_user_id IS '更新用户ID';
COMMENT ON COLUMN registration.triage_queue_detail.update_user_name IS '更新用户名';
COMMENT ON COLUMN registration.triage_queue_detail.is_delete IS '是否删除';
COMMENT ON COLUMN registration.triage_queue_detail.tenant_id IS '租户ID';

alter table outpatient_doctor.register
    add column  triage_status int default 0;
comment on column outpatient_doctor.register.triage_status is '分诊状态（0 未分诊 1 已分诊 2 已就诊）';