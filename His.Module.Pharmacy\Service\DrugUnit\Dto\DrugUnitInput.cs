﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品单位维护基础输入参数
/// </summary>
public class DrugUnitBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 单位名称
    /// </summary>
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 转换单位
    /// </summary>
    public virtual string? ConvertUnit { get; set; }
    
    /// <summary>
    /// 转换比率
    /// </summary>
    public virtual decimal? ConvertRatio { get; set; }
    
    public virtual string? UnitPinyin { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品单位维护分页查询输入参数
/// </summary>
public class PageDrugUnitInput : BasePageInput
{
    /// <summary>
    /// 单位名称
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 转换单位
    /// </summary>
    public string? ConvertUnit { get; set; }
    
    /// <summary>
    /// 转换比率
    /// </summary>
    public decimal? ConvertRatio { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品单位维护增加输入参数
/// </summary>
public class AddDrugUnitInput
{
    /// <summary>
    /// 单位名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "单位名称字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 转换单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "转换单位字符长度不能超过100")]
    public string? ConvertUnit { get; set; }
    
    /// <summary>
    /// 转换比率
    /// </summary>
    public decimal? ConvertRatio { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品单位维护删除输入参数
/// </summary>
public class DeleteDrugUnitInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品单位维护更新输入参数
/// </summary>
public class UpdateDrugUnitInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 单位名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "单位名称字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 转换单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "转换单位字符长度不能超过100")]
    public string? ConvertUnit { get; set; }
    
    /// <summary>
    /// 转换比率
    /// </summary>    
    public decimal? ConvertRatio { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品单位维护主键查询输入参数
/// </summary>
public class QueryByIdDrugUnitInput : DeleteDrugUnitInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetDrugUnitStatusInput : BaseStatusInput
{
}

/// <summary>
/// 药品单位维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugUnitInput : BaseImportInput
{
    /// <summary>
    /// 单位名称
    /// </summary>
    [ImporterHeader(Name = "单位名称")]
    [ExporterHeader("单位名称", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 转换单位
    /// </summary>
    [ImporterHeader(Name = "转换单位")]
    [ExporterHeader("转换单位", Format = "", Width = 25, IsBold = true)]
    public string? ConvertUnit { get; set; }
    
    /// <summary>
    /// 转换比率
    /// </summary>
    [ImporterHeader(Name = "转换比率")]
    [ExporterHeader("转换比率", Format = "", Width = 25, IsBold = true)]
    public decimal? ConvertRatio { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
}
