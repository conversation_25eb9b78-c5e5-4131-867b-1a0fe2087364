﻿namespace His.Module.MedicalTech.Service ;

    /// <summary>
    /// 申请单处理分页查询输入参数
    /// </summary>
    public class PageApplyProcessInput : BasePageInput
    {
        /// <summary>
        /// 申请单号
        /// </summary>
        public string ApplyNo { get; set; }

        /// <summary>
        /// 就诊流水号
        /// </summary>
        public string VisitNo { get; set; }

        /// <summary>
        /// 就诊卡号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 开单科室Id
        /// </summary>
        public long? BillingDeptId { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 门诊住院标识 0门诊 1住院
        /// </summary>
        public int? Flag { get; set; }
    }

    /// <summary>
    /// 申请单设置状态输入参数
    /// </summary>
    public class SetApplyProcessStatusInput : BaseIdInput
    {
        /// <summary>
        /// 申请单状态
        /// </summary>
        [Required(ErrorMessage = "申请单状态不能为空")]
        public int Status { get; set; }

        /// <summary>
        /// 申请单类型
        /// </summary>
        [Required(ErrorMessage = "申请单类型不能为空")]
        public string ApplyType { get; set; }
    }

    /// <summary>
    /// 申请单处理查询输入参数
    /// </summary>
    public class QueryByIdApplyProcessInput : BaseIdInput
    {
        /// <summary>
        /// 申请单类型
        /// </summary>
        [Required(ErrorMessage = "申请单类型不能为空")]
        public string ApplyType { get; set; }

        /// <summary>
        /// 计费id
        /// </summary>
        [Required(ErrorMessage = "计费id不能为空")]
        public long ChargeId { get; set; }
    }

    /// <summary>
    /// 申请单收费输入参数
    /// </summary>
    public class ChargeApplyProcessInput
    {
        /// <summary>
        /// 申请单信息
        /// </summary>
        [Required(ErrorMessage = "申请单信息不能为空")]
        public List<QueryByIdApplyProcessInput> ApplyDetails { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        [Required(ErrorMessage = "患者ID不能为空")]
        public long PatientId { get; set; }

        /// <summary>
        /// 是否使用共济账户
        /// </summary>
        public bool EnableMutualAid { get; set; }

        /// <summary>
        /// 共济账户余额
        /// </summary>
        public decimal? MutualAidAccountBalance { get; set; }

        /// <summary>
        /// 共济账户支付
        /// </summary>
        public decimal? MutualAidAccountBalancePayment { get; set; }

        /// <summary>
        /// 医保账户余额
        /// </summary>
        public decimal? MedicalInsuranceAccountBalance { get; set; }

        /// <summary>
        /// 医保账号支付
        /// </summary>
        public decimal? MedicalInsuranceAccountPayment { get; set; }

        /// <summary>
        /// 病人负担
        /// </summary>
        public decimal? PatientPayment { get; set; }

        /// <summary>
        /// 个人支付
        /// </summary>
        public decimal? PersonalPayment { get; set; }

        /// <summary>
        /// 现金支付
        /// </summary>
        public long? PaymentMethod { get; set; }
    }