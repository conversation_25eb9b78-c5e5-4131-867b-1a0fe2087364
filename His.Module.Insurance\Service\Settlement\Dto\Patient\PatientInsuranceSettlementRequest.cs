namespace His.Module.Insurance.Service.Settlement.Dto.Patient;

/// <summary>
/// 患者医保信息
/// </summary>
/// <summary>
/// 患者医保信息 DTO
/// </summary>
public class PatientInsuranceSettlementRequest:BaseSettlementRequest
{
    /// <summary>
    /// 个人编号（社会保障号码或身份证号）
    /// 为空时，动态库驱动读卡器读身份证
    /// </summary>
    public string PersonalNo { get; set; }

    /// <summary>
    /// 险种标志（调用数据字典接口获取，代码编号：XZBZ）
    /// </summary>
    public string InsuranceTypeCode { get; set; }

    /// <summary>
    /// 姓名（必须和医保数据库一致）
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// 0: 仅获取人员基本信息, 1: 住院, 4: 门诊大病(特病), 6: 普通门诊
    /// 默认 0，其他值调用数据字典接口（代码编号：YLTCLB）
    /// </summary>
    public string MedicalCategory { get; set; }

    /// <summary>
    /// 查询日期（查询某天的参保人信息）
    /// </summary>
    public DateTime QueryDate { get; set; }


}