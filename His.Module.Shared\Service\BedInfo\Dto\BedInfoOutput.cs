﻿
namespace His.Module.Shared;

/// <summary>
/// 床位信息输出参数
/// </summary>
public class BedInfoOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 床位编号
    /// </summary>
    public string BedNo { get; set; }    
    
    /// <summary>
    /// 科室id
    /// </summary>
    public long DeptId { get; set; }    
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string DeptName { get; set; }    
    
    /// <summary>
    /// 病区id
    /// </summary>
    public long WardId { get; set; }    
    
    /// <summary>
    /// 病区名称
    /// </summary>
    public string WardName { get; set; }    
    
    /// <summary>
    /// 房间编号
    /// </summary>
    public string? RoomNo { get; set; }    
    
    /// <summary>
    /// 床位类型
    /// </summary>
    public string? BedType { get; set; }    
    
    /// <summary>
    /// 床位状态
    /// </summary>
    public string? BedStatus { get; set; }    
    
    /// <summary>
    /// 床位等级
    /// </summary>
    public string? BedLevelName { get; set; }    
    public   long? BedLevelId { get; set; }
    /// <summary>
    /// 床位等级 描述
    /// </summary>
    public string BedLevelFkDisplayName { get; set; } 
    
    /// <summary>
    /// 排序
    /// </summary>
    public int OrderNo { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }    
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 床位信息数据导入模板实体
/// </summary>
public class ExportBedInfoOutput : ImportBedInfoInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
