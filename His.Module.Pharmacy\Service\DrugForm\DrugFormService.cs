﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品剂型维护服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugFormService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugForm> _drugFormRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DrugFormService(SqlSugarRepository<DrugForm> drugFormRep, ISqlSugarClient sqlSugarClient)
    {
        _drugFormRep = drugFormRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药品剂型维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品剂型维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugFormOutput>> Page(PageDrugFormInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugFormRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.FormName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.FormName), u => u.FormName.Contains(input.FormName.Trim()))
            .WhereIF(input.Status.HasValue, u => u.Status == (int)input.Status)
            .LeftJoin<DrugBigForm>((u, bigForm) => u.BigFormId == bigForm.Id)
            .Select((u, bigForm) => new DrugFormOutput
            {
                Id = u.Id,
                FormName = u.FormName,
                FormNamePinyin = u.FormNamePinyin,
                BigFormId = u.BigFormId,
                BigFormFkDisplayName = $"{bigForm.BigFormName}",
                BigFormName = u.BigFormName,
                Status =(StatusEnum) u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品剂型维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品剂型维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugForm> Detail([FromQuery] QueryByIdDrugFormInput input)
    {
        return await _drugFormRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品剂型维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品剂型维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugFormInput input)
    {
        var entity = input.Adapt<DrugForm>();
        return await _drugFormRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品剂型维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品剂型维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugFormInput input)
    {
        var entity = input.Adapt<DrugForm>();
        await _drugFormRep.AsUpdateable(entity)
        .IgnoreColumns(u => new {
            u.FormNamePinyin,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品剂型维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品剂型维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugFormInput input)
    {
        var entity = await _drugFormRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugFormRep.FakeDeleteAsync(entity);   //假删除
        //await _drugFormRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品剂型维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品剂型维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugFormInput> input)
    {
        var exp = Expressionable.Create<DrugForm>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugFormRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugFormRep.FakeDeleteAsync(list);   //假删除
        //return await _drugFormRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置药品剂型维护状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置药品剂型维护状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetDrugFormStatus(SetDrugFormStatusInput input)
    {
        await _drugFormRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataDrugFormInput input)
    {
        var bigFormIdData = await _drugFormRep.Context.Queryable<DrugBigForm>()
            .InnerJoinIF<DrugForm>(input.FromPage, (u, r) => u.Id == r.BigFormId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.BigFormName}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "bigFormId", bigFormIdData },
        };
    }
    
    /// <summary>
    /// 导出药品剂型维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品剂型维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugFormInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugFormOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药品剂型维护导出记录");
    }
    
    /// <summary>
    /// 下载药品剂型维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品剂型维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugFormOutput>(), "药品剂型维护导入模板", (_, info) =>
        {
            if (nameof(ExportDrugFormOutput.BigFormFkDisplayName) == info.Name) return _drugFormRep.Context.Queryable<DrugBigForm>().Select(u => $"{u.BigFormName}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入药品剂型维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品剂型维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportDrugFormInput, DrugForm>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 大剂型ID
                    var bigFormIdLabelList = pageItems.Where(x => x.BigFormFkDisplayName != null).Select(x => x.BigFormFkDisplayName).Distinct().ToList();
                    if (bigFormIdLabelList.Any()) {
                        var bigFormIdLinkMap = _drugFormRep.Context.Queryable<DrugBigForm>().Where(u => bigFormIdLabelList.Contains($"{u.BigFormName}")).ToList().ToDictionary(u => $"{u.BigFormName}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.BigFormId = bigFormIdLinkMap.GetValueOrDefault(e.BigFormFkDisplayName ?? "");
                            if (e.BigFormId == null) e.Error = "大剂型ID链接失败";
                        });
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugForm>>();
                    
                    var storageable = _drugFormRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.FormName?.Length > 100, "剂型名称长度不能超过100个字符")
                        .SplitError(it => it.Item.FormNamePinyin?.Length > 100, "剂型名称拼音长度不能超过100个字符")
                        .SplitError(it => it.Item.BigFormName?.Length > 100, "大剂型名称长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
