﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品持有人表服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugHolderService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugHolder> _drugHolderRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DrugHolderService(SqlSugarRepository<DrugHolder> drugHolderRep, ISqlSugarClient sqlSugarClient)
    {
        _drugHolderRep = drugHolderRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药品持有人表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品持有人表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugHolderOutput>> Page(PageDrugHolderInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugHolderRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.HolderName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.HolderName), u => u.HolderName.Contains(input.HolderName.Trim()))
            .WhereIF(input.Status.HasValue, u => u.Status ==(int) input.Status)
            .Select<DrugHolderOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品持有人表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品持有人表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugHolder> Detail([FromQuery] QueryByIdDrugHolderInput input)
    {
        return await _drugHolderRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品持有人表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品持有人表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugHolderInput input)
    {
        var entity = input.Adapt<DrugHolder>();
        return await _drugHolderRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品持有人表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品持有人表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugHolderInput input)
    {
        var entity = input.Adapt<DrugHolder>();
        await _drugHolderRep.AsUpdateable(entity)
        .IgnoreColumns(u => new {
            u.HolderNamePinyin,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品持有人表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品持有人表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugHolderInput input)
    {
        var entity = await _drugHolderRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugHolderRep.FakeDeleteAsync(entity);   //假删除
        //await _drugHolderRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品持有人表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品持有人表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugHolderInput> input)
    {
        var exp = Expressionable.Create<DrugHolder>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugHolderRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugHolderRep.FakeDeleteAsync(list);   //假删除
        //return await _drugHolderRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置药品持有人表状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置药品持有人表状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetDrugHolderStatus(SetDrugHolderStatusInput input)
    {
        await _drugHolderRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 导出药品持有人表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品持有人表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugHolderInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugHolderOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药品持有人表导出记录");
    }
    
    /// <summary>
    /// 下载药品持有人表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品持有人表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugHolderOutput>(), "药品持有人表导入模板");
    }
    
    /// <summary>
    /// 导入药品持有人表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品持有人表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportDrugHolderInput, DrugHolder>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugHolder>>();
                    
                    var storageable = _drugHolderRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.HolderName?.Length > 100, "持有人名称长度不能超过100个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
