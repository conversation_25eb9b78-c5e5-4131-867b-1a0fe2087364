﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品库存表基础输入参数
/// </summary>
public class DrugInventoryBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 生产厂家ID
    /// </summary>
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public virtual decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 待发药数量
    /// </summary>
    public virtual decimal? PendingQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public virtual decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 零售金额
    /// </summary>
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    public virtual decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 采购金额
    /// </summary>
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public virtual string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    public virtual DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    public virtual DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 最后一次供应商ID
    /// </summary>
    public virtual long? LastSupplierId { get; set; }
    
    /// <summary>
    /// 最后一次供应商名称
    /// </summary>
    public virtual string? LastSupplierName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 药品库存表分页查询输入参数
/// </summary>
public class SendDrugInventoryInput  
{
    /// <summary>
    /// 查询关键字
    /// </summary>
    public string? Keyword { get; set; }
    /// <summary>
    /// 前多少行 默认20
    /// </summary>
    public int? Limit { get; set; } 
    
    /// <summary>
    /// 库房ID
    /// </summary>
    public List<long?> StorageId { get; set; } 
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public string? DrugType { get; set; }
     
}

/// <summary>
/// 药品库存表分页查询输入参数
/// </summary>
public class PageDrugInventoryInput : BasePageInput
{
    
  

    /// <summary>
    /// 零库存 0 不包含0库存 1只查零库存 2 包含0库存
    /// </summary>
    public int? ZeroQuantity { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 生产厂家ID
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 有效期范围
    /// </summary>
     public DateTime?[] ExpirationDateRange { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 最后一次供应商ID
    /// </summary>
    public long? LastSupplierId { get; set; }
    
    /// <summary>
    /// 最后一次供应商名称
    /// </summary>
    public string? LastSupplierName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品库存表增加输入参数
/// </summary>
public class AddDrugInventoryInput
{
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "药房名称字符长度不能超过100")]
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 生产厂家ID
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "生产厂家名称字符长度不能超过100")]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 待发药数量
    /// </summary>
    public decimal? PendingQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 零售金额
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 采购金额
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 最后一次供应商ID
    /// </summary>
    public long? LastSupplierId { get; set; }
    
    /// <summary>
    /// 最后一次供应商名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "最后一次供应商名称字符长度不能超过100")]
    public string? LastSupplierName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
}

/// <summary>
/// 药品库存表删除输入参数
/// </summary>
public class DeleteDrugInventoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品库存表更新输入参数
/// </summary>
public class UpdateDrugInventoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>    
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>    
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>    
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药房名称字符长度不能超过100")]
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 生产厂家ID
    /// </summary>    
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "生产厂家名称字符长度不能超过100")]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>    
    public decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 待发药数量
    /// </summary>    
    public decimal? PendingQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>    
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 零售金额
    /// </summary>    
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>    
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 采购金额
    /// </summary>    
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>    
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>    
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 最后一次供应商ID
    /// </summary>    
    public long? LastSupplierId { get; set; }
    
    /// <summary>
    /// 最后一次供应商名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "最后一次供应商名称字符长度不能超过100")]
    public string? LastSupplierName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    public int? Status { get; set; }
    
}

/// <summary>
/// 药品库存表主键查询输入参数
/// </summary>
public class QueryByIdDrugInventoryInput : DeleteDrugInventoryInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataDrugInventoryInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 药品库存表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugInventoryInput : BaseImportInput
{
    /// <summary>
    /// 药品ID
    /// </summary>
    [ImporterHeader(Name = "药品ID")]
    [ExporterHeader("药品ID", Format = "", Width = 25, IsBold = true)]
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [ImporterHeader(Name = "药品编码")]
    [ExporterHeader("药品编码", Format = "", Width = 25, IsBold = true)]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [ImporterHeader(Name = "药品名称")]
    [ExporterHeader("药品名称", Format = "", Width = 25, IsBold = true)]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类型 文本
    /// </summary>
    [Dict("DrugType")]
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string DrugTypeDictLabel { get; set; }
    
    /// <summary>
    /// 药房ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房ID 文本
    /// </summary>
    [ImporterHeader(Name = "药房ID")]
    [ExporterHeader("药房ID", Format = "", Width = 25, IsBold = true)]
    public string StorageFkDisplayName { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>
    [ImporterHeader(Name = "药房编码")]
    [ExporterHeader("药房编码", Format = "", Width = 25, IsBold = true)]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    [ImporterHeader(Name = "药房名称")]
    [ExporterHeader("药房名称", Format = "", Width = 25, IsBold = true)]
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [ImporterHeader(Name = "规格")]
    [ExporterHeader("规格", Format = "", Width = 25, IsBold = true)]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 生产厂家ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家ID 文本
    /// </summary>
    [ImporterHeader(Name = "生产厂家ID")]
    [ExporterHeader("生产厂家ID", Format = "", Width = 25, IsBold = true)]
    public string ManufacturerFkDisplayName { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    [ImporterHeader(Name = "生产厂家名称")]
    [ExporterHeader("生产厂家名称", Format = "", Width = 25, IsBold = true)]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [ImporterHeader(Name = "数量")]
    [ExporterHeader("数量", Format = "", Width = 25, IsBold = true)]
    public decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [ImporterHeader(Name = "单位")]
    [ExporterHeader("单位", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 待发药数量
    /// </summary>
    [ImporterHeader(Name = "待发药数量")]
    [ExporterHeader("待发药数量", Format = "", Width = 25, IsBold = true)]
    public decimal? PendingQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [ImporterHeader(Name = "零售价")]
    [ExporterHeader("零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 零售金额
    /// </summary>
    [ImporterHeader(Name = "零售金额")]
    [ExporterHeader("零售金额", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    [ImporterHeader(Name = "进价")]
    [ExporterHeader("进价", Format = "", Width = 25, IsBold = true)]
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 采购金额
    /// </summary>
    [ImporterHeader(Name = "采购金额")]
    [ExporterHeader("采购金额", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [ImporterHeader(Name = "批号")]
    [ExporterHeader("批号", Format = "", Width = 25, IsBold = true)]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    [ImporterHeader(Name = "生产日期")]
    [ExporterHeader("生产日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    [ImporterHeader(Name = "有效期")]
    [ExporterHeader("有效期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [ImporterHeader(Name = "批准文号")]
    [ExporterHeader("批准文号", Format = "", Width = 25, IsBold = true)]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [ImporterHeader(Name = "国家医保编码")]
    [ExporterHeader("国家医保编码", Format = "", Width = 25, IsBold = true)]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 最后一次供应商ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? LastSupplierId { get; set; }
    
    /// <summary>
    /// 最后一次供应商ID 文本
    /// </summary>
    [ImporterHeader(Name = "最后一次供应商ID")]
    [ExporterHeader("最后一次供应商ID", Format = "", Width = 25, IsBold = true)]
    public string LastSupplierFkDisplayName { get; set; }
    
    /// <summary>
    /// 最后一次供应商名称
    /// </summary>
    [ImporterHeader(Name = "最后一次供应商名称")]
    [ExporterHeader("最后一次供应商名称", Format = "", Width = 25, IsBold = true)]
    public string? LastSupplierName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
}
