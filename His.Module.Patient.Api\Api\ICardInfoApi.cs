﻿using His.Module.Patient.Api.Api.Dto;

namespace His.Module.Patient.Api.Api;

public interface ICardInfoApi
{
    /// <summary>
    /// 获取就诊卡信息
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    Task<CardInfoDto> Detail(long? Id);

    /// <summary>
    /// 卡缴费
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CardPay(CardPayInput input);
}