﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品盘点明细表服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class StorageTakingDetailService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<StorageTakingDetail> _storageTakingDetailRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public StorageTakingDetailService(SqlSugarRepository<StorageTakingDetail> storageTakingDetailRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _storageTakingDetailRep = storageTakingDetailRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询药品盘点明细表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品盘点明细表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<StorageTakingDetailOutput>> Page(PageStorageTakingDetailInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _storageTakingDetailRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) || u.Spec.Contains(input.Keyword) || u.Unit.Contains(input.Keyword) || u.BatchNo.Contains(input.Keyword) || u.ApprovalNumber.Contains(input.Keyword) || u.MedicineCode.Contains(input.Keyword) || u.ManufacturerName.Contains(input.Keyword) || u.DrugType.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Spec), u => u.Spec.Contains(input.Spec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BatchNo), u => u.BatchNo.Contains(input.BatchNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApprovalNumber), u => u.ApprovalNumber.Contains(input.ApprovalNumber.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicineCode), u => u.MedicineCode.Contains(input.MedicineCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ManufacturerName), u => u.ManufacturerName.Contains(input.ManufacturerName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .Where( u => u.TakingRecordId == input.TakingRecordId)
            .WhereIF(input.DrugId != null, u => u.DrugId == input.DrugId)
            .WhereIF(input.CurrentQuantity != null, u => u.CurrentQuantity == input.CurrentQuantity)
            .WhereIF(input.TakingQuantity != null, u => u.TakingQuantity == input.TakingQuantity)
            .WhereIF(input.ProductionDateRange?.Length == 2, u => u.ProductionDate >= input.ProductionDateRange[0] && u.ProductionDate <= input.ProductionDateRange[1])
            .WhereIF(input.ExpirationDateRange?.Length == 2, u => u.ExpirationDate >= input.ExpirationDateRange[0] && u.ExpirationDate <= input.ExpirationDateRange[1])
            .WhereIF(input.ManufacturerId != null, u => u.ManufacturerId == input.ManufacturerId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<EnterpriseDictionary>((u, manufacturer) => u.ManufacturerId == manufacturer.Id)
            .Select((u, manufacturer) => new StorageTakingDetailOutput
            {
                Id = u.Id,
                TakingRecordId = u.TakingRecordId,
                DrugId = u.DrugId,
                DrugCode = u.DrugCode,
                DrugName = u.DrugName,
                Spec = u.Spec,
                Unit = u.Unit,
                CurrentQuantity = u.CurrentQuantity,
                CurrentSalePrice = u.CurrentSalePrice,
                TotalCurrentSalePrice = u.TotalCurrentSalePrice,
                TakingQuantity = u.TakingQuantity,
                TakingSalePrice = u.TakingSalePrice,
                TotalTakingSalePrice = u.TotalTakingSalePrice,
                TakingDifference = u.TakingDifference,
                TakingDifferenceSalePrice = u.TakingDifferenceSalePrice,
                BatchNo = u.BatchNo,
                ProductionDate = u.ProductionDate,
                ExpirationDate = u.ExpirationDate,
                ApprovalNumber = u.ApprovalNumber,
                MedicineCode = u.MedicineCode,
                ManufacturerId = u.ManufacturerId,
                ManufacturerFkDisplayName = $"{manufacturer.EnterpriseName}",
                ManufacturerName = u.ManufacturerName,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
                DrugType = u.DrugType,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品盘点明细表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品盘点明细表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<StorageTakingDetail> Detail([FromQuery] QueryByIdStorageTakingDetailInput input)
    {
        return await _storageTakingDetailRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品盘点明细表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品盘点明细表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddStorageTakingDetailInput input)
    {
        var entity = input.Adapt<StorageTakingDetail>();
        return await _storageTakingDetailRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品盘点明细表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品盘点明细表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateStorageTakingDetailInput input)
    {
        var entity = input.Adapt<StorageTakingDetail>();
        await _storageTakingDetailRep.AsUpdateable(entity)
        .IgnoreColumns(u => new {
            u.TakingRecordId,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品盘点明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品盘点明细表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteStorageTakingDetailInput input)
    {
        var entity = await _storageTakingDetailRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _storageTakingDetailRep.FakeDeleteAsync(entity);   //假删除
        //await _storageTakingDetailRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品盘点明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品盘点明细表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteStorageTakingDetailInput> input)
    {
        var exp = Expressionable.Create<StorageTakingDetail>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _storageTakingDetailRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _storageTakingDetailRep.FakeDeleteAsync(list);   //假删除
        //return await _storageTakingDetailRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataStorageTakingDetailInput input)
    {
        var manufacturerIdData = await _storageTakingDetailRep.Context.Queryable<EnterpriseDictionary>()
            .InnerJoinIF<StorageTakingDetail>(input.FromPage, (u, r) => u.Id == r.ManufacturerId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "manufacturerId", manufacturerIdData },
        };
    }
    
    /// <summary>
    /// 导出药品盘点明细表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品盘点明细表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageStorageTakingDetailInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportStorageTakingDetailOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType);
        });
        return ExcelHelper.ExportTemplate(list, "药品盘点明细表导出记录");
    }
    
    /// <summary>
    /// 下载药品盘点明细表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品盘点明细表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportStorageTakingDetailOutput>(), "药品盘点明细表导入模板", (_, info) =>
        {
            if (nameof(ExportStorageTakingDetailOutput.ManufacturerFkDisplayName) == info.Name) return _storageTakingDetailRep.Context.Queryable<EnterpriseDictionary>().Select(u => $"{u.EnterpriseName}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入药品盘点明细表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品盘点明细表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportStorageTakingDetailInput, StorageTakingDetail>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 生产厂商
                    var manufacturerIdLabelList = pageItems.Where(x => x.ManufacturerFkDisplayName != null).Select(x => x.ManufacturerFkDisplayName).Distinct().ToList();
                    if (manufacturerIdLabelList.Any()) {
                        var manufacturerIdLinkMap = _storageTakingDetailRep.Context.Queryable<EnterpriseDictionary>().Where(u => manufacturerIdLabelList.Contains($"{u.EnterpriseName}")).ToList().ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.ManufacturerId = manufacturerIdLinkMap.GetValueOrDefault(e.ManufacturerFkDisplayName ?? "");
                            if (e.ManufacturerId == null) e.Error = "生产厂商链接失败";
                        });
                    }
                    
                    // 映射字典值
                    foreach(var item in pageItems) {
                        if (string.IsNullOrWhiteSpace(item.DrugTypeDictLabel)) continue;
                        item.DrugType = drugTypeDictMap.GetValueOrDefault(item.DrugTypeDictLabel);
                        if (item.DrugType == null) item.Error = "药品类型字典映射失败";
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<StorageTakingDetail>>();
                    
                    var storageable = _storageTakingDetailRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.DrugCode?.Length > 100, "药品编码长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugName?.Length > 100, "药品名称长度不能超过100个字符")
                        .SplitError(it => it.Item.Spec?.Length > 100, "规格长度不能超过100个字符")
                        .SplitError(it => it.Item.Unit?.Length > 100, "单位长度不能超过100个字符")
                        .SplitError(it => it.Item.BatchNo?.Length > 100, "批号长度不能超过100个字符")
                        .SplitError(it => it.Item.ApprovalNumber?.Length > 100, "批准文号长度不能超过100个字符")
                        .SplitError(it => it.Item.MedicineCode?.Length > 100, "国家医保编码长度不能超过100个字符")
                        .SplitError(it => it.Item.ManufacturerName?.Length > 100, "生产厂商名称长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugType?.Length > 20, "药品类型长度不能超过20个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
