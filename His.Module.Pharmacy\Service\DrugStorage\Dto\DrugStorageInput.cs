﻿using His.Module.Shared.Api.Enum;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品库房维护基础输入参数
/// </summary>
public class DrugStorageBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 库房编码
    /// </summary>
    public virtual string? StorageCode { get; set; }

    /// <summary>
    /// 库房名称
    /// </summary>
    public virtual string? StorageName { get; set; }

    /// <summary>
    /// 存储药品类型
    /// </summary>
    public virtual String[]? StorageDrugType { get; set; }

    /// <summary>
    /// 库存金额上限
    /// </summary>
    public virtual decimal? StorageAmountLimit { get; set; }

    /// <summary>
    /// 父级库房ID
    /// </summary>
    public virtual long? ParentId { get; set; }

    /// <summary>
    /// 父级库房编码
    /// </summary>
    public virtual string? ParentCode { get; set; }

    /// <summary>
    /// 服务对象（门诊，急诊、住院）
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    public virtual MedServiceCategoryEnum? ServiceObject { get; set; }

    /// <summary>
    /// 采购入库审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? PurchaseAudit { get; set; }

    /// <summary>
    /// 采购退货审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? PurchaseReturnAudit { get; set; }

    /// <summary>
    /// 药店申领审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? ApplyAudit { get; set; }

    /// <summary>
    /// 药店退药审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? ApplyReturnAudit { get; set; }

    /// <summary>
    /// 出库审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? OutAudit { get; set; }

    /// <summary>
    /// 特殊处理审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? SpecialAudit { get; set; }

    /// <summary>
    /// 按批号盘点
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? BatchCheck { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public virtual StatusEnum? Status { get; set; }
}

/// <summary>
/// 药品库房维护分页查询输入参数
/// </summary>
public class PageDrugStorageInput : BasePageInput
{
    /// <summary>
    /// 库房名称
    /// </summary>
    public string? StorageName { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
    
    /// <summary>
    /// 服务对象
    /// </summary>
    public int? ServiceObject { get; set; }
}

/// <summary>
/// 药品库房维护增加输入参数
/// </summary>
public class AddDrugStorageInput
{
    /// <summary>
    /// 库房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "库房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }

    /// <summary>
    /// 库房名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "库房名称字符长度不能超过100")]
    public string? StorageName { get; set; }

    /// <summary>
    /// 存储药品类型
    /// </summary>
    public List<String>? StorageDrugType { get; set; }

    /// <summary>
    /// 库存金额上限
    /// </summary>
    public decimal? StorageAmountLimit { get; set; }

    /// <summary>
    /// 父级库房ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 父级库房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "父级库房编码字符长度不能超过100")]
    public string? ParentCode { get; set; }

    /// <summary>
    /// 服务对象（门诊，急诊、住院）
    /// </summary>
 
    public List<int>?  ServiceObject { get; set; }

    /// <summary>
    /// 采购入库审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? PurchaseAudit { get; set; }

    /// <summary>
    /// 采购退货审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? PurchaseReturnAudit { get; set; }

    /// <summary>
    /// 药店申领审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? ApplyAudit { get; set; }

    /// <summary>
    /// 药店退药审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? ApplyReturnAudit { get; set; }

    /// <summary>
    /// 出库审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? OutAudit { get; set; }

    /// <summary>
    /// 特殊处理审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? SpecialAudit { get; set; }

    /// <summary>
    /// 按批号盘点
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? BatchCheck { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }
}

/// <summary>
/// 药品库房维护删除输入参数
/// </summary>
public class DeleteDrugStorageInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 药品库房维护更新输入参数
/// </summary>
public class UpdateDrugStorageInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 库房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "库房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }

    /// <summary>
    /// 库房名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "库房名称字符长度不能超过100")]
    public string? StorageName { get; set; }

    /// <summary>
    /// 存储药品类型
    /// </summary>
    public List<String> StorageDrugType { get; set; }

    /// <summary>
    /// 库存金额上限
    /// </summary>
    public decimal? StorageAmountLimit { get; set; }

    /// <summary>
    /// 父级库房ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 父级库房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "父级库房编码字符长度不能超过100")]
    public string? ParentCode { get; set; }

    /// <summary>
    /// 服务对象（门诊，急诊、住院）
    /// </summary>
    public List<int>?  ServiceObject { get; set; }

    /// <summary>
    /// 采购入库审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? PurchaseAudit { get; set; }

    /// <summary>
    /// 采购退货审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? PurchaseReturnAudit { get; set; }

    /// <summary>
    /// 药店申领审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? ApplyAudit { get; set; }

    /// <summary>
    /// 药店退药审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? ApplyReturnAudit { get; set; }

    /// <summary>
    /// 出库审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? OutAudit { get; set; }

    /// <summary>
    /// 特殊处理审核
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? SpecialAudit { get; set; }

    /// <summary>
    /// 按批号盘点
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? BatchCheck { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }
}

/// <summary>
/// 药品库房维护主键查询输入参数
/// </summary>
public class QueryByIdDrugStorageInput : DeleteDrugStorageInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataDrugStorageInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetDrugStorageStatusInput : BaseStatusInput
{
}

/// <summary>
/// 药品库房维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugStorageInput : BaseImportInput
{
    /// <summary>
    /// 库房编码
    /// </summary>
    [ImporterHeader(Name = "库房编码")]
    [ExporterHeader("库房编码", Format = "", Width = 25, IsBold = true)]
    public string? StorageCode { get; set; }

    /// <summary>
    /// 库房名称
    /// </summary>
    [ImporterHeader(Name = "库房名称")]
    [ExporterHeader("库房名称", Format = "", Width = 25, IsBold = true)]
    public string? StorageName { get; set; }

    /// <summary>
    /// 存储药品类型
    /// </summary>
    [ImporterHeader(Name = "存储药品类型")]
    [ExporterHeader("存储药品类型", Format = "", Width = 25, IsBold = true)]
    public object? StorageDrugType { get; set; }

    /// <summary>
    /// 库存金额上限
    /// </summary>
    [ImporterHeader(Name = "库存金额上限")]
    [ExporterHeader("库存金额上限", Format = "", Width = 25, IsBold = true)]
    public decimal? StorageAmountLimit { get; set; }

    /// <summary>
    /// 父级库房ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ParentId { get; set; }

    /// <summary>
    /// 父级库房ID 文本
    /// </summary>
    [ImporterHeader(Name = "父级库房ID")]
    [ExporterHeader("父级库房ID", Format = "", Width = 25, IsBold = true)]
    public string ParentFkDisplayName { get; set; }

    /// <summary>
    /// 父级库房编码
    /// </summary>
    [ImporterHeader(Name = "父级库房编码")]
    [ExporterHeader("父级库房编码", Format = "", Width = 25, IsBold = true)]
    public string? ParentCode { get; set; }

    /// <summary>
    /// 服务对象（门诊，急诊、住院）
    /// </summary>
    [ImporterHeader(Name = "服务对象（门诊，急诊、住院）")]
    [ExporterHeader("服务对象（门诊，急诊、住院）", Format = "", Width = 25, IsBold = true)]
    public MedServiceCategoryEnum? ServiceObject { get; set; }

    /// <summary>
    /// 采购入库审核
    /// </summary>
    [ImporterHeader(Name = "采购入库审核")]
    [ExporterHeader("采购入库审核", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? PurchaseAudit { get; set; }

    /// <summary>
    /// 采购退货审核
    /// </summary>
    [ImporterHeader(Name = "采购退货审核")]
    [ExporterHeader("采购退货审核", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? PurchaseReturnAudit { get; set; }

    /// <summary>
    /// 药店申领审核
    /// </summary>
    [ImporterHeader(Name = "药店申领审核")]
    [ExporterHeader("药店申领审核", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? ApplyAudit { get; set; }

    /// <summary>
    /// 药店退药审核
    /// </summary>
    [ImporterHeader(Name = "药店退药审核")]
    [ExporterHeader("药店退药审核", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? ApplyReturnAudit { get; set; }

    /// <summary>
    /// 出库审核
    /// </summary>
    [ImporterHeader(Name = "出库审核")]
    [ExporterHeader("出库审核", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? OutAudit { get; set; }

    /// <summary>
    /// 特殊处理审核
    /// </summary>
    [ImporterHeader(Name = "特殊处理审核")]
    [ExporterHeader("特殊处理审核", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? SpecialAudit { get; set; }

    /// <summary>
    /// 按批号盘点
    /// </summary>
    [ImporterHeader(Name = "按批号盘点")]
    [ExporterHeader("按批号盘点", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? BatchCheck { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
}