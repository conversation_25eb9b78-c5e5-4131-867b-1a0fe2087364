﻿using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 医疗组工作记录表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("medical_team_work_log", "医疗组工作记录表")]
public class MedicalTeamWorkLog : EntityTenant
{
    /// <summary>
    /// 医疗组患者id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "team_patient_id", ColumnDescription = "医疗组患者id")]
    public virtual long TeamPatientId { get; set; }
    
    /// <summary>
    /// 医疗组id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "team_id", ColumnDescription = "医疗组id")]
    public virtual long TeamId { get; set; }
    
    /// <summary>
    /// 医疗组名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "team_name", ColumnDescription = "医疗组名称", Length = 255)]
    public virtual string TeamName { get; set; }
    
    /// <summary>
    /// 记录类型(1:交接班,2:病例讨论,3:教学记录等)
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "log_type", ColumnDescription = "记录类型(1:交接班,2:病例讨论,3:教学记录等)", Length = 255)]
    public virtual string LogType { get; set; }
    
    /// <summary>
    /// 记录时间
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "log_time", ColumnDescription = "记录时间")]
    public virtual DateTime LogTime { get; set; }
    
    /// <summary>
    /// 记录内容
    /// </summary>
    [SugarColumn(ColumnName = "content", ColumnDescription = "记录内容", Length = 0)]
    public virtual string? Content { get; set; }
    
    /// <summary>
    /// 参与人员
    /// </summary>
    [SugarColumn(ColumnName = "staff", ColumnDescription = "参与人员")]
    public virtual object? Staff { get; set; }
    
    /// <summary>
    /// 关联患者id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "关联患者id")]
    public virtual long PatientId { get; set; }
    
    /// <summary>
    /// 关联患者名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "关联患者名称", Length = 255)]
    public virtual string PatientName { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "inpatient_no", ColumnDescription = "住院号", Length = 255)]
    public virtual string InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "inpatient_serial_no", ColumnDescription = "住院流水号", Length = 255)]
    public virtual string InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
     
    
}
