﻿namespace His.Module.Shared.Service;

/// <summary>
/// 检查类别基础输入参数
/// </summary>
public class CheckCategoryBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public virtual string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 收费类别
    /// </summary>
    [Required(ErrorMessage = "收费类别不能为空")]
    public virtual long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public virtual StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
}

/// <summary>
/// 检查类别分页查询输入参数
/// </summary>
public class PageCheckCategoryInput : BasePageInput
{
    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 检查类别增加输入参数
/// </summary>
public class AddCheckCategoryInput
{
    /// <summary>
    /// 编码
    /// </summary>
    [MaxLength(32, ErrorMessage = "编码字符长度不能超过32")]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [MaxLength(20, ErrorMessage = "拼音码字符长度不能超过20")]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [MaxLength(20, ErrorMessage = "五笔码字符长度不能超过20")]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 收费类别
    /// </summary>
    [Required(ErrorMessage = "收费类别不能为空")]
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 检查类别删除输入参数
/// </summary>
public class DeleteCheckCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 检查类别更新输入参数
/// </summary>
public class UpdateCheckCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    [MaxLength(32, ErrorMessage = "编码字符长度不能超过32")]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [MaxLength(20, ErrorMessage = "拼音码字符长度不能超过20")]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [MaxLength(20, ErrorMessage = "五笔码字符长度不能超过20")]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 收费类别
    /// </summary>
    [Required(ErrorMessage = "收费类别不能为空")]
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 检查类别主键查询输入参数
/// </summary>
public class QueryByIdCheckCategoryInput : DeleteCheckCategoryInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataCheckCategoryInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetCheckCategoryStatusInput : BaseStatusInput
{
}

/// <summary>
/// 检查类别数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportCheckCategoryInput : BaseImportInput
{
    /// <summary>
    /// 编码
    /// </summary>
    [ImporterHeader(Name = "编码")]
    [ExporterHeader("编码", Format = "", Width = 25, IsBold = true)]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [ImporterHeader(Name = "*名称")]
    [ExporterHeader("*名称", Format = "", Width = 25, IsBold = true)]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [ImporterHeader(Name = "拼音码")]
    [ExporterHeader("拼音码", Format = "", Width = 25, IsBold = true)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [ImporterHeader(Name = "五笔码")]
    [ExporterHeader("五笔码", Format = "", Width = 25, IsBold = true)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 收费类别 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 收费类别 文本
    /// </summary>
    [ImporterHeader(Name = "*收费类别")]
    [ExporterHeader("*收费类别", Format = "", Width = 25, IsBold = true)]
    public string ChargeCategoryFkDisplayName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
}