﻿using Admin.NET.Core.Service;
using His.Module.Inpatient.Api.Api;
using Microsoft.AspNetCore.Http;

namespace His.Module.Inpatient;

/// <summary>
/// 医疗组维护服务 🧩
/// </summary>
[ApiDescriptionSettings(InpatientConst.GroupName, Order = 100)]
public class MedicalTeamService(SqlSugarRepository<MedicalTeam> medicalTeamRep,
    SqlSugarRepository<MedicalTeamMember> medicalTeamMemberRep,
    SqlSugarRepository<MedicalTeamPatient> medicalTeamPatientRep,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient, IMedicalTeamApi
{
    /// <summary>
    /// 分页查询医疗组维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询医疗组维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<MedicalTeamOutput>> Page(PageMedicalTeamInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = medicalTeamRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.TeamName.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.TeamType.Contains(input.Keyword) || u.TeamLeaderName.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TeamName), u => u.TeamName.Contains(input.TeamName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeptName), u => u.DeptName.Contains(input.DeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TeamType), u => u.TeamType.Contains(input.TeamType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TeamLeaderName), u => u.TeamLeaderName.Contains(input.TeamLeaderName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.TeamLeaderId != null, u => u.TeamLeaderId == input.TeamLeaderId)
            .WhereIF(input.EstablishDateRange?.Length == 2, u => u.EstablishDate >= input.EstablishDateRange[0] && u.EstablishDate <= input.EstablishDateRange[1])
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<MedicalTeamOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取医疗组维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取医疗组维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<MedicalTeam> Detail([FromQuery] QueryByIdMedicalTeamInput input)
    {
        return await medicalTeamRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加医疗组维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加医疗组维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddMedicalTeamInput input)
    {
        var entity = input.Adapt<MedicalTeam>();
        entity.TeamType = "诊疗组";
        entity.Status = 1;
        return await medicalTeamRep.InsertAsync(entity) ? entity.Id : 0;
    }
 
  
    /// <summary>
    /// 更新医疗组维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新医疗组维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateMedicalTeamInput input)
    {
        var entity = input.Adapt<MedicalTeam>();
        await medicalTeamRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除医疗组维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除医疗组维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteMedicalTeamInput input)
    {
 
        var entity = await medicalTeamRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await medicalTeamRep.FakeDeleteAsync(entity);   //假删除
        //await _medicalTeamRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除医疗组维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除医疗组维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteMedicalTeamInput> input)
    {
        var exp = Expressionable.Create<MedicalTeam>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await medicalTeamRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await medicalTeamRep.FakeDeleteAsync(list);   //假删除
        //return await _medicalTeamRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出医疗组维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出医疗组维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageMedicalTeamInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportMedicalTeamOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "医疗组维护导出记录");
    }
    
    /// <summary>
    /// 下载医疗组维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载医疗组维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportMedicalTeamOutput>(), "医疗组维护导入模板");
    }
    
    /// <summary>
    /// 导入医疗组维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入医疗组维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportMedicalTeamInput, MedicalTeam>(file, (list, markerErrorAction) =>
            {
                sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.DeptId == null){
                            x.Error = "所属科室ID不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<MedicalTeam>>();
                    
                    var storageable = medicalTeamRep.Context.Storageable(rows)
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.TeamName), "医疗组名称不能为空")
                        .SplitError(it => it.Item.TeamName?.Length > 100, "医疗组名称长度不能超过100个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.DeptName), "不能为空")
                        .SplitError(it => it.Item.DeptName?.Length > 100, "长度不能超过100个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.TeamType), "医疗组类型 字典医疗组不能为空")
                        .SplitError(it => it.Item.TeamType?.Length > 100, "医疗组类型 字典医疗组长度不能超过100个字符")
                        .SplitError(it => it.Item.TeamLeaderName?.Length > 20, "组长长度不能超过20个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }

    public Task<bool> AddPatient(AddMedicalTeamPatientInput dto)
    {
      // await medicalTeamPatientRep.AsQueryable()
      return Task.FromResult( true);
    }
}
