﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 门诊发药记录表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("outpatient_drug_send_record", "门诊发药记录表")]
public class OutpatientDrugSendRecord : EntityTenant
{
    /// <summary>
    /// 发药单号
    /// </summary>
    [SugarColumn(ColumnName = "send_no", ColumnDescription = "发药单号", Length = 100)]
    public virtual string? SendNo { get; set; }
    
    /// <summary>
    /// 发药人ID
    /// </summary>
    [SugarColumn(ColumnName = "send_user_id", ColumnDescription = "发药人ID")]
    public virtual long? SendUserId { get; set; }
    
    /// <summary>
    /// 发药人名称
    /// </summary>
    [SugarColumn(ColumnName = "send_user_name", ColumnDescription = "发药人名称", Length = 100)]
    public virtual string? SendUserName { get; set; }
    
    /// <summary>
    /// 发药时间
    /// </summary>
    [SugarColumn(ColumnName = "send_time", ColumnDescription = "发药时间")]
    public virtual DateTime? SendTime { get; set; }
    
    /// <summary>
    /// 审核人ID
    /// </summary>
    [SugarColumn(ColumnName = "audit_user_id", ColumnDescription = "审核人ID")]
    public virtual long? AuditUserId { get; set; }
    
    /// <summary>
    /// 审核人名称
    /// </summary>
    [SugarColumn(ColumnName = "audit_user_name", ColumnDescription = "审核人名称", Length = 100)]
    public virtual string? AuditUserName { get; set; }
    
    /// <summary>
    /// 审核时间
    /// </summary>
    [SugarColumn(ColumnName = "audit_time", ColumnDescription = "审核时间")]
    public virtual DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 调配人员ID
    /// </summary>
    [SugarColumn(ColumnName = "pick_user_id", ColumnDescription = "调配人员ID")]
    public virtual long? PickUserId { get; set; }
    
    /// <summary>
    /// 调配人员名称
    /// </summary>
    [SugarColumn(ColumnName = "pick_user_name", ColumnDescription = "调配人员名称", Length = 100)]
    public virtual string? PickUserName { get; set; }
    
    /// <summary>
    /// 调配时间
    /// </summary>
    [SugarColumn(ColumnName = "pick_time", ColumnDescription = "调配时间")]
    public virtual DateTime? PickTime { get; set; }
    
    /// <summary>
    /// 核对人员ID
    /// </summary>
    [SugarColumn(ColumnName = "check_user_id", ColumnDescription = "核对人员ID")]
    public virtual long? CheckUserId { get; set; }
    
    /// <summary>
    /// 核对人员名称
    /// </summary>
    [SugarColumn(ColumnName = "check_user_name", ColumnDescription = "核对人员名称", Length = 100)]
    public virtual string? CheckUserName { get; set; }
    
    /// <summary>
    /// 核对时间
    /// </summary>
    [SugarColumn(ColumnName = "check_time", ColumnDescription = "核对时间")]
    public virtual DateTime? CheckTime { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "药房ID")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>
    [SugarColumn(ColumnName = "storage_code", ColumnDescription = "药房编码", Length = 100)]
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者名称", Length = 100)]
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号ID
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "挂号ID")]
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 100)]
    public virtual string? VisitNo { get; set; }
    /// <summary>
    /// 卡号
    /// </summary>
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "卡号", Length = 100)]
    public virtual string? CardNo { get; set; }
    /// <summary>
    /// 门诊
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊", Length = 100)]
    public virtual string? OutpatientNo { get; set; }
    
 
 
    
    /// <summary>
    /// 处方ID
    /// </summary>
    [SugarColumn(ColumnName = "prescription_id", ColumnDescription = "处方ID")]
    public virtual long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    [SugarColumn(ColumnName = "prescription_detail_id", ColumnDescription = "处方ID")]
    public virtual long? PrescriptionDetailId { get; set; }
    
    /// <summary>
    /// 处方号
    /// </summary>
    [SugarColumn(ColumnName = "prescription_no", ColumnDescription = "处方号", Length = 100)]
    public virtual string? PrescriptionNo { get; set; }
    
    /// <summary>
    /// 处方时间
    /// </summary>
    [SugarColumn(ColumnName = "prescription_time", ColumnDescription = "处方时间")]
    public virtual DateTime? PrescriptionTime { get; set; }
    
    /// <summary>
    /// 处方类型
    /// </summary>
    [SugarColumn(ColumnName = "prescription_type", ColumnDescription = "处方类型", Length = 100)]
    public virtual string? PrescriptionType { get; set; }
    
    /// <summary>
    /// 库存ID
    /// </summary>
    [SugarColumn(ColumnName = "inventory_id", ColumnDescription = "库存ID")]
    public virtual long? InventoryId { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室ID")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "科室名称", Length = 100)]
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "医生ID")]
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生名称
    /// </summary>
    [SugarColumn(ColumnName = "doctor_name", ColumnDescription = "医生名称", Length = 100)]
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    [SugarColumn(ColumnName = "invoice_number", ColumnDescription = "发票号", Length = 100)]
    public virtual string? InvoiceNumber { get; set; }
    
    /// <summary>
    /// 收费人员ID
    /// </summary>
    [SugarColumn(ColumnName = "charge_staff_id", ColumnDescription = "收费人员ID")]
    public virtual long? ChargeStaffId { get; set; }
    
    /// <summary>
    /// 收费ID
    /// </summary>
    [SugarColumn(ColumnName = "charge_id", ColumnDescription = "收费ID")]
    public virtual long? ChargeId { get; set; }
    
    /// <summary>
    /// 收费时间
    /// </summary>
    [SugarColumn(ColumnName = "charge_time", ColumnDescription = "收费时间")]
    public virtual DateTime? ChargeTime { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品ID")]
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 100)]
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类型", Length = 100)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "药品规格", Length = 100)]
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "药品单位", Length = 100)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 发药数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "发药数量")]
    public virtual decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单次剂量
    /// </summary>
    [SugarColumn(ColumnName = "single_dose", ColumnDescription = "单次剂量", Length = 16, DecimalDigits=4)]
    public virtual decimal? SingleDose { get; set; }
    
    /// <summary>
    /// 单次剂量单位
    /// </summary>
    [SugarColumn(ColumnName = "single_dose_unit", ColumnDescription = "单次剂量单位", Length = 16)]
    public virtual string? SingleDoseUnit { get; set; }
    
    /// <summary>
    /// 用药途径ID
    /// </summary>
    [SugarColumn(ColumnName = "medication_routes_id", ColumnDescription = "用药途径ID")]
    public virtual long? MedicationRoutesId { get; set; }
    
    /// <summary>
    /// 用药频次ID
    /// </summary>
    [SugarColumn(ColumnName = "frequency_id", ColumnDescription = "用药频次ID")]
    public virtual long? FrequencyId { get; set; }
    
    /// <summary>
    /// 用药途径
    /// </summary>
    [SugarColumn(ColumnName = "medication_routes_name", ColumnDescription = "用药途径")]
    public virtual string? MedicationRoutesName { get; set; }    
    
    /// <summary>
    /// 用药频次
    /// </summary>
    [SugarColumn(ColumnName = "frequency_name", ColumnDescription = "用药频次")]
    public virtual string? FrequencyName { get; set; }    
    
    /// <summary>
    /// 用药天数
    /// </summary>
    [SugarColumn(ColumnName = "medication_days", ColumnDescription = "用药天数")]
    public virtual int? MedicationDays { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "零售价", Length = 16, DecimalDigits=4)]
    public virtual decimal? Price { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "总零售价", Length = 16, DecimalDigits=4)]
    public virtual decimal? Amount { get; set; }
    
    /// <summary>
    /// 草药付数
    /// </summary>
    [SugarColumn(ColumnName = "herbs_quantity", ColumnDescription = "草药付数")]
    public virtual int? HerbsQuantity { get; set; }
    
    /// <summary>
    /// 煎药方法
    /// </summary>
    [SugarColumn(ColumnName = "decoction_method", ColumnDescription = "煎药方法", Length = 128)]
    public virtual string? DecoctionMethod { get; set; }
    
    /// <summary>
    /// 是否代煎
    /// </summary>
    [SugarColumn(ColumnName = "is_decoction", ColumnDescription = "是否代煎")]
    public virtual int? IsDecoction { get; set; }
    
    /// <summary>
    /// 退药数量
    /// </summary>
    [SugarColumn(ColumnName = "refund_quantity", ColumnDescription = "退药数量")]
    public virtual decimal? RefundQuantity { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>
    [SugarColumn(ColumnName = "refund_amount", ColumnDescription = "总退药金额", Length = 20, DecimalDigits=4)]
    public virtual decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnName = "batch_no", ColumnDescription = "批号", Length = 100)]
    public virtual string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnName = "production_date", ColumnDescription = "生产日期")]
    public virtual DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    [SugarColumn(ColumnName = "expiration_date", ColumnDescription = "有效期")]
    public virtual DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [SugarColumn(ColumnName = "approval_number", ColumnDescription = "批准文号", Length = 100)]
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 药品通用名编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "药品通用名编码", Length = 100)]
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂家ID
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_id", ColumnDescription = "生产厂家ID")]
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_name", ColumnDescription = "生产厂家名称", Length = 100)]
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "  状态 0 默认 1  已退药")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 组号
    /// </summary>
    [SugarColumn(ColumnName = "group_no", ColumnDescription = "组号")]
    public string? GroupNo { get; set; }
    /// <summary>
    /// 组号标志
    /// </summary>
    [SugarColumn(ColumnName = "group_flag", ColumnDescription = "组号标志")]
    public string? GroupFlag { get; set; }
 
 
}
