﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品持有人表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_holder", "药品持有人表")]
public class DrugHolder : EntityTenant
{
    /// <summary>
    /// 持有人名称
    /// </summary>
    [SugarColumn(ColumnName = "holder_name", ColumnDescription = "持有人名称", Length = 100)]
    public virtual string? HolderName { get; set; }
    
    /// <summary>
    /// 持有人名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "holder_name_pinyin", ColumnDescription = "持有人名称拼音", Length = 100)]
    public virtual string? HolderNamePinyin { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "1 启用 2 停用")]
    public virtual int? Status { get; set; }
    
}
