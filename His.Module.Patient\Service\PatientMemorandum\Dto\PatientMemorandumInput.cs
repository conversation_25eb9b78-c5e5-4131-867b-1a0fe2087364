﻿using Admin.NET.Core;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Patient;

/// <summary>
/// 患者备忘录主表基础输入参数
/// </summary>
public class PatientMemorandumBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required(ErrorMessage = "患者Id不能为空")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public virtual string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 备忘内容
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 患者备忘录主表分页查询输入参数
/// </summary>
public class PagePatientMemorandumInput : BasePageInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 备忘内容
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 患者备忘录主表增加输入参数
/// </summary>
public class AddPatientMemorandumInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required(ErrorMessage = "患者Id不能为空")]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [MaxLength(64, ErrorMessage = "身份证号字符长度不能超过64")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [MaxLength(64, ErrorMessage = "门诊号字符长度不能超过64")]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 备忘内容
    /// </summary>
    [MaxLength(2000, ErrorMessage = "备忘内容字符长度不能超过2000")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 患者备忘录主表删除输入参数
/// </summary>
public class DeletePatientMemorandumInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 患者备忘录主表更新输入参数
/// </summary>
public class UpdatePatientMemorandumInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 患者Id
    /// </summary>    
    [Required(ErrorMessage = "患者Id不能为空")]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>    
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>    
    [MaxLength(64, ErrorMessage = "身份证号字符长度不能超过64")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>    
    [MaxLength(64, ErrorMessage = "门诊号字符长度不能超过64")]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 备忘内容
    /// </summary>    
    [MaxLength(2000, ErrorMessage = "备忘内容字符长度不能超过2000")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 患者备忘录主表主键查询输入参数
/// </summary>
public class QueryByIdPatientMemorandumInput : DeletePatientMemorandumInput
{
}

/// <summary>
/// 患者备忘录主表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportPatientMemorandumInput : BaseImportInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [ImporterHeader(Name = "*患者Id")]
    [ExporterHeader("*患者Id", Format = "", Width = 25, IsBold = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [ImporterHeader(Name = "患者姓名")]
    [ExporterHeader("患者姓名", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [ImporterHeader(Name = "身份证号")]
    [ExporterHeader("身份证号", Format = "", Width = 25, IsBold = true)]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [ImporterHeader(Name = "门诊号")]
    [ExporterHeader("门诊号", Format = "", Width = 25, IsBold = true)]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 备忘内容
    /// </summary>
    [ImporterHeader(Name = "备忘内容")]
    [ExporterHeader("备忘内容", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
