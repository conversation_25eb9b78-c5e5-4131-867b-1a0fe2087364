﻿namespace His.Module.Inpatient.Entity;

/// <summary>
/// 押金交易流水表
/// </summary>
[Tenant("*************")]
[SugarTable("deposit_transaction", "押金交易流水表")]
public class DepositTransaction : EntityTenantBaseData
{
    /// <summary>
    /// 押金账户ID
    /// </summary>
    [SugarColumn(ColumnName = "account_id", ColumnDescription = "押金账户ID")]
    public virtual long? AccountId { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    [SugarColumn(ColumnName = "transaction_type", ColumnDescription = "交易类型", Length = 32)]
    public virtual string? TransactionType { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 16, DecimalDigits = 2)]
    public virtual decimal Amount { get; set; }

    /// <summary>
    /// 剩余可退金额
    /// </summary>
    [SugarColumn(ColumnName = "refundable_amount", ColumnDescription = "剩余可退金额", Length = 16, DecimalDigits = 2)]
    public virtual decimal? RefundableAmount { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    [SugarColumn(ColumnName = "payment_method", ColumnDescription = "支付方式", Length = 64)]
    public virtual string? PaymentMethod { get; set; }

    /// <summary>
    /// 交易渠道
    /// </summary>
    [SugarColumn(ColumnName = "channel", ColumnDescription = "交易渠道", Length = 64)]
    public virtual string? Channel { get; set; }

    /// <summary>
    /// 外部凭证号
    /// </summary>
    [SugarColumn(ColumnName = "receipt_no", ColumnDescription = "外部凭证号", Length = 128)]
    public virtual string? ReceiptNo { get; set; }

    /// <summary>
    /// 交易状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "交易状态", Length = 32)]
    public virtual string? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 512)]
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    [SugarColumn(ColumnName = "invoice_no", ColumnDescription = "发票号", Length = 32)]
    public virtual string? InvoiceNo { get; set; }

}