using His.Module.Insurance.Service.Settlement.Dto;

namespace His.Module.Insurance.Service.Settlement.Model.Patient;

 

/// <summary>
/// 根据电子医保凭证二维码读取基本信息接口返回结果
/// </summary>
public class ReadEwmResponse:BaseSettlementResponse
{
    /// <summary>
    /// 执行代码（0为成功，其他为失败；执行成功时才返回下面的数据）
    /// </summary>
    public string resultCode { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string xm { get; set; }

    /// <summary>
    /// 身份证号码
    /// </summary>
    public string sfzhm { get; set; }
}