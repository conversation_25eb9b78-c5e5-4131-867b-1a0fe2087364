﻿namespace His.Module.Inpatient;

/// <summary>
/// 入院登记记录输出参数
/// </summary>
public class InpatientRegisterDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院次数
    /// </summary>
    public int? InpatientCount { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    public int? Sex { get; set; }
    
    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }
    
    /// <summary>
    /// 年龄单位
    /// </summary>
    public string? AgeUnit { get; set; }
    
    /// <summary>
    /// 出生日期
    /// </summary>
    public DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    public string? Phone { get; set; }
    
    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string? ContactName { get; set; }
    
    /// <summary>
    /// 联系人关系
    /// </summary>
    public string? ContactRelationship { get; set; }
    
    /// <summary>
    /// 联系人地址
    /// </summary>
    public string? ContactAddress { get; set; }
    
    /// <summary>
    /// 联系人电话号码
    /// </summary>
    public string? ContactPhone { get; set; }
    
    /// <summary>
    /// 现居住地省
    /// </summary>
    public int? ResidenceProvince { get; set; }
    
    /// <summary>
    /// 现居住地市
    /// </summary>
    public int? ResidenceCity { get; set; }
    
    /// <summary>
    /// 现居住地县
    /// </summary>
    public int? ResidenceCounty { get; set; }
    
    /// <summary>
    /// 详细现居住地
    /// </summary>
    public string? ResidenceAddress { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>
    public DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 病区ID
    /// </summary>
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    public string? WardName { get; set; }
    
    /// <summary>
    /// 诊疗组ID
    /// </summary>
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 诊疗组名称
    /// </summary>
    public string? TeamName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>
    public long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>
    public string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>
    public string? SettlementCategory { get; set; }
    
    /// <summary>
    /// 入院诊断编号
    /// </summary>
    public string? AdmissionDiagnosisCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public string? AdmissionDiagnosisName { get; set; }
    
    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    public string? PregnancyRiskLevel { get; set; }
    
    /// <summary>
    /// 高危因素
    /// </summary>
    public string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
    /// <summary>
    /// 创建者部门Id
    /// </summary>
    public long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary>
    public string? CreateOrgName { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }
    
}
