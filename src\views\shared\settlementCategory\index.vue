﻿<script lang="ts" setup name="settlementCategory">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useSettlementCategoryApi } from '/@/api/shared/settlementCategory';
import editDialog from '/@/views/shared/settlementCategory/component/editDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from '/@/components/table/importData.vue';

const settlementCategoryApi = useSettlementCategoryApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
	exportLoading: false,
	tableLoading: false,
	stores: {},
	showAdvanceQueryUI: false,
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'createTime', // 默认的排序字段
		order: 'descending', // 排序方向
		descStr: 'descending', // 降序排序的关键字符
	},
	tableData: [],
});

// 页面加载时
onMounted(async () => {});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	const result = await settlementCategoryApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
	state.tableParams.total = result?.total;
	state.tableData = result?.items ?? [];
	state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

// 删除
const delSettlementCategory = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await settlementCategoryApi.delete({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 批量删除
const batchDelSettlementCategory = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await settlementCategoryApi.batchDelete(state.selectData.map((u) => ({ id: u.id }))).then((res) => {
				ElMessage.success(`成功批量删除${res.data.result}条记录`);
				handleQuery();
			});
		})
		.catch(() => {});
};

// 设置状态
const changeSettlementCategoryStatus = async (row: any) => {
	await settlementCategoryApi.setStatus({ id: row.id, status: row.status }).then(() => ElMessage.success('状态设置成功'));
};

// 导出数据
const exportSettlementCategoryCommand = async (command: string) => {
	try {
		state.exportLoading = true;
		if (command === 'select') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams, { selectKeyList: state.selectData.map((u) => u.id) });
			await settlementCategoryApi.exportData(params).then((res) => downloadStreamFile(res));
		} else if (command === 'current') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams);
			await settlementCategoryApi.exportData(params).then((res) => downloadStreamFile(res));
		} else if (command === 'all') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams, { page: 1, pageSize: 99999999 });
			await settlementCategoryApi.exportData(params).then((res) => downloadStreamFile(res));
		}
	} finally {
		state.exportLoading = false;
	}
};

handleQuery();
</script>
<template>
	<div class="settlementCategory-container" v-loading="state.exportLoading">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="编号">
							<el-input v-model="state.tableQueryParams.code" clearable placeholder="请输入编号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="名称">
							<el-input v-model="state.tableQueryParams.name" clearable placeholder="请输入名称" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="拼音码">
							<el-input v-model="state.tableQueryParams.pinyinCode" clearable placeholder="请输入拼音码" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'settlementCategory:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" v-if="!state.showAdvanceQueryUI" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" v-if="state.showAdvanceQueryUI" style="margin-left: 5px"> 隐藏 </el-button>
								<el-button
									type="danger"
									style="margin-left: 5px"
									icon="ele-Delete"
									@click="batchDelSettlementCategory"
									:disabled="state.selectData.length == 0"
									v-auth="'settlementCategory:batchDelete'"
								>
									删除
								</el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef.openDialog(null, '新增结算类别')" v-auth="'settlementCategory:add'"> 新增 </el-button>
								<el-dropdown :show-timeout="70" :hide-timeout="50" @command="exportSettlementCategoryCommand">
									<el-button type="primary" style="margin-left: 5px" icon="ele-FolderOpened" v-reclick="20000" v-auth="'settlementCategory:export'"> 导出 </el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item command="select" :disabled="state.selectData.length == 0">导出选中</el-dropdown-item>
											<el-dropdown-item command="current">导出本页</el-dropdown-item>
											<el-dropdown-item command="all">导出全部</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
								<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'settlementCategory:import'"> 导入 </el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" v-if="auth('settlementCategory:batchDelete') || auth('settlementCategory:export')" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="code" label="编号" show-overflow-tooltip />
				<el-table-column prop="name" label="名称" show-overflow-tooltip />
				<el-table-column prop="pinyinCode" label="拼音码" show-overflow-tooltip />
				<el-table-column prop="medCategory" label="医疗类别" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.medCategory" code="MedCategoryEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="usageScope" label="使用范围" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.usageScope" code="MedServiceCategoryEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="medInsType" label="医保类型" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.medInsType" code="MedInsTypeEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="medicalPoolingCategoryName" label="医疗统筹类别" show-overflow-tooltip />
				<el-table-column prop="medicalInsuranceFlagName" label="险种标志" show-overflow-tooltip />
				<el-table-column prop="isInpatientArrearsAllowed" label="住院是否允许欠费" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.isInpatientArrearsAllowed" code="YesNoEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="inpatientAllowedArrearsAmount" label="住院允许欠费金额" show-overflow-tooltip />
				<el-table-column prop="inpatientAllowedArrearsRatio" label="住院允许欠费比例" show-overflow-tooltip />
				<el-table-column prop="status" label="状态" v-auth="'settlementCategory:setStatus'" show-overflow-tooltip>
					<template #default="scope">
						<el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2" size="small" @change="changeSettlementCategoryStatus(scope.row)" />
					</template>
				</el-table-column>
				<el-table-column prop="orderNo" label="排序" show-overflow-tooltip />
				<el-table-column prop="remark" label="备注" show-overflow-tooltip />
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('settlementCategory:update') || auth('settlementCategory:delete')">
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="editDialogRef.openDialog(scope.row, '编辑结算类别')" v-auth="'settlementCategory:update'"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="primary" @click="delSettlementCategory(scope.row)" v-auth="'settlementCategory:delete'"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
			<ImportData ref="importDataRef" :import="settlementCategoryApi.importData" :download="settlementCategoryApi.downloadTemplate" v-auth="'settlementCategory:import'" @refresh="handleQuery" />
			<printDialog ref="printDialogRef" :title="'打印结算类别'" @reloadTable="handleQuery" />
			<editDialog ref="editDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
