﻿namespace His.Module.OutpatientDoctor.OtherModelEntity;

/// <summary>
/// 药品库房表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_storage", "药品库房表")]
public class DrugStorage : EntityTenant
{
    /// <summary>
    /// 库房编码
    /// </summary>
    [SugarColumn(ColumnName = "storage_code", ColumnDescription = "库房编码", Length = 100)]
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "库房名称", Length = 100)]
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 存储药品类型（JSONB格式）  
    /// </summary>
    [SugarColumn(ColumnName = "storage_drug_type",
      
         IsJson = true , 
        ColumnDescription = "存储药品类型（JSONB格式）")]
    public List<String>  StorageDrugType { get; set; }
    /// <summary>
    /// 服务对象（门诊，急诊、住院）
    /// </summary>
    [SugarColumn(ColumnName = "service_object",  
        IsJson = true , ColumnDescription = "服务对象（门诊，急诊、住院） MedServiceCategoryEnum")]
    public   List<int?>   ServiceObject { get; set; }
    /// <summary>
    /// 库存金额上限
    /// </summary>
    [SugarColumn(ColumnName = "storage_amount_limit", ColumnDescription = "库存金额上限", Length = 20, DecimalDigits=4)]
    public virtual decimal? StorageAmountLimit { get; set; }
    
    /// <summary>
    /// 父级库房ID
    /// </summary>
    [SugarColumn(ColumnName = "parent_id", ColumnDescription = "父级库房ID")]
    public virtual long? ParentId { get; set; }
    
    /// <summary>
    /// 父级库房编码
    /// </summary>
    [SugarColumn(ColumnName = "parent_code", ColumnDescription = "父级库房编码", Length = 100)]
    public virtual string? ParentCode { get; set; }
    

    
    /// <summary>
    /// 采购入库审核（1 审核 2 不审核）
    /// </summary>
    [SugarColumn(ColumnName = "purchase_audit", ColumnDescription = "采购入库审核（1 审核 2 不审核）")]
    public virtual int? PurchaseAudit { get; set; }
    
    /// <summary>
    /// 采购退货审核（1 审核 2 不审核）
    /// </summary>
    [SugarColumn(ColumnName = "purchase_return_audit", ColumnDescription = "采购退货审核（1 审核 2 不审核）")]
    public virtual int? PurchaseReturnAudit { get; set; }
    
    /// <summary>
    /// 药店申领审核（1 审核 2 不审核）
    /// </summary>
    [SugarColumn(ColumnName = "apply_audit", ColumnDescription = "药店申领审核（1 审核 2 不审核）")]
    public virtual int? ApplyAudit { get; set; }
    
    /// <summary>
    /// 药店退药审核（1 审核 2 不审核）
    /// </summary>
    [SugarColumn(ColumnName = "apply_return_audit", ColumnDescription = "药店退药审核（1 审核 2 不审核）")]
    public virtual int? ApplyReturnAudit { get; set; }
    
    /// <summary>
    /// 出库审核（1 审核 2 不审核）
    /// </summary>
    [SugarColumn(ColumnName = "out_audit", ColumnDescription = "出库审核（1 审核 2 不审核）")]
    public virtual int? OutAudit { get; set; }
    
    /// <summary>
    /// 特殊处理审核（1 审核 2 不审核）
    /// </summary>
    [SugarColumn(ColumnName = "special_audit", ColumnDescription = "特殊处理审核（1 审核 2 不审核）")]
    public virtual int? SpecialAudit { get; set; }
    
    /// <summary>
    /// 按批号盘点（1 按批号 2 不按批号）
    /// </summary>
    [SugarColumn(ColumnName = "batch_check", ColumnDescription = "按批号盘点（1 按批号 2 不按批号）")]
    public virtual int? BatchCheck { get; set; }
    
    /// <summary>
    /// 草药单位转换 kg-g （1 转换 2 不转换）
    /// </summary>
    [SugarColumn(ColumnName = "herb_unit_conv", ColumnDescription = "草药单位转换 kg-g （1 转换 2 不转换）")]
    public virtual int? HerbUnitConv { get; set; }

    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态（1 启用 2 停用）")]
    public virtual int? Status { get; set; }
    [SugarColumn(ColumnName = "org_id", ColumnDescription = "科室id")]
    public virtual long? OrgId { get; set; }
}
