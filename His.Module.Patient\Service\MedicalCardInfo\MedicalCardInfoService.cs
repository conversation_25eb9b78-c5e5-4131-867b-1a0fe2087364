﻿using His.Module.Financial.Api.MedicalCard;
using His.Module.Financial.Api.MedicalCard.Dto;
using His.Module.Financial.Enum;
using His.Module.Patient.Api.Api;
using His.Module.Patient.Api.Api.Dto;
using His.Module.Patient.Api.Enum;
using His.Module.Patient.Enum;
namespace His.Module.Patient.Service;

/// <summary>
/// 就诊卡管理服务
/// </summary>
[ApiDescriptionSettings(PatientConst.GroupName, Order = 100)]
public class MedicalCardInfoService : IDynamicApiController, ITransient, ICardInfoApi
{
    private readonly SqlSugarRepository<MedicalCardInfo> _cardInfoRep;
    private readonly SqlSugarRepository<PatientInfo> _patientInfoRep;
    
    private readonly  IMedicalCardPaymentApi _medicalCardPaymentApi;  
    public MedicalCardInfoService(SqlSugarRepository<MedicalCardInfo> cardInfoRep
        , SqlSugarRepository<PatientInfo> patientInfoRep
        , IMedicalCardPaymentApi medicalCardPaymentApi)
    {
        _cardInfoRep = cardInfoRep;
        _patientInfoRep = patientInfoRep;
        _medicalCardPaymentApi = medicalCardPaymentApi;
    }

    /// <summary>
    /// 分页查询就诊卡
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    [DisplayName("分页查询就诊卡")]
    public async Task<SqlSugarPagedList<MedicalCardInfoOutput>> Page(MedicalCardInfoInput input)
    {
        input.Name = input.Name?.ToLower();
        input.IdCardNo = input.IdCardNo?.ToUpper();
        return await _cardInfoRep.AsQueryable()
            .LeftJoin<PatientInfo>((u, a) => u.PatientId == a.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.CardNo), (u, a) => u.CardNo == input.CardNo)
            .WhereIF(input.BusinessType is not null, (u, a) => u.BusinessType == input.BusinessType)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), (u, a) => a.Name.Contains(input.Name)
            || a.PinyinCode.Contains(input.Name)
            || a.WubiCode.Contains(input.Name))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardNo), (u, a) => a.IdCardNo.Contains(input.IdCardNo))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StartTime.ToString()), u => u.CreateTime >= input.StartTime)
            .WhereIF(!string.IsNullOrWhiteSpace(input.EndTime.ToString()), u => u.CreateTime <= input.EndTime)
            .Select((u, a) => new MedicalCardInfoOutput
            {
                Id = u.Id,
                PatientId = a.Id,
                PatientNo = a.PatientNo,
                CardNo = u.CardNo
            }, true).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 根据身份证号或者卡号获取就诊卡信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "CardInfoByIdOrCardNo"), HttpPost]
    [DisplayName("根据身份证号或者卡号获取就诊卡信息")]
    public async Task<MedicalCardInfoOutput> CardInfoByIdOrCardNo(MedicalCardInfoInput input)
    {
        PatientInfo patientInfo;
        MedicalCardInfo cardInfo;
        if (!string.IsNullOrEmpty(input.IdCardNo))
        {
            patientInfo = await _patientInfoRep.AsQueryable()
                              .Where(u => u.IdCardNo.ToUpper() == input.IdCardNo.ToUpper())
                              .FirstAsync()
                          ?? throw Oops.Oh(PatientErrorCodeEnum.P0001, input.IdCardNo);
            cardInfo = await _cardInfoRep.AsQueryable()
                           .Where(u => u.PatientId == patientInfo.Id
                                       && u.BusinessType == BusinessTypeEnum.ClassicCard
                                       && u.Status == CardStatusEnum.Normal
                           ).FirstAsync()
                       ?? throw Oops.Oh(PatientErrorCodeEnum.P0002, patientInfo.Name);
        }
        else    if (!string.IsNullOrEmpty(input.CardNo))  
        {
            cardInfo = await _cardInfoRep.AsQueryable()
                           .Where(u => u.CardNo == input.CardNo
                                       && u.BusinessType == BusinessTypeEnum.ClassicCard
                                       && u.Status == CardStatusEnum.Normal
                           ).FirstAsync()
                       ?? throw Oops.Oh(PatientErrorCodeEnum.P0003, input.CardNo);
            patientInfo = await _patientInfoRep.AsQueryable().Where(u => u.Id == cardInfo.PatientId).FirstAsync();
        }
        else
        {
            throw Oops.Oh("未能找到对应的就诊卡信息");
        }
        var output = patientInfo.Adapt<MedicalCardInfoOutput>();
        output.Id = cardInfo.Id;
        output.CardNo = cardInfo.CardNo;
        output.PatientId = cardInfo.PatientId;
        output.PatientNo = patientInfo.PatientNo;
        output.BusinessType = cardInfo.BusinessType;
        output.UseDepts = cardInfo.UseDepts;
        output.ChargeModes = cardInfo.ChargeModes;
        output.Balance = cardInfo.Balance;
        output.Status = cardInfo.Status;
        output.PatientName = patientInfo.Name;
        return output;
    }

    /// <summary>
    /// 增加就诊卡
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost, UnitOfWork]
    [DisplayName("增加就诊卡")]
    public async Task Add(AddMedicalCardInfoInput input)
    {
        //验证患者是否存在
        if (await _patientInfoRep.AsQueryable().ClearFilter().AnyAsync(u => u.IdCardNo.ToUpper() == input.IdCardNo.ToUpper()))
            throw Oops.Oh(PatientErrorCodeEnum.P0004, input.IdCardNo);
        var entityPatient = input.Adapt<PatientInfo>();
        entityPatient.PatientNo = await _patientInfoRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('patient_info_code_seq')As varchar),8,'0')");
        entityPatient.PinyinCode = TextUtil.GetFirstPinyin(entityPatient.Name);
        entityPatient.WubiCode = TextUtil.GetFirstWuBi(entityPatient.Name);
        entityPatient.IdCardNo = entityPatient.IdCardNo.ToUpper();

        //插入患者信息并返回患者ID
        var patientId = await _patientInfoRep.InsertReturnSnowflakeIdAsync(entityPatient);
        var entityCard = input.Adapt<MedicalCardInfo>();
        entityCard.CardNo = await _cardInfoRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('medical_card_info_card_no_seq')As varchar),8,'0')");
        entityCard.PatientId = patientId;
        entityCard.Status = CardStatusEnum.Normal;
        entityCard.IsStored = true;
        entityCard.Balance = 0;
        await _cardInfoRep.InsertAsync(entityCard);
    }

    /// <summary>
    /// 更新就诊卡
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新就诊卡")]
    public async Task Update(UpdateMedicalCardInfoInput input)
    {
        var entityPatient = input.Adapt<PatientInfo>();
        entityPatient.Id = input.PatientId;
        entityPatient.PinyinCode = TextUtil.GetFirstPinyin(entityPatient.Name);
        entityPatient.WubiCode = TextUtil.GetFirstWuBi(entityPatient.Name);
        entityPatient.IdCardNo = entityPatient.IdCardNo.ToUpper();
        //更新患者信息
        await _patientInfoRep.AsUpdateable(entityPatient).IgnoreColumns(true).ExecuteCommandAsync();
        var entityCard = input.Adapt<MedicalCardInfo>();
        entityCard.Id = input.Id;
        //更新卡信息
        await _cardInfoRep.AsUpdateable(entityCard).IgnoreColumns(true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 挂失
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Loss"), HttpPost]
    [DisplayName("挂失")]
    public async Task Loss(LossMedicalCardInfoInput input)
    {
        var card = await _cardInfoRep.AsQueryable()
            .Where(u => u.Id == input.Id)
            .FirstAsync() ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        switch (card.Status)
        {
            case CardStatusEnum.Loss:
                throw Oops.Oh(PatientErrorCodeEnum.P0005, card.CardNo);
            case CardStatusEnum.CardRefund:
                throw Oops.Oh(PatientErrorCodeEnum.P0006, card.CardNo);
            case CardStatusEnum.Normal:
            default:
                card.Status = CardStatusEnum.Loss;
                //更新卡信息 挂失
                await _cardInfoRep.AsUpdateable(card).UpdateColumns(u => new
                {
                    u.Status
                }).ExecuteCommandAsync();
                break;
        }
    }

    /// <summary>
    /// 恢复
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Restore"), HttpPost]
    [DisplayName("恢复")]
    public async Task Restore(RestoreMedicalCardInfoInput input)
    {
        var card = await _cardInfoRep.AsQueryable()
            .Where(u => u.Id == input.Id)
            .FirstAsync() ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (card.Status != CardStatusEnum.Loss)
        {
            throw Oops.Oh(PatientErrorCodeEnum.P0007, card.CardNo);
        }
        card.Status = CardStatusEnum.Normal;
        //更新卡信息 正常
        await _cardInfoRep.AsUpdateable(card).UpdateColumns(u => new
        {
            u.Status
        }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 退卡
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "CardRefund"), HttpPost, UnitOfWork]
    [DisplayName("退卡")]
    public async Task CardRefund(CardRefundMedicalCardInfoInput input)
    {
        var card = await _cardInfoRep.AsQueryable()
            .Where(u => u.Id == input.Id)
            .FirstAsync();
        switch (card.Status)
        {
            case CardStatusEnum.Loss:
                throw Oops.Oh(PatientErrorCodeEnum.P0005, card.CardNo);
            case CardStatusEnum.CardRefund:
                throw Oops.Oh(PatientErrorCodeEnum.P0006, card.CardNo);
        }

        
        //添加卡费用记录
        await _medicalCardPaymentApi.CardRefundBalance(new MedicalCardPaymentRefundBalanceDto()
        {
            PatientId = card.PatientId,
            CardId = card.Id,
            //PayMethodId = input.PayMethodId ?? 0,
           // PayAmount = -card.Balance ?? 0,

        });
 
        // card.Status = CardStatusEnum.CardRefund;
        // card.Balance = 0;
        // //更新卡信息 退卡
        // await _cardInfoRep.AsUpdateable(card).UpdateColumns(u => new
        // {
        //     u.Status, u.Balance
        // }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 卡充值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "CardRecharge"), HttpPost, UnitOfWork]
    [DisplayName("卡充值")]
    public async Task CardRecharge(CardRechargeInput input)
    {
        var card = await _cardInfoRep.AsQueryable()
                       .Where(u => u.Id == input.Id)
                       .FirstAsync()
                   ?? throw Oops.Oh(PatientErrorCodeEnum.P0003, input.Id);
        if (card?.Status == CardStatusEnum.Loss)
        {
            throw Oops.Oh(PatientErrorCodeEnum.P0005, input.Id);
        }
        if (card?.Status == CardStatusEnum.CardRefund)
        {
            throw Oops.Oh(PatientErrorCodeEnum.P0006, input.Id);
        }
        card.Balance += input.PayAmount;
        //添加卡费用记录
        await _medicalCardPaymentApi.Recharge(new MedicalCardPaymentRechargeDto()
        {
            PatientId = card.PatientId,
            CardId = card.Id,
            PayMethodId = input.PayMethodId ?? 0,
            PayAmount = input.PayAmount ?? 0,
            //CardBalance = card.Balance ?? 0,
        });
        
        
        // //更新卡信息
        // await _cardInfoRep.AsUpdateable(card).UpdateColumns(u => new
        // {
        //     u.Balance
        // }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取就诊卡信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    [DisplayName("获取就诊卡信息")]
    public async Task<CardInfoDto> Detail(long? id)
    {
        var cardInfoEntity = await _cardInfoRep.GetFirstAsync(u => u.Id == id);
        return cardInfoEntity.Adapt<CardInfoDto>();
    }
   
    /// <summary>
    /// 卡缴费 支付
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "CardPay"), HttpPost, UnitOfWork]
    [DisplayName("卡缴费")]
    public async Task CardPay(CardPayInput input)
    {
        var card = await _cardInfoRep.GetFirstAsync(u => u.Id == input.Id)
                   ?? throw Oops.Oh(PatientErrorCodeEnum.P0003, input.Id);
        if (card?.Status == CardStatusEnum.Loss)
        {
            throw Oops.Oh(PatientErrorCodeEnum.P0005, input.Id);
        }
        if (card?.Status == CardStatusEnum.CardRefund)
        {
            throw Oops.Oh(PatientErrorCodeEnum.P0006, input.Id);
        }
        card.Balance -= input.PayAmount;
        //添加卡费用记录
        await _medicalCardPaymentApi.Deduction(new MedicalCardPaymentDeductionDto()
        {
            PatientId = card.PatientId,
            CardId = card.Id,
          // PayMethodId = input.PayMethodId,
            PayAmount = -input.PayAmount,
           // CardBalance = card.Balance  ,
        });
         
        //
        // //更新卡信息
        // await _cardInfoRep.AsUpdateable(card).UpdateColumns(u => new
        // {
        //     u.Balance
        // }).ExecuteCommandAsync();
    }
    
}