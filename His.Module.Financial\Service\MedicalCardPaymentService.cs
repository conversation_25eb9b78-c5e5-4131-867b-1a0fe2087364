using Furion.DatabaseAccessor;
using His.Module.Financial.Api.MedicalCard;
using His.Module.Financial.Api.MedicalCard.Dto;
using His.Module.Financial.Const;
using His.Module.Financial.Entity;
using His.Module.Financial.Enum;
using His.Module.Financial.OtherModuleEntity;

namespace His.Module.Financial.Service;

/// <summary>
/// 医疗卡支付服务
/// </summary>
[ApiDescriptionSettings(FinancialConst.GroupName, Order = 100)]
public class MedicalCardPaymentService(
    SqlSugarRepository<MedicalCardRecharge> medicalCardRechargeRepository,
    SqlSugarRepository<MedicalCardInfo> medicalCardInfoRepository
) : IDynamicApiController, ITransient, IMedicalCardPaymentApi
{
    [UnitOfWork]
    public async Task<MedicalCardPaymentOutput> Recharge(MedicalCardPaymentRechargeDto input)
    {
        //           
        var medicalCardRecharge =
            input.Adapt<MedicalCardRecharge>();
        medicalCardRecharge.InvoiceNumber = await GetInvoiceNumber();
        var cardInfo = await GetMedicalCardInfo(medicalCardRecharge);
        // 修改余额
        cardInfo.Balance += medicalCardRecharge.PayAmount;
        await UpdateMedicalCardBalance(cardInfo);
        medicalCardRecharge.Status = CardRechargeStatusEnum.Recharge;
        medicalCardRecharge.Remark = "充值";
        await medicalCardRechargeRepository.InsertAsync(medicalCardRecharge);
  
        
        return new MedicalCardPaymentOutput() { Id = medicalCardRecharge.Id, };
    }

    /// <summary>
    /// 退费
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [UnitOfWork]
    public async Task<MedicalCardPaymentOutput> Refund(MedicalCardPaymentRefundDto input)
    {
        var medicalCardRecharge =
            input.Adapt<MedicalCardRecharge>();
        medicalCardRecharge.InvoiceNumber = await GetInvoiceNumber();
        var cardInfo = await GetMedicalCardInfo(medicalCardRecharge);
        // 修改余额
        cardInfo.Balance += medicalCardRecharge.PayAmount;
        await UpdateMedicalCardBalance(cardInfo);
        medicalCardRecharge.Status = CardRechargeStatusEnum.Refund;
        medicalCardRecharge.Remark="退费";
        await medicalCardRechargeRepository.InsertAsync(medicalCardRecharge);
        return new MedicalCardPaymentOutput() { Id = medicalCardRecharge.Id, };
    }

    /// <summary>
    /// 卡扣费
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<MedicalCardPaymentOutput> Deduction(MedicalCardPaymentDeductionDto input)
    {
        var medicalCardRecharge =
            input.Adapt<MedicalCardRecharge>();
        medicalCardRecharge.InvoiceNumber = await GetInvoiceNumber();
        var cardInfo = await GetMedicalCardInfo(medicalCardRecharge);
        // 修改余额
        cardInfo.Balance -= medicalCardRecharge.PayAmount;
        if (cardInfo.Balance < 0)
        {
            throw new Exception("卡余额不足");
        }
              
        await UpdateMedicalCardBalance(cardInfo);
        medicalCardRecharge.Status = CardRechargeStatusEnum.Deduction;
        medicalCardRecharge.Remark = "扣款";
        await medicalCardRechargeRepository.InsertAsync(medicalCardRecharge);

        return new MedicalCardPaymentOutput() { Id = medicalCardRecharge.Id, };
    }
    /// <summary>
    ///   卡余额退款
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
   [UnitOfWork]
    public async Task<MedicalCardPaymentOutput> CardRefundBalance(MedicalCardPaymentRefundBalanceDto input)
    {
        var medicalCardRecharge =
            input.Adapt<MedicalCardRecharge>();
        medicalCardRecharge.InvoiceNumber = await GetInvoiceNumber();
        var cardInfo = await GetMedicalCardInfo(medicalCardRecharge);
        // 修改余额
        medicalCardRecharge.PayAmount=-cardInfo.Balance;
        cardInfo.Balance=0;
      
        await UpdateMedicalCardBalance(cardInfo);
        medicalCardRecharge.Status = CardRechargeStatusEnum.CardRefundBalance;
        medicalCardRecharge.Remark = "退卡余额";
        
        await medicalCardRechargeRepository.InsertAsync(medicalCardRecharge);
        return new MedicalCardPaymentOutput() { Id = medicalCardRecharge.Id, };
    }

    /// <summary>
    /// 卡红冲
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public Task<MedicalCardPaymentOutput> RedInvoice(MedicalCardPaymentRedInvoiceDto input)
    {
        throw new NotImplementedException();
    }

    private async Task<MedicalCardInfo> GetMedicalCardInfo(MedicalCardRecharge medicalCardRecharge)
    {
        var cardInfo = await medicalCardInfoRepository.AsQueryable()
            .WhereIF(medicalCardRecharge.CardId > 0, u => u.Id == medicalCardRecharge.CardId)
             .WhereIF(medicalCardRecharge.PatientId > 0, u => u.PatientId == medicalCardRecharge.PatientId)
            .WhereIF(!string.IsNullOrWhiteSpace(medicalCardRecharge.CardNo),
                u => u.CardNo == medicalCardRecharge.CardNo)
            .FirstAsync();

        medicalCardRecharge.CardNo = cardInfo.CardNo;
        medicalCardRecharge.CardId = cardInfo.Id;
        medicalCardRecharge.PatientId = cardInfo.PatientId;
        medicalCardRecharge.PayMethodId = medicalCardRecharge.PayMethodId ?? 0;
        return cardInfo;
    }

   private async Task UpdateMedicalCardBalance(MedicalCardInfo cardInfo)
    {
        await medicalCardInfoRepository.UpdateAsync(u => new MedicalCardInfo()
            {
                Balance = cardInfo.Balance,
                Id = cardInfo.Id
            },
            u => u.Id == cardInfo.Id
        );
    }


    private async Task<string> GetInvoiceNumber()
    {
        return "F" +
               await medicalCardRechargeRepository.Context.Ado.GetStringAsync(
                   "SELECT LPAD(CAST(NEXTVAL('card_recharge_inv_number_seq')As varchar),8,'0')");
    }
}