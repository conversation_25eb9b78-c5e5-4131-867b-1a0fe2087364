﻿namespace His.Module.Pharmacy;

/// <summary>
/// 采购计划明细表输出参数
/// </summary>
public class DrugPurchasePlanDetailDto
{
    /// <summary>
    /// 生产厂家
    /// </summary>
    public string ManufacturerIdFkColumn { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    public string SupplierIdFkColumn { get; set; }
    
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 采购计划ID
    /// </summary>
    public long? PlanId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 药店库存数量
    /// </summary>
    public int? PharmacyQuantity { get; set; }
    
    /// <summary>
    /// 药库库存数量
    /// </summary>
    public int? StorageQuantity { get; set; }
    
    /// <summary>
    /// 当前销售数量
    /// </summary>
    public int? CurrentSaleQuantity { get; set; }
    
    /// <summary>
    /// 上次销售数量
    /// </summary>
    public int? LastSaleQuantity { get; set; }
    
    /// <summary>
    /// 平均销售数量
    /// </summary>
    public int? AverageSaleQuantity { get; set; }
    
    /// <summary>
    /// 计划采购数量
    /// </summary>
    public int? Quantity { get; set; }
    
    /// <summary>
    /// 采购单价
    /// </summary>
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 总采购价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 生产厂家
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    public string? SupplierName { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
