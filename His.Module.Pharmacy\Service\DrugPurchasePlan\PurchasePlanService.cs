namespace His.Module.Pharmacy.Service;

public class PurchasePlanService : ITransient
{
    private readonly SqlSugarRepository<DrugInventory> _drugInventoryRep;
    private readonly SqlSugarRepository<DrugStorage> _drugStorageRep;
    private readonly SqlSugarRepository<DrugDictionary> _drugDictionaryRep;

    public PurchasePlanService(
        SqlSugarRepository<DrugInventory> drugInventoryRep,
        SqlSugarRepository<DrugStorage> drugStorageRep,
        SqlSugarRepository<DrugDictionary> drugDictionaryRep
    )
    {
        _drugDictionaryRep = drugDictionaryRep;
        _drugStorageRep = drugStorageRep;
        _drugInventoryRep = drugInventoryRep;
    }

    public async Task<List<DrugInventory>> GetAllActiveAsync()
    {
        return await _drugInventoryRep.AsQueryable().Where
                (x => x.IsDelete == false)
            .Where(x=>x.ExpirationDate > DateTime.Now.AddMonths(3))  // 存在3个月以上效期的库存‌
            .ToListAsync();
            ;
    }
}