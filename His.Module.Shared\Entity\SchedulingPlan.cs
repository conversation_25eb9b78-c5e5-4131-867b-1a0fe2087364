﻿namespace His.Module.Shared.Entity;

/// <summary>
/// 排班计划表
/// </summary>
[SugarTable(null, "排班计划表")]
[Tenant("1300000000014")]
public class SchedulingPlan : EntityTenant
{
    /// <summary>
    /// 医生id
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "医生id")]
    public long? DoctorId { get; set; }

    /// <summary>
    /// 时间段id
    /// </summary>
    [SugarColumn(ColumnName = "time_period_id", ColumnDescription = "时间段id")]
    public long? TimePeriodId { get; set; }
    
    
    /// <summary>
    /// 时间段id
    /// </summary>
    [SugarColumn(ColumnName = "time_period_code", ColumnDescription = "时间段id")]
    public string? TimePeriodCode { get; set; }
    [SugarColumn(ColumnName = "time_period_name", ColumnDescription = "时间段id")]
    public string? TimePeriodName { get; set; }

    /// <summary>
    /// 号别id
    /// </summary>
    [SugarColumn(ColumnName = "reg_category_id", ColumnDescription = "号别id")]
    public long? RegCategoryId { get; set; }

    [SugarColumn(ColumnName = "reg_category_name", ColumnDescription = "号别")]
    public string? RegCategoryName { get; set; }
    /// <summary>
    /// 限号数
    /// </summary>
    [SugarColumn(ColumnName = "reg_limit", ColumnDescription = "限号数")]
    public int? RegLimit { get; set; }

    /// <summary>
    /// 限预约号数
    /// </summary>
    [SugarColumn(ColumnName = "app_limit", ColumnDescription = "限预约号数")]
    public int? AppLimit { get; set; }

    /// <summary>
    /// 已挂号数
    /// </summary>
    [SugarColumn(ColumnName = "reg_number", ColumnDescription = "已挂号数")]
    public int? RegNumber { get; set; }

    /// <summary>
    /// 已预约号数
    /// </summary>
    [SugarColumn(ColumnName = "app_number", ColumnDescription = "已预约号数")]
    public int? AppNumber { get; set; }

    /// <summary>
    /// 门诊日期
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_date", ColumnDescription = "门诊日期")]
    public DateTime OutpatientDate { get; set; }
    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(ColumnName = "start_time", ColumnDescription = "开始时间")]
    public virtual TimeSpan? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(ColumnName = "end_time", ColumnDescription = "结束时间")]
    public virtual TimeSpan? EndTime { get; set; }
    /// <summary>
    /// 科室id
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室id")]
    public long? DeptId { get; set; }

    /// <summary>
    /// 星期几
    /// </summary>
    [SugarColumn(ColumnName = "week_day", ColumnDescription = "星期几", Length = 32)]
    public string? WeekDay { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public string? Remark { get; set; }
    [SugarColumn(IsIgnore = true)]
    public string? DeptName { get; set; }
    [SugarColumn(IsIgnore = true)]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    [SugarColumn(ColumnName = "room_id", ColumnDescription = "诊室id")]
    public long? RoomId { get; set; }

    /// <summary>
    /// 诊室
    /// </summary>
    [SugarColumn(ColumnName = "room_name", ColumnDescription = "诊室", Length = 32)]
    public string? RoomName { get; set; }
}