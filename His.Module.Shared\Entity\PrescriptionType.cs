﻿namespace His.Module.Shared.Entity;

/// <summary>
/// 处方类型表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("prescription_type", "处方类型表")]
public class PrescriptionType : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码", Length = 32)]
    public virtual string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 32)]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 32)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 32)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 可使用的收费类别
    /// </summary>
    [SugarColumn(ColumnName = "charge_categorys", ColumnDescription = "可使用的收费类别", IsJson = true)]
    public virtual List<long>? ChargeCategorys { get; set; }

    /// <summary>
    /// 处方条目
    /// </summary>
    [SugarColumn(ColumnName = "prescription_entries", ColumnDescription = "处方条目")]
    public virtual int? PrescriptionEntries { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
}