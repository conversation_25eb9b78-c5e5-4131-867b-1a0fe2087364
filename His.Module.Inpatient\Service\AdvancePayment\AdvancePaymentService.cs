﻿using His.Module.Inpatient.OtherModuleEntity;
namespace His.Module.Inpatient;

/// <summary>
/// 预交金服务 🧩
/// </summary>
[ApiDescriptionSettings(InpatientConst.GroupName, Order = 100)]
public class AdvancePaymentService : ID<PERSON><PERSON><PERSON><PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<AdvancePayment> _advancePaymentRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public AdvancePaymentService(SqlSugarRepository<AdvancePayment> advancePaymentRep, ISqlSugarClient sqlSugarClient)
    {
        _advancePaymentRep = advancePaymentRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询预交金 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询预交金")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<AdvancePaymentOutput>> Page(PageAdvancePaymentInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query =
            
            _advancePaymentRep.AsTenant().QueryableWithAttr<AdvancePayment>()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.VoucherNo.Contains(input.Keyword) || u.InpatientNo.Contains(input.Keyword) || u.InpatientSerialNo.Contains(input.Keyword) || u.MedicalRecordNo.Contains(input.Keyword) || u.InsuranceNo.Contains(input.Keyword) || u.AdvanceAmountChinese.Contains(input.Keyword) || u.PaymentMethod.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VoucherNo), u => u.VoucherNo.Contains(input.VoucherNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientNo), u => u.InpatientNo.Contains(input.InpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientSerialNo), u => u.InpatientSerialNo.Contains(input.InpatientSerialNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicalRecordNo), u => u.MedicalRecordNo.Contains(input.MedicalRecordNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InsuranceNo), u => u.InsuranceNo.Contains(input.InsuranceNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AdvanceAmountChinese), u => u.AdvanceAmountChinese.Contains(input.AdvanceAmountChinese.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PaymentMethod), u => u.PaymentMethod.Contains(input.PaymentMethod.Trim()))
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.RegisterId != null, u => u.RegisterId == input.RegisterId)
            .WhereIF(input.AdvanceTimeRange?.Length == 2, u => u.AdvanceTime >= input.AdvanceTimeRange[0] && u.AdvanceTime <= input.AdvanceTimeRange[1])
            .WhereIF(input.PaymentRecordId != null, u => u.PaymentRecordId == input.PaymentRecordId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<PatientInfo>((u, patient) => u.PatientId == patient.Id)
            .LeftJoin<InpatientRegister>((u, patient, register) => u.RegisterId == register.Id)
            .Select((u, patient, register) => new AdvancePaymentOutput
            {
                Id = u.Id,
                VoucherNo = u.VoucherNo,
                PatientId = u.PatientId,
               // PatientFkDisplayName = $"{patient.Name}",
                RegisterId = u.RegisterId,
              //  RegisterFkDisplayName = $"{register.PatientNo}-{register.PatientName}-{register.InsuranceNo}-{register.InpatientTime}",
                InpatientNo = u.InpatientNo,
                InpatientSerialNo = u.InpatientSerialNo,
                MedicalRecordNo = u.MedicalRecordNo,
                InsuranceNo = u.InsuranceNo,
                AdvanceAmount = u.AdvanceAmount,
                AdvanceAmountChinese = u.AdvanceAmountChinese,
                AdvanceTime = u.AdvanceTime,
                PaymentMethod = u.PaymentMethod,
                PaymentRecordId = u.PaymentRecordId,
                Status = u.Status,
                CreateOrgId = u.CreateOrgId,
                CreateOrgName = u.CreateOrgName,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取预交金详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取预交金详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<AdvancePayment> Detail([FromQuery] QueryByIdAdvancePaymentInput input)
    {
        return await _advancePaymentRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加预交金 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加预交金")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddAdvancePaymentInput input)
    {
        var entity = input.Adapt<AdvancePayment>();
        return await _advancePaymentRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新预交金 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新预交金")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateAdvancePaymentInput input)
    {
        var entity = input.Adapt<AdvancePayment>();
        await _advancePaymentRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除预交金 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除预交金")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteAdvancePaymentInput input)
    {
        var entity = await _advancePaymentRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _advancePaymentRep.FakeDeleteAsync(entity);   //假删除
        //await _advancePaymentRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除预交金 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除预交金")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteAdvancePaymentInput> input)
    {
        var exp = Expressionable.Create<AdvancePayment>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _advancePaymentRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _advancePaymentRep.FakeDeleteAsync(list);   //假删除
        //return await _advancePaymentRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataAdvancePaymentInput input)
    {
        var patientIdData = await _advancePaymentRep.Context.Queryable<PatientInfo>()
            .InnerJoinIF<AdvancePayment>(input.FromPage, (u, r) => u.Id == r.PatientId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.Name}"
            }).ToListAsync();
        var registerIdData = await _advancePaymentRep.Context.Queryable<InpatientRegister>()
            .InnerJoinIF<AdvancePayment>(input.FromPage, (u, r) => u.Id == r.RegisterId)
            .Select(u => new {
                Value = u.Id, Label = $"{u.PatientId}-{u.PatientName}-{u.InpatientNo}-{u.InpatientTime}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "patientId", patientIdData },
            { "registerId", registerIdData },
        };
    }
    
    /// <summary>
    /// 导出预交金记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出预交金记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageAdvancePaymentInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportAdvancePaymentOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "预交金导出记录");
    }
    
    /// <summary>
    /// 下载预交金数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载预交金数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportAdvancePaymentOutput>(), "预交金导入模板", (_, info) =>
        {
            if (nameof(ExportAdvancePaymentOutput.PatientFkDisplayName) == info.Name) return _advancePaymentRep.Context.Queryable<PatientInfo>().Select(u => $"{u.Name}").Distinct().ToList();
            if (nameof(ExportAdvancePaymentOutput.RegisterFkDisplayName) == info.Name) return _advancePaymentRep.Context.Queryable<InpatientRegister>().Select(u => $"{u.PatientId}-{u.PatientName}-{u.InpatientNo}-{u.InpatientTime}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入预交金记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入预交金记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportAdvancePaymentInput, AdvancePayment>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 患者ID
                    var patientIdLabelList = pageItems.Where(x => x.PatientFkDisplayName != null).Select(x => x.PatientFkDisplayName).Distinct().ToList();
                    if (patientIdLabelList.Any()) {
                        var patientIdLinkMap = _advancePaymentRep.Context.Queryable<PatientInfo>().Where(u => patientIdLabelList.Contains($"{u.Name}")).ToList().ToDictionary(u => $"{u.Name}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.PatientId = patientIdLinkMap.GetValueOrDefault(e.PatientFkDisplayName ?? "");
                            if (e.PatientId == null) e.Error = "患者ID链接失败";
                        });
                    }
                    // 链接 住院登记号
                    var registerIdLabelList = pageItems.Where(x => x.RegisterFkDisplayName != null).Select(x => x.RegisterFkDisplayName).Distinct().ToList();
                    if (registerIdLabelList.Any()) {
                        var registerIdLinkMap = _advancePaymentRep.Context.Queryable<InpatientRegister>().Where(u => registerIdLabelList.Contains($"{u.PatientId}-{u.PatientName}-{u.InpatientNo}-{u.InpatientTime}")).ToList().ToDictionary(u => $"{u.PatientId}-{u.PatientName}-{u.InpatientNo}-{u.InpatientTime}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.RegisterId = registerIdLinkMap.GetValueOrDefault(e.RegisterFkDisplayName ?? "");
                            if (e.RegisterId == null) e.Error = "住院登记号链接失败";
                        });
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<AdvancePayment>>();
                    
                    var storageable = _advancePaymentRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.VoucherNo?.Length > 100, "收据号长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientNo?.Length > 100, "住院号长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientSerialNo?.Length > 100, "住院流水号长度不能超过100个字符")
                        .SplitError(it => it.Item.MedicalRecordNo?.Length > 100, "病案号长度不能超过100个字符")
                        .SplitError(it => it.Item.InsuranceNo?.Length > 100, "保险号码长度不能超过100个字符")
                        .SplitError(it => it.Item.AdvanceAmountChinese?.Length > 100, "大写金额长度不能超过100个字符")
                        .SplitError(it => it.Item.PaymentMethod?.Length > 100, "付款方式长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
