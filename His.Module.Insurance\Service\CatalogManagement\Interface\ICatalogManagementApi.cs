﻿using His.Module.Insurance.Service.CatalogManagement.Dto;
namespace His.Module.Insurance.Service.CatalogManagement.Interface;

/// <summary>
/// 目录管理接口
/// </summary>
public interface ICatalogManagementApi : ITransient
{
    #region 自付比例管理

    /// <summary>
    /// 获取自付比例
    /// </summary>
    Task<GetZfblResponse> GetZfbl(GetZfblRequest request);

    #endregion

    #region 医院项目目录

    /// <summary>
    /// 查询医院项目目录
    /// </summary>
    Task<QueryYyxmInfoResponse> QueryYyxmInfo(QueryYyxmInfoRequest request);

    /// <summary>
    /// 增量查询下载医院项目目录
    /// </summary>
    Task<QueryYyxmInfoBySxhResponse> QueryYyxmInfoBySxh(QueryYyxmInfoBySxhRequest request);

    /// <summary>
    /// 新增或更新医院项目
    /// </summary>
    Task<AddYyxmResponse> AddYyxm(AddYyxmRequest request);

    #endregion

    #region 医保目录

    /// <summary>
    /// 查询医保核心端目录
    /// </summary>
    Task<QueryMlResponse> QueryMl(QueryMlRequest request);

    /// <summary>
    /// 增量查询下载医保核心端目录
    /// </summary>
    Task<QueryMlBySxhResponse> QueryMlBySxh(QueryMlBySxhRequest request);

    #endregion

    #region 疾病目录

    /// <summary>
    /// 查询医保疾病目录
    /// </summary>
    Task<QuerySiSickResponse> QuerySiSick(QuerySiSickRequest request);

    /// <summary>
    /// 增量查询下载医保疾病目录
    /// </summary>
    Task<QuerySiSickBySxhResponse> QuerySiSickBySxh(QuerySiSickBySxhRequest request);

    #endregion

    #region 手术目录

    /// <summary>
    /// 查询医保手术目录
    /// </summary>
    Task<QueryOperationResponse> QueryOperation(QueryOperationRequest request);

    #endregion

    #region 医师管理

    /// <summary>
    /// 查询医院医师信息
    /// </summary>
    Task<QueryYsResponse> QueryYs(QueryYsRequest request);

    /// <summary>
    /// 新增或更新医师信息
    /// </summary>
    Task<AddYsResponse> AddYs(AddYsRequest request);

    #endregion

    #region 科室管理

    /// <summary>
    /// 查询医院医保科室信息
    /// </summary>
    Task<QueryHospDeptResponse> QueryHospDept(QueryHospDeptRequest request);

    #endregion

    #region 数据字典

    /// <summary>
    /// 获取数据字典
    /// </summary>
    Task<QuerySiCodeResponse> QuerySiCode(QuerySiCodeRequest request);

    #endregion
}