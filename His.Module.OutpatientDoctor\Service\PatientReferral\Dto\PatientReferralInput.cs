﻿using Admin.NET.Core;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using System.ComponentModel.DataAnnotations;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 患者转介表基础输入参数
/// </summary>
public class PatientReferralBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required(ErrorMessage = "患者Id不能为空")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 转介前医生Id
    /// </summary>
    public virtual long? BeforeDoctorId { get; set; }
    
    /// <summary>
    /// 转介前医生姓名
    /// </summary>
    public virtual string? BeforeDoctorName { get; set; }
    
    /// <summary>
    /// 转介前科室Id
    /// </summary>
    public virtual long? BeforeDeptId { get; set; }
    
    /// <summary>
    /// 转介前科室名称
    /// </summary>
    public virtual string? BeforeDeptName { get; set; }
    
    /// <summary>
    /// 转介后医生Id
    /// </summary>
    public virtual long? AfterDoctorId { get; set; }
    
    /// <summary>
    /// 转介后医生姓名
    /// </summary>
    public virtual string? AfterDoctorName { get; set; }
    
    /// <summary>
    /// 转介后科室Id
    /// </summary>
    public virtual long? AfterDeptId { get; set; }
    
    /// <summary>
    /// 转介后科室名称
    /// </summary>
    public virtual string? AfterDeptName { get; set; }
    
    /// <summary>
    /// 转介时间
    /// </summary>
    public virtual DateTime? ReferralTime { get; set; }
    
    /// <summary>
    /// 转介原因
    /// </summary>
    public virtual string? ReferralReason { get; set; }
    
    /// <summary>
    /// 转介前挂号记录Id
    /// </summary>
    public virtual long? BeforeRegisterId { get; set; }
    
    /// <summary>
    /// 转介后挂号记录Id
    /// </summary>
    public virtual long? AfterRegisterId { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 患者转介表分页查询输入参数
/// </summary>
public class PagePatientReferralInput : BasePageInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 转介前医生Id
    /// </summary>
    public long? BeforeDoctorId { get; set; }
    
    /// <summary>
    /// 转介前医生姓名
    /// </summary>
    public string? BeforeDoctorName { get; set; }
    
    /// <summary>
    /// 转介前科室Id
    /// </summary>
    public long? BeforeDeptId { get; set; }
    
    /// <summary>
    /// 转介前科室名称
    /// </summary>
    public string? BeforeDeptName { get; set; }
    
    /// <summary>
    /// 转介后医生Id
    /// </summary>
    public long? AfterDoctorId { get; set; }
    
    /// <summary>
    /// 转介后医生姓名
    /// </summary>
    public string? AfterDoctorName { get; set; }
    
    /// <summary>
    /// 转介后科室Id
    /// </summary>
    public long? AfterDeptId { get; set; }
    
    /// <summary>
    /// 转介后科室名称
    /// </summary>
    public string? AfterDeptName { get; set; }
    
    /// <summary>
    /// 转介时间范围
    /// </summary>
     public DateTime?[] ReferralTimeRange { get; set; }
    
    /// <summary>
    /// 转介原因
    /// </summary>
    public string? ReferralReason { get; set; }
    
    /// <summary>
    /// 转介前挂号记录Id
    /// </summary>
    public long? BeforeRegisterId { get; set; }
    
    /// <summary>
    /// 转介后挂号记录Id
    /// </summary>
    public long? AfterRegisterId { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 患者转介表增加输入参数
/// </summary>
public class AddPatientReferralInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required(ErrorMessage = "患者Id不能为空")]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者名称字符长度不能超过64")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [MaxLength(64, ErrorMessage = "门诊号字符长度不能超过64")]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 转介前医生Id
    /// </summary>
    public long? BeforeDoctorId { get; set; }
    
    /// <summary>
    /// 转介前医生姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "转介前医生姓名字符长度不能超过64")]
    public string? BeforeDoctorName { get; set; }
    
    /// <summary>
    /// 转介前科室Id
    /// </summary>
    public long? BeforeDeptId { get; set; }
    
    /// <summary>
    /// 转介前科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "转介前科室名称字符长度不能超过64")]
    public string? BeforeDeptName { get; set; }
    
    /// <summary>
    /// 转介后医生Id
    /// </summary>
    public long? AfterDoctorId { get; set; }
    
    /// <summary>
    /// 转介后医生姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "转介后医生姓名字符长度不能超过64")]
    public string? AfterDoctorName { get; set; }
    
    /// <summary>
    /// 转介后科室Id
    /// </summary>
    public long? AfterDeptId { get; set; }
    
    /// <summary>
    /// 转介后科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "转介后科室名称字符长度不能超过64")]
    public string? AfterDeptName { get; set; }
    
    /// <summary>
    /// 转介时间
    /// </summary>
    public DateTime? ReferralTime { get; set; }
    /// <summary>
    /// 预约挂号时间
    /// </summary>
    public DateTime? RegTime { get; set; }
    /// <summary>
    /// 转介原因
    /// </summary>
    [Required(ErrorMessage = "转介原因不能为空"), MaxLength(256, ErrorMessage = "转介原因字符长度不能超过256")]
    public string? ReferralReason { get; set; }
    
    /// <summary>
    /// 转介前挂号记录Id
    /// </summary>
    public long? BeforeRegisterId { get; set; }
    
    /// <summary>
    /// 转介后挂号记录Id
    /// </summary>
    public long? AfterRegisterId { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注信息字符长度不能超过256")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 患者转介表删除输入参数
/// </summary>
public class DeletePatientReferralInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

 
/// <summary>
/// 患者转介表主键查询输入参数
/// </summary>
public class QueryByIdPatientReferralInput : DeletePatientReferralInput
{
}

/// <summary>
/// 患者转介表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportPatientReferralInput : BaseImportInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [ImporterHeader(Name = "*患者Id")]
    [ExporterHeader("*患者Id", Format = "", Width = 25, IsBold = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    [ImporterHeader(Name = "患者名称")]
    [ExporterHeader("患者名称", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [ImporterHeader(Name = "门诊号")]
    [ExporterHeader("门诊号", Format = "", Width = 25, IsBold = true)]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 转介前医生Id
    /// </summary>
    [ImporterHeader(Name = "转介前医生Id")]
    [ExporterHeader("转介前医生Id", Format = "", Width = 25, IsBold = true)]
    public long? BeforeDoctorId { get; set; }
    
    /// <summary>
    /// 转介前医生姓名
    /// </summary>
    [ImporterHeader(Name = "转介前医生姓名")]
    [ExporterHeader("转介前医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? BeforeDoctorName { get; set; }
    
    /// <summary>
    /// 转介前科室Id
    /// </summary>
    [ImporterHeader(Name = "转介前科室Id")]
    [ExporterHeader("转介前科室Id", Format = "", Width = 25, IsBold = true)]
    public long? BeforeDeptId { get; set; }
    
    /// <summary>
    /// 转介前科室名称
    /// </summary>
    [ImporterHeader(Name = "转介前科室名称")]
    [ExporterHeader("转介前科室名称", Format = "", Width = 25, IsBold = true)]
    public string? BeforeDeptName { get; set; }
    
    /// <summary>
    /// 转介后医生Id
    /// </summary>
    [ImporterHeader(Name = "转介后医生Id")]
    [ExporterHeader("转介后医生Id", Format = "", Width = 25, IsBold = true)]
    public long? AfterDoctorId { get; set; }
    
    /// <summary>
    /// 转介后医生姓名
    /// </summary>
    [ImporterHeader(Name = "转介后医生姓名")]
    [ExporterHeader("转介后医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? AfterDoctorName { get; set; }
    
    /// <summary>
    /// 转介后科室Id
    /// </summary>
    [ImporterHeader(Name = "转介后科室Id")]
    [ExporterHeader("转介后科室Id", Format = "", Width = 25, IsBold = true)]
    public long? AfterDeptId { get; set; }
    
    /// <summary>
    /// 转介后科室名称
    /// </summary>
    [ImporterHeader(Name = "转介后科室名称")]
    [ExporterHeader("转介后科室名称", Format = "", Width = 25, IsBold = true)]
    public string? AfterDeptName { get; set; }
    
    /// <summary>
    /// 转介时间
    /// </summary>
    [ImporterHeader(Name = "转介时间")]
    [ExporterHeader("转介时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? ReferralTime { get; set; }
    
    /// <summary>
    /// 转介原因
    /// </summary>
    [ImporterHeader(Name = "转介原因")]
    [ExporterHeader("转介原因", Format = "", Width = 25, IsBold = true)]
    public string? ReferralReason { get; set; }
    
    /// <summary>
    /// 转介前挂号记录Id
    /// </summary>
    [ImporterHeader(Name = "转介前挂号记录Id")]
    [ExporterHeader("转介前挂号记录Id", Format = "", Width = 25, IsBold = true)]
    public long? BeforeRegisterId { get; set; }
    
    /// <summary>
    /// 转介后挂号记录Id
    /// </summary>
    [ImporterHeader(Name = "转介后挂号记录Id")]
    [ExporterHeader("转介后挂号记录Id", Format = "", Width = 25, IsBold = true)]
    public long? AfterRegisterId { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    [ImporterHeader(Name = "状态，默认1（有效）")]
    [ExporterHeader("状态，默认1（有效）", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    [ImporterHeader(Name = "备注信息")]
    [ExporterHeader("备注信息", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
