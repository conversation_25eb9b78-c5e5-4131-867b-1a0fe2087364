﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    </PropertyGroup>

    <ItemGroup>
      <None Update="Configuration\InpatientNurse.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Admin.NET.Core\Admin.NET.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Entity\" />
      <Folder Include="Service\" />
    </ItemGroup>

</Project>
