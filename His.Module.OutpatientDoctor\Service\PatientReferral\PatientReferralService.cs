﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using Microsoft.AspNetCore.Http;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 患者转介表服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class PatientReferralService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<PatientReferral> _patientReferralRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public PatientReferralService(SqlSugarRepository<PatientReferral> patientReferralRep, ISqlSugarClient sqlSugarClient)
    {
        _patientReferralRep = patientReferralRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询患者转介表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询患者转介表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<PatientReferralOutput>> Page(PagePatientReferralInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _patientReferralRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.PatientName.Contains(input.Keyword) || u.OutpatientNo.Contains(input.Keyword) || u.BeforeDoctorName.Contains(input.Keyword) || u.BeforeDeptName.Contains(input.Keyword) || u.AfterDoctorName.Contains(input.Keyword) || u.AfterDeptName.Contains(input.Keyword) || u.ReferralReason.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo), u => u.OutpatientNo.Contains(input.OutpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BeforeDoctorName), u => u.BeforeDoctorName.Contains(input.BeforeDoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BeforeDeptName), u => u.BeforeDeptName.Contains(input.BeforeDeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AfterDoctorName), u => u.AfterDoctorName.Contains(input.AfterDoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AfterDeptName), u => u.AfterDeptName.Contains(input.AfterDeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ReferralReason), u => u.ReferralReason.Contains(input.ReferralReason.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.BeforeDoctorId != null, u => u.BeforeDoctorId == input.BeforeDoctorId)
            .WhereIF(input.BeforeDeptId != null, u => u.BeforeDeptId == input.BeforeDeptId)
            .WhereIF(input.AfterDoctorId != null, u => u.AfterDoctorId == input.AfterDoctorId)
            .WhereIF(input.AfterDeptId != null, u => u.AfterDeptId == input.AfterDeptId)
            .WhereIF(input.ReferralTimeRange?.Length == 2, u => u.ReferralTime >= input.ReferralTimeRange[0] && u.ReferralTime <= input.ReferralTimeRange[1])
            .WhereIF(input.BeforeRegisterId != null, u => u.BeforeRegisterId == input.BeforeRegisterId)
            .WhereIF(input.AfterRegisterId != null, u => u.AfterRegisterId == input.AfterRegisterId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<PatientReferralOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取患者转介表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取患者转介表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<PatientReferral> Detail([FromQuery] QueryByIdPatientReferralInput input)
    {
        return await _patientReferralRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加患者转介表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加患者转介表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddPatientReferralInput input)
    {
        var entity = input.Adapt<PatientReferral>();
        entity.ReferralTime=DateTime.Now;
      
        return await _patientReferralRep.InsertAsync(entity) ? entity.Id : 0;
    }

     

    /// <summary>
    /// 删除患者转介表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除患者转介表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeletePatientReferralInput input)
    {
        var entity = await _patientReferralRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _patientReferralRep.FakeDeleteAsync(entity);   //假删除
        //await _patientReferralRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除患者转介表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除患者转介表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeletePatientReferralInput> input)
    {
        var exp = Expressionable.Create<PatientReferral>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _patientReferralRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _patientReferralRep.FakeDeleteAsync(list);   //假删除
        //return await _patientReferralRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出患者转介表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出患者转介表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PagePatientReferralInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportPatientReferralOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "患者转介表导出记录");
    }
    
    /// <summary>
    /// 下载患者转介表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载患者转介表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportPatientReferralOutput>(), "患者转介表导入模板");
    }
    
    /// <summary>
    /// 导入患者转介表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入患者转介表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportPatientReferralInput, PatientReferral>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.PatientId == null){
                            x.Error = "患者Id不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<PatientReferral>>();
                    
                    var storageable = _patientReferralRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.PatientName?.Length > 64, "患者名称长度不能超过64个字符")
                        .SplitError(it => it.Item.OutpatientNo?.Length > 64, "门诊号长度不能超过64个字符")
                        .SplitError(it => it.Item.BeforeDoctorName?.Length > 64, "转介前医生姓名长度不能超过64个字符")
                        .SplitError(it => it.Item.BeforeDeptName?.Length > 64, "转介前科室名称长度不能超过64个字符")
                        .SplitError(it => it.Item.AfterDoctorName?.Length > 64, "转介后医生姓名长度不能超过64个字符")
                        .SplitError(it => it.Item.AfterDeptName?.Length > 64, "转介后科室名称长度不能超过64个字符")
                        .SplitError(it => it.Item.ReferralReason?.Length > 256, "转介原因长度不能超过256个字符")
                        .SplitError(it => it.Item.Remark?.Length > 256, "备注信息长度不能超过256个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
