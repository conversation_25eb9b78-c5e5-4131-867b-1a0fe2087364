﻿namespace His.Module.Insurance.Entity;

/// <summary>
/// 医师信息
/// </summary>
[Tenant("1300000000013")]
[SugarTable("doctor_info", "医师信息")]
public class DoctorInfo : EntityTenant
{
    /// <summary>
    /// 医师编码
    /// </summary>
    [SugarColumn(ColumnName = "ys_bm", ColumnDescription = "医师编码", Length = 50)]
    public virtual string? YsBm { get; set; }

    /// <summary>
    /// 医师姓名
    /// </summary>
    [SugarColumn(ColumnName = "ys_xm", ColumnDescription = "医师姓名", Length = 100)]
    public virtual string? YsXm { get; set; }

    /// <summary>
    /// 医师性别
    /// </summary>
    [SugarColumn(ColumnName = "ys_xb", ColumnDescription = "医师性别", Length = 10)]
    public virtual string? YsXb { get; set; }

    /// <summary>
    /// 医师身份证号
    /// </summary>
    [SugarColumn(ColumnName = "ys_sfzh", ColumnDescription = "医师身份证号", Length = 20)]
    public virtual string? YsSfzh { get; set; }

    /// <summary>
    /// 医师执业证号
    /// </summary>
    [SugarColumn(ColumnName = "ys_zyzzh", ColumnDescription = "医师执业证号", Length = 50)]
    public virtual string? YsZyzzh { get; set; }

    /// <summary>
    /// 医师级别
    /// </summary>
    [SugarColumn(ColumnName = "ys_jb", ColumnDescription = "医师级别", Length = 10)]
    public virtual string? YsJb { get; set; }

    /// <summary>
    /// 医师类别
    /// </summary>
    [SugarColumn(ColumnName = "ys_lb", ColumnDescription = "医师类别", Length = 10)]
    public virtual string? YsLb { get; set; }

    /// <summary>
    /// 科室编码
    /// </summary>
    [SugarColumn(ColumnName = "ks_bm", ColumnDescription = "科室编码", Length = 50)]
    public virtual string? KsBm { get; set; }

    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "ks_mc", ColumnDescription = "科室名称", Length = 200)]
    public virtual string? KsMc { get; set; }

    /// <summary>
    /// 医师状态
    /// </summary>
    [SugarColumn(ColumnName = "ys_zt", ColumnDescription = "医师状态", Length = 10)]
    public virtual string? YsZt { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "bz", ColumnDescription = "备注", Length = 500)]
    public virtual string? Bz { get; set; }

    /// <summary>
    /// 同步序号
    /// </summary>
    [SugarColumn(ColumnName = "sxh", ColumnDescription = "同步序号")]
    public virtual long? Sxh { get; set; }

    /// <summary>
    /// 最后同步时间
    /// </summary>
    [SugarColumn(ColumnName = "last_sync_time", ColumnDescription = "最后同步时间")]
    public virtual DateTime? LastSyncTime { get; set; }
}
