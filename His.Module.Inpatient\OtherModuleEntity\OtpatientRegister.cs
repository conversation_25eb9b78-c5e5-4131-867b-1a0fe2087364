﻿namespace His.Module.Inpatient.OtherModuleEntity;

/// <summary>
/// 挂号记录表
/// </summary>

[SugarTable("register", "挂号记录表")]
[Tenant("1300000000005")]
public class OutpatientRegister : EntityTenant
{
    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 64)]
    public virtual string VisitNo { get; set; }

    /// <summary>
    /// 就诊次数
    /// </summary>
    [SugarColumn(ColumnName = "visit_num", ColumnDescription = "就诊次数")]
    public virtual int? VisitNum { get; set; }
 

    /// <summary>
    /// 性别
    /// </summary>
    [SugarColumn(ColumnName = "sex", ColumnDescription = "性别")]
    public virtual GenderEnum Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    [SugarColumn(ColumnName = "age", ColumnDescription = "年龄")]
    public virtual int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [SugarColumn(ColumnName = "age_unit", ColumnDescription = "年龄单位", Length = 32)]
    public virtual string AgeUnit { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    [SugarColumn(ColumnName = "birthday", ColumnDescription = "出生日期")]
    public virtual DateTime? Birthday { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    [SugarColumn(ColumnName = "card_type", ColumnDescription = "证件类型", Length = 32)]
    public virtual CardTypeEnum? CardType { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    [SugarColumn(ColumnName = "id_card_no", ColumnDescription = "身份证号", Length = 32)]
    public virtual string IdCardNo { get; set; }
 

    /// <summary>
    /// 保险号码
    /// </summary>
    [SugarColumn(ColumnName = "insurance_num", ColumnDescription = "保险号码", Length = 64)]
    public virtual string InsuranceNum { get; set; }

    /// <summary>
    /// 保险类型
    /// </summary>
    [SugarColumn(ColumnName = "insurance_type", ColumnDescription = "保险类型", Length = 16)]
    public virtual string InsuranceType { get; set; }
 

    /// <summary>
    /// 就诊类型 初诊|复诊
    /// </summary>
    [SugarColumn(ColumnName = "visit_type", ColumnDescription = "就诊类型 初诊|复诊")]
    public virtual short? VisitType { get; set; }

    /// <summary>
    /// 就诊科室id
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "就诊科室id")]
    public virtual long? DeptId { get; set; }

    /// <summary>
    /// 医生id
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "医生id")]
    public virtual long? DoctorId { get; set; }

    /// <summary>
    /// 挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 症状
    /// </summary>
    [SugarColumn(ColumnName = "symptom", ColumnDescription = "症状", Length = 128)]
    public virtual string Symptom { get; set; }
 
 

    /// <summary>
    /// 门诊号 第一次就诊的流水号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号 第一次就诊的流水号")]
    public virtual string? OutpatientNo { get; set; }
    [SugarColumn(ColumnName = "card_id", ColumnDescription = "就诊卡id")]
    public virtual long? CardId { get; set; }
     

 

    /// <summary>
    /// 诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic_name", ColumnDescription = "诊断名称", Length = 128)]
    public virtual string DiagnosticName { get; set; }
     

    /// <summary>
    /// 诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic_code", ColumnDescription = "诊断编号", Length = 128)]
    public virtual string DiagnosticCode { get; set; }
    

     
}