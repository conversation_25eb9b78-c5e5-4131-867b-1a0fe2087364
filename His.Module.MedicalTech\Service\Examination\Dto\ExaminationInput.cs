﻿using Furion.DataValidation;

namespace His.Module.MedicalTech;

/// <summary>
/// 检查基础输入参数
/// </summary>
public class ExaminationBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>
    public virtual string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    public virtual long? RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    public virtual string VisitNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public virtual long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string PatientName { get; set; }

    /// <summary>
    /// 检查类别Id
    /// </summary>
    [Required(ErrorMessage = "检查类别Id不能为空")]
    public virtual long? CheckCategoryId { get; set; }

    /// <summary>
    /// 检查类别名称
    /// </summary>
    [Required(ErrorMessage = "检查类别名称不能为空")]
    public virtual string? CheckCategoryName { get; set; }

    /// <summary>
    /// 检查部位Id
    /// </summary>
    public virtual long? CheckPointId { get; set; }

    /// <summary>
    /// 检查部位名称
    /// </summary>
    public virtual string? CheckPointName { get; set; }

    /// <summary>
    /// 检查目的
    /// </summary>
    public virtual string? CheckObjective { get; set; }

    /// <summary>
    /// 临床诊断
    /// </summary>
    public virtual string? ClinicalDiagnosis { get; set; }

    /// <summary>
    /// 病历摘要
    /// </summary>
    public virtual string? MedicalRecordSummary { get; set; }

    /// <summary>
    /// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    /// </summary>
    public virtual int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 门诊住院标识
    /// </summary>
    public virtual int? Flag { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    public virtual DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public virtual long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    public virtual string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public virtual long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    public virtual string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public virtual DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    public virtual long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    public virtual string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    public virtual string? ExecuteDeptAddress { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    public virtual long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    public virtual string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public virtual long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费人员名称
    /// </summary>
    public virtual string? ChargeStaffName { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public virtual DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 医生签名
    /// </summary>
    public virtual string? DoctorSign { get; set; }

    /// <summary>
    /// 医嘱Id
    /// </summary>
    public virtual long? MedicalAdviceId { get; set; }
}

/// <summary>
/// 检查分页查询输入参数
/// </summary>
public class PageExaminationInput : BasePageInput
{
    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required(ErrorMessage = "就诊Id不能为空")]
    public long RegisterId { get; set; }

    /// <summary>
    /// 0 门诊 1住院
    /// </summary>
    [Required(ErrorMessage = "门诊住院标志不能为空")]
    public int Flag { get; set; }
}

/// <summary>
/// 检查增加输入参数
/// </summary>
public class AddExaminationInput
{
    /// <summary>
    /// 申请单号
    /// </summary>
    [MaxLength(64, ErrorMessage = "申请单号字符长度不能超过64")]
    public string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required(ErrorMessage = "就诊Id不能为空")]
    public long? RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    [Required(ErrorMessage = "就诊流水号不能为空")]
    [MaxLength(64, ErrorMessage = "就诊流水号字符长度不能超过64")]
    public string VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [Required(ErrorMessage = "门诊号不能为空")]
    public string OutpatientNo { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [Required(ErrorMessage = "就诊卡号不能为空")]
    public string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [Required(ErrorMessage = "患者Id不能为空")]
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [Required(ErrorMessage = "患者姓名不能为空")]
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string PatientName { get; set; }

    /// <summary>
    /// 检查类别Id
    /// </summary>
    [Required(ErrorMessage = "检查类别Id不能为空")]
    public long? CheckCategoryId { get; set; }

    /// <summary>
    /// 检查类别名称
    /// </summary>
    [Required(ErrorMessage = "检查类别名称不能为空")]
    [MaxLength(64, ErrorMessage = "检查类别名称字符长度不能超过64")]
    public string? CheckCategoryName { get; set; }

    /// <summary>
    /// 检查部位Id
    /// </summary>
    public long? CheckPointId { get; set; }

    /// <summary>
    /// 检查部位名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "检查部位名称字符长度不能超过64")]
    public string? CheckPointName { get; set; }

    /// <summary>
    /// 检查目的
    /// </summary>
    [MaxLength(200, ErrorMessage = "检查目的字符长度不能超过200")]
    public string? CheckObjective { get; set; }

    /// <summary>
    /// 临床诊断
    /// </summary>
    [MaxLength(200, ErrorMessage = "临床诊断字符长度不能超过200")]
    public string? ClinicalDiagnosis { get; set; }

    /// <summary>
    /// 病历摘要
    /// </summary>
    [MaxLength(500, ErrorMessage = "病历摘要字符长度不能超过500")]
    public string? MedicalRecordSummary { get; set; }

    /// <summary>
    /// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注字符长度不能超过256")]
    public string? Remark { get; set; }

    /// <summary>
    /// 门诊住院标识
    /// </summary>
    public int? Flag { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    public DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "开单科室名称字符长度不能超过64")]
    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "开单医生名称字符长度不能超过64")]
    public string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "执行科室名称字符长度不能超过64")]
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    [MaxLength(100, ErrorMessage = "执行科室地址字符长度不能超过100")]
    public string? ExecuteDeptAddress { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    public long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "执行医生名称字符长度不能超过64")]
    public string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费人员名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "收费人员名称字符长度不能超过64")]
    public string? ChargeStaffName { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 医生签名
    /// </summary>
    public string? DoctorSign { get; set; }

    /// <summary>
    /// 医嘱Id
    /// </summary>
    public long? MedicalAdviceId { get; set; }

    /// <summary>
    /// 检查明细列表
    /// </summary>
    public List<AddExaminationDetailsInput> ExaminationDetails { get; set; }
}

/// <summary>
/// 检查删除输入参数
/// </summary>
public class DeleteExaminationInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 检查更新输入参数
/// </summary>
public class UpdateExaminationInput : AddExaminationInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 检查主键查询输入参数
/// </summary>
public class QueryByIdExaminationInput : DeleteExaminationInput
{
}

/// <summary>
/// 检查明细增加输入参数
/// </summary>
public class AddExaminationDetailsInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// 检查Id
    /// </summary>

    public virtual long ExaminationId { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>

    public virtual string ApplyNo { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>

    public virtual long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>

    public virtual string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>

    public virtual string? ItemName { get; set; }

    /// <summary>
    /// 单位
    /// </summary>

    public virtual string? Unit { get; set; }

    /// <summary>
    /// 单价
    /// </summary>

    public virtual decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [Required(ErrorMessage = "数量不能为空")]
    [DataValidation(ValidationTypes.PositiveNumber, ErrorMessage = "数量必须大于0")]
    public virtual decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>

    public virtual decimal? Amount { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>

    public virtual int? IsPackage { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>

    public virtual string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>

    public virtual string? NationalstandardCode { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>

    public virtual long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    [Required(ErrorMessage = "执行科室不能为空")]
    public virtual long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>

    public virtual string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>

    public virtual decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>

    public virtual int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>

    public virtual DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人员Id
    /// </summary>

    public virtual long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人员名称
    /// </summary>

    public virtual string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>

    public virtual string? Remark { get; set; }
}

/// <summary>
/// 删除检查明细输入参数
/// </summary>
public class DeleteExaminationDetailInput
{
    /// <summary>
    /// 检查明细ID
    /// </summary>
    [Required(ErrorMessage = "检查明细ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 状态修改输入参数
/// </summary>
public class SetExaminationStatusInput : BaseIdInput
{
    /// <summary>
    /// 状态 字典 ApplyStatus
    /// </summary>
    [Required(ErrorMessage = "状态不能为空")]
    public int Status { get; set; }
}

/// <summary>
/// 检查收费输入参数
/// </summary>
public class ChargeExaminationInput : BaseIdInput
{
    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }
}