<template>
	<el-dialog v-model="state.isShowDialog" title="押金缴费" width="700px" :close-on-click-modal="false" :close-on-press-escape="false" draggable>
		<!-- 账户信息 -->
		<el-card shadow="never" style="margin-bottom: 20px">
			<template #header>
				<span>账户信息</span>
			</template>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="住院号">{{ state.accountInfo.inpatientNo }}</el-descriptions-item>
				<el-descriptions-item label="当前余额">
					<span :class="state.accountInfo.currentBalance > 0 ? 'text-success' : 'text-danger'"> ¥{{ (state.accountInfo.currentBalance || 0).toFixed(2) }} </span>
				</el-descriptions-item>
				<el-descriptions-item label="总缴费金额">
					<span class="text-success">¥{{ (state.accountInfo.totalPaidAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="总退款金额">
					<span class="text-warning">¥{{ (state.accountInfo.totalRefundedAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
			</el-descriptions>
		</el-card>

		<!-- 缴费表单 -->
		<el-form :model="state.ruleForm" :rules="state.rules" ref="ruleFormRef" label-width="120px" label-position="right">
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="缴费金额" prop="amount">
						<el-input-number v-model="state.ruleForm.amount" placeholder="请输入缴费金额" :min="0.01" :precision="2" style="width: 100%" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="支付渠道" prop="channel">
						<el-select v-model="state.ruleForm.channel" placeholder="请选择支付渠道">
							<el-option label="现金" value="CASH" />
							<el-option label="银行卡" value="BANK_CARD" />
							<el-option label="微信支付" value="WECHAT_PAY" />
							<el-option label="支付宝" value="ALIPAY" />
							<el-option label="医保卡" value="MEDICAL_CARD" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="支付方式" prop="payType">
						<el-select v-model="state.ruleForm.payType" placeholder="请选择支付方式">
							<el-option label="现金支付" value="CASH_PAY" />
							<el-option label="刷卡支付" value="CARD_PAY" />
							<el-option label="扫码支付" value="QR_PAY" />
							<el-option label="转账支付" value="TRANSFER_PAY" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="外部凭证号" prop="receiptNo">
						<el-input v-model="state.ruleForm.receiptNo" placeholder="请输入外部凭证号（可选）" clearable />
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="备注" prop="remark">
						<el-input v-model="state.ruleForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（可选）" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="closeDialog">取消</el-button>
				<el-button type="primary" @click="handleSubmit" :loading="state.loading"> 确认缴费 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="PaymentDialog">
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useDepositApi, type PaymentDepositInput, type DepositAccount } from '/@/api/inpatient/deposit';

// 定义 emits
const emit = defineEmits(['reload-data']);

// API实例
const depositApi = useDepositApi();

// 响应式数据
const state = reactive({
	isShowDialog: false,
	loading: false,
	accountInfo: {} as DepositAccount,
	ruleForm: {
		accountId: 0,
		amount: undefined,
		channel: '',
		payType: '',
		receiptNo: '',
		remark: '',
	} as PaymentDepositInput,
	rules: {
		amount: [
			{ required: true, message: '请输入缴费金额', trigger: 'blur' },
			{ type: 'number', min: 0.01, message: '缴费金额必须大于0', trigger: 'blur' },
		],
		channel: [{ required: true, message: '请选择支付渠道', trigger: 'change' }],
		payType: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
	},
});

// 引用
const ruleFormRef = ref();

// 打开对话框
const openDialog = (accountInfo: DepositAccount) => {
	state.accountInfo = { ...accountInfo };
	state.isShowDialog = true;
	resetForm();
	state.ruleForm.accountId = accountInfo.id || 0;
};

// 关闭对话框
const closeDialog = () => {
	state.isShowDialog = false;
	resetForm();
};

// 重置表单
const resetForm = () => {
	state.ruleForm = {
		accountId: 0,
		amount: undefined,
		channel: '',
		payType: '',
		receiptNo: '',
		remark: '',
	};
	ruleFormRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = async () => {
	try {
		const valid = await ruleFormRef.value?.validate();
		if (!valid) return;

		// 确认对话框
		await ElMessageBox.confirm(`确认缴费金额：¥${state.ruleForm.amount?.toFixed(2)}？`, '确认缴费', {
			confirmButtonText: '确认',
			cancelButtonText: '取消',
			type: 'warning',
		});

		state.loading = true;

		await depositApi.payment(state.ruleForm);

		ElMessage.success('押金缴费成功');
		closeDialog();
		emit('reload-data');
	} catch (error: any) {
		if (error !== 'cancel') {
			console.error('押金缴费失败:', error);
			ElMessage.error(error.message || '押金缴费失败');
		}
	} finally {
		state.loading = false;
	}
};

// 暴露方法
defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.dialog-footer {
	text-align: right;
}

.text-success {
	color: #67c23a;
	font-weight: bold;
}

.text-warning {
	color: #e6a23c;
	font-weight: bold;
}

.text-danger {
	color: #f56c6c;
	font-weight: bold;
}

:deep(.el-input),
:deep(.el-input-number),
:deep(.el-select) {
	width: 100%;
}
</style>
