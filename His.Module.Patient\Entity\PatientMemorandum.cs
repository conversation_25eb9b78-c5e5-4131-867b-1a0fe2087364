﻿using Admin.NET.Core;
namespace His.Module.Patient.Entity;

/// <summary>
/// 患者备忘录主表
/// </summary>
[Tenant("1300000000003")]
[SugarTable("patient_memorandum", "患者备忘录主表")]
public class PatientMemorandum : EntityTenant
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 64)]
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [SugarColumn(ColumnName = "id_card_no", ColumnDescription = "身份证号", Length = 64)]
    public virtual string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 64)]
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 备忘内容
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备忘内容", Length = 2000)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
