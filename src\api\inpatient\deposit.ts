import { useBaseApi } from '/@/api/base';

// 押金管理接口服务
export const useDepositApi = () => {
	const baseApi = useBaseApi('deposit');
	return {
		// 创建押金账户
		add: (data: any) => baseApi.post('add', data),

		// 获取押金账户详情
		detail: (data: any) => baseApi.get('detail', data),

		// 押金缴费
		payment: (data: any) => baseApi.post('payment', data),

		// 押金退款
		refund: (data: any) => baseApi.post('refund', data),

		// 红冲（冲正）
		correctPaymentError: (data: any) => baseApi.post('correctPaymentError', data),

		// 开始结算
		startSettlement: (data: any) => baseApi.post('startSettlement', data),

		// 分页查询押金账户
		page: baseApi.page,

		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	};
};

// 押金账户数据类型定义
export interface DepositAccount {
	id?: number;
	inpatientRegisterId?: number;
	inpatientNo?: string;
	patientId?: number;
	status?: number;
	totalPaidAmount?: number;
	totalRefundedAmount?: number;
	totalUsedAmount?: number;
	currentBalance?: number;
	closedTime?: string;
	createTime?: string;
	updateTime?: string;
	transactions?: DepositTransaction[];
}

// 押金交易记录数据类型定义
export interface DepositTransaction {
	id?: number;
	accountId?: number;
	transactionType?: string; // 0:缴费 1:退款 2:使用 3:冲正
	amount?: number;
	refundableAmount?: number;
	channel?: string;
	paymentMethod?: string;
	receiptNo?: string;
	status?: string; // 1:成功 0:失败
	invoiceNo?: string;
	remark?: string;
	createTime?: string;
	updateTime?: string;
}

// 创建押金账户输入参数
export interface AddDepositInput {
	inpatientRegisterId: number;
	inpatientNo: string;
	patientId: number;
}

// 押金缴费输入参数
export interface PaymentDepositInput {
	accountId: number;
	amount?: number;
	channel: string;
	payType: string;
	receiptNo?: string;
	remark?: string;
}

// 押金退款输入参数
export interface RefundDepositInput {
	accountId: number;
	totalRefundAmount?: number;
	reason?: string;
}

// 红冲输入参数
export interface CorrectPaymentErrorInput {
	originalTransactionId: number;
	correctAmount: number;
	reason?: string;
}

// 查询押金账户详情输入参数
export interface QueryByIdDepositInput {
	id: number;
}

// 根据住院号或患者ID查询账户输入参数
export interface FindAccountInput {
	inpatientNo?: string;
	patientId?: number;
}
