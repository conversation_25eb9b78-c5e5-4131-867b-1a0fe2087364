namespace His.Module.OutpatientDoctor.Api.Charge.Dto;

public class OutpatientChargeResult
{
    
    /// <summary>
    /// 收费记录Id
    /// </summary> 
    public  long ChargeId { get; set; }
    public  long BillingId { get; set; }
    /// <summary>
    /// 收费员Id
    /// </summary> 
    public  long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费员姓名
    /// </summary> 
    public  string? ChargeStaffName { get; set; }
 

    /// <summary>
    /// 计费时间
    /// </summary> 
    public  DateTime ChargeTime { get; set; }
    
    public  List<OutpatientChargeDetailsOutput> Details { get; set; }  
 
}
public class OutpatientChargeDetailsOutput
{
    
    /// <summary>
    /// 收费记录Id
    /// </summary> 
    public  long? ChargeId { get; set; }

    /// <summary>
    /// 收费员Id
    /// </summary> 
    public  long? ItemId { get; set; }
    /// <summary>
    /// 收费明细id
    /// </summary>
    public  long? ChargeDetailId { get; set; }
 
}