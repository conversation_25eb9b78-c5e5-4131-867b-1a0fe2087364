﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品盘点基础输入参数
/// </summary>
public class StorageTakingRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 盘点单号
    /// </summary>
    public virtual string? TakingNo { get; set; }
    
    /// <summary>
    /// 盘点时间
    /// </summary>
    public virtual DateTime? TakingTimeTokingTime { get; set; }
    
    /// <summary>
    /// 盘点结果
    /// </summary>
    [Dict("StorageTakingStatus", AllowNullValue=true)]

    public virtual int? TakingResult { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    public virtual decimal? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    public virtual decimal? TakingQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    public virtual decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    public virtual decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 药品盘点分页查询输入参数
/// </summary>
public class PageStorageTakingRecordInput : BasePageInput
{
    /// <summary>
    /// 盘点单号
    /// </summary>
    public string? TakingNo { get; set; }
    
    /// <summary>
    /// 盘点时间范围
    /// </summary>
     public DateTime?[] TakingTimeRange { get; set; }
    
    /// <summary>
    /// 盘点结果
    /// </summary>
    public int? TakingResult { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    public decimal? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    public decimal? TakingQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品盘点增加输入参数
/// </summary>
public class AddStorageTakingRecordInput
{
    /// <summary>
    /// 盘点单号
    /// </summary>
    [MaxLength(100, ErrorMessage = "盘点单号字符长度不能超过100")]
    public string? TakingNo { get; set; }
    
    /// <summary>
    /// 盘点时间
    /// </summary>
    public DateTime? TakingTime { get; set; }
    
    /// <summary>
    /// 盘点结果
    /// </summary>
    public int? TakingResult { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "库房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "库房名称字符长度不能超过100")]
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    public decimal? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    public decimal? TakingQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
}

/// <summary>
/// 药品盘点删除输入参数
/// </summary>
public class DeleteStorageTakingRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}
public class SubmitStorageTakingRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品盘点更新输入参数
/// </summary>
public class UpdateStorageTakingRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 盘点单号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "盘点单号字符长度不能超过100")]
    public string? TakingNo { get; set; }
    
    /// <summary>
    /// 盘点时间
    /// </summary>    
    public DateTime? TakingTime { get; set; }
    
    /// <summary>
    /// 盘点结果
    /// </summary>    
    public int? TakingResult { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>    
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "库房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "库房名称字符长度不能超过100")]
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>    
    public decimal? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>    
    public decimal? TakingQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>    
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>    
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    public List<UpdateStorageTakingDetailInput> Details { get; set; }
    
}

/// <summary>
/// 药品盘点主键查询输入参数
/// </summary>
public class QueryByIdStorageTakingRecordInput : DeleteStorageTakingRecordInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataStorageTakingRecordInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 药品盘点数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportStorageTakingRecordInput : BaseImportInput
{
    /// <summary>
    /// 盘点单号
    /// </summary>
    [ImporterHeader(Name = "盘点单号")]
    [ExporterHeader("盘点单号", Format = "", Width = 25, IsBold = true)]
    public string? TakingNo { get; set; }
    
    /// <summary>
    /// 盘点时间
    /// </summary>
    [ImporterHeader(Name = "盘点时间")]
    [ExporterHeader("盘点时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? TakingTime { get; set; }
    
    /// <summary>
    /// 盘点结果
    /// </summary>
    [ImporterHeader(Name = "盘点结果")]
    [ExporterHeader("盘点结果", Format = "", Width = 25, IsBold = true)]
    public int? TakingResult { get; set; }
    
    /// <summary>
    /// 库房 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房 文本
    /// </summary>
    [ImporterHeader(Name = "库房")]
    [ExporterHeader("库房", Format = "", Width = 25, IsBold = true)]
    public string StorageFkDisplayName { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [ImporterHeader(Name = "库房编码")]
    [ExporterHeader("库房编码", Format = "", Width = 25, IsBold = true)]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>
    [ImporterHeader(Name = "库房名称")]
    [ExporterHeader("库房名称", Format = "", Width = 25, IsBold = true)]
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    [ImporterHeader(Name = "现有数量")]
    [ExporterHeader("现有数量", Format = "", Width = 25, IsBold = true)]
    public decimal? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    [ImporterHeader(Name = "盘点数量")]
    [ExporterHeader("盘点数量", Format = "", Width = 25, IsBold = true)]
    public decimal? TakingQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    [ImporterHeader(Name = "现有零售价")]
    [ExporterHeader("现有零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    [ImporterHeader(Name = "盘点零售价")]
    [ExporterHeader("盘点零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 状态 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 状态 文本
    /// </summary>
    [Dict("StorageInOutStatus")]
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public string StatusDictLabel { get; set; }
    
}
