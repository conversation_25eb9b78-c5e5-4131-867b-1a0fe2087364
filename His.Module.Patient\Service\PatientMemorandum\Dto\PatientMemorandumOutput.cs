﻿using Magicodes.ExporterAndImporter.Core;

namespace His.Module.Patient;

/// <summary>
/// 患者备忘录主表输出参数
/// </summary>
public class PatientMemorandumOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 患者Id
    /// </summary>
    public long PatientId { get; set; }    
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }    
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }    
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }    
    
    /// <summary>
    /// 备忘内容
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    public long? CreateOrgId { get; set; }    
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    public string? CreateOrgName { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 患者备忘录主表数据导入模板实体
/// </summary>
public class ExportPatientMemorandumOutput : ImportPatientMemorandumInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
