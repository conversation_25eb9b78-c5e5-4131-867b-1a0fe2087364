<template>
	<el-dialog v-model="state.isShowDialog" title="红冲（冲正）" width="600px" :close-on-click-modal="false" :close-on-press-escape="false" draggable>
		<!-- 原始交易信息 -->
		<el-card shadow="never" style="margin-bottom: 20px">
			<template #header>
				<span>原始交易信息</span>
			</template>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="交易ID">{{ state.originalTransaction.id }}</el-descriptions-item>
				<el-descriptions-item label="原始金额">
					<span class="text-success">¥{{ (state.originalTransaction.amount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="支付渠道">{{ state.originalTransaction.channel }}</el-descriptions-item>
				<el-descriptions-item label="支付方式">{{ state.originalTransaction.paymentMethod }}</el-descriptions-item>
				<el-descriptions-item label="外部凭证号">{{ state.originalTransaction.receiptNo || '-' }}</el-descriptions-item>
				<el-descriptions-item label="发票号">{{ state.originalTransaction.invoiceNo || '-' }}</el-descriptions-item>
				<el-descriptions-item label="交易时间" :span="2">{{ state.originalTransaction.createTime }}</el-descriptions-item>
			</el-descriptions>
		</el-card>

		<!-- 冲正表单 -->
		<el-form :model="state.ruleForm" :rules="state.rules" ref="ruleFormRef" label-width="120px" label-position="right">
			<el-row :gutter="20">
				<el-col :span="24">
					<el-form-item label="正确金额" prop="correctAmount">
						<el-input-number v-model="state.ruleForm.correctAmount" placeholder="请输入正确的金额" :min="0" :precision="2" style="width: 100%" />
						<div class="form-tip">
							<span>原始金额：¥{{ (state.originalTransaction.amount || 0).toFixed(2) }}</span>
							<span v-if="state.ruleForm.correctAmount !== undefined" style="margin-left: 10px">
								差额：¥{{ Math.abs((state.originalTransaction.amount || 0) - state.ruleForm.correctAmount).toFixed(2) }}
								{{ (state.originalTransaction.amount || 0) > state.ruleForm.correctAmount ? '(退还)' : '(补缴)' }}
							</span>
						</div>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="冲正原因" prop="reason">
						<el-input v-model="state.ruleForm.reason" type="textarea" :rows="4" placeholder="请输入冲正原因，如：金额录入错误、支付方式错误等" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<!-- 冲正说明 -->
		<el-alert title="冲正说明" type="warning" :closable="false" style="margin-bottom: 20px">
			<template #default>
				<ul style="margin: 0; padding-left: 20px">
					<li>红冲操作将撤销原始交易，并按正确金额重新记账</li>
					<li>如果正确金额小于原始金额，差额将退还到账户</li>
					<li>如果正确金额大于原始金额，需要补缴差额</li>
					<li>红冲操作不可撤销，请谨慎操作</li>
					<li>原始交易的可退金额将被清零</li>
				</ul>
			</template>
		</el-alert>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="closeDialog">取消</el-button>
				<el-button type="danger" @click="handleSubmit" :loading="state.loading"> 确认红冲 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="CorrectDialog">
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useDepositApi, type CorrectPaymentErrorInput, type DepositTransaction } from '/@/api/inpatient/deposit';

// 定义 emits
const emit = defineEmits(['reload-data']);

// API实例
const depositApi = useDepositApi();

// 响应式数据
const state = reactive({
	isShowDialog: false,
	loading: false,
	originalTransaction: {} as DepositTransaction,
	ruleForm: {
		originalTransactionId: 0,
		correctAmount: 0,
		reason: '',
	} as CorrectPaymentErrorInput,
	rules: {
		correctAmount: [
			{ required: true, message: '请输入正确金额', trigger: 'blur' },
			{ type: 'number', min: 0, message: '正确金额不能小于0', trigger: 'blur' },
		],
		reason: [
			{ required: true, message: '请输入冲正原因', trigger: 'blur' },
			{ min: 1, max: 500, message: '冲正原因长度在1到500个字符', trigger: 'blur' },
		],
	},
});

// 引用
const ruleFormRef = ref();

// 打开对话框
const openDialog = (transaction: DepositTransaction) => {
	state.originalTransaction = { ...transaction };
	state.isShowDialog = true;
	resetForm();
	state.ruleForm.originalTransactionId = transaction.id || 0;
	state.ruleForm.correctAmount = transaction.amount || 0;
};

// 关闭对话框
const closeDialog = () => {
	state.isShowDialog = false;
	resetForm();
};

// 重置表单
const resetForm = () => {
	state.ruleForm = {
		originalTransactionId: 0,
		correctAmount: 0,
		reason: '',
	};
	ruleFormRef.value?.clearValidate();
};

// 提交表单
const handleSubmit = async () => {
	try {
		const valid = await ruleFormRef.value?.validate();
		if (!valid) return;

		const originalAmount = state.originalTransaction.amount || 0;
		const correctAmount = state.ruleForm.correctAmount || 0;
		const difference = Math.abs(originalAmount - correctAmount);

		let confirmMessage = `确认红冲交易ID：${state.originalTransaction.id}？\n`;
		confirmMessage += `原始金额：¥${originalAmount.toFixed(2)}\n`;
		confirmMessage += `正确金额：¥${correctAmount.toFixed(2)}\n`;

		if (originalAmount > correctAmount) {
			confirmMessage += `将退还：¥${difference.toFixed(2)}`;
		} else if (originalAmount < correctAmount) {
			confirmMessage += `需补缴：¥${difference.toFixed(2)}`;
		} else {
			confirmMessage += `金额无变化，仅更新记录`;
		}

		confirmMessage += `\n冲正原因：${state.ruleForm.reason}`;

		// 确认对话框
		await ElMessageBox.confirm(confirmMessage, '确认红冲', {
			confirmButtonText: '确认',
			cancelButtonText: '取消',
			type: 'warning',
		});

		state.loading = true;

		await depositApi.correctPaymentError(state.ruleForm);

		ElMessage.success('红冲操作成功');
		closeDialog();
		emit('reload-data');
	} catch (error: any) {
		if (error !== 'cancel') {
			console.error('红冲操作失败:', error);
			ElMessage.error(error.message || '红冲操作失败');
		}
	} finally {
		state.loading = false;
	}
};

// 暴露方法
defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.dialog-footer {
	text-align: right;
}

.form-tip {
	font-size: 12px;
	color: #909399;
	margin-top: 5px;
}

.text-success {
	color: #67c23a;
	font-weight: bold;
}

:deep(.el-input),
:deep(.el-input-number) {
	width: 100%;
}
</style>
