namespace His.Module.Insurance.Service.Settlement.Model.Outpatient;

/// <summary>
/// destroy_mz_pre & destroy_mz 共用入参
/// </summary>
public class DestroyMzRequest
{
    /// <summary>结算号id（医保系统唯一标识）</summary>
    public string p_jshid { get; set; }

    /// <summary>HIS内部病历号</summary>
    public string p_blh { get; set; }

    /// <summary>卡号（撤销个人账户消费时必传）</summary>
    public string p_kh { get; set; }

    /// <summary>医保口令（口令管理地区必传，阳煤必传）</summary>
    public string p_kl { get; set; }

    /// <summary>电子社保/医保电子凭证二维码或令牌</summary>
    public string p_ewm { get; set; }

    /// <summary>医保三类终端刷脸授权码</summary>
    public string p_authno { get; set; }
}

