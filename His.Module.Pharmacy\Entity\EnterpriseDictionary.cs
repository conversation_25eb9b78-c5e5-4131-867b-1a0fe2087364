﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 企业字典表，包含供应商和生产厂家信息
/// </summary>
[Tenant("1300000000008")]
[SugarTable("enterprise_dictionary", "企业字典表，包含供应商和生产厂家信息")]
public class EnterpriseDictionary : EntityTenant
{
    /// <summary>
    /// 企业编码
    /// </summary>
    [SugarColumn(ColumnName = "enterprise_code", ColumnDescription = "企业编码", Length = 100)]
    public virtual string? EnterpriseCode { get; set; }
    
    /// <summary>
    /// 企业名称
    /// </summary>
    [SugarColumn(ColumnName = "enterprise_name", ColumnDescription = "企业名称", Length = 100)]
    public virtual string? EnterpriseName { get; set; }
    
    /// <summary>
    /// 企业名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "enterprise_name_pinyin", ColumnDescription = "企业名称拼音", Length = 100)]
    public virtual string? EnterpriseNamePinyin { get; set; }
    
    /// <summary>
    /// 企业类型（supplier 或 manufacturer）
    /// </summary>
    [SugarColumn(ColumnName = "enterprise_type", ColumnDescription = "企业类型（supplier 或 manufacturer）", Length = 50)]
    public virtual string? EnterpriseType { get; set; }
    
    
    /*
     * add column contact_name varchar(100),
    add column contact_phone varchar(100),
    add column enterprise_address varchar(100);
     * 
     */
    
    
    /// <summary>
    ///  联系人
    /// </summary>
    [SugarColumn(ColumnName = "contact_name", ColumnDescription = "联系人", Length = 50)]
    public virtual string? ContactName { get; set; }
    /// <summary>
    ///  联系人
    /// </summary>
    [SugarColumn(ColumnName = "contact_phone", ColumnDescription = "联系电话", Length = 50)]
    public virtual string? ContactPhone{ get; set; }
    
    /// <summary>
    ///  企业地址
    /// </summary>
    [SugarColumn(ColumnName = "enterprise_address", ColumnDescription = "企业地址", Length = 500)]
    public virtual string? EnterpriseAddress { get; set; }
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "1 启用 2 停用")]
    public virtual int? Status { get; set; }
    
}
