﻿ 
namespace Admin.NET.Core;

/// <summary>
/// 科室药房表
/// </summary>
[Tenant("1300000000001")]
[SugarTable("sys_org_storage", "科室药房表")]
public class SysOrgStorage : EntityBase
{
    /// <summary>
    /// 科室Id
    /// </summary>
    [SugarColumn(ColumnName = "org_id", ColumnDescription = "科室Id")]
    public virtual long? OrgId { get; set; }
    
    /// <summary>
    /// 科室编码
    /// </summary>
    [SugarColumn(ColumnName = "org_code", ColumnDescription = "科室编码", Length = 100)]
    public virtual string? OrgCode { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "org_name", ColumnDescription = "科室名称", Length = 100)]
    public virtual string? OrgName { get; set; }
    
    /// <summary>
    /// 药房Id
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "药房Id")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>
    [SugarColumn(ColumnName = "storage_code", ColumnDescription = "药房编码", Length = 100)]
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "药房名称", Length = 100)]
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
