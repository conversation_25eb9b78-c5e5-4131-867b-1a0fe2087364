﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药理分类维护基础输入参数
/// </summary>
public class PharmacologicalClassBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 药理分类编码
    /// </summary>
    public virtual string? ClassCode { get; set; }
    
    /// <summary>
    /// 药理分类名称
    /// </summary>
    public virtual string? ClassName { get; set; }
    
    /// <summary>
    /// 父级分类ID
    /// </summary>
    public virtual long? ParentId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药理分类维护分页查询输入参数
/// </summary>
public class PagePharmacologicalClassInput : BasePageInput
{
    /// <summary>
    /// 药理分类编码
    /// </summary>
    public string? ClassCode { get; set; }
    
    /// <summary>
    /// 药理分类名称
    /// </summary>
    public string? ClassName { get; set; }
    
    /// <summary>
    /// 父级分类ID
    /// </summary>
    public long? ParentId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药理分类维护增加输入参数
/// </summary>
public class AddPharmacologicalClassInput
{
    /// <summary>
    /// 药理分类编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药理分类编码字符长度不能超过100")]
    public string? ClassCode { get; set; }
    
    /// <summary>
    /// 药理分类名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "药理分类名称字符长度不能超过100")]
    public string? ClassName { get; set; }
    
    /// <summary>
    /// 父级分类ID
    /// </summary>
    public long? ParentId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药理分类维护删除输入参数
/// </summary>
public class DeletePharmacologicalClassInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药理分类维护更新输入参数
/// </summary>
public class UpdatePharmacologicalClassInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 药理分类编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药理分类编码字符长度不能超过100")]
    public string? ClassCode { get; set; }
    
    /// <summary>
    /// 药理分类名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药理分类名称字符长度不能超过100")]
    public string? ClassName { get; set; }
    
    /// <summary>
    /// 父级分类ID
    /// </summary>    
    public long? ParentId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药理分类维护主键查询输入参数
/// </summary>
public class QueryByIdPharmacologicalClassInput : DeletePharmacologicalClassInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataPharmacologicalClassInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetPharmacologicalClassStatusInput : BaseStatusInput
{
}

/// <summary>
/// 药理分类维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportPharmacologicalClassInput : BaseImportInput
{
    /// <summary>
    /// 药理分类编码
    /// </summary>
    [ImporterHeader(Name = "药理分类编码")]
    [ExporterHeader("药理分类编码", Format = "", Width = 25, IsBold = true)]
    public string? ClassCode { get; set; }
    
    /// <summary>
    /// 药理分类名称
    /// </summary>
    [ImporterHeader(Name = "药理分类名称")]
    [ExporterHeader("药理分类名称", Format = "", Width = 25, IsBold = true)]
    public string? ClassName { get; set; }
    
    /// <summary>
    /// 父级分类ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ParentId { get; set; }
    
    /// <summary>
    /// 父级分类ID 文本
    /// </summary>
    [ImporterHeader(Name = "父级分类ID")]
    [ExporterHeader("父级分类ID", Format = "", Width = 25, IsBold = true)]
    public string ParentFkDisplayName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
}
