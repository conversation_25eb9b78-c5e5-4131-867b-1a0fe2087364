﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Inpatient;

/// <summary>
/// 医疗组成员表基础输入参数
/// </summary>
public class MedicalTeamMemberBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 医疗组ID
    /// </summary>
    [Required(ErrorMessage = "医疗组ID不能为空")]
    public virtual long? TeamId { get; set; }
    
    /// <summary>
    /// 成员ID
    /// </summary>
    [Required(ErrorMessage = "成员ID不能为空")]
    public virtual long? StaffId { get; set; }
    
    /// <summary>
    /// 成员名称
    /// </summary>
    [Required(ErrorMessage = "成员名称不能为空")]
    public virtual string StaffName { get; set; }
    
    /// <summary>
    /// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
    /// </summary>
    public virtual string? RoleType { get; set; }
    
    /// <summary>
    /// 加入日期
    /// </summary>
    public virtual DateTime? JoinDate { get; set; }
    
    /// <summary>
    /// 离开日期
    /// </summary>
    public virtual DateTime? LeaveDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 医疗组成员表分页查询输入参数
/// </summary>
public class PageMedicalTeamMemberInput : BasePageInput
{
    /// <summary>
    /// 医疗组ID
    /// </summary>
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 成员ID
    /// </summary>
    public long? StaffId { get; set; }
    
    /// <summary>
    /// 成员名称
    /// </summary>
    public string StaffName { get; set; }
    
    /// <summary>
    /// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
    /// </summary>
    public string? RoleType { get; set; }
    
    /// <summary>
    /// 加入日期范围
    /// </summary>
     public DateTime?[] JoinDateRange { get; set; }
    
    /// <summary>
    /// 离开日期范围
    /// </summary>
     public DateTime?[] LeaveDateRange { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 医疗组成员表增加输入参数
/// </summary>
public class AddMedicalTeamMemberInput
{
    /// <summary>
    /// 医疗组ID
    /// </summary>
    [Required(ErrorMessage = "医疗组ID不能为空")]
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 成员ID
    /// </summary>
    [Required(ErrorMessage = "成员ID不能为空")]
    public long? StaffId { get; set; }
    
    /// <summary>
    /// 成员名称
    /// </summary>
    [Required(ErrorMessage = "成员名称不能为空")]
    [MaxLength(200, ErrorMessage = "成员名称字符长度不能超过200")]
    public string StaffName { get; set; }
    
    /// <summary>
    /// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
    /// </summary>
    [MaxLength(200, ErrorMessage = "角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)字符长度不能超过200")]
    public string? RoleType { get; set; }
    
    /// <summary>
    /// 加入日期
    /// </summary>
    public DateTime? JoinDate { get; set; }
    
    /// <summary>
    /// 离开日期
    /// </summary>
    public DateTime? LeaveDate { get; set; }
    
 
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 医疗组成员表删除输入参数
/// </summary>
public class DeleteMedicalTeamMemberInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 医疗组成员表更新输入参数
/// </summary>
public class UpdateMedicalTeamMemberInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 医疗组ID
    /// </summary>    
    [Required(ErrorMessage = "医疗组ID不能为空")]
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 成员ID
    /// </summary>    
    [Required(ErrorMessage = "成员ID不能为空")]
    public long? StaffId { get; set; }
    
    /// <summary>
    /// 成员名称
    /// </summary>    
    [Required(ErrorMessage = "成员名称不能为空")]
    [MaxLength(200, ErrorMessage = "成员名称字符长度不能超过200")]
    public string StaffName { get; set; }
    
    /// <summary>
    /// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
    /// </summary>    
    [MaxLength(200, ErrorMessage = "角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)字符长度不能超过200")]
    public string? RoleType { get; set; }
    
    /// <summary>
    /// 加入日期
    /// </summary>    
    public DateTime? JoinDate { get; set; }
    
    /// <summary>
    /// 离开日期
    /// </summary>    
    public DateTime? LeaveDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 医疗组成员表主键查询输入参数
/// </summary>
public class QueryByIdMedicalTeamMemberInput : DeleteMedicalTeamMemberInput
{
}

/// <summary>
/// 医疗组成员表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportMedicalTeamMemberInput : BaseImportInput
{
    /// <summary>
    /// 医疗组ID
    /// </summary>
    [ImporterHeader(Name = "*医疗组ID")]
    [ExporterHeader("*医疗组ID", Format = "", Width = 25, IsBold = true)]
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 成员ID
    /// </summary>
    [ImporterHeader(Name = "*成员ID")]
    [ExporterHeader("*成员ID", Format = "", Width = 25, IsBold = true)]
    public long? StaffId { get; set; }
    
    /// <summary>
    /// 成员名称
    /// </summary>
    [ImporterHeader(Name = "*成员名称")]
    [ExporterHeader("*成员名称", Format = "", Width = 25, IsBold = true)]
    public string StaffName { get; set; }
    
    /// <summary>
    /// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
    /// </summary>
    [ImporterHeader(Name = "角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)")]
    [ExporterHeader("角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)", Format = "", Width = 25, IsBold = true)]
    public string? RoleType { get; set; }
    
    /// <summary>
    /// 加入日期
    /// </summary>
    [ImporterHeader(Name = "加入日期")]
    [ExporterHeader("加入日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? JoinDate { get; set; }
    
    /// <summary>
    /// 离开日期
    /// </summary>
    [ImporterHeader(Name = "离开日期")]
    [ExporterHeader("离开日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? LeaveDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    [ImporterHeader(Name = "状态(1:启用 2:停用,)")]
    [ExporterHeader("状态(1:启用 2:停用,)", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
