﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品单位维护服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugUnitService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugUnit> _drugUnitRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DrugUnitService(SqlSugarRepository<DrugUnit> drugUnitRep, ISqlSugarClient sqlSugarClient)
    {
        _drugUnitRep = drugUnitRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药品单位维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品单位维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugUnitOutput>> Page(PageDrugUnitInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugUnitRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Unit.Contains(input.Keyword) || u.ConvertUnit.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ConvertUnit), u => u.ConvertUnit.Contains(input.ConvertUnit.Trim()))
            .WhereIF(input.Status.HasValue, u => u.Status == (int)input.Status)
            .Select<DrugUnitOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品单位维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品单位维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugUnit> Detail([FromQuery] QueryByIdDrugUnitInput input)
    {
        return await _drugUnitRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品单位维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品单位维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugUnitInput input)
    {
        var entity = input.Adapt<DrugUnit>();
        return await _drugUnitRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品单位维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品单位维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugUnitInput input)
    {
        var entity = input.Adapt<DrugUnit>();
        await _drugUnitRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品单位维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品单位维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugUnitInput input)
    {
        var entity = await _drugUnitRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugUnitRep.FakeDeleteAsync(entity);   //假删除
        //await _drugUnitRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品单位维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品单位维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugUnitInput> input)
    {
        var exp = Expressionable.Create<DrugUnit>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugUnitRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugUnitRep.FakeDeleteAsync(list);   //假删除
        //return await _drugUnitRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置药品单位维护状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置药品单位维护状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetDrugUnitStatus(SetDrugUnitStatusInput input)
    {
        await _drugUnitRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 导出药品单位维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品单位维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugUnitInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugUnitOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药品单位维护导出记录");
    }
    
    /// <summary>
    /// 下载药品单位维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品单位维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugUnitOutput>(), "药品单位维护导入模板");
    }
    
    /// <summary>
    /// 导入药品单位维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品单位维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportDrugUnitInput, DrugUnit>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugUnit>>();
                    
                    var storageable = _drugUnitRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Unit?.Length > 100, "单位名称长度不能超过100个字符")
                        .SplitError(it => it.Item.ConvertUnit?.Length > 100, "转换单位长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
