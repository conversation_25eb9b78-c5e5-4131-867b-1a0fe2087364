﻿import {useBaseApi} from '/@/api/base';

// 入院登记记录接口服务
export const useInpatientRegisterApi = () => {
	const baseApi = useBaseApi("inpatientRegister");
	return {
		// 分页查询入院登记记录
		page: baseApi.page,
		// 查看入院登记记录详细
		detail: baseApi.detail,
		// 新增入院登记记录
		add: baseApi.add,
		// 更新入院登记记录
		update: baseApi.update,
		// 删除入院登记记录
		delete: baseApi.delete,
		// 批量删除入院登记记录
		batchDelete: baseApi.batchDelete,
		// 导出入院登记记录数据
		exportData: baseApi.exportData,
		// 导入入院登记记录数据
		importData: baseApi.importData,
		// 下载入院登记记录数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 入院登记记录实体
export interface InpatientRegister {
	// 主键Id
	id: number;
	// 住院流水号
	inpatientSerialNo: string;
	// 住院号
	inpatientNo: string;
	// 住院次数
	inpatientCount: number;
	// 就诊卡号
	medicalCardNo: string;
	// 患者ID
	patientId: number;
	// 患者姓名
	patientName: string;
	// 性别
	sex: number;
	// 年龄
	age: number;
	// 年龄单位
	ageUnit: string;
	// 出生日期
	birthday: string;
	// 证件类型
	cardType: number;
	// 身份证号
	idCardNo: string;
	// 电话号码
	phone: string;
	// 联系人姓名
	contactName: string;
	// 联系人关系
	contactRelationship: string;
	// 联系人地址
	contactAddress: string;
	// 联系人电话号码
	contactPhone: string;
	// 现居住地省
	residenceProvince: number;
	// 现居住地市
	residenceCity: number;
	// 现居住地县
	residenceCounty: number;
	// 详细现居住地
	residenceAddress: string;
	// 入院时间
	inpatientTime: string;
	// 科室ID
	deptId: number;
	// 科室名称
	deptName: string;
	// 病区ID
	wardId: number;
	// 病区名称
	wardName: string;
	// 诊疗组ID
	teamId: number;
	// 诊疗组名称
	teamName: string;
	// 医生ID
	doctorId: number;
	// 医生姓名
	doctorName: string;
	// 接诊医生id
	receivingDoctorId: number;
	// 接诊医生名称
	receivingDoctorName: string;
	// 入院途径
	inpatientWay: string;
	// 结算类别
	settlementCategory: string;
	// 入院诊断编号
	admissionDiagnosisCode: string;
	// 入院诊断名称
	admissionDiagnosisName: string;
	// 妊娠风险评估
	pregnancyRiskLevel: string;
	// 高危因素
	highRiskFactors: string;
	// 状态
	status: number;
	// 备注
	remark: string;
	// 租户Id
	tenantId: number;
	// 创建者部门Id
	createOrgId: number;
	// 创建者部门名称
	createOrgName: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
}