﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Shared;

/// <summary>
/// 预交金建议服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class AdvancePaymentSuggestionService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<AdvancePaymentSuggestion> _advancePaymentSuggestionRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public AdvancePaymentSuggestionService(SqlSugarRepository<AdvancePaymentSuggestion> advancePaymentSuggestionRep, ISqlSugarClient sqlSugarClient)
    {
        _advancePaymentSuggestionRep = advancePaymentSuggestionRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询预交金建议 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询预交金建议")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<AdvancePaymentSuggestionOutput>> Page(PageAdvancePaymentSuggestionInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _advancePaymentSuggestionRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.DiagnosisCode.Contains(input.Keyword) || u.DiagnosisName.Contains(input.Keyword) || u.DiseaseTypeName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DiagnosisCode), u => u.DiagnosisCode.Contains(input.DiagnosisCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DiagnosisName), u => u.DiagnosisName.Contains(input.DiagnosisName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DiseaseTypeName), u => u.DiseaseTypeName.Contains(input.DiseaseTypeName.Trim()))
            .Select<AdvancePaymentSuggestionOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取预交金建议详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取预交金建议详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<AdvancePaymentSuggestion> Detail([FromQuery] QueryByIdAdvancePaymentSuggestionInput input)
    {
        return await _advancePaymentSuggestionRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加预交金建议 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加预交金建议")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddAdvancePaymentSuggestionInput input)
    {
        var entity = input.Adapt<AdvancePaymentSuggestion>();
        return await _advancePaymentSuggestionRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新预交金建议 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新预交金建议")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateAdvancePaymentSuggestionInput input)
    {
        var entity = input.Adapt<AdvancePaymentSuggestion>();
        await _advancePaymentSuggestionRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除预交金建议 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除预交金建议")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteAdvancePaymentSuggestionInput input)
    {
        var entity = await _advancePaymentSuggestionRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _advancePaymentSuggestionRep.FakeDeleteAsync(entity);   //假删除
        //await _advancePaymentSuggestionRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除预交金建议 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除预交金建议")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteAdvancePaymentSuggestionInput> input)
    {
        var exp = Expressionable.Create<AdvancePaymentSuggestion>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _advancePaymentSuggestionRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _advancePaymentSuggestionRep.FakeDeleteAsync(list);   //假删除
        //return await _advancePaymentSuggestionRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置预交金建议状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置预交金建议状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetAdvancePaymentSuggestionStatus(SetAdvancePaymentSuggestionStatusInput input)
    {
        await _advancePaymentSuggestionRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 导出预交金建议记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出预交金建议记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageAdvancePaymentSuggestionInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportAdvancePaymentSuggestionOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "预交金建议导出记录");
    }
    
    /// <summary>
    /// 下载预交金建议数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载预交金建议数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportAdvancePaymentSuggestionOutput>(), "预交金建议导入模板");
    }
    
    /// <summary>
    /// 导入预交金建议记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入预交金建议记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportAdvancePaymentSuggestionInput, AdvancePaymentSuggestion>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<AdvancePaymentSuggestion>>();
                    
                    var storageable = _advancePaymentSuggestionRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.DiagnosisCode?.Length > 128, "诊断编码长度不能超过128个字符")
                        .SplitError(it => it.Item.DiagnosisName?.Length > 255, "诊断名称长度不能超过255个字符")
                        .SplitError(it => it.Item.DiseaseTypeName?.Length > 255, "病种名称长度不能超过255个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
