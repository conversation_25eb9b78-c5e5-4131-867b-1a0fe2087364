﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Shared;
/// <summary>
/// 床位信息基础输入参数
/// </summary>
public class BedInfoBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 床位编号
    /// </summary>
    [Required(ErrorMessage = "床位编号不能为空")]
    public virtual string BedNo { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    [Required(ErrorMessage = "科室id不能为空")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [Required(ErrorMessage = "科室名称不能为空")]
    public virtual string DeptName { get; set; }
    
    /// <summary>
    /// 病区id
    /// </summary>
    [Required(ErrorMessage = "病区id不能为空")]
    public virtual long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    [Required(ErrorMessage = "病区名称不能为空")]
    public virtual string WardName { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    public virtual string? RoomNo { get; set; }
    
    /// <summary>
    /// 床位类型
    /// </summary>
    [Dict("InpatientBedType", AllowNullValue=true)]
    public virtual string? BedType { get; set; }
    
    /// <summary>
    /// 床位状态
    /// </summary>
    [Dict("InpatientBedStatus", AllowNullValue=true)]
    public virtual string? BedStatus { get; set; }
    
    /// <summary>
    /// 床位等级
    /// </summary>
    public virtual string? BedLevelName { get; set; }
    public virtual long? BedLevelId { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    [Required(ErrorMessage = "排序不能为空")]
    public virtual int? OrderNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 床位信息分页查询输入参数
/// </summary>
public class PageBedInfoInput : BasePageInput
{
    /// <summary>
    /// 床位编号
    /// </summary>
    public string BedNo { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string DeptName { get; set; }
    
    /// <summary>
    /// 病区id
    /// </summary>
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    public string WardName { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    public string? RoomNo { get; set; }
    
    /// <summary>
    /// 床位类型
    /// </summary>
    [Dict("InpatientBedType", AllowNullValue=true)]
    public string? BedType { get; set; }
    
    /// <summary>
    /// 床位状态
    /// </summary>
    [Dict("InpatientBedStatus", AllowNullValue=true)]
    public string? BedStatus { get; set; }
    
    /// <summary>
    /// 床位等级
    /// </summary>
    public string? BedLevelName { get; set; }
    public   long? BedLevelId { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 床位信息增加输入参数
/// </summary>
public class AddBedInfoInput
{
    /// <summary>
    /// 床位编号
    /// </summary>
    [Required(ErrorMessage = "床位编号不能为空")]
    [MaxLength(100, ErrorMessage = "床位编号字符长度不能超过100")]
    public string BedNo { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    [Required(ErrorMessage = "科室id不能为空")]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [Required(ErrorMessage = "科室名称不能为空")]
    [MaxLength(100, ErrorMessage = "科室名称字符长度不能超过100")]
    public string DeptName { get; set; }
    
    /// <summary>
    /// 病区id
    /// </summary>
    [Required(ErrorMessage = "病区id不能为空")]
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    [Required(ErrorMessage = "病区名称不能为空")]
    [MaxLength(100, ErrorMessage = "病区名称字符长度不能超过100")]
    public string WardName { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    [MaxLength(100, ErrorMessage = "房间编号字符长度不能超过100")]
    public string? RoomNo { get; set; }
    
    /// <summary>
    /// 床位类型
    /// </summary>
    [Dict("InpatientBedType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "床位类型字符长度不能超过100")]
    public string? BedType { get; set; }
    
    /// <summary>
    /// 床位状态
    /// </summary>
    [Dict("InpatientBedStatus", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "床位状态字符长度不能超过100")]
    public string? BedStatus { get; set; }
    
    /// <summary>
    /// 床位等级
    /// </summary>
    [MaxLength(100, ErrorMessage = "床位等级字符长度不能超过100")]
    public string? BedLevelName { get; set; }
    public   long? BedLevelId { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    [Required(ErrorMessage = "排序不能为空")]
    public int? OrderNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 床位信息删除输入参数
/// </summary>
public class DeleteBedInfoInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 床位信息更新输入参数
/// </summary>
public class UpdateBedInfoInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 床位编号
    /// </summary>    
    [Required(ErrorMessage = "床位编号不能为空")]
    [MaxLength(100, ErrorMessage = "床位编号字符长度不能超过100")]
    public string BedNo { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>    
    [Required(ErrorMessage = "科室id不能为空")]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>    
    [Required(ErrorMessage = "科室名称不能为空")]
    [MaxLength(100, ErrorMessage = "科室名称字符长度不能超过100")]
    public string DeptName { get; set; }
    
    /// <summary>
    /// 病区id
    /// </summary>    
    [Required(ErrorMessage = "病区id不能为空")]
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>    
    [Required(ErrorMessage = "病区名称不能为空")]
    [MaxLength(100, ErrorMessage = "病区名称字符长度不能超过100")]
    public string WardName { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "房间编号字符长度不能超过100")]
    public string? RoomNo { get; set; }
    
    /// <summary>
    /// 床位类型
    /// </summary>    
    [Dict("InpatientBedType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "床位类型字符长度不能超过100")]
    public string? BedType { get; set; }
    
    /// <summary>
    /// 床位状态
    /// </summary>    
    [Dict("InpatientBedStatus", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "床位状态字符长度不能超过100")]
    public string? BedStatus { get; set; }
    
    /// <summary>
    /// 床位等级
    /// </summary>    
    [MaxLength(100, ErrorMessage = "床位等级字符长度不能超过100")]
    public string? BedLevelName { get; set; }
    public   long? BedLevelId { get; set; }
    /// <summary>
    /// 排序
    /// </summary>    
    [Required(ErrorMessage = "排序不能为空")]
    public int? OrderNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 床位信息主键查询输入参数
/// </summary>
public class QueryByIdBedInfoInput : DeleteBedInfoInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataBedInfoInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetBedInfoStatusInput : BaseStatusInput
{
}

/// <summary>
/// 床位信息数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportBedInfoInput : BaseImportInput
{
    /// <summary>
    /// 床位编号
    /// </summary>
    [ImporterHeader(Name = "*床位编号")]
    [ExporterHeader("*床位编号", Format = "", Width = 25, IsBold = true)]
    public string BedNo { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    [ImporterHeader(Name = "*科室id")]
    [ExporterHeader("*科室id", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [ImporterHeader(Name = "*科室名称")]
    [ExporterHeader("*科室名称", Format = "", Width = 25, IsBold = true)]
    public string DeptName { get; set; }
    
    /// <summary>
    /// 病区id
    /// </summary>
    [ImporterHeader(Name = "*病区id")]
    [ExporterHeader("*病区id", Format = "", Width = 25, IsBold = true)]
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    [ImporterHeader(Name = "*病区名称")]
    [ExporterHeader("*病区名称", Format = "", Width = 25, IsBold = true)]
    public string WardName { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    [ImporterHeader(Name = "房间编号")]
    [ExporterHeader("房间编号", Format = "", Width = 25, IsBold = true)]
    public string? RoomNo { get; set; }
    
    /// <summary>
    /// 床位类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? BedType { get; set; }
    
    /// <summary>
    /// 床位类型 文本
    /// </summary>
    [Dict("InpatientBedType")]
    [ImporterHeader(Name = "床位类型")]
    [ExporterHeader("床位类型", Format = "", Width = 25, IsBold = true)]
    public string BedTypeDictLabel { get; set; }
    
    /// <summary>
    /// 床位状态 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? BedStatus { get; set; }
    
    /// <summary>
    /// 床位状态 文本
    /// </summary>
    [Dict("InpatientBedStatus")]
    [ImporterHeader(Name = "床位状态")]
    [ExporterHeader("床位状态", Format = "", Width = 25, IsBold = true)]
    public string BedStatusDictLabel { get; set; }
    
    /// <summary>
    /// 床位等级 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? BedLevelName { get; set; }
    
    /// <summary>
    /// 床位等级 文本
    /// </summary>
    [ImporterHeader(Name = "床位等级")]
    [ExporterHeader("床位等级", Format = "", Width = 25, IsBold = true)]
    public string BedLevelFkDisplayName { get; set; }
    [ImporterHeader(Name = "床位等级")]
    [ExporterHeader("床位等级", Format = "", Width = 25, IsBold = true)]
    public   long? BedLevelId { get; set; }
    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "*排序")]
    [ExporterHeader("*排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
