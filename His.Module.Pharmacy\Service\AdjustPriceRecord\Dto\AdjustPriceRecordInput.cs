﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品调价记录基础输入参数
/// </summary>
public class AdjustPriceRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    public virtual string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    public virtual string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public virtual int? Quantity { get; set; }

    /// <summary>
    /// 旧零售价
    /// </summary>
    public virtual decimal? OldSalePrice { get; set; }

    /// <summary>
    /// 新零售价
    /// </summary>
    public virtual decimal? NewSalePrice { get; set; }

    /// <summary>
    /// 总零售价
    /// </summary>
    public virtual decimal? TotalSalePrice { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    public virtual string? BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public virtual DateTime? ProductionDate { get; set; }

    /// <summary>
    /// 有效期
    /// </summary>
    public virtual DateTime? ExpirationDate { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>
    public virtual string? ApprovalNumber { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    public virtual string? MedicineCode { get; set; }

    /// <summary>
    /// 生产厂商名称
    /// </summary>
    public virtual string? ManufacturerName { get; set; }

    /// <summary>
    /// 调价时间
    /// </summary>
    public virtual DateTime? AdjustTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue = true)]
    public virtual int? Status { get; set; }
}

/// <summary>
/// 药品调价记录分页查询输入参数
/// </summary>
public class PageAdjustPriceRecordInput : BasePageInput
{
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int? Quantity { get; set; }

    /// <summary>
    /// 旧零售价
    /// </summary>
    public decimal? OldSalePrice { get; set; }

    /// <summary>
    /// 新零售价
    /// </summary>
    public decimal? NewSalePrice { get; set; }

    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    public string? BatchNo { get; set; }

    /// <summary>
    /// 生产日期范围
    /// </summary>
    public DateTime?[] ProductionDateRange { get; set; }

    /// <summary>
    /// 有效期范围
    /// </summary>
    public DateTime?[] ExpirationDateRange { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>
    public string? ApprovalNumber { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 生产厂商ID
    /// </summary>
    public long? ManufacturerId { get; set; }

    /// <summary>
    /// 生产厂商名称
    /// </summary>
    public string? ManufacturerName { get; set; }

    /// <summary>
    /// 调价时间范围
    /// </summary>
    public DateTime?[] AdjustTimeRange { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue = true)]
    public int? Status { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品调价记录增加输入参数
/// </summary>
public class AddAdjustPriceRecordInput
{
    /// <summary>
    /// 药品编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int? Quantity { get; set; }

    /// <summary>
    /// 旧零售价
    /// </summary>
    public decimal? OldSalePrice { get; set; }

    /// <summary>
    /// 新零售价
    /// </summary>
    public decimal? NewSalePrice { get; set; }
    
    /// <summary>
    /// 旧总零售价
    /// </summary>
    public decimal? TotalOldSalePrice { get; set; }

    /// <summary>
    /// 新总零售价
    /// </summary>
    public decimal? TotalNewSalePrice { get; set; }

    

    /// <summary>
    /// 批号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductionDate { get; set; }

    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? ExpirationDate { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 生产厂商名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "生产厂商名称字符长度不能超过100")]
    public string? ManufacturerName { get; set; }

    /// <summary>
    /// 调价时间
    /// </summary>
    public DateTime? AdjustTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue = true)]
    public int? Status { get; set; }
}

/// <summary>
/// 药品调价记录删除输入参数
/// </summary>
public class DeleteAdjustPriceRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 药品调价记录更新输入参数
/// </summary>
public class UpdateAdjustPriceRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>    
    public int? Quantity { get; set; }

    /// <summary>
    /// 旧零售价
    /// </summary>    
    public decimal? OldSalePrice { get; set; }

    /// <summary>
    /// 新零售价
    /// </summary>    
    public decimal? NewSalePrice { get; set; }

    /// <summary>
    /// 旧总零售价
    /// </summary>
    public decimal? TotalOldSalePrice { get; set; }

    /// <summary>
    /// 新总零售价
    /// </summary>
    public decimal? TotalNewSalePrice { get; set; }

    /// <summary>
    /// 批号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>    
    public DateTime? ProductionDate { get; set; }

    /// <summary>
    /// 有效期
    /// </summary>    
    public DateTime? ExpirationDate { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 生产厂商名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "生产厂商名称字符长度不能超过100")]
    public string? ManufacturerName { get; set; }

    /// <summary>
    /// 调价时间
    /// </summary>    
    public DateTime? AdjustTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>    
    [Dict("StorageInOutStatus", AllowNullValue = true)]
    public int? Status { get; set; }
}

public class SubmitAdjustPriceRecordInput
{
    public virtual long? Id { get; set; }
}

/// <summary>
/// 药品调价记录主键查询输入参数
/// </summary>
public class QueryByIdAdjustPriceRecordInput : DeleteAdjustPriceRecordInput
{
}

/// <summary>
/// 药品调价记录数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportAdjustPriceRecordInput : BaseImportInput
{
    /// <summary>
    /// 药品编码
    /// </summary>
    [ImporterHeader(Name = "药品编码")]
    [ExporterHeader("药品编码", Format = "", Width = 25, IsBold = true)]
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    [ImporterHeader(Name = "药品名称")]
    [ExporterHeader("药品名称", Format = "", Width = 25, IsBold = true)]
    public string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [ImporterHeader(Name = "规格")]
    [ExporterHeader("规格", Format = "", Width = 25, IsBold = true)]
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [ImporterHeader(Name = "单位")]
    [ExporterHeader("单位", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [ImporterHeader(Name = "数量")]
    [ExporterHeader("数量", Format = "", Width = 25, IsBold = true)]
    public int? Quantity { get; set; }

    /// <summary>
    /// 旧零售价
    /// </summary>
    [ImporterHeader(Name = "旧零售价")]
    [ExporterHeader("旧零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? OldSalePrice { get; set; }

    /// <summary>
    /// 新零售价
    /// </summary>
    [ImporterHeader(Name = "新零售价")]
    [ExporterHeader("新零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? NewSalePrice { get; set; }

    /// <summary>
    /// 总零售价
    /// </summary>
    [ImporterHeader(Name = "总零售价")]
    [ExporterHeader("总零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalSalePrice { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [ImporterHeader(Name = "批号")]
    [ExporterHeader("批号", Format = "", Width = 25, IsBold = true)]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    [ImporterHeader(Name = "生产日期")]
    [ExporterHeader("生产日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ProductionDate { get; set; }

    /// <summary>
    /// 有效期
    /// </summary>
    [ImporterHeader(Name = "有效期")]
    [ExporterHeader("有效期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ExpirationDate { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>
    [ImporterHeader(Name = "批准文号")]
    [ExporterHeader("批准文号", Format = "", Width = 25, IsBold = true)]
    public string? ApprovalNumber { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    [ImporterHeader(Name = "国家医保编码")]
    [ExporterHeader("国家医保编码", Format = "", Width = 25, IsBold = true)]
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 生产厂商名称
    /// </summary>
    [ImporterHeader(Name = "生产厂商名称")]
    [ExporterHeader("生产厂商名称", Format = "", Width = 25, IsBold = true)]
    public string? ManufacturerName { get; set; }

    /// <summary>
    /// 调价时间
    /// </summary>
    [ImporterHeader(Name = "调价时间")]
    [ExporterHeader("调价时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? AdjustTime { get; set; }

    /// <summary>
    /// 状态 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public int? Status { get; set; }

    /// <summary>
    /// 状态 文本
    /// </summary>
    [Dict("StorageInOutStatus")]
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public string StatusDictLabel { get; set; }
}