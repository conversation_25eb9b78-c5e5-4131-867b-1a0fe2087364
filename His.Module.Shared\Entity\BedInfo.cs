﻿using Admin.NET.Core;

namespace His.Module.Shared.Entity;

/// <summary>
/// 床位信息
/// </summary>
[Tenant("1300000000014")]
[SugarTable("bed_info", "床位信息")]
public class BedInfo : EntityTenant
{
    /// <summary>
    /// 床位编号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "bed_no", ColumnDescription = "床位编号", Length = 100)]
    public virtual string BedNo { get; set; }
    
    /// <summary>
    /// 部门id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "部门id")]
    public virtual long DeptId { get; set; }
    
    /// <summary>
    /// 部门名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "部门名称", Length = 100)]
    public virtual string DeptName { get; set; }
    
    /// <summary>
    /// 病房id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "ward_id", ColumnDescription = "病房id")]
    public virtual long WardId { get; set; }
    
    /// <summary>
    /// 病房名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "ward_name", ColumnDescription = "病房名称", Length = 100)]
    public virtual string WardName { get; set; }
    
    /// <summary>
    /// 房间编号
    /// </summary>
    [SugarColumn(ColumnName = "room_no", ColumnDescription = "房间编号", Length = 100)]
    public virtual string? RoomNo { get; set; }
    
    /// <summary>
    /// 床位类型
    /// </summary>
    [SugarColumn(ColumnName = "bed_type", ColumnDescription = "床位类型", Length = 100)]
    public virtual string? BedType { get; set; }
    
    /// <summary>
    /// 床位状态 隔离,Q 污染,K 关闭,C 空床,N 占床,O

    /// </summary>
    [SugarColumn(ColumnName = "bed_status", ColumnDescription = "床位状态", Length = 100)]
    public virtual string? BedStatus { get; set; }
    
    /// <summary>
    /// 床位等级
    /// </summary>
    [SugarColumn(ColumnName = "bed_level_name", ColumnDescription = "床位等级名称", Length = 100)]
    public virtual string? BedLevelName { get; set; }
    
    /// <summary>
    /// 床位等级
    /// </summary>
    [SugarColumn(ColumnName = "bed_level_id", ColumnDescription = "床位等级id" )]
    public virtual long? BedLevelId { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int OrderNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
