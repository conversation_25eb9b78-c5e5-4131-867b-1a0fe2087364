﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.Registration.Const;
using His.Module.Registration.Entity;
using Microsoft.AspNetCore.Http;

namespace His.Module.Registration;

/// <summary>
/// 诊台维护服务 🧩
/// </summary>
[ApiDescriptionSettings(RegistrationConst.GroupName, Order = 100)]
public class ClinicConsoleService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ClinicConsole> _clinicConsoleRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public ClinicConsoleService(SqlSugarRepository<ClinicConsole> clinicConsoleRep, ISqlSugarClient sqlSugarClient)
    {
        _clinicConsoleRep = clinicConsoleRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询诊台维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询诊台维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<ClinicConsoleOutput>> Page(PageClinicConsoleInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _clinicConsoleRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Name.Contains(input.Keyword) || u.Code.Contains(input.Keyword) || u.RoomName.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RoomName), u => u.RoomName.Contains(input.RoomName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.RoomId != null, u => u.RoomId == input.RoomId)
            .WhereIF(input.CurrentCount != null, u => u.CurrentCount == input.CurrentCount)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<ClinicConsoleOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取诊台维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取诊台维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<ClinicConsole> Detail([FromQuery] QueryByIdClinicConsoleInput input)
    {
        return await _clinicConsoleRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加诊台维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加诊台维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddClinicConsoleInput input)
    {
        var entity = input.Adapt<ClinicConsole>();
        return await _clinicConsoleRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新诊台维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新诊台维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateClinicConsoleInput input)
    {
        var entity = input.Adapt<ClinicConsole>();
        await _clinicConsoleRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除诊台维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除诊台维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteClinicConsoleInput input)
    {
        var entity = await _clinicConsoleRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _clinicConsoleRep.FakeDeleteAsync(entity);   //假删除
        //await _clinicConsoleRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除诊台维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除诊台维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteClinicConsoleInput> input)
    {
        var exp = Expressionable.Create<ClinicConsole>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _clinicConsoleRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _clinicConsoleRep.FakeDeleteAsync(list);   //假删除
        //return await _clinicConsoleRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出诊台维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出诊台维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageClinicConsoleInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportClinicConsoleOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "诊台维护导出记录");
    }
    
    /// <summary>
    /// 下载诊台维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载诊台维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportClinicConsoleOutput>(), "诊台维护导入模板");
    }
    
    /// <summary>
    /// 导入诊台维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入诊台维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportClinicConsoleInput, ClinicConsole>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<ClinicConsole>>();
                    
                    var storageable = _clinicConsoleRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Name?.Length > 255, "诊台名称长度不能超过255个字符")
                        .SplitError(it => it.Item.Code?.Length > 255, "诊台编码长度不能超过255个字符")
                        .SplitError(it => it.Item.RoomName?.Length > 255, "诊室名称长度不能超过255个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
