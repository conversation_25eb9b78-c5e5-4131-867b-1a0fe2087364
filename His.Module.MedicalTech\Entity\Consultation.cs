﻿namespace His.Module.MedicalTech.Entity;

/// <summary>
/// 会诊表
/// </summary>
[Tenant("1300000000009")]
[SugarTable("consultation", "会诊表")]
public class Consultation : EntityTenantBaseData
{
    /// <summary>
    /// 申请单号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "apply_no", ColumnDescription = "申请单号", Length = 64)]
    public virtual string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "就诊Id")]
    public virtual long RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊流水号", Length = 64)]
    public virtual string VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 64)]
    public virtual string OutpatientNo { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "就诊卡号", Length = 64)]
    public virtual string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 64)]
    public virtual string PatientName { get; set; }

    /// <summary>
    /// 就诊时间
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "visit_time", ColumnDescription = "就诊时间")]
    public virtual DateTime VisitTime { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [SugarColumn(ColumnName = "sex", ColumnDescription = "性别", Length = 32)]
    public virtual string? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    [SugarColumn(ColumnName = "age", ColumnDescription = "年龄")]
    public virtual int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [SugarColumn(ColumnName = "age_unit", ColumnDescription = "年龄单位", Length = 32)]
    public virtual string? AgeUnit { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    [SugarColumn(ColumnName = "flag", ColumnDescription = "门诊住院标识 0门诊 1住院")]
    public virtual int? Flag { get; set; }

    /// <summary>
    /// 期望会诊时间
    /// </summary>
    [SugarColumn(ColumnName = "expected_time", ColumnDescription = "期望会诊时间")]
    public virtual DateTime? ExpectedTime { get; set; }

    /// <summary>
    /// 会诊类型  字典 ConsultationType
    /// </summary>
    [SugarColumn(ColumnName = "type", ColumnDescription = "会诊类型  字典 ConsultationType", Length = 32)]
    public virtual string? Type { get; set; }

    /// <summary>
    /// 会诊状态  字典 ConsultationStatus
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "会诊状态  字典 ConsultationStatus", Length = 32)]
    public virtual string? Status { get; set; }

    /// <summary>
    /// 病情摘要
    /// </summary>
    [SugarColumn(ColumnName = "clinical_summary", ColumnDescription = "病情摘要", Length = 0)]
    public virtual string? ClinicalSummary { get; set; }

    /// <summary>
    /// 会诊目的
    /// </summary>
    [SugarColumn(ColumnName = "purpose", ColumnDescription = "会诊目的", Length = 0)]
    public virtual string? Purpose { get; set; }

    /// <summary>
    /// 会诊意见
    /// </summary>
    [SugarColumn(ColumnName = "consultation_opinion", ColumnDescription = "会诊意见", Length = 0)]
    public virtual string? ConsultationOpinion { get; set; }

    /// <summary>
    /// 意见填写时间
    /// </summary>
    [SugarColumn(ColumnName = "opinion_time", ColumnDescription = "意见填写时间")]
    public virtual DateTime? OpinionTime { get; set; }

    /// <summary>
    /// 意见填写人Id
    /// </summary>
    [SugarColumn(ColumnName = "opinion_staff_id", ColumnDescription = "意见填写人Id")]
    public virtual long? OpinionStaffId { get; set; }

    /// <summary>
    /// 意见填写人名称
    /// </summary>
    [SugarColumn(ColumnName = "opinion_staff_name", ColumnDescription = "意见填写人名称", Length = 64)]
    public virtual string? OpinionStaffName { get; set; }

    /// <summary>
    /// 意见填写人签名
    /// </summary>
    [SugarColumn(ColumnName = "opinion_staff_sign", ColumnDescription = "意见填写人签名", Length = 0)]
    public virtual string? OpinionStaffSign { get; set; }

    /// <summary>
    /// 申请时间
    /// </summary>
    [SugarColumn(ColumnName = "apply_time", ColumnDescription = "申请时间")]
    public virtual DateTime? ApplyTime { get; set; }

    /// <summary>
    /// 申请科室Id
    /// </summary>
    [SugarColumn(ColumnName = "apply_dept_id", ColumnDescription = "申请科室Id")]
    public virtual long? ApplyDeptId { get; set; }

    /// <summary>
    /// 申请科室名称
    /// </summary>
    [SugarColumn(ColumnName = "apply_dept_name", ColumnDescription = "申请科室名称", Length = 64)]
    public virtual string? ApplyDeptName { get; set; }

    /// <summary>
    /// 申请医生Id
    /// </summary>
    [SugarColumn(ColumnName = "apply_doctor_id", ColumnDescription = "申请医生Id")]
    public virtual long? ApplyDoctorId { get; set; }

    /// <summary>
    /// 申请医生名称
    /// </summary>
    [SugarColumn(ColumnName = "apply_doctor_name", ColumnDescription = "申请医生名称", Length = 64)]
    public virtual string? ApplyDoctorName { get; set; }

    /// <summary>
    /// 申请医生签名
    /// </summary>
    [SugarColumn(ColumnName = "apply_doctor_sign", ColumnDescription = "申请医生签名", Length = 0)]
    public virtual string? ApplyDoctorSign { get; set; }

    /// <summary>
    /// 会诊科室Id
    /// </summary>
    [SugarColumn(ColumnName = "consultation_dept_id", ColumnDescription = "会诊科室Id")]
    public virtual long? ConsultationDeptId { get; set; }

    /// <summary>
    /// 会诊科室名称
    /// </summary>
    [SugarColumn(ColumnName = "consultation_dept_name", ColumnDescription = "会诊科室名称", Length = 64)]
    public virtual string? ConsultationDeptName { get; set; }

    /// <summary>
    /// 会诊医生Id
    /// </summary>
    [SugarColumn(ColumnName = "consultation_doctor_id", ColumnDescription = "会诊医生Id")]
    public virtual long? ConsultationDoctorId { get; set; }

    /// <summary>
    /// 会诊医生名称
    /// </summary>
    [SugarColumn(ColumnName = "consultation_doctor_name", ColumnDescription = "会诊医生名称", Length = 64)]
    public virtual string? ConsultationDoctorName { get; set; }

    /// <summary>
    /// 会诊接受时间
    /// </summary>
    [SugarColumn(ColumnName = "consultation_accept_time", ColumnDescription = "会诊接受时间")]
    public virtual DateTime? ConsultationAcceptTime { get; set; }

    /// <summary>
    /// 会诊结束时间
    /// </summary>
    [SugarColumn(ColumnName = "consultation_end_time", ColumnDescription = "会诊结束时间")]
    public virtual DateTime? ConsultationEndTime { get; set; }

    /// <summary>
    /// 院外会诊机构名称
    /// </summary>
    [SugarColumn(ColumnName = "outside_hospital_name", ColumnDescription = "院外会诊机构名称", Length = 64)]
    public virtual string? OutsideHospitalName { get; set; }
}