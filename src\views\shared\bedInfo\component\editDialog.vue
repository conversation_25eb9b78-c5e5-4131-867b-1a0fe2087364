﻿<script lang="ts" name="bedInfo" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useBedInfoApi } from '/@/api/shared/bedInfo';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
 
import PinyinSelect from '/@/components/pinyinSelect/index.vue';
const basicInfoApi = useBasicInfoApi();
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const bedInfoApi = useBedInfoApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
	bedNo: [{ required: true, message: '请选择床位编号！', trigger: 'blur', },],
	deptId: [{ required: true, message: '请选择科室id！', trigger: 'blur', },],
	deptName: [{ required: true, message: '请选择科室名称！', trigger: 'blur', },],
	wardId: [{ required: true, message: '请选择病区id！', trigger: 'blur', },],
	wardName: [{ required: true, message: '请选择病区名称！', trigger: 'blur', },],
	orderNo: [{ required: true, message: '请选择排序！', trigger: 'blur', },],
});

// 页面加载时
onMounted(async () => {
	const data = await bedInfoApi.getDropdownData(false).then(res => res.data.result) ?? {};
	state.dropdownData.bedLevel = data.bedLevel ?? [];
	await basicInfoApi.getDepartments({ orgTypes: ['InpatientDept'] }).then(res => {
		state.dropdownData.deptList = res.data.result;
	});

});
const deptChange = async (value: any) => {
	if (value) {
		state.ruleForm.deptName = state.dropdownData.deptList.find((e: any) => e.id === value)?.name;
		//病区
		await basicInfoApi.getDepartments({ parentId: value,orgTypes: ['InpatientWard'] }).then(res => {
			state.dropdownData.wardList = res.data.result;
		});

	} else {
		state.ruleForm.deptName = '';
	}
};
const wardChange = async (value: any) => {
	
	if (value) {
		state.ruleForm.wardName = state.dropdownData.wardList.find((e: any) => e.id === value)?.name;
	} else {
		state.ruleForm.wardName = '';
	}
};
const bedLevelChange = async (value: any) => {
	debugger
	if (value) {
		state.ruleForm.bedLevelName = state.dropdownData.bedLevel.find((e: any) => e.value === value)?.label;
	} else {
		state.ruleForm.bedLevelName = '';
	}
};
// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	state.showDialog = true;
	row = row ?? { status: 1, orderNo: 100, };
    if(row){
         await basicInfoApi.getDepartments({ parentId: row.deptId,orgTypes: ['InpatientWard'] }).then(res => {
			state.dropdownData.wardList = res.data.result;
		});
	}

	state.ruleForm = row.id ? await bedInfoApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await bedInfoApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="bedInfo-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="床位编号" prop="bedNo">
							<el-input v-model="state.ruleForm.bedNo" placeholder="请输入床位编号" maxlength="100"
								show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科室" prop="deptId">
						<PinyinSelect v-model="state.ruleForm.deptId" placeholder="请选择科室" @change="deptChange"
								:options="state.dropdownData.deptList" />

						</el-form-item>
					</el-col>
					 
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="病区" prop="wardId">
						<PinyinSelect v-model="state.ruleForm.wardId" placeholder="请选择病区" @change="wardChange"
								:options="state.dropdownData.wardList" />
						</el-form-item>
					</el-col>
			 
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="房间编号" prop="roomNo">
							<el-input v-model="state.ruleForm.roomNo" placeholder="请输入房间编号" maxlength="100"
								show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="床位类型" prop="bedType">
							<g-sys-dict v-model="state.ruleForm.bedType" code="InpatientBedType" render-as="select"
								placeholder="请选择床位类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="床位状态" prop="bedStatus">
							<g-sys-dict v-model="state.ruleForm.bedStatus" code="InpatientBedStatus" render-as="select"
								placeholder="请选择床位状态" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="床位等级" prop="bedLevelId">
							<el-select clearable filterable v-model="state.ruleForm.bedLevelId" placeholder="请选择床位等级"
							 @change="bedLevelChange"
							>
								<el-option v-for="(item, index) in state.dropdownData.bedLevel" :key="index"
								
									:value="item.value" :label="item.label" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="排序" prop="orderNo">
							<el-input-number v-model="state.ruleForm.orderNo" placeholder="请输入排序" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="state.ruleForm.id">
						<el-form-item label="状态" prop="status">
							<g-sys-dict v-model="state.ruleForm.status" code="StatusEnum" render-as="select"
								placeholder="请选择状态" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="255"
								show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>