﻿using Admin.NET.Core;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Registration;

/// <summary>
/// 诊台维护基础输入参数
/// </summary>
public class ClinicConsoleBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 诊台名称
    /// </summary>
    public virtual string? Name { get; set; }
    
    /// <summary>
    /// 诊台编码
    /// </summary>
    public virtual string? Code { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    public virtual long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public virtual string? RoomName { get; set; }
    
    /// <summary>
    /// 当前人数
    /// </summary>
    public virtual int? CurrentCount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 诊台维护分页查询输入参数
/// </summary>
public class PageClinicConsoleInput : BasePageInput
{
    /// <summary>
    /// 诊台名称
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊台编码
    /// </summary>
    public string? Code { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 当前人数
    /// </summary>
    public int? CurrentCount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 诊台维护增加输入参数
/// </summary>
public class AddClinicConsoleInput
{
    /// <summary>
    /// 诊台名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "诊台名称字符长度不能超过255")]
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊台编码
    /// </summary>
    [MaxLength(255, ErrorMessage = "诊台编码字符长度不能超过255")]
    public string? Code { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "诊室名称字符长度不能超过255")]
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 当前人数
    /// </summary>
    public int? CurrentCount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 诊台维护删除输入参数
/// </summary>
public class DeleteClinicConsoleInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 诊台维护更新输入参数
/// </summary>
public class UpdateClinicConsoleInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 诊台名称
    /// </summary>    
    [MaxLength(255, ErrorMessage = "诊台名称字符长度不能超过255")]
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊台编码
    /// </summary>    
    [MaxLength(255, ErrorMessage = "诊台编码字符长度不能超过255")]
    public string? Code { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>    
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>    
    [MaxLength(255, ErrorMessage = "诊室名称字符长度不能超过255")]
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 当前人数
    /// </summary>    
    public int? CurrentCount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 诊台维护主键查询输入参数
/// </summary>
public class QueryByIdClinicConsoleInput : DeleteClinicConsoleInput
{
}

/// <summary>
/// 诊台维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportClinicConsoleInput : BaseImportInput
{
    /// <summary>
    /// 诊台名称
    /// </summary>
    [ImporterHeader(Name = "诊台名称")]
    [ExporterHeader("诊台名称", Format = "", Width = 25, IsBold = true)]
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊台编码
    /// </summary>
    [ImporterHeader(Name = "诊台编码")]
    [ExporterHeader("诊台编码", Format = "", Width = 25, IsBold = true)]
    public string? Code { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    [ImporterHeader(Name = "诊室id")]
    [ExporterHeader("诊室id", Format = "", Width = 25, IsBold = true)]
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [ImporterHeader(Name = "诊室名称")]
    [ExporterHeader("诊室名称", Format = "", Width = 25, IsBold = true)]
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 当前人数
    /// </summary>
    [ImporterHeader(Name = "当前人数")]
    [ExporterHeader("当前人数", Format = "", Width = 25, IsBold = true)]
    public int? CurrentCount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
