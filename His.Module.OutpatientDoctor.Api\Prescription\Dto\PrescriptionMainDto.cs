﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 处方主表输出参数
/// </summary>
public class PrescriptionMainDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 处方号
    /// </summary>
    public string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    public DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 处方类型
    /// </summary>
    public string? PrescriptionType { get; set; }

    /// <summary>
    /// 处方名称
    /// </summary>
    public string? PrescriptionName { get; set; }

    /// <summary>
    /// 处方类型
    /// </summary>
    public string? OutpatientPrescriptionType { get; set; }
    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string?  CardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
    public string?  OutpatientNo { get; set; }
 
    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 挂号Id
    /// </summary>
    public long? RegisterId { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }
    /// <summary>
    /// 开单科室名称
    /// </summary>
 
    public virtual string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生姓名
    /// </summary> 
    public virtual string? BillingDoctorName { get; set; }
    /// <summary>
    /// 开单医生签名
    /// </summary>
    public string? BillingDoctorSign { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 退费人员Id
    /// </summary>
    public long? RefundStaffId { get; set; }

    /// <summary>
    /// 退费时间
    /// </summary>
    public DateTime? RefundTime { get; set; }

    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 诊断编码
    /// </summary>
    public string? DiagnosticCode { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosticName { get; set; }

    /// <summary>
    /// 次诊断1编码
    /// </summary>
    public string? Diagnostic1Code { get; set; }

    /// <summary>
    /// 次诊断1名称
    /// </summary>
    public string? Diagnostic1Name { get; set; }

    /// <summary>
    /// 次诊断2编码
    /// </summary>
    public string? Diagnostic2Code { get; set; }

    /// <summary>
    /// 次诊断2名称
    /// </summary>
    public string? Diagnostic2Name { get; set; }

    /// <summary>
    /// 中医诊断编码
    /// </summary>
    public string? TcmDiagnosticCode { get; set; }

    /// <summary>
    /// 中医诊断名称
    /// </summary>
    public string? TcmDiagnosticName { get; set; }

    /// <summary>
    /// 中医证型编号
    /// </summary> 
    public virtual string? TcmSyndromeCode { get; set; }

    /// <summary>
    /// 中医证型名称
    /// </summary> 
    public virtual string? TcmSyndromeName { get; set; }
    /// <summary>
    /// 是否打印
    /// </summary>
    public int? IsPrint { get; set; }

    /// <summary>
    /// 中药付数
    /// </summary>
    public int? HerbsQuantity { get; set; }

    /// <summary>
    /// 中药煎法
    /// </summary>
    public string? HerbsDecoction { get; set; }

    /// <summary>
    /// 是否代煎
    /// </summary>
    public int? IsDecoction { get; set; }

    /// <summary>
    /// 创建者部门Id
    /// </summary>
    public long? CreateOrgId { get; set; }

    /// <summary>
    /// 创建者部门名称
    /// </summary>
    public string? CreateOrgName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    /// 打印时间
    /// </summary>
    public DateTime? PrintTime { get; set; }

    /// <summary>
    /// 收费主表Id
    /// </summary>
    public long? ChargeMainId { get; set; }

    /// <summary>
    /// 退费发票号
    /// </summary>
    public string? RefundInvoiceNumber { get; set; }
    
    /// <summary>
    /// 药房Id
    /// </summary>
    public long? StorageId { get; set; }

    /// <summary>
    /// 药房名称
    /// </summary>
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 总金额
    /// </summary>
 
    public   decimal? TotalAmount { get; set; }
    
    
    public List<PrescriptionDetailDto> Details { get; set; }   
    
}
