using His.Module.Insurance.Service.Settlement.Dto;

namespace His.Module.Insurance.Service.Settlement.Model.Outpatient;

/// <summary>
/// 门诊预结算接口返回结果
/// </summary>
public class SettleMzPreResponse:BaseSettlementResponse
{
    /// <summary>
    /// 执行代码（0成功，其余失败）
    /// </summary>
    public string resultcode { get; set; }

    /// <summary>
    /// *住院流水号（系统生成）
    /// </summary>
    public string zylsh { get; set; }

    /// <summary>
    /// *疾病编码（门诊录入疾病返回，主要生育使用）
    /// </summary>
    public string jbbm { get; set; }

    /// <summary>
    /// *病人负担金额（含个人账户支付）
    /// </summary>
    public double brfdje { get; set; }

    /// <summary>
    /// 医保负担金额（医保报销总金额）
    /// </summary>
    public double ybfdje { get; set; }

    /// <summary>
    /// 医疗补助金额（优抚对象补助等）
    /// </summary>
    public double ylbzje { get; set; }

    /// <summary>
    /// 医院负担金额
    /// </summary>
    public double yyfdje { get; set; }

    /// <summary>
    /// 本次结算费用总额（=病人+医保+补助+医院）
    /// </summary>
    public double zje { get; set; }

    /// <summary>
    /// 医疗统筹登记号
    /// </summary>
    public string yltcdjh { get; set; }

    /// <summary>
    /// 统计人员类别（用于 HIS 打印门诊发票，见字典 TJRYLB）
    /// </summary>
    public string tjrylb { get; set; }

    /// <summary>
    /// 病人结算日期
    /// </summary>
    public DateTime brjsrq { get; set; }

    /// <summary>
    /// 个人账户支付
    /// </summary>
    public double grzhzf { get; set; }

    /// <summary>
    /// 本次统筹支付
    /// </summary>
    public double tczf { get; set; }

    /// <summary>
    /// 本次大额支付
    /// </summary>
    public double dezf { get; set; }

    /// <summary>
    /// 大额商业保险
    /// </summary>
    public double desybx { get; set; }

    /// <summary>
    /// 本次公务员补助
    /// </summary>
    public double gwybz { get; set; }

    /// <summary>
    /// 本次财政列支
    /// </summary>
    public double czlz { get; set; }

    /// <summary>
    /// 暂缓支付
    /// </summary>
    public double zhzf { get; set; }

    /// <summary>
    /// 累计统筹支付
    /// </summary>
    public double ljtczf { get; set; }

    /// <summary>
    /// 累计大额支付
    /// </summary>
    public double ljdezf { get; set; }

    /// <summary>
    /// 累计门诊额度
    /// </summary>
    public double ljmzed { get; set; }

    /// <summary>
    /// 累积个人支付
    /// </summary>
    public double ljgrzf { get; set; }

    /// <summary>
    /// 其他统筹支付
    /// </summary>
    public double qttczf { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    public string fph { get; set; }

    /// <summary>
    /// 发票人员类别
    /// </summary>
    public string fprylb { get; set; }

    /// <summary>
    /// 账户余额
    /// </summary>
    public double zhye { get; set; }

    /// <summary>
    /// 优抚对象减免金额
    /// </summary>
    public double jmje { get; set; }

    /// <summary>
    /// 本次起付线
    /// </summary>
    public double bcqfx { get; set; }

    /// <summary>
    /// 本次进入统筹额度
    /// </summary>
    public double bcnrtcfw { get; set; }

    /// <summary>
    /// 本年进入统筹额度
    /// </summary>
    public double bnynrtcfw { get; set; }

    /// <summary>
    /// 首先自付金额
    /// </summary>
    public double sxzfje { get; set; }

    /// <summary>
    /// 统筹范围内个人负担金额（济南专用，含本次起付线）
    /// </summary>
    public double tcfwnfd { get; set; }

    /// <summary>
    /// 医保额度累计（济南专用）
    /// </summary>
    public double ljmznrtc { get; set; }

    /// <summary>
    /// 本次目录外（济南、省直专用）
    /// </summary>
    public double bcmlw { get; set; }

    /// <summary>
    /// 累计目录外（济南、省直专用）
    /// </summary>
    public double ljmlw { get; set; }

    /// <summary>
    /// 累计起付线（济南、省直专用）
    /// </summary>
    public double ljqfx { get; set; }

    /// <summary>
    /// 本次补充支付（济南、省直专用）
    /// </summary>
    public double bcbczf { get; set; }

    /// <summary>
    /// 累计补充支付（济南、省直专用）
    /// </summary>
    public double ljbczf { get; set; }

    /// <summary>
    /// 其中基本账户支付额（兖矿专用）
    /// </summary>
    public double qzjbzhzf { get; set; }

    /// <summary>
    /// 本次消费现金（济南普通门诊使用）
    /// </summary>
    public double xj { get; set; }

    /// <summary>
    /// 大病补助金额（潍坊使用）
    /// </summary>
    public double dbbzje { get; set; }

    /// <summary>
    /// 本年度累计普通门诊报销金额/门诊额度累计（济南）
    /// </summary>
    public double bnptmzljbxje { get; set; }

    /// <summary>
    /// 本年度累计门诊大病报销金额
    /// </summary>
    public double bnmzdbljbxje { get; set; }

    /// <summary>
    /// 二次报销金额（济南职工使用）
    /// </summary>
    public double ecbxje { get; set; }

    /// <summary>
    /// 医疗机构减免（东营、威海专用）
    /// </summary>
    public double fpryyljgjm { get; set; }

    /// <summary>
    /// 贫困人口补充报销金额 / 商业兜底（潍坊、东营、威海）
    /// </summary>
    public double pkrkbcbxje { get; set; }

    /// <summary>
    /// 民政救助（威海专用）
    /// </summary>
    public double mzbzje { get; set; }

    /// <summary>
    /// 民政优抚（威海专用）
    /// </summary>
    public double yfbzy { get; set; }

    /// <summary>
    /// 乙类首先自付（济南）
    /// </summary>
    public double bfzf { get; set; }

    /// <summary>
    /// 累计统筹报销（济南）
    /// </summary>
    public double ljbxje { get; set; }

    /// <summary>
    /// 累计自付金额（省直）
    /// </summary>
    public double ljzfje { get; set; }

    /// <summary>
    /// 苯丙酮尿酸症医保报销金额（威海）
    /// </summary>
    public double bbtnzbxje { get; set; }

    /// <summary>
    /// 苯丙酮尿酸症医院报销金额（威海）
    /// </summary>
    public double bbtnzyyfdje { get; set; }

    /// <summary>
    /// 贫困人口再救助
    /// </summary>
    public double pkryzjzje { get; set; }

    /// <summary>
    /// 老党员报销金额
    /// </summary>
    public double ldybxje { get; set; }

    /// <summary>
    /// 重复取药提示（聊城：慢性病30天内重复取药时返回）
    /// </summary>
    public string cfqyts { get; set; }

    /// <summary>
    /// 医疗统筹类别明细（601普通门诊，602门诊统筹）
    /// </summary>
    public string yltclbmx { get; set; }

    /// <summary>
    /// 惠民保报销金额目录内
    /// </summary>
    public double hmbbxjemln { get; set; }

    /// <summary>
    /// 惠民保报销金额目录外
    /// </summary>
    public double hmbbxjemlw { get; set; }

    /// <summary>
    /// 惠民保报销金额
    /// </summary>
    public double hmbbxje { get; set; }

    /// <summary>
    /// 省异地个人账户支付（异地结算时消费的本人账户金额，可打印发票）
    /// </summary>
    public double sydgrzhzf { get; set; }
}