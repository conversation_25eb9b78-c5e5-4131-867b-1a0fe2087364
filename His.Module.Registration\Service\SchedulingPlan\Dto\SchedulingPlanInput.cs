﻿using Admin.NET.Core;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Registration;

/// <summary>
/// 医生排班计划基础输入参数
/// </summary>
public class SchedulingPlanBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 医生id
    /// </summary>
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 时间段id
    /// </summary>
    public virtual long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    public virtual string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    public virtual string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 号别id
    /// </summary>
    public virtual long? RegCategoryId { get; set; }
    
    /// <summary>
    /// 挂号类别名称
    /// </summary>
    public virtual string? RegCategoryName { get; set; }
    
    /// <summary>
    /// 限号数
    /// </summary>
    public virtual int? RegLimit { get; set; }
    
    /// <summary>
    /// 限预约号数
    /// </summary>
    public virtual int? AppLimit { get; set; }
    
    /// <summary>
    /// 已挂号数
    /// </summary>
    public virtual int? RegNumber { get; set; }
    
    /// <summary>
    /// 已预约号数
    /// </summary>
    public virtual int? AppNumber { get; set; }
    
    /// <summary>
    /// 门诊日期
    /// </summary>
    public virtual DateTime? OutpatientDate { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public virtual DateTime? StartTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    public virtual DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 星期几
    /// </summary>
    public virtual string? WeekDay { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    public virtual long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public virtual string? RoomName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    public virtual string? IpAddress { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 医生排班计划分页查询输入参数
/// </summary>
public class PageSchedulingPlanInput : BasePageInput
{
   
    
    /// <summary>
    /// 门诊日期范围
    /// </summary>
     public DateTime  OutpatientDate  { get; set; }
    
   
    /// <summary>
    /// 科室id
    /// </summary>
    public long? DeptId { get; set; }
   
}

/// <summary>
/// 医生排班计划增加输入参数
/// </summary>
public class AddSchedulingPlanInput
{
    /// <summary>
    /// 医生id
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "医生姓名字符长度不能超过64")]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 时间段id
    /// </summary>
    public long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    [MaxLength(64, ErrorMessage = "时间段编码字符长度不能超过64")]
    public string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "时间段名称字符长度不能超过64")]
    public string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 号别id
    /// </summary>
    public long? RegCategoryId { get; set; }
    
    /// <summary>
    /// 挂号类别名称
    /// </summary>
    [MaxLength(200, ErrorMessage = "挂号类别名称字符长度不能超过200")]
    public string? RegCategoryName { get; set; }
    
    /// <summary>
    /// 限号数
    /// </summary>
    public int? RegLimit { get; set; }
    
    /// <summary>
    /// 限预约号数
    /// </summary>
    public int? AppLimit { get; set; }
    
    /// <summary>
    /// 已挂号数
    /// </summary>
    public int? RegNumber { get; set; }
    
    /// <summary>
    /// 已预约号数
    /// </summary>
    public int? AppNumber { get; set; }
    
    /// <summary>
    /// 门诊日期
    /// </summary>
    public DateTime? OutpatientDate { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public TimeSpan? StartTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    public TimeSpan? EndTime { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "科室名称字符长度不能超过64")]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 星期几
    /// </summary>
    [MaxLength(32, ErrorMessage = "星期几字符长度不能超过32")]
    public string? WeekDay { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "诊室名称字符长度不能超过64")]
    public string? RoomName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    [MaxLength(64, ErrorMessage = "ip地址字符长度不能超过64")]
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注字符长度不能超过256")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 医生排班计划删除输入参数
/// </summary>
public class DeleteSchedulingPlanInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 医生排班计划更新输入参数
/// </summary>
public class UpdateSchedulingPlanInput: AddSchedulingPlanInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
     
}

/// <summary>
/// 医生排班计划主键查询输入参数
/// </summary>
public class QueryByIdSchedulingPlanInput : DeleteSchedulingPlanInput
{
}

/// <summary>
/// 医生排班计划数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportSchedulingPlanInput : BaseImportInput
{
    /// <summary>
    /// 医生id
    /// </summary>
    [ImporterHeader(Name = "医生id")]
    [ExporterHeader("医生id", Format = "", Width = 25, IsBold = true)]
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    [ImporterHeader(Name = "医生姓名")]
    [ExporterHeader("医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 时间段id
    /// </summary>
    [ImporterHeader(Name = "时间段id")]
    [ExporterHeader("时间段id", Format = "", Width = 25, IsBold = true)]
    public long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    [ImporterHeader(Name = "时间段编码")]
    [ExporterHeader("时间段编码", Format = "", Width = 25, IsBold = true)]
    public string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    [ImporterHeader(Name = "时间段名称")]
    [ExporterHeader("时间段名称", Format = "", Width = 25, IsBold = true)]
    public string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 号别id
    /// </summary>
    [ImporterHeader(Name = "号别id")]
    [ExporterHeader("号别id", Format = "", Width = 25, IsBold = true)]
    public long? RegCategoryId { get; set; }
    
    /// <summary>
    /// 挂号类别名称
    /// </summary>
    [ImporterHeader(Name = "挂号类别名称")]
    [ExporterHeader("挂号类别名称", Format = "", Width = 25, IsBold = true)]
    public string? RegCategoryName { get; set; }
    
    /// <summary>
    /// 限号数
    /// </summary>
    [ImporterHeader(Name = "限号数")]
    [ExporterHeader("限号数", Format = "", Width = 25, IsBold = true)]
    public int? RegLimit { get; set; }
    
    /// <summary>
    /// 限预约号数
    /// </summary>
    [ImporterHeader(Name = "限预约号数")]
    [ExporterHeader("限预约号数", Format = "", Width = 25, IsBold = true)]
    public int? AppLimit { get; set; }
    
    /// <summary>
    /// 已挂号数
    /// </summary>
    [ImporterHeader(Name = "已挂号数")]
    [ExporterHeader("已挂号数", Format = "", Width = 25, IsBold = true)]
    public int? RegNumber { get; set; }
    
    /// <summary>
    /// 已预约号数
    /// </summary>
    [ImporterHeader(Name = "已预约号数")]
    [ExporterHeader("已预约号数", Format = "", Width = 25, IsBold = true)]
    public int? AppNumber { get; set; }
    
    /// <summary>
    /// 门诊日期
    /// </summary>
    [ImporterHeader(Name = "门诊日期")]
    [ExporterHeader("门诊日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? OutpatientDate { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    [ImporterHeader(Name = "开始时间")]
    [ExporterHeader("开始时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? StartTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    [ImporterHeader(Name = "结束时间")]
    [ExporterHeader("结束时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    [ImporterHeader(Name = "科室id")]
    [ExporterHeader("科室id", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [ImporterHeader(Name = "科室名称")]
    [ExporterHeader("科室名称", Format = "", Width = 25, IsBold = true)]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 星期几
    /// </summary>
    [ImporterHeader(Name = "星期几")]
    [ExporterHeader("星期几", Format = "", Width = 25, IsBold = true)]
    public string? WeekDay { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    [ImporterHeader(Name = "诊室id")]
    [ExporterHeader("诊室id", Format = "", Width = 25, IsBold = true)]
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [ImporterHeader(Name = "诊室名称")]
    [ExporterHeader("诊室名称", Format = "", Width = 25, IsBold = true)]
    public string? RoomName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    [ImporterHeader(Name = "ip地址")]
    [ExporterHeader("ip地址", Format = "", Width = 25, IsBold = true)]
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
