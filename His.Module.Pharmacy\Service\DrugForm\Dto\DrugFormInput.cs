﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品剂型维护基础输入参数
/// </summary>
public class DrugFormBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 剂型名称
    /// </summary>
    public virtual string? FormName { get; set; }
    
    /// <summary>
    /// 大剂型ID
    /// </summary>
    public virtual int? BigFormId { get; set; }
    
    /// <summary>
    /// 大剂型名称
    /// </summary>
    public virtual string? BigFormName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品剂型维护分页查询输入参数
/// </summary>
public class PageDrugFormInput : BasePageInput
{
    /// <summary>
    /// 剂型名称
    /// </summary>
    public string? FormName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品剂型维护增加输入参数
/// </summary>
public class AddDrugFormInput
{
    /// <summary>
    /// 剂型名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "剂型名称字符长度不能超过100")]
    public string? FormName { get; set; }
    
    /// <summary>
    /// 大剂型ID
    /// </summary>
    public int? BigFormId { get; set; }
    
    /// <summary>
    /// 大剂型名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "大剂型名称字符长度不能超过100")]
    public string? BigFormName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品剂型维护删除输入参数
/// </summary>
public class DeleteDrugFormInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品剂型维护更新输入参数
/// </summary>
public class UpdateDrugFormInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 剂型名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "剂型名称字符长度不能超过100")]
    public string? FormName { get; set; }
    
    /// <summary>
    /// 大剂型ID
    /// </summary>    
    public int? BigFormId { get; set; }
    
    /// <summary>
    /// 大剂型名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "大剂型名称字符长度不能超过100")]
    public string? BigFormName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品剂型维护主键查询输入参数
/// </summary>
public class QueryByIdDrugFormInput : DeleteDrugFormInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataDrugFormInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetDrugFormStatusInput : BaseStatusInput
{
}

/// <summary>
/// 药品剂型维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugFormInput : BaseImportInput
{
    /// <summary>
    /// 剂型名称
    /// </summary>
    [ImporterHeader(Name = "剂型名称")]
    [ExporterHeader("剂型名称", Format = "", Width = 25, IsBold = true)]
    public string? FormName { get; set; }
    
    /// <summary>
    /// 剂型名称拼音
    /// </summary>
    [ImporterHeader(Name = "剂型名称拼音")]
    [ExporterHeader("剂型名称拼音", Format = "", Width = 25, IsBold = true)]
    public string? FormNamePinyin { get; set; }
    
    /// <summary>
    /// 大剂型ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? BigFormId { get; set; }
    
    /// <summary>
    /// 大剂型ID 文本
    /// </summary>
    [ImporterHeader(Name = "大剂型ID")]
    [ExporterHeader("大剂型ID", Format = "", Width = 25, IsBold = true)]
    public string BigFormFkDisplayName { get; set; }
    
    /// <summary>
    /// 大剂型名称
    /// </summary>
    [ImporterHeader(Name = "大剂型名称")]
    [ExporterHeader("大剂型名称", Format = "", Width = 25, IsBold = true)]
    public string? BigFormName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
}
