﻿namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 处方主表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("prescription_main", "处方主表")]
public class PrescriptionMain : EntityTenantBaseData
{
    /// <summary>
    /// 药房
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "药房", Length = 64)]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 发药人
    /// </summary>
    [SugarColumn(ColumnName = "send_user_id", ColumnDescription = "发药人", Length = 64)]
    public virtual long? SendUserId { get; set; }
    /// <summary>
    /// 退药人
    /// </summary>
    [SugarColumn(ColumnName = "refund_user_id", ColumnDescription = "退药人", Length = 64)]
    public virtual long? RefundUserId { get; set; }
    /// <summary>
    /// 退费原因
    /// </summary>
    [SugarColumn(ColumnName = "refund_reason", ColumnDescription = "退费原因", Length = 64)]
    public virtual string? RefundReason { get; set; }
    /// <summary>
    /// 药房
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "药房", Length = 64)]
    public virtual string? StorageName { get; set; }
    /// <summary>
    /// 处方号
    /// </summary>
    [SugarColumn(ColumnName = "prescription_no", ColumnDescription = "处方号", Length = 64)]
    public virtual string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    [SugarColumn(ColumnName = "prescription_time", ColumnDescription = "处方时间")]
    public virtual DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 处方类型
    /// </summary>
    [SugarColumn(ColumnName = "prescription_type", ColumnDescription = "处方类型", Length = 64)]
    public virtual string? PrescriptionType { get; set; }

    /// <summary>
    /// 处方名称
    /// </summary>
    [SugarColumn(ColumnName = "prescription_name", ColumnDescription = "处方名称", Length = 64)]
    public virtual string? PrescriptionName { get; set; }

    /// <summary>
    /// 门诊处方类型
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_prescription_type", ColumnDescription = "门诊处方类型", Length = 64)]
    public virtual string? OutpatientPrescriptionType { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号")]
    public virtual string? VisitNo { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "就诊卡号")]
    public virtual string? CardNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>

    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号")]
    public virtual string? OutpatientNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 64)]
    public virtual string? PatientName { get; set; }

    /// <summary>
    /// 挂号Id
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "挂号Id")]
    public virtual long? RegisterId { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_id", ColumnDescription = "开单科室Id")]
    public virtual long? BillingDeptId { get; set; }


    /// <summary>
    /// 开单科室名称
    /// </summary>

    [SugarColumn(ColumnName = "billing_dept_name", ColumnDescription = "开单科室姓名", Length = 64)]
    public virtual string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_name", ColumnDescription = "开单医生姓名", Length = 64)]
    public virtual string? BillingDoctorName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_id", ColumnDescription = "开单医生Id")]
    public virtual long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生签名
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_sign", ColumnDescription = "开单医生签名", Length = 0)]
    public virtual string? BillingDoctorSign { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_staff_id", ColumnDescription = "收费人员Id")]
    public virtual long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    [SugarColumn(ColumnName = "charge_time", ColumnDescription = "收费时间")]
    public virtual DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 退费人员Id
    /// </summary>
    [SugarColumn(ColumnName = "refund_staff_id", ColumnDescription = "退费人员Id")]
    public virtual long? RefundStaffId { get; set; }

    /// <summary>
    /// 退费时间
    /// </summary>
    [SugarColumn(ColumnName = "refund_time", ColumnDescription = "退费时间")]
    public virtual DateTime? RefundTime { get; set; }

    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 诊断编码
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic_code", ColumnDescription = "诊断编码", Length = 128)]
    public virtual string? DiagnosticCode { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic_name", ColumnDescription = "诊断名称", Length = 128)]
    public virtual string? DiagnosticName { get; set; }

    /// <summary>
    /// 次诊断1编码
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic1_code", ColumnDescription = "次诊断1编码", Length = 128)]
    public virtual string? Diagnostic1Code { get; set; }

    /// <summary>
    /// 次诊断1名称
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic1_name", ColumnDescription = "次诊断1名称", Length = 128)]
    public virtual string? Diagnostic1Name { get; set; }

    /// <summary>
    /// 次诊断2编码
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic2_code", ColumnDescription = "次诊断2编码", Length = 128)]
    public virtual string? Diagnostic2Code { get; set; }

    /// <summary>
    /// 次诊断2名称
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic2_name", ColumnDescription = "次诊断2名称", Length = 128)]
    public virtual string? Diagnostic2Name { get; set; }

    /// <summary>
    /// 中医诊断编码
    /// </summary>
    [SugarColumn(ColumnName = "tcm_diagnostic_code", ColumnDescription = "中医诊断编码", Length = 128)]
    public virtual string? TcmDiagnosticCode { get; set; }

    /// <summary>
    /// 中医诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "tcm_diagnostic_name", ColumnDescription = "中医诊断名称", Length = 128)]
    public virtual string? TcmDiagnosticName { get; set; }
//

    /// <summary>
    /// 中医证型编号
    /// </summary>
    [SugarColumn(ColumnName = "tcm_syndrome_code", ColumnDescription = "中医证型编号", Length = 128)]
    public virtual string? TcmSyndromeCode { get; set; }

    /// <summary>
    /// 中医证型名称
    /// </summary>
    [SugarColumn(ColumnName = "tcm_syndrome_name", ColumnDescription = "中医证型名称", Length = 128)]
    public virtual string? TcmSyndromeName { get; set; }
    /// <summary>
    /// 是否打印
    /// </summary>
    [SugarColumn(ColumnName = "is_print", ColumnDescription = "是否打印")]
    public virtual int? IsPrint { get; set; }

    /// <summary>
    /// 中药付数
    /// </summary>
    [SugarColumn(ColumnName = "herbs_quantity", ColumnDescription = "中药付数")]
    public virtual int? HerbsQuantity { get; set; }

    /// <summary>
    /// 中药煎法
    /// </summary>
    [SugarColumn(ColumnName = "herbs_decoction", ColumnDescription = "中药煎法", Length = 128)]
    public virtual string? HerbsDecoction { get; set; }

    /// <summary>
    /// 是否代煎
    /// </summary>
    [SugarColumn(ColumnName = "is_decoction", ColumnDescription = "是否代煎")]
    public virtual int? IsDecoction { get; set; }

    /// <summary>
    /// 打印时间
    /// </summary>
    [SugarColumn(ColumnName = "print_time", ColumnDescription = "打印时间")]
    public virtual DateTime? PrintTime { get; set; }

    /// <summary>
    /// 收费主表Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_main_id", ColumnDescription = "收费主表Id")]
    public virtual long? ChargeMainId { get; set; }

    /// <summary>
    /// 退费发票号
    /// </summary>
    [SugarColumn(ColumnName = "refund_invoice_number", ColumnDescription = "退费发票号", Length = 32)]
    public virtual string? RefundInvoiceNumber { get; set; }
    
    /// <summary>
    /// 总金额
    /// </summary>
    [SugarColumn(ColumnName = "total_amount", ColumnDescription = "总金额" )]
    public virtual decimal? TotalAmount { get; set; }
    
}