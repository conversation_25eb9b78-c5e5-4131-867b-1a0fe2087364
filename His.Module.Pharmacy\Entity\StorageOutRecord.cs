﻿namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品出库记录表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("storage_out_record", "药品出库记录表")]
public class StorageOutRecord : EntityTenant
{
    /// <summary>
    /// 库房ID
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "库房ID")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [SugarColumn(ColumnName = "storage_code", ColumnDescription = "库房编码", Length = 100)]
    public virtual string? StorageCode { get; set; }

    /// <summary>
    /// 库房名称
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "库房名称", Length = 100)]
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类型", Length = 100)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 出库单号
    /// </summary>
    [SugarColumn(ColumnName = "storage_out_no", ColumnDescription = "出库单号", Length = 100)]
    public virtual string? StorageOutNo { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    [SugarColumn(ColumnName = "supplier_id", ColumnDescription = "供应商ID")]
    public virtual long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商编码
    /// </summary>
    [SugarColumn(ColumnName = "supplier_code", ColumnDescription = "供应商编码", Length = 100)]
    public virtual string? SupplierCode { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    [SugarColumn(ColumnName = "supplier_name", ColumnDescription = "供应商名称", Length = 100)]
    public virtual string? SupplierName { get; set; }
    
    /// <summary>
    /// 出库类型
    /// </summary>
    [SugarColumn(ColumnName = "storage_out_type", ColumnDescription = "出库类型", Length = 100)]
    public virtual string? StorageOutType { get; set; }
    
    /// <summary>
    /// 出库日期
    /// </summary>
    [SugarColumn(ColumnName = "storage_out_time", ColumnDescription = "出库日期")]
    public virtual DateTime? StorageOutTime { get; set; }
    
    /// <summary>
    /// 目标科室ID
    /// </summary>
    [SugarColumn(ColumnName = "target_dept_id", ColumnDescription = "目标科室ID")]
    public virtual long? TargetDeptId { get; set; }
    
    /// <summary>
    /// 目标科室编码
    /// </summary>
    [SugarColumn(ColumnName = "target_dept_code", ColumnDescription = "目标科室编码", Length = 100)]
    public virtual string? TargetDeptCode { get; set; }
    
    /// <summary>
    /// 目标科室名称
    /// </summary>
    [SugarColumn(ColumnName = "target_dept_name", ColumnDescription = "目标科室名称", Length = 100)]
    public virtual string? TargetDeptName { get; set; }
    
    /// <summary>
    /// 目标用户ID
    /// </summary>
    [SugarColumn(ColumnName = "target_user_id", ColumnDescription = "目标用户ID")]
    public virtual long? TargetUserId { get; set; }
    
    /// <summary>
    /// 目标用户编码
    /// </summary>
    [SugarColumn(ColumnName = "target_user_code", ColumnDescription = "目标用户编码", Length = 100)]
    public virtual string? TargetUserCode { get; set; }
    
    /// <summary>
    /// 目标用户名称
    /// </summary>
    [SugarColumn(ColumnName = "target_user_name", ColumnDescription = "目标用户名称", Length = 100)]
    public virtual string? TargetUserName { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    [SugarColumn(ColumnName = "total_purchase_price", ColumnDescription = "总进价", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [SugarColumn(ColumnName = "total_sale_price", ColumnDescription = "总零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    [SugarColumn(ColumnName = "invoice_no", ColumnDescription = "发票号", Length = 100)]
    public virtual string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 100)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
}
