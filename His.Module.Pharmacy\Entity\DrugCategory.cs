﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品分类表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_category", "药品分类表")]
public class DrugCategory : EntityTenant
{
    /// <summary>
    /// 分类编码
    /// </summary>
    [SugarColumn(ColumnName = "category_code", ColumnDescription = "分类编码", Length = 100)]
    public virtual string? CategoryCode { get; set; }
    
    /// <summary>
    /// 分类名称
    /// </summary>
    [SugarColumn(ColumnName = "category_name", ColumnDescription = "分类名称", Length = 100)]
    public virtual string? CategoryName { get; set; }
    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态（1 启用 2 停用）")]
    public virtual int? Status { get; set; }
    
}
