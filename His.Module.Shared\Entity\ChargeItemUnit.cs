﻿namespace His.Module.Shared.Entity;

/// <summary>
/// 收费项目单位表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("charge_item_unit", "收费项目单位表")]
public class ChargeItemUnit : EntityTenant
{
    /// <summary>
    /// 单位编码
    /// </summary>
    [SugarColumn(ColumnName = "unit_code", ColumnDescription = "单位编码", Length = 64)]
    public virtual string? UnitCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [SugarColumn(ColumnName = "unit_name", ColumnDescription = "单位名称", Length = 64)]
    public virtual string? UnitName { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 64)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 64)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }
}