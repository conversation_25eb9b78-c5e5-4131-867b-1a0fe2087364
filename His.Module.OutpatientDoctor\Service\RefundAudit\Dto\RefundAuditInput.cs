﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 退费审核表基础输入参数
/// </summary>
public class RefundAuditBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 退费申请ID
    /// </summary>
    public virtual long? ApplyId { get; set; }
    
    /// <summary>
    /// 审核时间
    /// </summary>
    public virtual DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 审核部门ID
    /// </summary>
    public virtual long? AuditDeptId { get; set; }
    
    /// <summary>
    /// 审核部门编码
    /// </summary>
    public virtual string? AuditDeptCode { get; set; }
    
    /// <summary>
    /// 审核部门名称
    /// </summary>
    public virtual string? AuditDeptName { get; set; }
    
    /// <summary>
    /// 审核人ID
    /// </summary>
    public virtual long? AuditUserId { get; set; }
    
    /// <summary>
    /// 审核人编码
    /// </summary>
    public virtual string? AuditUserCode { get; set; }
    
    /// <summary>
    /// 审核人名称
    /// </summary>
    public virtual string? AuditUserName { get; set; }
    
    /// <summary>
    /// 审核原因
    /// </summary>
    public virtual string? AuditReason { get; set; }
    
    /// <summary>
    /// 审核状态
    /// </summary>
    public virtual int? AuditStatus { get; set; }
    
}

/// <summary>
/// 退费审核表分页查询输入参数
/// </summary>
public class RefundAuditQueryInput 
{
    /// <summary>
    /// 退费申请ID
    /// </summary>
    public long? ApplyId { get; set; }
    
    /// <summary>
    /// 审核时间范围
    /// </summary>
     public DateTime?[] AuditTimeRange { get; set; }
    
    /// <summary>
    /// 审核部门ID
    /// </summary>
    public long? AuditDeptId { get; set; }
    
    /// <summary>
    /// 审核部门编码
    /// </summary>
    public string? AuditDeptCode { get; set; }
    
    /// <summary>
    /// 审核部门名称
    /// </summary>
    public string? AuditDeptName { get; set; }
    
    /// <summary>
    /// 审核人ID
    /// </summary>
    public long? AuditUserId { get; set; }
    
    /// <summary>
    /// 审核人编码
    /// </summary>
    public string? AuditUserCode { get; set; }
    
    /// <summary>
    /// 审核人名称
    /// </summary>
    public string? AuditUserName { get; set; }
    
    /// <summary>
    /// 审核原因
    /// </summary>
    public string? AuditReason { get; set; }
    
    /// <summary>
    /// 审核状态
    /// </summary>
    public int? AuditStatus { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 退费审核表增加输入参数
/// </summary>
public class AddRefundAuditInput
{
    /// <summary>
    /// 退费申请ID
    /// </summary>
    public long? ApplyId { get; set; }
  
    
    /// <summary>
    /// 审核原因
    /// </summary>
    [MaxLength(200, ErrorMessage = "审核原因字符长度不能超过200")]
    public string? AuditReason { get; set; }
    
    /// <summary>
    /// 审核状态 1 审核完成(通过) 2 审核驳回
    /// </summary>
    public int? AuditStatus { get; set; }
    
}

/// <summary>
/// 退费审核表删除输入参数
/// </summary>
public class DeleteRefundAuditInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}


/// <summary>
/// 退费审核表主键查询输入参数
/// </summary>
public class QueryByIdRefundAuditInput : DeleteRefundAuditInput
{
}
