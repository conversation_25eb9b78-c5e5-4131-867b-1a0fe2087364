namespace His.Module.OutpatientDoctor.Api.Charge.Dto;

public class OutpatientChargeDetailDto
{
    /// <summary>
    /// 收费主表Id
    /// </summary>
    public virtual long? ChargeId { get; set; }
    
    /// <summary>
    /// 单据Id(处方id)
    /// </summary>
    public virtual long? BillingId { get; set; }
    
    /// <summary>
    /// 单据号
    /// </summary>
    public virtual string? BillingNo { get; set; }
    
    /// <summary>
    /// 单据明细Id
    /// </summary>
    public virtual long? BillingDetailId { get; set; }
    
    /// <summary>
    /// 单据类型
    /// </summary>
    public virtual long? BillingType { get; set; }
    
    /// <summary>
    /// 单据时间
    /// </summary>
    public virtual DateTime? BillingTime { get; set; }
    
    /// <summary>
    /// 执行科室Id
    /// </summary>
    public virtual long? ExecuteDeptId { get; set; }
    /// <summary>
    /// 执行科室
    /// </summary>
  
    public virtual string? ExecuteDeptName { get; set; }
    /// <summary>
    /// 执行医生Id
    /// </summary>
    public virtual long? ExecuteDoctorId { get; set; }
    
    /// <summary>
    /// 排除医生姓名
    /// </summary>
    public virtual string? ExecuteDoctorName { get; set; }
    
    /// <summary>
    /// 执行状态
    /// </summary>
    public int? ExecuteStatus { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime? ExecuteTime { get; set; }
    
    /// <summary>
    /// 开单科室Id
    /// </summary>
    public virtual long? BillingDeptId { get; set; }
    
    /// <summary>
    /// 开单医生Id
    /// </summary>
    public virtual long? BillingDoctorId { get; set; }
    
    /// <summary>
    /// 开单医生姓名
    /// </summary>
    public virtual string? BillingDoctorName { get; set; }
    
    /// <summary>
    /// 项目Id
    /// </summary>
    public virtual long? ItemId { get; set; }
    
    /// <summary>
    /// 项目编号
    /// </summary>
    public virtual string? ItemCode { get; set; }
    
    /// <summary>
    /// 项目名称
    /// </summary>
    public virtual string? ItemName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public virtual decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单次用量
    /// </summary>
    public virtual decimal? SingleDose { get; set; }
    
    /// <summary>
    /// 单次用量单位
    /// </summary>
    public virtual string? SingleDoseUnit { get; set; }
    
    /// <summary>
    /// 给药途径Id
    /// </summary>
    public virtual long? MedicationRoutesId { get; set; }
    
    /// <summary>
    /// 频次Id
    /// </summary>
    public virtual long? FrequencyId { get; set; }
    
    /// <summary>
    /// 用药天数
    /// </summary>
    public virtual Int16? MedicationDays { get; set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    public virtual decimal? Price { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    public virtual decimal? Amount { get; set; }
    
    /// <summary>
    /// 草药数量
    /// </summary>
    public virtual int? HerbsQuantity { get; set; }
    
    /// <summary>
    /// 是否退费
    /// </summary>
    public virtual int? Withdrawal { get; set; }
    
    /// <summary>
    /// 退费时间
    /// </summary>
    public virtual DateTime? WithdrawalTime { get; set; }
    
    /// <summary>
    /// 支付方式1Id
    /// </summary>
    public virtual long? PayMethod1Id { get; set; }
    
    /// <summary>
    /// 支付方式1金额
    /// </summary>
    public virtual decimal? PayAmount1 { get; set; }
    
    /// <summary>
    /// 支付方式2Id
    /// </summary>
    public virtual long? PayMethod2Id { get; set; }
    
    /// <summary>
    /// 支付方式2金额
    /// </summary>
    public virtual decimal? PayAmount2 { get; set; }
    
    /// <summary>
    /// 药房Id
    /// </summary>
    public virtual long? PharmacyId { get; set; }
    
    /// <summary>
    /// 总支付金额
    /// </summary>
    public virtual decimal? TotalPayAmount { get; set; }
    
    /// <summary>
    /// 收费类别Id
    /// </summary>
    public virtual long? ChargeCategoryId { get; set; }
    /// <summary>
    /// 收费类别编号
    /// </summary>
    public virtual string? ChargeCategoryCode { get; set; }
    /// <summary>
    /// 生产厂家
    /// </summary>
    public virtual string? Manufacturer { get; set; }
    /// <summary>
    /// 医保编码
    /// </summary>
    public virtual string? MedicineCode { get; set; }
    /// <summary>
    /// 套餐Id
    /// </summary>
    public virtual long? PackId { get; set; }
    
    /// <summary>
    /// 套餐编码
    /// </summary>
    public virtual string? PackCode { get; set; }
    
    /// <summary>
    /// 套餐名称
    /// </summary>
    public virtual string? PackName { get; set; }
    
    /// <summary>
    /// 套餐数量
    /// </summary>
    public virtual Int16? PackNumber { get; set; }
    
    /// <summary>
    /// 套餐单价
    /// </summary>
    public virtual decimal? PackPrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 创建者部门Id
    /// </summary>
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary>
    public virtual string? CreateOrgName { get; set; }
}
