﻿<script lang="ts" name="medicalTeamMember" setup>
import { ref, reactive, onMounted } from "vue";

import type { FormRules } from "element-plus";
import { formatDate, formatOnlyDate } from '/@/utils/formatTime';
import { ElMessageBox, ElMessage } from "element-plus";
import { useMedicalTeamMemberApi } from '/@/api/inpatient/medicalTeamMember';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import PinyinSelect from '/@/components/pinyinSelect/index.vue';
const basicInfoApi = useBasicInfoApi();
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const medicalTeamMemberApi = useMedicalTeamMemberApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	activeName: 'first',
	tableData: [] as any[],
	tableLoading: false,
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'createTime', // 默认的排序字段
		order: 'descending', // 排序方向
		descStr: 'descending', // 降序排序的关键字符
	},
});

// 自行添加其他规则Member
const rules = ref<FormRules>({

	staffId: [{ required: true, message: '请选择成员！', trigger: 'blur', },]
});

// 页面加载时
onMounted(async () => {

});
const teamLeaderChange = async (value: any) => {
	if (value) {
		state.ruleForm.staffName = state.dropdownData.userList.find((e: any) => e.id === value)?.name;
	} else {
		state.ruleForm.staffName = '';
	}
};
// 打开弹窗
const openDialog = async (row: any, title: string) => {

	state.showDialog = true;
	state.title = title;
	row = row ?? {};
	state.ruleForm.teamId = row.id;
	state.ruleForm.teamName = row.teamName;
	state.showDialog = true;
	await basicInfoApi.getUsers({ orgId: row.deptId }).then(res => {
		state.dropdownData.userList = res.data.result;
	});
	handleQuery();
};

// 关闭弹窗
const closeDialog = () => {
	//emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await medicalTeamMemberApi[state.ruleForm.id ? 'update' : 'add'](values);
			//closeDialog();
			//closeDialog();
			state.ruleForm = {};
			handleQuery();

		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};
// 查询操作
const handleQuery = async () => {
	const params: any = {
		deptId: state.ruleForm.deptId,
		teamId: state.ruleForm.teamId

	}
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	const result = await medicalTeamMemberApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
	state.tableParams.total = result?.total;
	state.tableData = result?.items ?? [];

	console.log('tableData', state.tableData);
	state.tableLoading = false;
};
// 修改 
const udpateMedicalTeamMember = (row: any) => {
	state.ruleForm = Object.assign({}, row);

};
// 删除
const delMedicalTeamMember = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, "提示", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning",
	}).then(async () => {
		await medicalTeamMemberApi.delete({ id: row.id });
		handleQuery();
		ElMessage.success("删除成功");
	}).catch(() => { });
};
//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="medicalTeamMember-container">
		<el-dialog v-model="state.showDialog" :width="800" style="height:550px" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-tabs v-model="state.activeName" class="demo-tabs">
				<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
					<el-row :gutter="35">
						<el-form-item v-show="false">
							<el-input v-model="state.ruleForm.id" />
						</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="医疗组" prop="teamName">
								<el-input v-model="state.ruleForm.teamName" placeholder="请输入医疗组" show-word-limit
									clearable :disabled="true" />

							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="成员" prop="staffId">
								<PinyinSelect v-model="state.ruleForm.staffId" placeholder="请选择成员"
									@change="teamLeaderChange" :options="state.dropdownData.userList" />
							</el-form-item>
						</el-col>

						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="角色类型" prop="roleType">
								<!-- <el-input v-model="state.ruleForm.roleType"
								placeholder="请输入角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)" maxlength="200" show-word-limit
								clearable /> -->
								<g-sys-dict v-model="state.ruleForm.roleType" code="MedicalTeamRole" render-as="select"
									placeholder="请选择角色类型" />

							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="加入日期" prop="joinDate">
								<el-date-picker v-model="state.ruleForm.joinDate" type="date" placeholder="加入日期" />
							</el-form-item>
						</el-col>
						<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="离开日期" prop="leaveDate">
								<el-date-picker v-model="state.ruleForm.leaveDate" type="date" placeholder="离开日期" />
							</el-form-item>
						</el-col> -->

						<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
							<el-form-item label="备注" prop="remark">
								<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="255"
									show-word-limit clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4" class="mb20">
							<el-form-item label="" prop="remark">
								<el-button style="float: rights;" @click="submit" type="primary"
									v-reclick="1000">保存</el-button>
							</el-form-item>
						</el-col>

					</el-row>
				</el-form>

				<el-table height="300" :data="state.tableData" v-loading="state.tableLoading" tooltip-effect="light"
					row-key="id" border>

					<el-table-column type="index" label="序号" width="55" align="center" />

					<el-table-column prop='staffName' label='成员名称' show-overflow-tooltip width="120" />
					<el-table-column prop='roleType' label='角色类型' show-overflow-tooltip width="100" />
					<el-table-column prop='joinDate' label='加入日期' show-overflow-tooltip width="120">


						<template #default="scope">
							{{ formatOnlyDate(scope.row.joinDate, "YYYY-mm-dd") }}
						</template>
					</el-table-column>
					<el-table-column prop='leaveDate' label='离开日期' show-overflow-tooltip width="120" />
					<!-- <el-table-column prop='status' label='状态' show-overflow-tooltip width="100">
						<template #default="scope">
							<el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2" size="small"
								@change="changeMembertatus(scope.row)" />
						</template>

					</el-table-column> -->
					<el-table-column prop='remark' label='备注' show-overflow-tooltip width="200" />

					<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip>
						<template #default="scope">
							<el-button icon="ele-Edit" size="small" text type="primary"
								@click="udpateMedicalTeamMember(scope.row)"> 编辑 </el-button>
							<el-button icon="ele-Delete" size="small" text type="danger"
								@click="delMedicalTeamMember(scope.row)"> 删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>



			</el-tabs>


		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>