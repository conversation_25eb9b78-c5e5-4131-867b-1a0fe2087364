﻿<script lang="ts" name="inpatientRegister" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useInpatientRegisterApi } from '/@/api/inpatient/inpatientRegister';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const inpatientRegisterApi = useInpatientRegisterApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await inpatientRegisterApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await inpatientRegisterApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="inpatientRegister-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="住院流水号" prop="inpatientSerialNo">
							<el-input v-model="state.ruleForm.inpatientSerialNo" placeholder="请输入住院流水号" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="住院号" prop="inpatientNo">
							<el-input v-model="state.ruleForm.inpatientNo" placeholder="请输入住院号" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="住院次数" prop="inpatientCount">
							<el-input-number v-model="state.ruleForm.inpatientCount" placeholder="请输入住院次数" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="就诊卡号" prop="medicalCardNo">
							<el-input v-model="state.ruleForm.medicalCardNo" placeholder="请输入就诊卡号" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="患者ID" prop="patientId">
							<el-input v-model="state.ruleForm.patientId" placeholder="请输入患者ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="患者姓名" prop="patientName">
							<el-input v-model="state.ruleForm.patientName" placeholder="请输入患者姓名" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="性别" prop="sex">
							<el-input-number v-model="state.ruleForm.sex" placeholder="请输入性别" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="年龄" prop="age">
							<el-input-number v-model="state.ruleForm.age" placeholder="请输入年龄" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="年龄单位" prop="ageUnit">
							<el-input v-model="state.ruleForm.ageUnit" placeholder="请输入年龄单位" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="出生日期" prop="birthday">
							<el-date-picker v-model="state.ruleForm.birthday" type="date" placeholder="出生日期" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="证件类型" prop="cardType">
							<el-input-number v-model="state.ruleForm.cardType" placeholder="请输入证件类型" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="身份证号" prop="idCardNo">
							<el-input v-model="state.ruleForm.idCardNo" placeholder="请输入身份证号" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="电话号码" prop="phone">
							<el-input v-model="state.ruleForm.phone" placeholder="请输入电话号码" maxlength="16" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="联系人姓名" prop="contactName">
							<el-input v-model="state.ruleForm.contactName" placeholder="请输入联系人姓名" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="联系人关系" prop="contactRelationship">
							<el-input v-model="state.ruleForm.contactRelationship" placeholder="请输入联系人关系" maxlength="16" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="联系人地址" prop="contactAddress">
							<el-input v-model="state.ruleForm.contactAddress" placeholder="请输入联系人地址" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="联系人电话号码" prop="contactPhone">
							<el-input v-model="state.ruleForm.contactPhone" placeholder="请输入联系人电话号码" maxlength="16" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="现居住地省" prop="residenceProvince">
							<el-input-number v-model="state.ruleForm.residenceProvince" placeholder="请输入现居住地省" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="现居住地市" prop="residenceCity">
							<el-input-number v-model="state.ruleForm.residenceCity" placeholder="请输入现居住地市" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="现居住地县" prop="residenceCounty">
							<el-input-number v-model="state.ruleForm.residenceCounty" placeholder="请输入现居住地县" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="详细现居住地" prop="residenceAddress">
							<el-input v-model="state.ruleForm.residenceAddress" placeholder="请输入详细现居住地" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="入院时间" prop="inpatientTime">
							<el-date-picker v-model="state.ruleForm.inpatientTime" type="date" placeholder="入院时间" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="科室ID" prop="deptId">
							<el-input v-model="state.ruleForm.deptId" placeholder="请输入科室ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="科室名称" prop="deptName">
							<el-input v-model="state.ruleForm.deptName" placeholder="请输入科室名称" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="病区ID" prop="wardId">
							<el-input v-model="state.ruleForm.wardId" placeholder="请输入病区ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="病区名称" prop="wardName">
							<el-input v-model="state.ruleForm.wardName" placeholder="请输入病区名称" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="诊疗组ID" prop="teamId">
							<el-input v-model="state.ruleForm.teamId" placeholder="请输入诊疗组ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="诊疗组名称" prop="teamName">
							<el-input v-model="state.ruleForm.teamName" placeholder="请输入诊疗组名称" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="医生ID" prop="doctorId">
							<el-input v-model="state.ruleForm.doctorId" placeholder="请输入医生ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="医生姓名" prop="doctorName">
							<el-input v-model="state.ruleForm.doctorName" placeholder="请输入医生姓名" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="接诊医生id" prop="receivingDoctorId">
							<el-input v-model="state.ruleForm.receivingDoctorId" placeholder="请输入接诊医生id" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="接诊医生名称" prop="receivingDoctorName">
							<el-input v-model="state.ruleForm.receivingDoctorName" placeholder="请输入接诊医生名称" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="入院途径" prop="inpatientWay">
							<el-input v-model="state.ruleForm.inpatientWay" placeholder="请输入入院途径" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="结算类别" prop="settlementCategory">
							<el-input v-model="state.ruleForm.settlementCategory" placeholder="请输入结算类别" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="入院诊断编号" prop="admissionDiagnosisCode">
							<el-input v-model="state.ruleForm.admissionDiagnosisCode" placeholder="请输入入院诊断编号" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="入院诊断名称" prop="admissionDiagnosisName">
							<el-input v-model="state.ruleForm.admissionDiagnosisName" placeholder="请输入入院诊断名称" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="妊娠风险评估" prop="pregnancyRiskLevel">
							<el-input v-model="state.ruleForm.pregnancyRiskLevel" placeholder="请输入妊娠风险评估" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="高危因素" prop="highRiskFactors">
							<el-input v-model="state.ruleForm.highRiskFactors" placeholder="请输入高危因素" maxlength="256" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="状态" prop="status">
							<el-input-number v-model="state.ruleForm.status" placeholder="请输入状态" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>