using His.Module.Insurance.Service.Settlement.Model.Outpatient;

namespace His.Module.Insurance.Service.Settlement;

public static class SettleMzPreResponseGenerator
{
    /// <summary>
    /// 根据总金额生成SettleMzPreResponse对象的模拟数据
    /// </summary>
    /// <param name="totalAmount">总金额(zje)</param>
    /// <returns>填充了模拟数据的SettleMzPreResponse对象</returns>
    public static SettleMzPreResponse GenerateMockData(double totalAmount)
    {
        var random = new Random();
        
        // 模拟生成各部分金额（确保总和等于zje）
        double ybfdje = Math.Round(totalAmount * 0.7, 2); // 医保负担70%
        double brfdje = Math.Round(totalAmount - ybfdje, 2); // 病人负担剩余部分
        
        // 确保计算准确，避免精度问题
        brfdje = Math.Round(totalAmount - ybfdje, 2);
        
        var response = new SettleMzPreResponse
        {
            // 基本信息
            resultcode = "0", // 成功
            zylsh = $"ZYLSH{DateTime.Now:yyyyMMddHHmmss}", // 住院流水号
            jbbm = $"JBBM{random.Next(1000, 9999)}", // 疾病编码
            brjsrq = DateTime.Now, // 病人结算日期
            
            // 费用信息（基于总金额分配）
            zje = totalAmount, // 总费用
            brfdje = brfdje, // 病人负担金额
            ybfdje = ybfdje, // 医保负担金额
            ylbzje = 0.00, // 医疗补助金额
            yyfdje = 0.00, // 医院负担金额
            
            // 医保相关信息
            yltcdjh = $"YLTCDJH{random.Next(10000, 99999)}", // 医疗统筹登记号
            tjrylb = "TJRYLB001", // 统计人员类别
            
            // 支付明细
            grzhzf = Math.Round(brfdje * 0.5, 2), // 个人账户支付(病人负担的一半)
            tczf = ybfdje, // 本次统筹支付
            dezf = 0.00, // 本次大额支付
            desybx = 0.00, // 大额商业保险
            gwybz = 0.00, // 本次公务员补助
            czlz = 0.00, // 本次财政列支
            zhzf = 0.00, // 暂缓支付
            
            // 累计信息
            ljtczf = Math.Round(ybfdje * 3, 2), // 累计统筹支付
            ljdezf = 0.00, // 累计大额支付
            ljmzed = 2000.00, // 累计门诊额度
            ljgrzf = Math.Round(brfdje * 2, 2), // 累积个人支付
            qttczf = 0.00, // 其他统筹支付
            
            // 发票信息
            fph = $"FP{DateTime.Now:yyyyMMdd}{random.Next(1000, 9999)}", // 发票号
            fprylb = "FPRLB001", // 发票人员类别
            zhye = Math.Round(1000.00 - brfdje, 2), // 账户余额(假设原有1000)
            
            // 减免和自付信息
            jmje = 0.00, // 优抚对象减免金额
            bcqfx = Math.Min(50.00, ybfdje * 0.2), // 本次起付线
            // bcnrtcfw = Math.Round(ybfdje - bcqfx, 2), // 本次进入统筹额度
            // bnynrtcfw = Math.Round(bcnrtcfw * 2, 2), // 本年进入统筹额度
            sxzfje = 0.00, // 首先自付金额
            
            // 地区专用字段
            tcfwnfd = brfdje, // 统筹范围内个人负担金额
            ljmznrtc = Math.Round(ybfdje * 4, 2), // 医保额度累计
            bcmlw = 0.00, // 本次目录外
            ljmlw = 0.00, // 累计目录外
            // ljqfx = Math.Round(bcqfx * 3, 2), // 累计起付线
            
            // 补充支付信息
            bcbczf = 0.00, // 本次补充支付
            ljbczf = 0.00, // 累计补充支付
            
            // 特定地区使用字段
            xj = brfdje, // 本次消费现金
            bnptmzljbxje = Math.Round(ybfdje * 2, 2), // 本年度累计普通门诊报销金额
            ecbxje = 0.00, // 二次报销金额
            
            // 其他字段
            cfqyts = "", // 重复取药提示
            yltclbmx = "601", // 医疗统筹类别明细(普通门诊)
            hmbbxje = 0.00, // 惠民保报销金额
            sydgrzhzf = 0.00 // 省异地个人账户支付
        };
        response.bcnrtcfw = Math.Round(ybfdje - response.bcqfx, 2); // 本次进入统筹额度
        response.bnynrtcfw = Math.Round(response.bcnrtcfw * 2, 2);// 本年进入统筹额度
        response.ljqfx = Math.Round(response.bcqfx * 3, 2); // 累计起付线
        return response;
    }
}
