﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品退货记录表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("storage_refund_record", "药品退货记录表")]
public class StorageRefundRecord : EntityTenant
{
    /// <summary>
    /// 退货单号
    /// </summary>
    [SugarColumn(ColumnName = "refund_no", ColumnDescription = "退货单号", Length = 100)]
    public virtual string? RefundNo { get; set; }
    
    /// <summary>
    /// 退货时间
    /// </summary>
    [SugarColumn(ColumnName = "refund_time", ColumnDescription = "退货时间")]
    public virtual DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退货类型
    /// </summary>
    [SugarColumn(ColumnName = "refund_type", ColumnDescription = "退货类型", Length = 100)]
    public virtual string? RefundType { get; set; }
    
    /// <summary>
    /// 退货原因
    /// </summary>
    [SugarColumn(ColumnName = "refund_reason", ColumnDescription = "退货原因", Length = 100)]
    public virtual string? RefundReason { get; set; }
    
    /// <summary>
    /// 申请部门ID
    /// </summary>
    [SugarColumn(ColumnName = "apply_dept_id", ColumnDescription = "申请部门ID")]
    public virtual long? ApplyDeptId { get; set; }
    
    /// <summary>
    /// 申请部门编码
    /// </summary>
    [SugarColumn(ColumnName = "apply_dept_code", ColumnDescription = "申请部门编码", Length = 100)]
    public virtual string? ApplyDeptCode { get; set; }
    
    /// <summary>
    /// 申请部门名称
    /// </summary>
    [SugarColumn(ColumnName = "apply_dept_name", ColumnDescription = "申请部门名称", Length = 100)]
    public virtual string? ApplyDeptName { get; set; }
    
    /// <summary>
    /// 目标库房ID
    /// </summary>
    [SugarColumn(ColumnName = "target_storage_id", ColumnDescription = "目标库房ID")]
    public virtual long? TargetStorageId { get; set; }
    
    /// <summary>
    /// 目标库房编码
    /// </summary>
    [SugarColumn(ColumnName = "target_storage_code", ColumnDescription = "目标库房编码", Length = 100)]
    public virtual string? TargetStorageCode { get; set; }
    
    /// <summary>
    /// 目标库房名称
    /// </summary>
    [SugarColumn(ColumnName = "target_storage_name", ColumnDescription = "目标库房名称", Length = 100)]
    public virtual string? TargetStorageName { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [SugarColumn(ColumnName = "total_sale_price", ColumnDescription = "总零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
}
