﻿using His.Module.Shared.Api.Enum;
namespace His.Module.Patient.Entity;

/// <summary>
/// 患者信息表
/// </summary>
[SugarTable(null, "患者信息表")]
[Tenant("1300000000003")]
public class PatientInfo : EntityTenant
{
    /// <summary>
    /// 患者唯一号
    /// </summary>
    [SugarColumn(ColumnDescription = "患者唯一号", Length = 32)]
    public virtual string? PatientNo { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "患者姓名", Length = 32)]
    public string? Name { get; set; }

    /// <summary>
    /// 英文姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "英文姓名", Length = 64)]
    public string? EnglishName { get; set; }

    /// <summary>
    /// 姓名拼音码
    /// </summary>

    [SugarColumn(ColumnDescription = "姓名拼音码", Length = 32)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 姓名五笔码
    /// </summary>

    [SugarColumn(ColumnDescription = "姓名五笔码", Length = 32)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 性别
    /// </summary>

    [SugarColumn(ColumnDescription = "性别")]
    public GenderEnum Sex { get; set; } = GenderEnum.Male;

    /// <summary>
    /// 年龄
    /// </summary>

    [SugarColumn(ColumnDescription = "年龄", Length = 16)]
    public int Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>

    [SugarColumn(ColumnDescription = "年龄单位", Length = 32)]
    public virtual string? AgeUnit { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>

    [SugarColumn(ColumnName = "birthday", ColumnDescription = "出生日期")]
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>

    [SugarColumn(ColumnDescription = "证件类型")]
    public CardTypeEnum CardType { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    [SugarColumn(ColumnDescription = "身份证号", Length = 32)]
    public string IdCardNo { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    [SugarColumn(ColumnDescription = "民族", Length = 16)]
    public string? Nation { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    [SugarColumn(ColumnDescription = "电话号码", Length = 16)]
    public string? Phone { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "联系人姓名", Length = 32)]
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    [SugarColumn(ColumnDescription = "联系人关系", Length = 16)]
    public string? ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>
    [SugarColumn(ColumnDescription = "联系人地址", Length = 64)]
    public string? ContactAddress { get; set; }

    /// <summary>
    /// 联系人电话号码
    /// </summary>
    [SugarColumn(ColumnDescription = "联系人电话号码", Length = 16)]
    public string? ContactPhone { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>
    [SugarColumn(ColumnDescription = "国籍", Length = 16)]
    public string? Nationality { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    [SugarColumn(ColumnDescription = "职业", Length = 16)]
    public string? Occupation { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>
    [SugarColumn(ColumnDescription = "婚姻", Length = 16)]
    public string? Marriage { get; set; }

    /// <summary>
    /// 籍贯省
    /// </summary>
    [SugarColumn(ColumnDescription = "籍贯省")]
    public int? NativePlaceProvince { get; set; }

    /// <summary>
    /// 籍贯市
    /// </summary>
    [SugarColumn(ColumnDescription = "籍贯市")]
    public int? NativePlaceCity { get; set; }

    /// <summary>
    /// 籍贯县
    /// </summary>
    [SugarColumn(ColumnDescription = "籍贯县")]
    public int? NativePlaceCounty { get; set; }

    /// <summary>
    /// 出生地省
    /// </summary>
    [SugarColumn(ColumnDescription = "出生地省")]
    public int? BirthplaceProvince { get; set; }

    /// <summary>
    /// 出生地市
    /// </summary>
    [SugarColumn(ColumnDescription = "出生地市")]
    public int? BirthplaceCity { get; set; }

    /// <summary>
    /// 出生地县
    /// </summary>
    [SugarColumn(ColumnDescription = "出生地县")]
    public int? BirthplaceCounty { get; set; }

    /// <summary>
    /// 现居住地省
    /// </summary>

    [SugarColumn(ColumnDescription = "现居住地省")]
    public int ResidenceProvince { get; set; }

    /// <summary>
    /// 现居住地市
    /// </summary>

    [SugarColumn(ColumnDescription = "现居住地市")]
    public int ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>

    [SugarColumn(ColumnDescription = "现居住地县")]
    public int ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>

    [SugarColumn(ColumnDescription = "详细现居住地", Length = 128)]
    public string? ResidenceAddress { get; set; }

    /// <summary>
    /// 工作地址省
    /// </summary>
    [SugarColumn(ColumnDescription = "工作地址省")]
    public int? WorkProvince { get; set; }

    /// <summary>
    /// 工作地址市
    /// </summary>
    [SugarColumn(ColumnDescription = "工作地址市")]
    public int? WorkCity { get; set; }

    /// <summary>
    /// 工作地址县
    /// </summary>
    [SugarColumn(ColumnDescription = "工作地址县")]
    public int? WorkCounty { get; set; }

    /// <summary>
    /// 详细工作地址
    /// </summary>
    [SugarColumn(ColumnDescription = "详细工作地址", Length = 128)]
    public string? WorkAddress { get; set; }

    /// <summary>
    /// 工作单位
    /// </summary>
    [SugarColumn(ColumnDescription = "工作单位", Length = 128)]
    public string? WorkPlace { get; set; }

    /// <summary>
    /// 单位电话
    /// </summary>
    [SugarColumn(ColumnDescription = "单位电话", Length = 16)]
    public string? WorkPlacePhone { get; set; }

    /// <summary>
    /// 医保类别
    /// </summary>
    [SugarColumn(ColumnDescription = "医保类别")]
    public int? MedInsCategory { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [SugarColumn(ColumnDescription = "医保类型")]
    public MedInsTypeEnum? MedInsType { get; set; }

    /// <summary>
    /// 医疗类别（费别）
    /// </summary>
    [SugarColumn(ColumnDescription = "医疗类别（费别）")]
    public long? MedCategory { get; set; }

    /// <summary>
    /// 险种类型
    /// </summary>
    [SugarColumn(ColumnDescription = "险种类型")]
    public string? InsuranceType { get; set; }

    /// <summary>
    /// 是否无卡
    /// </summary>
    [SugarColumn(ColumnDescription = "是否无卡")]
    public YesNoEnum IsNoCard { get; set; }

    /// <summary>
    /// 医保卡信息
    /// </summary>
    [SugarColumn(ColumnDescription = "医保卡信息", Length = 512)]
    public string? MedInsCardInfo { get; set; }
}