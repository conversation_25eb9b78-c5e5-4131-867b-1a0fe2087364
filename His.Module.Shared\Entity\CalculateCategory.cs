﻿namespace His.Module.Shared.Entity;

/// <summary>
/// 系统核算类别表
/// </summary>
[SugarTable(null, "系统核算类别表")]
[Tenant("1300000000014")]
public class CalculateCategory : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码")]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 32)]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 20)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 20)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public int? OrderNo { get; set; } = 100;
}