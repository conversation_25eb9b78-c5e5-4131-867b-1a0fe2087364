﻿using Furion.JsonSerialization;
using His.Module.Insurance.Entity;
using His.Module.Insurance.Service.CatalogManagement;
using His.Module.InsuranceSettlementForLiaocheng.Infrastructure;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
namespace His.Module.InsuranceSettlementForLiaocheng.Implement.CatalogManagement;

/// <summary>
/// 目录管理API实现
/// </summary>
public class CatalogManagementApiImpl : CatalogManagementApiBase, ITransient
{
    private readonly InsuranceSettlementBasicApi _basicApi;

    public CatalogManagementApiImpl(
        InsuranceSettlementBasicApi basicApi,
        SqlSugarRepository<HospitalItemCatalog> hospitalItemRep,
        SqlSugarRepository<InsuranceItemCatalog> insuranceItemRep,
        SqlSugarRepository<InsuranceDictionary> dictionaryRep,
        SqlSugarRepository<InsuranceSickCatalog> sickCatalogRep,
        SqlSugarRepository<InsuranceOperationCatalog> operationCatalogRep,
        SqlSugarRepository<DoctorInfo> doctorInfoRep,
        SqlSugarRepository<LimitPriceInfo> limitPriceRep,
        SqlSugarRepository<FirstSelfPayRatioInfo> firstSelfPayRatioRep,
        ILoggerFactory logger)
        : base(hospitalItemRep, insuranceItemRep, dictionaryRep, sickCatalogRep, operationCatalogRep, doctorInfoRep, limitPriceRep, firstSelfPayRatioRep, logger)
    {
        _basicApi = basicApi;
    }

    #region 实现抽象方法

    /// <summary>
    /// 调用接口
    /// </summary>
    protected async override Task<T> CallRemoteApi<T>(string methodName, object requestData)
    {
        try
        {
            var userKey = await _basicApi.GetUserKey();
            var response = await _basicApi.SendSoapInvokeRequestAsync(userKey, "", methodName, requestData);

            if (response.IsSuccess)
            {
                var result = JSON.Deserialize<T>(response.resulttext, new JsonSerializerSettings
                {
                    ContractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new SnakeCaseNamingStrategy()
                    }
                });

                return result ?? new T();
            }

            throw new Exception($"调用接口 {methodName} 失败: {response.resulttext}");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "调用接口 {MethodName} 异常", methodName);
            throw;
        }
    }

    #endregion
}