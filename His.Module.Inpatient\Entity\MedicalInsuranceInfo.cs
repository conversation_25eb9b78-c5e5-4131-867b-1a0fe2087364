﻿using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 医保信息表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("medical_insurance_info", "医保信息表")]
public class MedicalInsuranceInfo : EntityTenant
{
    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 住院登记号
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "住院登记号")]
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_no", ColumnDescription = "住院号", Length = 100)]
    public virtual string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_serial_no", ColumnDescription = "住院流水号", Length = 100)]
    public virtual string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    [SugarColumn(ColumnName = "medical_record_no", ColumnDescription = "病案号", Length = 100)]
    public virtual string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 保险号码
    /// </summary>
    [SugarColumn(ColumnName = "insurance_no", ColumnDescription = "保险号码", Length = 100)]
    public virtual string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 工作单位
    /// </summary>
    [SugarColumn(ColumnName = "work_unit", ColumnDescription = "工作单位", Length = 100)]
    public virtual string? WorkUnit { get; set; }
    
    /// <summary>
    /// 保险类型
    /// </summary>
    [SugarColumn(ColumnName = "insurance_type", ColumnDescription = "保险类型", Length = 100)]
    public virtual string? InsuranceType { get; set; }
    
    /// <summary>
    /// 住院类别
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_category", ColumnDescription = "住院类别", Length = 100)]
    public virtual string? InpatientCategory { get; set; }
    
    /// <summary>
    /// 险种标志
    /// </summary>
    [SugarColumn(ColumnName = "insurance_flag", ColumnDescription = "险种标志", Length = 100)]
    public virtual string? InsuranceFlag { get; set; }
    
    /// <summary>
    /// 就医类别
    /// </summary>
    [SugarColumn(ColumnName = "medical_use_category", ColumnDescription = "就医类别", Length = 100)]
    public virtual string? MedicalUseCategory { get; set; }
    
    /// <summary>
    /// 医保卡号
    /// </summary>
    [SugarColumn(ColumnName = "medical_insurance_no", ColumnDescription = "医保卡号", Length = 100)]
    public virtual string? MedicalInsuranceNo { get; set; }
    
    /// <summary>
    /// 参保地
    /// </summary>
    [SugarColumn(ColumnName = "insurance_place", ColumnDescription = "参保地", Length = 100)]
    public virtual string? InsurancePlace { get; set; }
    
    /// <summary>
    /// 单位电话
    /// </summary>
    [SugarColumn(ColumnName = "work_unit_phone", ColumnDescription = "单位电话", Length = 100)]
    public virtual string? WorkUnitPhone { get; set; }
    
    /// <summary>
    /// 单位邮编
    /// </summary>
    [SugarColumn(ColumnName = "work_unit_post_code", ColumnDescription = "单位邮编", Length = 100)]
    public virtual string? WorkUnitPostCode { get; set; }
    
    /// <summary>
    /// 工作地址
    /// </summary>
    [SugarColumn(ColumnName = "work_unit_address", ColumnDescription = "工作地址", Length = 100)]
    public virtual string? WorkUnitAddress { get; set; }
    
    /// <summary>
    /// 工作地址（街道）
    /// </summary>
    [SugarColumn(ColumnName = "work_unit_address_street", ColumnDescription = "工作地址（街道）", Length = 100)]
    public virtual string? WorkUnitAddressStreet { get; set; }
    
    /// <summary>
    /// 医保卡余额
    /// </summary>
    [SugarColumn(ColumnName = "insurance_balance", ColumnDescription = "医保卡余额", Length = 20, DecimalDigits=4)]
    public virtual decimal? InsuranceBalance { get; set; }
    
    /// <summary>
    /// 转出医疗统筹登记号
    /// </summary>
    [SugarColumn(ColumnName = "out_register_no", ColumnDescription = "转出医疗统筹登记号", Length = 100)]
    public virtual string? OutRegisterNo { get; set; }
    
    /// <summary>
    /// 无第三方标志
    /// </summary>
    [SugarColumn(ColumnName = "no_third_party", ColumnDescription = "无第三方标志")]
    public virtual int? NoThirdParty { get; set; }
    
    /// <summary>
    /// 外伤标志
    /// </summary>
    [SugarColumn(ColumnName = "external_injury_flag", ColumnDescription = "外伤标志")]
    public virtual int? ExternalInjuryFlag { get; set; }
    
    /// <summary>
    /// 第三方标志
    /// </summary>
    [SugarColumn(ColumnName = "third_party_flag", ColumnDescription = "第三方标志")]
    public virtual int? ThirdPartyFlag { get; set; }
    
    /// <summary>
    /// 医疗住院方式
    /// </summary>
    [SugarColumn(ColumnName = "medical_inpatient_type", ColumnDescription = "医疗住院方式")]
    public virtual int? MedicalInpatientType { get; set; }
    
    /// <summary>
    /// 住院类型
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_type", ColumnDescription = "住院类型")]
    public virtual int? InpatientType { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 创建组织ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建组织ID")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建组织名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建组织名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
