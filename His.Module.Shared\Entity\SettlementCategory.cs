﻿using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 结算类别表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("settlement_category", "结算类别表")]
public class SettlementCategory : EntityTenant
{
    /// <summary>
    /// 编号
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编号", Length = 32)]
    public virtual string? Code { get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 32)]
    public virtual string? Name { get; set; }
    
    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 20)]
    public virtual string? PinyinCode { get; set; }
    
    /// <summary>
    /// 医疗类别
    /// </summary>
    [SugarColumn(ColumnName = "med_category", ColumnDescription = "医疗类别", Length = 32)]
    public virtual string? MedCategory { get; set; }
    
    /// <summary>
    /// 使用范围
    /// </summary>
    [SugarColumn(ColumnName = "usage_scope", ColumnDescription = "使用范围", Length = 32)]
    public virtual string? UsageScope { get; set; }
    
    /// <summary>
    /// 医保类型
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_type", ColumnDescription = "医保类型", Length = 32)]
    public virtual string? MedInsType { get; set; }
    
    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    [SugarColumn(ColumnName = "medical_pooling_category", ColumnDescription = "医疗统筹类别", Length = 64)]
    public virtual string? MedicalPoolingCategory { get; set; }
    
    /// <summary>
    /// 医疗统筹类别名称
    /// </summary>
    [SugarColumn(ColumnName = "medical_pooling_category_name", ColumnDescription = "医疗统筹类别名称", Length = 64)]
    public virtual string? MedicalPoolingCategoryName { get; set; }
    
    /// <summary>
    /// 险种标志
    /// </summary>
    [SugarColumn(ColumnName = "medical_insurance_flag", ColumnDescription = "险种标志", Length = 64)]
    public virtual string? MedicalInsuranceFlag { get; set; }
    
    /// <summary>
    /// 险种标志名称
    /// </summary>
    [SugarColumn(ColumnName = "medical_insurance_flag_name", ColumnDescription = "险种标志名称", Length = 64)]
    public virtual string? MedicalInsuranceFlagName { get; set; }
    
    /// <summary>
    /// 住院是否允许欠费 1是2否
    /// </summary>
    [SugarColumn(ColumnName = "is_inpatient_arrears_allowed", ColumnDescription = "住院是否允许欠费 1是2否")]
    public virtual int? IsInpatientArrearsAllowed { get; set; }
    
    /// <summary>
    /// 住院允许欠费金额
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_allowed_arrears_amount", ColumnDescription = "住院允许欠费金额", Length = 16, DecimalDigits=2)]
    public virtual decimal? InpatientAllowedArrearsAmount { get; set; }
    
    /// <summary>
    /// 住院允许欠费比例
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_allowed_arrears_ratio", ColumnDescription = "住院允许欠费比例", Length = 5, DecimalDigits=2)]
    public virtual decimal? InpatientAllowedArrearsRatio { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
    
}
