﻿using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 押金退款来源关联表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("refund_source_link", "押金退款来源关联表")]
public class RefundSourceLink : EntityBaseId
{
    /// <summary>
    /// 退款记录ID
    /// </summary>
    [SugarColumn(ColumnName = "refund_transaction_id", ColumnDescription = "退款记录ID")]
    public virtual long? RefundTransactionId { get; set; }
    
    /// <summary>
    /// 缴纳记录ID
    /// </summary>
    [SugarColumn(ColumnName = "payment_transaction_id", ColumnDescription = "缴纳记录ID")]
    public virtual long? PaymentTransactionId { get; set; }
    
    /// <summary>
    /// 退费金额
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "退费金额", Length = 16, DecimalDigits=2)]
    public virtual decimal? Amount { get; set; }
    
}
