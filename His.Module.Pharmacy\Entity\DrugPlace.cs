﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品产地表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_place", "药品产地表")]
public class DrugPlace : EntityTenant
{
    /// <summary>
    /// 产地名称
    /// </summary>
    [SugarColumn(ColumnName = "place_name", ColumnDescription = "产地名称", Length = 100)]
    public virtual string? PlaceName { get; set; }
    
    /// <summary>
    /// 产地名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "place_name_pinyin", ColumnDescription = "产地名称拼音", Length = 100)]
    public virtual string? PlaceNamePinyin { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "1 启用 2 停用")]
    public virtual int? Status { get; set; }
    
}
