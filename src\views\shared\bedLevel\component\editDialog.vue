﻿<script lang="ts" name="bedLevel" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useBedLevelApi } from '/@/api/shared/bedLevel';
import { auth } from '/@/utils/authFunction';
import PinyinSelect from '/@/components/pinyinSelect/index.vue';
import { useChargeItemApi } from '/@/api/shared/chargeItem';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
const basicInfoApi = useBasicInfoApi();
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const bedLevelApi = useBedLevelApi();
const ruleFormRef = ref();

const state = reactive({
	tableLoading:false,
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'createTime', // 默认的排序字段
		order: 'descending', // 排序方向
		descStr: 'descending', // 降序排序的关键字符
	},
	tableData: [] as any[],
});

// 自行添加其他规则
const rules = ref<FormRules>({
	levelName: [{ required: true, message: '请选择床位等级名称！', trigger: 'blur', },],
 
});

// 页面加载时
onMounted(async () => {
	 const emptyRow = {} as any;
	state.tableData = [emptyRow];
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	debugger;
	state.showDialog = true;
	state.title = title;
	row = row ?? {};
	if(!row.id){
		state.ruleForm.status=1;
	}
	state.ruleForm = row.id ? await bedLevelApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));

};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			state.loading = true;
			let values = state.ruleForm;
			const chargeItems=state.tableData.filter(item=>item.chargeItemCode !=null);
			values.chargeItems=chargeItems;
			console.log(values);
			await bedLevelApi[state.ruleForm.id ? 'update' : 'add'](values)
			.finally(() => {
				state.loading = false;
			})
			;
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};
const chargeItemRemoteMethod = async (query: string) => { 
	   const result = await useChargeItemApi().list({
			keyword: query, 
	        PrescriptionTypeCode:"011",
		 
		});
 
		state.dropdownData.chargeList =(result.data?.result?.items || []);
};
const updatePrice = async (row: any) => { 
	row.amount=row.price*row.quantity;
};

const chargeItemChange = async (chargeId :any, row: any) => { 
	
	
	    const chargeItem = state.dropdownData.chargeList.find((e: any) => e.code === chargeId);
	    console.log(chargeItem,row,state.dropdownData.chargeList);
        row.price=chargeItem.price;
		row.quantity=1;
		updatePrice(row);
		row.chargeItemId=chargeItem.id;
		row.chargeItemCode=chargeItem.code;
		row.chargeItemName=chargeItem.name;

		debugger;
		if(state.tableData.filter(e=>e.chargeItemCode===null).length===0 )
		{
			const emptyRow = {chargeItemCode:null} as any;
			state.tableData .push(emptyRow);
		}

};
 const delBedLevelChargeItem = async (row: any) => { 
	if(row.id===null)
	{
		const index = state.tableData.findIndex(e=>e.chargeItemId===row.chargeItemId);
		state.tableData.splice(index,1);
	}else{
        await bedLevelApi.deleteChargeItem(row.id);
	}
	
};
//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="bedLevel-container">
		<el-dialog v-model="state.showDialog" v-loading="state.loading" :width="800" style="height: 450px" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="床位等级名称" prop="levelName">
							<el-input v-model="state.ruleForm.levelName" placeholder="请输入床位等级名称" maxlength="100"
								show-word-limit clearable />
						</el-form-item>
					</el-col>
			 
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20"  >
						<el-form-item label="状态" prop="status">
							<g-sys-dict v-model="state.ruleForm.status" code="StatusEnum" render-as="select"
								placeholder="请选择状态" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="255"
								show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		 
      <el-table :data="state.tableData"  height="220"
        style="width: 100%; " v-loading="state.tableLoading" tooltip-effect="light" row-key="id"  
        border>
    
        <el-table-column type="index" label="序号" width="55" align="center" />
		<!-- <el-table-column prop='amount' label='类别' show-overflow-tooltip >
			</el-table-column> -->
        <el-table-column prop='chargeItemName' label='费用名称' show-overflow-tooltip  width="160">
		 <template #default="scope">
                <!-- <PinyinSelect v-model="state.ruleForm.wardId" placeholder="请选择病区"  
								:options="state.dropdownData.wardList" /> -->
						<el-select filterable v-model="scope.row.chargeItemName"
						:disabled="scope.row.id!=state.ruleForm.editRowId" remote reserve-keyword :remote-method="chargeItemRemoteMethod"
						:loading="state.loading"
						@change="(val: any) => chargeItemChange(val,scope.row )"
						placeholder="请选择收费项目"  clearable >
 
						<!-- <el-option v-if="state.icd10Data.length == 0"
							:value="state.currentPrescription.diagnosticCode ?? ''"
							:label="state.currentPrescription.diagnosticName ?? ''"> </el-option> -->
						<el-option  v-for="item in state.dropdownData.chargeList" :key="item.code"
							:label="item.name" :value="item.code" />
					</el-select>
        </template>
        </el-table-column>
        <el-table-column prop='chargeItemCode' label='费用编号' show-overflow-tooltip />
        <el-table-column prop='price' label='单价' show-overflow-tooltip />
        <el-table-column prop='quantity' label='数量' show-overflow-tooltip >
			 <template #default="scope">
				<el-input-number v-model="scope.row.quantity" @input="updatePrice(scope.row)" />
			 </template>
		</el-table-column>
		<el-table-column prop='amount' label='金额' show-overflow-tooltip />
 
        <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip
          v-if="auth('bedLevel:update') || auth('bedLevel:delete')">
          <template #default="scope">
    
            <el-button icon="ele-Delete" size="small" text type="primary" @click="delBedLevelChargeItem(scope.row)"
              v-auth="'bedLevel:delete'"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
 
    
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>