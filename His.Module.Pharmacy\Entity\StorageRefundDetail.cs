﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品退货明细表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("storage_refund_detail", "药品退货明细表")]
public class StorageRefundDetail : EntityTenant
{
    /// <summary>
    /// 退货记录ID
    /// </summary>
    [SugarColumn(ColumnName = "refund_record_id", ColumnDescription = "退货记录ID")]
    public virtual long? RefundRecordId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品ID")]
    public virtual long? DrugId { get; set; }
    [SugarColumn(ColumnName = "inventory_id", ColumnDescription = "库存id")]
    public virtual long? InventoryId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 100)]
    public virtual string? DrugName { get; set; }
        
    /// <summary>
    /// 药品类型
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类型", Length = 20)]
    public virtual string? DrugType { get; set; }
    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "规格", Length = 100)]
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 100)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量")]
    public virtual int? Quantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [SugarColumn(ColumnName = "sale_price", ColumnDescription = "零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [SugarColumn(ColumnName = "total_sale_price", ColumnDescription = "总零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnName = "batch_no", ColumnDescription = "批号", Length = 100)]
    public virtual string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnName = "production_date", ColumnDescription = "生产日期")]
    public virtual DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    [SugarColumn(ColumnName = "expiration_date", ColumnDescription = "有效期")]
    public virtual DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [SugarColumn(ColumnName = "approval_number", ColumnDescription = "批准文号", Length = 100)]
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "国家医保编码", Length = 100)]
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂商ID
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_id", ColumnDescription = "生产厂商ID")]
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_name", ColumnDescription = "生产厂商名称", Length = 100)]
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
  
    
}
