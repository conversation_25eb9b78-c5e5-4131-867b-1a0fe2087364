﻿using Admin.NET.Core;
namespace His.Module.Registration.Entity;

/// <summary>
/// 诊台表
/// </summary>
[Tenant("1300000000004")]
[SugarTable("clinic_console", "诊台表")]
public class ClinicConsole : EntityTenant
{
    /// <summary>
    /// 诊台名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "诊台名称", Length = 255)]
    public virtual string? Name { get; set; }
    
    /// <summary>
    /// 诊台编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "诊台编码", Length = 255)]
    public virtual string? Code { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    [SugarColumn(ColumnName = "room_id", ColumnDescription = "诊室id")]
    public virtual long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [SugarColumn(ColumnName = "room_name", ColumnDescription = "诊室名称", Length = 255)]
    public virtual string? RoomName { get; set; }
    
    /// <summary>
    /// 当前人数
    /// </summary>
    [SugarColumn(ColumnName = "current_count", ColumnDescription = "当前人数")]
    public virtual int? CurrentCount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
