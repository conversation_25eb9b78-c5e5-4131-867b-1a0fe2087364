{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  // 代码生成配置项-程序集名称集合
  "CodeGen": {
    "EntityAssemblyNames": [ "Admin.NET.Application", "His.Module.Shared", "His.Module.Patient", "His.Module.Financial", "His.Module.Inpatient", "His.Module.InpatientDoctor", "His.Module.Insurance", "His.Module.MedicalTech", "His.Module.Nursing", "His.Module.OutpatientDoctor", "His.Module.Pharmacy", "His.Module.Registration", "His.Module.InpatientNurse"], // 实体所在程序集（自行添加新建程序集名称）
    "BaseEntityNames": [ "EntityTenantId", "EntityTenant", "EntityTenantBaseData", "EntityBaseData", "EntityBase", "EntityBaseId" ], // 实体基类名称
    "EntityBaseColumn": {
      "EntityTenantId": [ "Id", "TenantId" ],
      "EntityTenant": [ "Id", "CreateTime", "UpdateTime", "CreateUserId", "UpdateUserId", "CreateUserName", "UpdateUserName", "IsDelete", "TenantId" ],
      "EntityTenantBaseData": [ "Id", "CreateTime", "UpdateTime", "CreateUserId", "UpdateUserId", "CreateUserName", "UpdateUserName", "IsDelete", "CreateOrgId", "CreateOrgName", "TenantId" ],
      "EntityBaseData": [ "Id", "CreateTime", "UpdateTime", "CreateUserId", "UpdateUserId", "CreateUserName", "UpdateUserName", "IsDelete", "CreateOrgId", "CreateOrgName" ],
      "EntityBase": [ "Id", "CreateTime", "UpdateTime", "CreateUserId", "UpdateUserId", "CreateUserName", "UpdateUserName", "IsDelete" ],
      "EntityBaseId": [ "Id" ]
    },
    "FrontRootPath": "frontend", // 前端项目根目录
    "BackendApplicationNamespaces": [ "Admin.NET.Application", "His.Module.Shared", "His.Module.Patient", "His.Module.Financial", "His.Module.Inpatient", "His.Module.InpatientDoctor", "His.Module.Insurance", "His.Module.MedicalTech", "His.Module.Nursing", "His.Module.OutpatientDoctor", "His.Module.Pharmacy", "His.Module.Registration", "His.Module.InpatientNurse" ] // 后端生成到的项目（自行添加新建命名空间）
  }
}