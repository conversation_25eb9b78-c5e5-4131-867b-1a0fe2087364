﻿namespace His.Module.Pharmacy;

/// <summary>
/// 药品盘点明细表输出参数
/// </summary>
public class StorageTakingDetailDto
{
    /// <summary>
    /// 生产厂商
    /// </summary>
    public string ManufacturerIdFkColumn { get; set; }
    
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 盘点记录ID
    /// </summary>
    public long? TakingRecordId { get; set; }
    public   long? InventoryId { get; set; }
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    public decimal? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 现有零售总价
    /// </summary>
    public decimal? TotalCurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    public decimal? TakingQuantity { get; set; }
    
    public   int? TakingDifference { get; set; }
    public   decimal? TakingDifferenceSalePrice { get; set; }
    /// <summary>
    /// 盘点零售价
    /// </summary>
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售总价
    /// </summary>
    public decimal? TotalTakingSalePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂商
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    public string? DrugType { get; set; }
    
}
