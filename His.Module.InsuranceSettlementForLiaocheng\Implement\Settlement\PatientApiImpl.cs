using Admin.NET.Core;
using Furion.DependencyInjection;
using His.Module.Insurance.Service.Settlement.Dto;
using His.Module.Insurance.Service.Settlement.Dto.Patient;
using His.Module.Insurance.Service.Settlement.Interface;
using His.Module.Insurance.Service.Settlement.Model.Patient;
using His.Module.InsuranceSettlementForLiaocheng.Infrastructure;

using Microsoft.Extensions.Options;
using ReadCardRequest = His.Module.Insurance.Service.Settlement.Model.Patient.ReadCardRequest;

namespace His.Module.InsuranceSettlementForLiaocheng.Implement.Settlement;
using His.Module.Insurance.Service.Settlement;
public class PatientApiImpl(
    UserManager userManager,
    IOptions<InsuranceOptions> insuranceOptions,
    InsuranceSettlementBasicApi insuranceSettlementApi
): IPatientApi,ITransient
{
    /// <summary>
    /// 是否为本地患者
    /// </summary>
    public bool IsLocal(PatientBaseSettlementDto patient)
    {
        // sbjgbh = "000000";
        //
        // //异地慢病社保机构编号传370000或37000000
        // if (patient.MedicalCategory == "9")
        // {
        //     basic.p_yltclb = "4";
        //     basic.p_xzbz = "C";//险种标志
        //     sbjgbh = "37000000";
        // }
        // //异地门诊统筹社保机构编号传37000000
        // if (patient.MedicalCategory == "10")
        // {
        //     basic.p_yltclb = "6";
        //     basic.p_xzbz = "C";//险种标志
        //     sbjgbh = "37000000";
        // }
        return false;
       // throw new NotImplementedException();
    }

    /// <summary>
    /// 3.2.1获取人员基本信息（无卡）
    ///接口名称：query_basic_info
    /// 接口作用：在收到HIS的获取申请后，连接社保中心，获取个人基本信息。
    /// 接口类型：查询类
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<QueryBasicInfoResponse> QueryBasicInfo(QueryBasicInfoRequest   request)//QueryBasicInfoDto dto )
    {
        var sbjgbh = "000000";
      //  var request=dto.toRequest();
        if (!this.IsLocal(request))
        {
          sbjgbh = "37000000"; // 异地社保机构编号 
        }
 
        var result=await insuranceSettlementApi.SendSoapInvokeRequestAsync(request.UserKey, sbjgbh, "query_basic_info",
            request);
        return (QueryBasicInfoResponse)result;
    }

    /// <summary>
    /// 接口名称：read_card
    ///接口作用：读取卡片信息，取得人员相关信息。
    ///接口类型：查询类
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<ReadCardResponse> ReadCard(ReadCardRequest request)
    {
        var sbjgbh = "000000";
 
        if (!this.IsLocal(request))
        {
            sbjgbh = "37000000"; // 异地社保机构编号 
        }
        var result=await insuranceSettlementApi.SendSoapInvokeRequestAsync(request.UserKey, sbjgbh, "read_card",
            request);
        return (ReadCardResponse)result;

    }

    /// <summary>
    /// 接口名称：get_sscard_balance
    /// 接口作用：查询个人账户余额信息，只有省直、东营等银行管理账户使用，济南、淄博无法使用，需通过read_card获取余额。
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<QueryBalanceResponse> GetBalance(QueryBalanceRequest request)
    {
        
        var result=await insuranceSettlementApi.SendSoapInvokeRequestAsync( "get_sscard_balance",
            request);
        return (QueryBalanceResponse)result;
    }

    /// <summary>
    /// 接口名称：read_ewm
    /// 接口作用：根据电子医保凭证二维码获取个人身份证号码和姓名。
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<ReadEwmResponse> ReadEwm(ReadEwmRequest request)
    {
          
        var result=await insuranceSettlementApi.SendSoapInvokeRequestAsync( "get_sscard_balance",
            request);
        return (ReadEwmResponse)result;
    }

}