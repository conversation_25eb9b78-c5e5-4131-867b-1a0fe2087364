﻿using Furion.DatabaseAccessor;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Service.Dto;
using His.Module.Shared.Api.Api.ChargeItem;
using Yitter.IdGenerator;


namespace His.Module.MedicalTech.Service;

/// <summary>
/// 检验模板服务 🧩
/// </summary>
[ApiDescriptionSettings(MedicalTechConst.GroupName, Order = 100)]
public class TemplateLabTestService(
    UserManager userManager,
     IChargeItemApi _chargeItemApi,
     IChargeApi _chargeApi,
    SqlSugarRepository<TemplateLabTest> templateLabTestRep, 
    SqlSugarRepository<TemplateLabTestDetail> templateLabTestDetailRep) : IDynamicApiController, ITransient
{
    
    /// <summary>
    /// 查询模板列表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询模板列表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    [UnitOfWork]
    public async Task<SqlSugarPagedList<TemplateLabTest>> Page(PageTemplateLabTestInput input)
    {

       var list=await templateLabTestRep.AsQueryable()
            .ToPagedListAsync(input.Page, input.PageSize);
        
        return list;
    }
    /// <summary>
    /// 查询模板列表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询模板列表")]
    [ApiDescriptionSettings(Name = "List"), HttpPost]
    [UnitOfWork]
    public async Task<List<TemplateLabTest>> GetList(QueryTemplateLabTestInput input)
    {

        var list=await templateLabTestRep.AsQueryable()
            .ToListAsync();
        
        return list;
    }
    /// <summary>
    /// 增加处方表 ➕
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("查询处方模板明细")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    [UnitOfWork]
    public async Task<List<TemplateLabTestDetail>> GetDetail([FromQuery] long id)
    {
 
        var details = await templateLabTestDetailRep
            .AsQueryable().Where(u => u.TemplateId == id).ToListAsync();
 
        return details;
    }
 
    /// <summary>
    /// 增加 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("保存模板")]
    [ApiDescriptionSettings(Name = "Save"), HttpPost]
    [UnitOfWork]
    public async Task<long> Save(TemplateLabTestInput input)
    {
        var entity = input.Adapt<TemplateLabTest>();
 
        entity.Status = 1; // 默认为1
        if (input.Id is > 0)
        {
            await templateLabTestRep.UpdateAsync(entity) ;
            await templateLabTestDetailRep.AsDeleteable().Where(u => u.TemplateId == entity.Id)
                .ExecuteCommandAsync();
        }
        else
        {  entity.Id = 0; //修改时重新插入
            await templateLabTestRep.InsertAsync(entity) ;
        }
        // 将输入对象映射为 LabTest 实体列表
        var entities = input.Details.Adapt<List<TemplateLabTestDetail>>();
        // 批量获取所有关联的收费项目详细信息（优化数据库查询，避免N+1问题）
        var chargeItems = await _chargeItemApi.GetDetails([.. entities.Select(u => (long)u.ItemId)]);
        // 遍历处理每个检验实体
        foreach (var item in entities)
        {
            // 根据 ItemId 匹配对应的收费项目
            var chargeItem = chargeItems.FirstOrDefault(u => u.Id == item.ItemId);
            // 生成分布式唯一ID
            item.Id = YitIdHelper.NextId();
           item.TemplateId=entity.Id;
       
            // 填充收费项目基础信息
            item.ItemCode = chargeItem.Code;
            item.ItemName = chargeItem.Name;
            item.Unit = chargeItem.Unit;
            item.Price = chargeItem.Price;
            // 计算总金额 = 数量 × 单价
            item.Amount = item.Quantity * chargeItem.Price; 
            // 转换套餐标识为可空整型
            item.IsPackage = (int?)chargeItem.Package;
            // 设置业务时间相关字段
   
            // 设置收费分类和初始状态
            item.ChargeCategoryId = chargeItem.ChargeCategoryId;
            item.Status = 1;
            // 处理套餐项目（当收费项目是套餐时）
            item.Id = 0; //修改时重新插入
            item.TemplateId = entity.Id;
            await templateLabTestDetailRep.InsertAsync(item);
        }
        // 批量插入主处置记录（优化数据库操作）
       
      
   
        return entity.Id;
    }


 
    /// <summary>
    /// 删除处方主表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除模板主表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeleteLabTestInput input)
    {
        var main=await templateLabTestRep.GetFirstAsync(u => u.Id == input.Id);

        
        if(main.TemplateScope==3 && main.CreateUserId!=    userManager.UserId){
            
            throw Oops.Oh("只能删除自己创建的模板");
        }

        await templateLabTestRep.FakeDeleteAsync(main); 
        await templateLabTestDetailRep.AsDeleteable()
            .Where(u => u.TemplateId == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除处方明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除模板明细表")]
    [ApiDescriptionSettings(Name = "DeleteDetail"), HttpPost]
    public async Task DeleteDetail(DeleteLabTestInput input)
    { 
        var detail=await templateLabTestDetailRep.GetFirstAsync(u => u.Id == input.Id);
        var main=await templateLabTestRep.GetFirstAsync(u => u.Id == detail.TemplateId);

        
        if(main.TemplateScope==3 && main.CreateUserId!=    userManager.UserId){
            
            throw Oops.Oh("只能删除自己创建的模板");
        }

        await templateLabTestDetailRep.DeleteByIdAsync(input.Id); 

        //await _prescriptionDetailRep.DeleteAsync(entity);   //真删除
    }
 
 
 
}