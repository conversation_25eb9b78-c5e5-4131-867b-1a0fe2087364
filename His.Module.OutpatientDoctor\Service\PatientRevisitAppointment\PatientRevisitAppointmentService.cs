﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using Microsoft.AspNetCore.Http;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 复诊预约记录服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class PatientRevisitAppointmentService(
    UserManager userManager,
    SqlSugarRepository<PatientRevisitAppointment> patientRevisitAppointmentRep,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 分页查询复诊预约记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询复诊预约记录")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<PatientRevisitAppointmentOutput>> Page(PagePatientRevisitAppointmentInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = patientRevisitAppointmentRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.PatientName.Contains(input.Keyword) || u.VisitNo.Contains(input.Keyword) || u.IdCardNo.Contains(input.Keyword) || u.OutpatientNo.Contains(input.Keyword) || u.AppointmentDoctorName.Contains(input.Keyword) || u.AppointmentDeptName.Contains(input.Keyword) || u.RevisitReason.Contains(input.Keyword) || u.RevisitDeptName.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo.Contains(input.VisitNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardNo), u => u.IdCardNo.Contains(input.IdCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo), u => u.OutpatientNo.Contains(input.OutpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AppointmentDoctorName), u => u.AppointmentDoctorName.Contains(input.AppointmentDoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AppointmentDeptName), u => u.AppointmentDeptName.Contains(input.AppointmentDeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RevisitReason), u => u.RevisitReason.Contains(input.RevisitReason.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RevisitDeptName), u => u.RevisitDeptName.Contains(input.RevisitDeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.RegisterId != null, u => u.RegisterId == input.RegisterId)
            .WhereIF(input.AppointmentDoctorId != null, u => u.AppointmentDoctorId == input.AppointmentDoctorId)
            .WhereIF(input.AppointmentDeptId != null, u => u.AppointmentDeptId == input.AppointmentDeptId)
            .WhereIF(input.RevisitTimeRange?.Length == 2, u => u.RevisitTime >= input.RevisitTimeRange[0] && u.RevisitTime <= input.RevisitTimeRange[1])
            .WhereIF(input.RevisitDeptId != null, u => u.RevisitDeptId == input.RevisitDeptId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<PatientRevisitAppointmentOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取复诊预约记录详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取复诊预约记录详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<PatientRevisitAppointment> Detail([FromQuery] QueryByIdPatientRevisitAppointmentInput input)
    {
        return await patientRevisitAppointmentRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加复诊预约记录 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加复诊预约记录")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddPatientRevisitAppointmentInput input)
    {
        var entity = input.Adapt<PatientRevisitAppointment>();
        entity.AppointmentDeptId = userManager.OrgId;
        entity.AppointmentDoctorId = userManager.UserId;
        entity.AppointmentDoctorName = userManager.RealName;
        entity.AppointmentDeptName= userManager.OrgName;
        if(entity.RevisitTime<=DateTime.Now) 
            throw Oops.Oh("复诊时间应大于今天");
        
        return await patientRevisitAppointmentRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新复诊预约记录 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新复诊预约记录")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdatePatientRevisitAppointmentInput input)
    {
        var exist= await patientRevisitAppointmentRep.GetFirstAsync(u => u.Id == input.Id)?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if(exist.CreateUserId!=userManager.UserId)
            throw Oops.Oh("只能更新自己创建的复诊预约记录");
        
        var entity = input.Adapt<PatientRevisitAppointment>();
        
        
        entity.AppointmentDeptId = userManager.OrgId;
        entity.AppointmentDoctorId = userManager.UserId;
        entity.AppointmentDoctorName = userManager.RealName;
        entity.AppointmentDeptName= userManager.OrgName;
        if(entity.RevisitTime<=DateTime.Now) 
            throw Oops.Oh("复诊时间应大于今天");

        await patientRevisitAppointmentRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除复诊预约记录 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除复诊预约记录")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeletePatientRevisitAppointmentInput input)
    {
        var entity = await patientRevisitAppointmentRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
      
        if(entity.CreateUserId!=userManager.UserId)
            throw Oops.Oh("只能删除自己创建的复诊预约记录");
        await patientRevisitAppointmentRep.FakeDeleteAsync(entity);   //假删除
        //await _patientRevisitAppointmentRep.DeleteAsync(entity);   //真删除
    }

 
    
    /// <summary>
    /// 导出复诊预约记录记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出复诊预约记录记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PagePatientRevisitAppointmentInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportPatientRevisitAppointmentOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "复诊预约记录导出记录");
    }
    
    /// <summary>
    /// 下载复诊预约记录数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载复诊预约记录数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportPatientRevisitAppointmentOutput>(), "复诊预约记录导入模板");
    }
    
    /// <summary>
    /// 导入复诊预约记录记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入复诊预约记录记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportPatientRevisitAppointmentInput, PatientRevisitAppointment>(file, (list, markerErrorAction) =>
            {
                sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.PatientId == null){
                            x.Error = "患者Id不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<PatientRevisitAppointment>>();
                    
                    var storageable = patientRevisitAppointmentRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.PatientName?.Length > 64, "患者姓名长度不能超过64个字符")
                        .SplitError(it => it.Item.VisitNo?.Length > 64, "就诊卡号长度不能超过64个字符")
                        .SplitError(it => it.Item.IdCardNo?.Length > 64, "身份证号长度不能超过64个字符")
                        .SplitError(it => it.Item.OutpatientNo?.Length > 64, "门诊号长度不能超过64个字符")
                        .SplitError(it => it.Item.AppointmentDoctorName?.Length > 64, "预约医生姓名长度不能超过64个字符")
                        .SplitError(it => it.Item.AppointmentDeptName?.Length > 64, "预约科室名称长度不能超过64个字符")
                        .SplitError(it => it.Item.RevisitReason?.Length > 256, "复诊原因长度不能超过256个字符")
                        .SplitError(it => it.Item.RevisitDeptName?.Length > 64, "复诊科室名称长度不能超过64个字符")
                        .SplitError(it => it.Item.Remark?.Length > 256, "备注信息长度不能超过256个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
