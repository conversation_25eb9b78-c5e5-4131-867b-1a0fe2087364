﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy;

/// <summary>
/// 采购计划服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugPurchasePlanService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugPurchasePlan> _drugPurchasePlanRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public DrugPurchasePlanService(SqlSugarRepository<DrugPurchasePlan> drugPurchasePlanRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _drugPurchasePlanRep = drugPurchasePlanRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询采购计划 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询采购计划")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugPurchasePlanOutput>> Page(PageDrugPurchasePlanInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugPurchasePlanRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.PlanNo.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PlanNo), u => u.PlanNo.Contains(input.PlanNo.Trim()))
            .WhereIF(input.PlanTimeRange?.Length == 2, u => u.PlanTime >= input.PlanTimeRange[0] && u.PlanTime <= input.PlanTimeRange[1])
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<DrugPurchasePlanOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取采购计划详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取采购计划详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugPurchasePlan> Detail([FromQuery] QueryByIdDrugPurchasePlanInput input)
    {
        return await _drugPurchasePlanRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加采购计划 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加采购计划")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugPurchasePlanInput input)
    {
        var entity = input.Adapt<DrugPurchasePlan>();
        return await _drugPurchasePlanRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新采购计划 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新采购计划")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugPurchasePlanInput input)
    {
        var entity = input.Adapt<DrugPurchasePlan>();
        await _drugPurchasePlanRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除采购计划 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除采购计划")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugPurchasePlanInput input)
    {
        var entity = await _drugPurchasePlanRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugPurchasePlanRep.FakeDeleteAsync(entity);   //假删除
        //await _drugPurchasePlanRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除采购计划 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除采购计划")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugPurchasePlanInput> input)
    {
        var exp = Expressionable.Create<DrugPurchasePlan>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugPurchasePlanRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugPurchasePlanRep.FakeDeleteAsync(list);   //假删除
        //return await _drugPurchasePlanRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出采购计划记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出采购计划记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugPurchasePlanInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugPurchasePlanOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var statusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.StatusDictLabel = statusDictMap.GetValueOrDefault(e.Status==null?"0":e.Status.ToString() );
        });
        return ExcelHelper.ExportTemplate(list, "采购计划导出记录");
    }
    
    /// <summary>
    /// 下载采购计划数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载采购计划数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugPurchasePlanOutput>(), "采购计划导入模板");
    }
    
    /// <summary>
    /// 导入采购计划记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入采购计划记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var statusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportDrugPurchasePlanInput, DrugPurchasePlan>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 映射字典值
                    // foreach(var item in pageItems) {
                    //     if (string.IsNullOrWhiteSpace(item.StatusDictLabel)) continue;
                    //     item.Status = statusDictMap.GetValueOrDefault(item.StatusDictLabel);
                    //     if (item.Status == null) item.Error = "状态（0 未处理 1 处理中 2 已完成等）字典映射失败";
                    // }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugPurchasePlan>>();
                    
                    var storageable = _drugPurchasePlanRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.PlanNo?.Length > 100, "采购计划号长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
