﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
	  <NoWarn>1701;1702;1591;8632</NoWarn>
	  <DocumentationFile></DocumentationFile>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <GenerateDocumentationFile>True</GenerateDocumentationFile>
	  <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Configuration\OutpatientDoctor.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\His.Module.Financial.Api\His.Module.Financial.Api.csproj" />
    <ProjectReference Include="..\His.Module.Inpatient.Api\His.Module.Inpatient.Api.csproj" />
      <ProjectReference Include="..\His.Module.MedicalTech.Api\His.Module.MedicalTech.Api.csproj"/>
    <ProjectReference Include="..\His.Module.OutpatientDoctor.Api\His.Module.OutpatientDoctor.Api.csproj" />
    <ProjectReference Include="..\His.Module.Patient.Api\His.Module.Patient.Api.csproj" />
    <ProjectReference Include="..\His.Module.Pharmacy.Api\His.Module.Pharmacy.Api.csproj" />
    <ProjectReference Include="..\His.Module.Shared.Api\His.Module.Shared.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Configuration\" />
  </ItemGroup>

</Project>
