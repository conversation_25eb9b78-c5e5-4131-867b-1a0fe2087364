﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy;

/// <summary>
/// 采购计划明细表服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugPurchasePlanDetailService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugPurchasePlanDetail> _drugPurchasePlanDetailRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public DrugPurchasePlanDetailService(SqlSugarRepository<DrugPurchasePlanDetail> drugPurchasePlanDetailRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _drugPurchasePlanDetailRep = drugPurchasePlanDetailRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询采购计划明细表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询采购计划明细表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugPurchasePlanDetailOutput>> Page(PageDrugPurchasePlanDetailInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugPurchasePlanDetailRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) || u.DrugType.Contains(input.Keyword) || u.Spec.Contains(input.Keyword) || u.Unit.Contains(input.Keyword) || u.ManufacturerName.Contains(input.Keyword) || u.SupplierName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Spec), u => u.Spec.Contains(input.Spec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ManufacturerName), u => u.ManufacturerName.Contains(input.ManufacturerName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SupplierName), u => u.SupplierName.Contains(input.SupplierName.Trim()))
            .WhereIF(input.PlanId != null, u => u.PlanId == input.PlanId)
            .WhereIF(input.DrugId != null, u => u.DrugId == input.DrugId)
            .WhereIF(input.PharmacyQuantity != null, u => u.PharmacyQuantity == input.PharmacyQuantity)
            .WhereIF(input.StorageQuantity != null, u => u.StorageQuantity == input.StorageQuantity)
            .WhereIF(input.CurrentSaleQuantity != null, u => u.CurrentSaleQuantity == input.CurrentSaleQuantity)
            .WhereIF(input.LastSaleQuantity != null, u => u.LastSaleQuantity == input.LastSaleQuantity)
            .WhereIF(input.AverageSaleQuantity != null, u => u.AverageSaleQuantity == input.AverageSaleQuantity)
            .WhereIF(input.Quantity != null, u => u.Quantity == input.Quantity)
            .WhereIF(input.ManufacturerId != null, u => u.ManufacturerId == input.ManufacturerId)
            .WhereIF(input.SupplierId != null, u => u.SupplierId == input.SupplierId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<EnterpriseDictionary>((u, manufacturer) => u.ManufacturerId == manufacturer.Id)
            .LeftJoin<EnterpriseDictionary>((u, manufacturer, supplier) => u.SupplierId == supplier.Id)
            .Select((u, manufacturer, supplier) => new DrugPurchasePlanDetailOutput
            {
                Id = u.Id,
                PlanId = u.PlanId,
                DrugId = u.DrugId,
                DrugCode = u.DrugCode,
                DrugName = u.DrugName,
                DrugType = u.DrugType,
                Spec = u.Spec,
                Unit = u.Unit,
                PharmacyQuantity = u.PharmacyQuantity,
                StorageQuantity = u.StorageQuantity,
                CurrentSaleQuantity = u.CurrentSaleQuantity,
                LastSaleQuantity = u.LastSaleQuantity,
                AverageSaleQuantity = u.AverageSaleQuantity,
                Quantity = u.Quantity,
                PurchasePrice = u.PurchasePrice,
                TotalPurchasePrice = u.TotalPurchasePrice,
                ManufacturerId = u.ManufacturerId,
                ManufacturerFkDisplayName = $"{manufacturer.EnterpriseName}",
                ManufacturerName = u.ManufacturerName,
                SupplierId = u.SupplierId,
                SupplierFkDisplayName = $"{supplier.EnterpriseName}",
                SupplierName = u.SupplierName,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取采购计划明细表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取采购计划明细表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugPurchasePlanDetail> Detail([FromQuery] QueryByIdDrugPurchasePlanDetailInput input)
    {
        return await _drugPurchasePlanDetailRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加采购计划明细表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加采购计划明细表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugPurchasePlanDetailInput input)
    {
        var entity = input.Adapt<DrugPurchasePlanDetail>();
        return await _drugPurchasePlanDetailRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新采购计划明细表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新采购计划明细表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugPurchasePlanDetailInput input)
    {
        var entity = input.Adapt<DrugPurchasePlanDetail>();
        await _drugPurchasePlanDetailRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除采购计划明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除采购计划明细表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugPurchasePlanDetailInput input)
    {
        var entity = await _drugPurchasePlanDetailRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugPurchasePlanDetailRep.FakeDeleteAsync(entity);   //假删除
        //await _drugPurchasePlanDetailRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除采购计划明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除采购计划明细表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugPurchasePlanDetailInput> input)
    {
        var exp = Expressionable.Create<DrugPurchasePlanDetail>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugPurchasePlanDetailRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugPurchasePlanDetailRep.FakeDeleteAsync(list);   //假删除
        //return await _drugPurchasePlanDetailRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataDrugPurchasePlanDetailInput input)
    {
        var manufacturerIdData = await _drugPurchasePlanDetailRep.Context.Queryable<EnterpriseDictionary>()
            .InnerJoinIF<DrugPurchasePlanDetail>(input.FromPage, (u, r) => u.Id == r.ManufacturerId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
        var supplierIdData = await _drugPurchasePlanDetailRep.Context.Queryable<EnterpriseDictionary>()
            .InnerJoinIF<DrugPurchasePlanDetail>(input.FromPage, (u, r) => u.Id == r.SupplierId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "manufacturerId", manufacturerIdData },
            { "supplierId", supplierIdData },
        };
    }
    
    /// <summary>
    /// 导出采购计划明细表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出采购计划明细表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugPurchasePlanDetailInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugPurchasePlanDetailOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType);
        });
        return ExcelHelper.ExportTemplate(list, "采购计划明细表导出记录");
    }
    
    /// <summary>
    /// 下载采购计划明细表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载采购计划明细表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugPurchasePlanDetailOutput>(), "采购计划明细表导入模板", (_, info) =>
        {
            if (nameof(ExportDrugPurchasePlanDetailOutput.ManufacturerFkDisplayName) == info.Name) return _drugPurchasePlanDetailRep.Context.Queryable<EnterpriseDictionary>().Select(u => $"{u.EnterpriseName}").Distinct().ToList();
            if (nameof(ExportDrugPurchasePlanDetailOutput.SupplierFkDisplayName) == info.Name) return _drugPurchasePlanDetailRep.Context.Queryable<EnterpriseDictionary>().Select(u => $"{u.EnterpriseName}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入采购计划明细表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入采购计划明细表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportDrugPurchasePlanDetailInput, DrugPurchasePlanDetail>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 生产厂家
                    var manufacturerIdLabelList = pageItems.Where(x => x.ManufacturerFkDisplayName != null).Select(x => x.ManufacturerFkDisplayName).Distinct().ToList();
                    if (manufacturerIdLabelList.Any()) {
                        var manufacturerIdLinkMap = _drugPurchasePlanDetailRep.Context.Queryable<EnterpriseDictionary>().Where(u => manufacturerIdLabelList.Contains($"{u.EnterpriseName}")).ToList().ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.ManufacturerId = manufacturerIdLinkMap.GetValueOrDefault(e.ManufacturerFkDisplayName ?? "");
                            if (e.ManufacturerId == null) e.Error = "生产厂家链接失败";
                        });
                    }
                    // 链接 供应商ID
                    var supplierIdLabelList = pageItems.Where(x => x.SupplierFkDisplayName != null).Select(x => x.SupplierFkDisplayName).Distinct().ToList();
                    if (supplierIdLabelList.Any()) {
                        var supplierIdLinkMap = _drugPurchasePlanDetailRep.Context.Queryable<EnterpriseDictionary>().Where(u => supplierIdLabelList.Contains($"{u.EnterpriseName}")).ToList().ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.SupplierId = supplierIdLinkMap.GetValueOrDefault(e.SupplierFkDisplayName ?? "");
                            if (e.SupplierId == null) e.Error = "供应商ID链接失败";
                        });
                    }
                    
                    // 映射字典值
                    foreach(var item in pageItems) {
                        if (string.IsNullOrWhiteSpace(item.DrugTypeDictLabel)) continue;
                        item.DrugType = drugTypeDictMap.GetValueOrDefault(item.DrugTypeDictLabel);
                        if (item.DrugType == null) item.Error = "药品类型字典映射失败";
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugPurchasePlanDetail>>();
                    
                    var storageable = _drugPurchasePlanDetailRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.DrugCode?.Length > 100, "药品编码长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugName?.Length > 100, "药品名称长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugType?.Length > 100, "药品类型长度不能超过100个字符")
                        .SplitError(it => it.Item.Spec?.Length > 100, "规格长度不能超过100个字符")
                        .SplitError(it => it.Item.Unit?.Length > 100, "单位长度不能超过100个字符")
                        .SplitError(it => it.Item.ManufacturerName?.Length > 100, "生产厂家名称长度不能超过100个字符")
                        .SplitError(it => it.Item.SupplierName?.Length > 100, "供应商名称长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
