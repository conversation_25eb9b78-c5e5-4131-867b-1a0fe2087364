﻿
namespace His.Module.Inpatient.OtherModuleEntity;

/// <summary>
/// 频次表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("frequency", "频次表")]
public class Frequency : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码", Length = 32)]
    public virtual string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 32)]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 32)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 32)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 时间间隔
    /// </summary>
    [SugarColumn(ColumnName = "time_interval", ColumnDescription = "时间间隔")]
    public virtual int? TimeInterval { get; set; }

    /// <summary>
    /// 时间单位 0时 1天 2周
    /// </summary>
    [SugarColumn(ColumnName = "time_unit", ColumnDescription = "时间单位 0时 1天 2周")]
    public virtual Int16? TimeUnit { get; set; }

    /// <summary>
    /// 执行频率
    /// </summary>
    [SugarColumn(ColumnName = "execution_frequency", ColumnDescription = "执行频率")]
    public virtual int? ExecutionFrequency { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [SugarColumn(ColumnName = "execution_time", ColumnDescription = "执行时间", Length = 256)]
    public virtual string? ExecutionTime { get; set; }

    /// <summary>
    /// 持续标识 0持续 1非持续
    /// </summary>
    [SugarColumn(ColumnName = "sustain", ColumnDescription = "持续标识 0持续 1非持续")]
    public virtual Int16? Sustain { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    // [SugarColumn(ColumnName = "usage_scope", ColumnDescription = "使用范围")]
    // public virtual MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
}