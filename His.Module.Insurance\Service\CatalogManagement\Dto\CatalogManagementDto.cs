﻿using His.Module.Insurance.Service.Settlement.Dto;
namespace His.Module.Insurance.Service.CatalogManagement.Dto;

#region 自付比例相关

/// <summary>
/// 获取自付比例请求
/// </summary>
public class GetZfblRequest
{
    /// <summary>
    /// 医院项目编码
    /// </summary>
    /// <remarks>必填参数</remarks>
    [Required]
    public string p_yyxmbm { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    /// <remarks>必填参数</remarks>
    [Required]
    public DateTime p_rq { get; set; }

    /// <summary>
    /// 个人编号
    /// </summary>
    /// <remarks>可选参数</remarks>
    public string? p_grbh { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    /// <remarks>
    /// 可选参数
    /// 4：门诊大病，5：意外伤害，6：普通门诊统筹
    /// 其他值调用数据字典接口获取，代码编号：YLTCLB
    /// </remarks>
    public string? p_yltclb { get; set; }

    /// <summary>
    /// 险种标志
    /// </summary>
    /// <remarks>
    /// 可选参数
    /// 医疗 C，工伤 D，生育 E
    /// 可调用数据字典接口获取，代码编号：XZBZ
    /// </remarks>
    public string? p_xzbz { get; set; }
}

/// <summary>
/// 自付比例信息
/// </summary>
public class ZfblInfo
{
    /// <summary>
    /// 自付比例
    /// </summary>
    public decimal zfbl { get; set; }

    /// <summary>
    /// 说明
    /// </summary>
    public string? sm { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    /// <remarks>
    /// 原社保机构类型，A：职工，B：居民
    /// 其他具体值调用数据字典接口获取，代码编号：RQLB
    /// </remarks>
    public string? rqlb { get; set; }
}

/// <summary>
/// 获取自付比例响应
/// </summary>
public class GetZfblResponse : BaseSettlementResponse
{
    /// <summary>
    /// 自付比例信息列表
    /// </summary>
    public List<ZfblInfo> zfbl_ds { get; set; } = [];
}

#endregion
#region 医院项目目录相关

/// <summary>
/// 医院项目信息
/// </summary>
public class YyxmInfo
{
    /// <summary>
    /// 医院项目编码
    /// </summary>
    public string yyxmbm { get; set; }

    /// <summary>
    /// 医院项目名称
    /// </summary>
    public string yyxmmc { get; set; }

    /// <summary>
    /// 门诊结算项目编号
    /// </summary>
    public string? mzjsxmbh { get; set; }

    /// <summary>
    /// 住院结算项目编号
    /// </summary>
    public string? zyjsxmbh { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    /// <remarks>自负比例的百分数,比如自负比例为15%,则该数值为15</remarks>
    public decimal? zfbl { get; set; }

    /// <summary>
    /// 自付比例说明
    /// </summary>
    public string? sm { get; set; }

    /// <summary>
    /// 医疗项目编码
    /// </summary>
    public string? ylxmbm { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    /// <remarks>包装单位</remarks>
    public string? gg { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? dw { get; set; }

    /// <summary>
    /// 参考价
    /// </summary>
    public decimal? ckj { get; set; }

    /// <summary>
    /// 剂型
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：JXMC</remarks>
    public string? jxm { get; set; }

    /// <summary>
    /// 生产企业
    /// </summary>
    public string? scqy { get; set; }

    /// <summary>
    /// 处方药标志
    /// </summary>
    /// <remarks>1:是,0或空:否</remarks>
    public string? cfybz { get; set; }

    /// <summary>
    /// GMP标志
    /// </summary>
    /// <remarks>1:是,0或空:否</remarks>
    public string? gmpbz { get; set; }

    /// <summary>
    /// 最小规格
    /// </summary>
    public string? zxgg { get; set; }

    /// <summary>
    /// 包含数量
    /// </summary>
    public decimal? bzsl { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public string? gxsj { get; set; }

    /// <summary>
    /// 起始日期
    /// </summary>
    /// <remarks>自付比例的起始日期</remarks>
    public DateTime? qsrq { get; set; }

    /// <summary>
    /// 终止日期
    /// </summary>
    /// <remarks>自付比例的终止日期</remarks>
    public DateTime? zzrq { get; set; }

    /// <summary>
    /// 审批标志
    /// </summary>
    /// <remarks>'0'：尚未审批；'1':审批通过；'2'：审批未通过, 可调用数据字典接口获取，代码编号：SPBZ</remarks>
    public string? spbz { get; set; }

    /// <summary>
    /// 险种标志
    /// </summary>
    /// <remarks>医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ</remarks>
    public string? xzbz { get; set; }

    /// <summary>
    /// 药品标志
    /// </summary>
    /// <remarks>1：药品, 其他具体值调用数据字典接口获取，代码编号：YPBZ</remarks>
    public string? ypbz { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    /// <remarks>原社保机构类型，A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB</remarks>
    public string? rqlb { get; set; }

    /// <summary>
    /// 包装规格
    /// </summary>
    public string? bzgg { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    /// <remarks>自付比例的医疗统筹类别</remarks>
    public string? yltclb { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal dj { get; set; }

    /// <summary>
    /// 同步序号
    /// </summary>
    public long? sxh { get; set; }
}

/// <summary>
/// 查询医院项目目录请求
/// </summary>
public class QueryYyxmInfoRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    /// <remarks>
    /// p_filetype的值为"excel"、"txt"、或"json"其中一种
    /// 不传时，默认为"json"
    /// </remarks>
    public string p_filetype { get; set; } = "json";

    /// <summary>
    /// 药品标志
    /// </summary>
    /// <remarks>
    /// 全部下载时数据量过大，容易下载失败，增加参数，按药品标志分类下载
    /// 调用数据字典接口获取，代码编号：YPBZ
    /// </remarks>
    public string? p_ypbz { get; set; }

    /// <summary>
    /// 医院项目编码
    /// </summary>
    /// <remarks>
    /// 默认接空，如果为空时下载全部，不为空时按yyxmbm过滤进行下载
    /// </remarks>
    public string? p_yyxmbm { get; set; }
}

/// <summary>
/// 查询医院项目目录响应
/// </summary>
public class QueryYyxmInfoResponse : BaseSettlementResponse
{
    /// <summary>
    /// 医院项目信息列表
    /// </summary>
    public List<YyxmInfo> YyxmDs { get; set; } = [];
}

/// <summary>
/// 增量查询医院项目目录请求
/// </summary>
public class QueryYyxmInfoBySxhRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    /// <remarks>
    /// p_filetype的值为"excel"、"txt"、或"json"其中一种
    /// 不传时，默认为"json"
    /// </remarks>
    public string p_filetype { get; set; } = "json";

    /// <summary>
    /// 顺序号
    /// </summary>
    /// <remarks>医院端目前本地数据库中目录最大的流水号。长度为20</remarks>
    public double? p_sxh { get; set; }
}

/// <summary>
/// 限价信息
/// </summary>
public class XjInfo
{
    /// <summary>
    /// 医疗项目编码
    /// </summary>
    public string? ylxmbm { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    /// <remarks>A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB</remarks>
    public string? rqlb { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    /// <remarks>4：门诊大病，5：意外伤害，6：普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB</remarks>
    public string? yltclb { get; set; }

    /// <summary>
    /// 待遇人员类别
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：DYRYLB</remarks>
    public string? dyrylb { get; set; }

    /// <summary>
    /// 起始日期
    /// </summary>
    public DateTime? qsrq { get; set; }

    /// <summary>
    /// 终止日期
    /// </summary>
    public DateTime? zzrq { get; set; }

    /// <summary>
    /// 限价
    /// </summary>
    public decimal? xj { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? bz { get; set; }

    /// <summary>
    /// 医院编码
    /// </summary>
    public string? yybm { get; set; }

    /// <summary>
    /// 医院级别
    /// </summary>
    public string? yyjb { get; set; }

    /// <summary>
    /// 医疗机构性质
    /// </summary>
    public string? yljgxz { get; set; }
}

/// <summary>
/// 增量查询医院项目目录响应
/// </summary>
public class QueryYyxmInfoBySxhResponse : BaseSettlementResponse
{
    /// <summary>
    /// 医院项目信息
    /// </summary>
    /// <remarks>数据集</remarks>
    public List<YyxmInfo> yyxm_ds { get; set; } = [];

    /// <summary>
    /// 是否还有需更新的记录
    /// </summary>
    /// <remarks>1有，0无</remarks>
    public string? sfjxxz { get; set; }

    /// <summary>
    /// 本次下载的记录条数
    /// </summary>
    public int sl { get; set; }

    /// <summary>
    /// 限价信息
    /// </summary>
    /// <remarks>数据集</remarks>
    public List<XjInfo>? xj_ds { get; set; } = [];
}

/// <summary>
/// 新增医院项目请求
/// </summary>
public class AddYyxmRequest
{
    /// <summary>
    /// 医院项目编码
    /// </summary>
    [Required]
    public string p_yyxmbm { get; set; }

    /// <summary>
    /// 医院项目名称
    /// </summary>
    [Required]
    public string p_yyxmmc { get; set; }

    /// <summary>
    /// 药品标志
    /// </summary>
    /// <remarks>1：药品，0:诊疗，2:一次性材料, 其他具体值调用数据字典接口获取，代码编号：YPBZ</remarks>
    [Required]
    public string p_ypbz { get; set; }

    /// <summary>
    /// 最小包装规格的单价
    /// </summary>
    [Required]
    public decimal p_dj { get; set; }

    /// <summary>
    /// 最小规格
    /// </summary>
    [Required]
    public string p_zxgg { get; set; }

    /// <summary>
    /// 大包装包含小规格的数量
    /// </summary>
    [Required]
    public decimal p_bhsl { get; set; }

    /// <summary>
    /// 大包装规格
    /// </summary>
    [Required]
    public string p_zdgg { get; set; }

    /// <summary>
    /// 门诊结算项目编号
    /// </summary>
    /// <remarks>使用地纬结算系统的项目编号</remarks>
    [Required]
    public string p_mzjsxmbh { get; set; }

    /// <summary>
    /// 住院结算项目编号
    /// </summary>
    /// <remarks>使用地纬结算系统的项目编号</remarks>
    [Required]
    public string p_zyjsxmbh { get; set; }

    /// <summary>
    /// 禁忌
    /// </summary>
    public string? p_jj { get; set; }

    /// <summary>
    /// 生产企业
    /// </summary>
    public string? p_scqy { get; set; }

    /// <summary>
    /// 商品名
    /// </summary>
    public string? p_spm { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? p_dw { get; set; }

    /// <summary>
    /// 是否GMP
    /// </summary>
    /// <remarks>1：GMP，0：非GMP</remarks>
    public string? p_gmpbz { get; set; }

    /// <summary>
    /// 是否处方药
    /// </summary>
    /// <remarks>1：处方药，0：非处方药</remarks>
    public string? p_cfybz { get; set; }

    /// <summary>
    /// 剂型
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：JXMC</remarks>
    public string? p_jxm { get; set; }

    /// <summary>
    /// 对应的医保项目编码
    /// </summary>
    /// <remarks>为空时，需要到地纬结算平台中维护和医保目录的对应关系。(医保目录可以通过query_ml服务获取)</remarks>
    public string? p_ylxmbm { get; set; }

    /// <summary>
    /// 补充信息
    /// </summary>
    public string? p_bcxm { get; set; }

    /// <summary>
    /// 适应症
    /// </summary>
    public string? p_syz { get; set; }

    /// <summary>
    /// 批件产品名称
    /// </summary>
    public string? p_pjcpmc { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    public string? p_zczh { get; set; }

    /// <summary>
    /// 注册截止日期
    /// </summary>
    /// <remarks>日期格式：yyyyMMdd</remarks>
    public DateTime? p_zcjzrq { get; set; }

    /// <summary>
    /// 产地
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：CDM</remarks>
    public string? p_cdm { get; set; }
}

/// <summary>
/// 新增医院项目响应
/// </summary>
public class AddYyxmResponse : BaseSettlementResponse
{
}

#endregion
#region 医保目录相关

/// <summary>
/// 医保项目信息
/// </summary>
public class YlxmInfo
{
    /// <summary>
    /// 医疗项目编码
    /// </summary>
    public string ylxmbm { get; set; }

    /// <summary>
    /// 医疗项目标准名称
    /// </summary>
    public string ylxmbzmc { get; set; }

    /// <summary>
    /// 拼音
    /// </summary>
    public string? py { get; set; }

    /// <summary>
    /// 适用症
    /// </summary>
    public string? syz { get; set; }

    /// <summary>
    /// 禁忌
    /// </summary>
    public string? jj { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    /// <remarks>包装单位</remarks>
    public string? gg { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? dw { get; set; }

    /// <summary>
    /// 参考价
    /// </summary>
    public decimal? ckj { get; set; }

    /// <summary>
    /// 剂型码
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：JXMC</remarks>
    public string? jxm { get; set; }

    /// <summary>
    /// 注销标志
    /// </summary>
    /// <remarks>1:注销,0或空:未注销，可调用数据字典接口获取，代码编号：ZXBZ</remarks>
    public string? zxbz { get; set; }

    /// <summary>
    /// 生产企业
    /// </summary>
    public string? scqy { get; set; }

    /// <summary>
    /// 产地码
    /// </summary>
    /// <remarks>C:进口GMP达标,D,进口非GMP达标,G:国产,H:合资,M:合资GMP达标, 其他具体值调用数据字典接口获取，代码编号：CDM</remarks>
    public string? cdm { get; set; }

    /// <summary>
    /// 处方药标志
    /// </summary>
    /// <remarks>1:是,0或空:否</remarks>
    public string? cfybz { get; set; }

    /// <summary>
    /// GMP标志
    /// </summary>
    /// <remarks>1:是,0或空:否</remarks>
    public string? gmpbz { get; set; }

    /// <summary>
    /// 最小规格
    /// </summary>
    public string? zxgg { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public string? gxsj { get; set; }

    /// <summary>
    /// 药品标志
    /// </summary>
    /// <remarks>1：药品, 其他具体值调用数据字典接口获取，代码编号：YPBZ</remarks>
    public string? ypbz { get; set; }

    /// <summary>
    /// 结算项目编号
    /// </summary>
    public string? jsxmbh { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    public string? zczh { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>
    public string? pzwh { get; set; }

    /// <summary>
    /// 包装规格
    /// </summary>
    public string? bzgg { get; set; }

    /// <summary>
    /// 上市备案号
    /// </summary>
    public string? lstd_fil_no { get; set; }

    /// <summary>
    /// 同步序号
    /// </summary>
    public long? sxh { get; set; }
}

/// <summary>
/// 查询医保核心端目录请求
/// </summary>
public class QueryMlRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    /// <remarks>
    /// p_filetype的值为"excel"、"txt"、或"json"其中一种
    /// 不传时，默认为"json"
    /// </remarks>
    public string p_filetype { get; set; } = "json";
}

/// <summary>
/// 查询医保核心端目录响应
/// </summary>
public class QueryMlResponse : BaseSettlementResponse
{
    /// <summary>
    /// 医疗项目信息
    /// </summary>
    /// <remarks>数据集</remarks>
    public List<YlxmInfo> ylxm_ds { get; set; } = [];

    /// <summary>
    /// 首先自付比例信息
    /// </summary>
    /// <remarks>数据集</remarks>
    public List<SxzfblInfo> sxzfbl_ds { get; set; } = [];
}

/// <summary>
/// 首先自付比例信息
/// </summary>
public class SxzfblInfo
{
    /// <summary>
    /// 医疗项目编码
    /// </summary>
    public string? ylxmbm { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    /// <remarks>原社保机构类型：A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB</remarks>
    public string? rqlb { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    /// <remarks>4：门诊大病，5：意外伤害，6：普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB</remarks>
    public string? yltclb { get; set; }

    /// <summary>
    /// 待遇人员类别
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：DYRYLB</remarks>
    public string? dyrylb { get; set; }

    /// <summary>
    /// 起始日期
    /// </summary>
    /// <remarks>自付比例的起始日期</remarks>
    public DateTime? qsrq { get; set; }

    /// <summary>
    /// 终止日期
    /// </summary>
    /// <remarks>自付比例的终止日期</remarks>
    public DateTime? zzrq { get; set; }

    /// <summary>
    /// 首先自付比例
    /// </summary>
    public decimal? sxzfbl { get; set; }

    /// <summary>
    /// 说明
    /// </summary>
    public string? sm { get; set; }

    /// <summary>
    /// 险种标志
    /// </summary>
    /// <remarks>医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ</remarks>
    public string? xzbz { get; set; }
}

/// <summary>
/// 增量查询医保核心端目录响应
/// </summary>
public class QueryMlBySxhResponse : BaseSettlementResponse
{
    /// <summary>
    /// 医疗项目信息
    /// </summary>
    /// <remarks>数据集</remarks>
    public List<YlxmInfo> ylxm_ds { get; set; } = [];

    /// <summary>
    /// 首先自付比例信息
    /// </summary>
    /// <remarks>数据集</remarks>
    public List<SxzfblInfo> sxzfbl_ds { get; set; } = [];

    /// <summary>
    /// 是否还有需更新的记录
    /// </summary>
    /// <remarks>1有，0无</remarks>
    public string? sfjxxz { get; set; }

    /// <summary>
    /// 本次下载的记录条数
    /// </summary>
    /// <remarks>指医疗项目数据集数量，不是首先自付比例数据集数量</remarks>
    public int sl { get; set; }

    /// <summary>
    /// 限价信息
    /// </summary>
    /// <remarks>数据集</remarks>
    public List<XjInfo>? xj_ds { get; set; } = [];
}

/// <summary>
/// 新增或者更新医师信息请求
/// </summary>
public class AddYsRequest
{
    /// <summary>
    /// 医师编码
    /// </summary>
    /// <remarks>医院内部医师编码</remarks>
    [Required]
    public string p_ysbm { get; set; }

    /// <summary>
    /// 医师姓名
    /// </summary>
    [Required]
    public string p_ysxm { get; set; }

    /// <summary>
    /// 医师性别
    /// </summary>
    /// <remarks>1：男，2：女</remarks>
    [Required]
    public string p_ysxb { get; set; }

    /// <summary>
    /// 医师身份证号
    /// </summary>
    [Required]
    public string p_yssfzh { get; set; }

    /// <summary>
    /// 医师执业证号
    /// </summary>
    [Required]
    public string p_yszyzzh { get; set; }

    /// <summary>
    /// 医师级别
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：YSJB</remarks>
    [Required]
    public string p_ysjb { get; set; }

    /// <summary>
    /// 医师类别
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：YSLB</remarks>
    [Required]
    public string p_yslb { get; set; }

    /// <summary>
    /// 科室编码
    /// </summary>
    /// <remarks>医院内部科室编码</remarks>
    [Required]
    public string p_ksbm { get; set; }

    /// <summary>
    /// 科室名称
    /// </summary>
    [Required]
    public string p_ksmc { get; set; }

    /// <summary>
    /// 医师状态
    /// </summary>
    /// <remarks>1：在职，0：离职</remarks>
    public string? p_yszt { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? p_bz { get; set; }
}

/// <summary>
/// 查询医院医师信息请求
/// </summary>
public class QueryYsRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    /// <remarks>
    /// p_filetype的值为"excel"、"txt"、或"json"其中一种
    /// 不传时，默认为"json"
    /// </remarks>
    public string p_filetype { get; set; } = "json";

    /// <summary>
    /// 医师编码
    /// </summary>
    /// <remarks>医院内部医师编码，为空时查询全部</remarks>
    public string? p_ysbm { get; set; }
}

/// <summary>
/// 查询医院医师信息响应
/// </summary>
public class QueryYsResponse : BaseSettlementResponse
{
    /// <summary>
    /// 医师信息
    /// </summary>
    /// <remarks>数据集</remarks>
    public List<YsInfo> ys_ds { get; set; } = [];
}

/// <summary>
/// 医师信息
/// </summary>
public class YsInfo
{
    /// <summary>
    /// 医师编码
    /// </summary>
    /// <remarks>医院内部医师编码</remarks>
    public string? ysbm { get; set; }

    /// <summary>
    /// 医师姓名
    /// </summary>
    public string? ysxm { get; set; }

    /// <summary>
    /// 医师性别
    /// </summary>
    /// <remarks>1：男，2：女</remarks>
    public string? ysxb { get; set; }

    /// <summary>
    /// 医师身份证号
    /// </summary>
    public string? yssfzh { get; set; }

    /// <summary>
    /// 医师执业证号
    /// </summary>
    public string? yszyzzh { get; set; }

    /// <summary>
    /// 医师级别
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：YSJB</remarks>
    public string? ysjb { get; set; }

    /// <summary>
    /// 医师类别
    /// </summary>
    /// <remarks>具体值调用数据字典接口获取，代码编号：YSLB</remarks>
    public string? yslb { get; set; }

    /// <summary>
    /// 科室编码
    /// </summary>
    /// <remarks>医院内部科室编码</remarks>
    public string? ksbm { get; set; }

    /// <summary>
    /// 科室名称
    /// </summary>
    public string? ksmc { get; set; }

    /// <summary>
    /// 医师状态
    /// </summary>
    /// <remarks>1：在职，0：离职</remarks>
    public string? yszt { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? bz { get; set; }

    /// <summary>
    /// 同步序号
    /// </summary>
    public long? sxh { get; set; }
}

/// <summary>
/// 新增医师信息响应
/// </summary>
public class AddYsResponse : BaseSettlementResponse
{
}

/// <summary>
/// 增量查询医保目录请求
/// </summary>
public class QueryMlBySxhRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    /// <remarks>
    /// p_filetype的值为"excel"、"txt"、或"json"其中一种
    /// 不传时，默认为"json"
    /// </remarks>
    public string p_filetype { get; set; } = "json";

    /// <summary>
    /// 顺序号
    /// </summary>
    /// <remarks>
    /// 医院端目前本地数据库中目录最大的流水号。长度为20
    /// 上传本顺序号时，请取要下载的社保局的最大顺序号，不要取全部数据的最大顺序号
    /// </remarks>
    public double? p_sxh { get; set; }
}



#endregion
#region 疾病目录相关

/// <summary>
/// 疾病信息
/// </summary>
public class SiSickInfo
{
    /// <summary>
    /// 疾病编码
    /// </summary>
    public string JbBm { get; set; }

    /// <summary>
    /// 疾病名称
    /// </summary>
    public string JbMc { get; set; }

    /// <summary>
    /// 疾病名称拼音
    /// </summary>
    public string? Py { get; set; }

    /// <summary>
    /// 门诊大病类别
    /// </summary>
    public string? MzdbLb { get; set; }

    /// <summary>
    /// 社保机构编号
    /// </summary>
    public string? SbjgBh { get; set; }

    /// <summary>
    /// 注销标志
    /// </summary>
    public string? ZxBz { get; set; }

    /// <summary>
    /// 同步序号
    /// </summary>
    public long? Sxh { get; set; }
}

/// <summary>
/// 查询疾病目录请求
/// </summary>
public class QuerySiSickRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    public string FileType { get; set; } = "json";
}

/// <summary>
/// 查询疾病目录响应
/// </summary>
public class QuerySiSickResponse : BaseSettlementResponse
{
    /// <summary>
    /// 医保疾病信息列表
    /// </summary>
    public List<SiSickInfo> YbjbDs { get; set; } = new List<SiSickInfo>();
}

/// <summary>
/// 增量查询疾病目录请求
/// </summary>
public class QuerySiSickBySxhRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    public string FileType { get; set; } = "json";

    /// <summary>
    /// 顺序号
    /// </summary>
    public long? Sxh { get; set; }
}

/// <summary>
/// 增量查询疾病目录响应
/// </summary>
public class QuerySiSickBySxhResponse : BaseSettlementResponse
{
    /// <summary>
    /// 医保疾病信息列表
    /// </summary>
    public List<SiSickInfo> YbjbDs { get; set; } = new List<SiSickInfo>();

    /// <summary>
    /// 是否还有需更新的记录
    /// </summary>
    public string? SfjXxz { get; set; }

    /// <summary>
    /// 本次下载的记录条数
    /// </summary>
    public int Sl { get; set; }
}

#endregion
#region 手术目录相关

/// <summary>
/// 手术信息
/// </summary>
public class OperationInfo
{
    /// <summary>
    /// 手术编码
    /// </summary>
    public string SsBm { get; set; }

    /// <summary>
    /// 手术名称
    /// </summary>
    public string SsMc { get; set; }

    /// <summary>
    /// 手术名称拼音
    /// </summary>
    public string? Py { get; set; }

    /// <summary>
    /// 注销标志
    /// </summary>
    public string? ZxBz { get; set; }
}

/// <summary>
/// 查询手术目录请求
/// </summary>
public class QueryOperationRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    public string FileType { get; set; } = "json";
}

/// <summary>
/// 查询手术目录响应
/// </summary>
public class QueryOperationResponse : BaseSettlementResponse
{
    /// <summary>
    /// 医保手术项目信息列表
    /// </summary>
    public List<OperationInfo> SsxmDs { get; set; } = new List<OperationInfo>();
}

#endregion
#region 医师管理相关





#endregion
#region 科室管理相关

/// <summary>
/// 科室信息
/// </summary>
public class DeptInfo
{
    /// <summary>
    /// 科室编码
    /// </summary>
    public string KsBm { get; set; }

    /// <summary>
    /// 科室名称
    /// </summary>
    public string KsMc { get; set; }

    /// <summary>
    /// 起始日期
    /// </summary>
    public DateTime? QsRq { get; set; }

    /// <summary>
    /// 终止日期
    /// </summary>
    public DateTime? ZzRq { get; set; }
}

/// <summary>
/// 查询科室信息请求
/// </summary>
public class QueryHospDeptRequest
{
    /// <summary>
    /// 出参返回格式
    /// </summary>
    public string FileType { get; set; } = "json";

    /// <summary>
    /// 科室编码
    /// </summary>
    public string? KsBm { get; set; }
}

/// <summary>
/// 查询科室信息响应
/// </summary>
public class QueryHospDeptResponse : BaseSettlementResponse
{
    /// <summary>
    /// 科室信息列表
    /// </summary>
    public List<DeptInfo> DeptDs { get; set; } = new List<DeptInfo>();
}

#endregion
#region 数据字典相关

/// <summary>
/// 数据字典项
/// </summary>
public class SiCodeInfo
{
    /// <summary>
    /// 代码编号
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 字典名称
    /// </summary>
    public string Content { get; set; }
}

/// <summary>
/// 查询数据字典请求
/// </summary>
public class QuerySiCodeRequest
{
    /// <summary>
    /// 代码编号
    /// </summary>
    [Required]
    public string DmBh { get; set; }
}

/// <summary>
/// 查询数据字典响应
/// </summary>
public class QuerySiCodeResponse : BaseSettlementResponse
{
    /// <summary>
    /// 数据字典列表
    /// </summary>
    public List<SiCodeInfo> CodeDs { get; set; } = new List<SiCodeInfo>();
}

#endregion
#region 查询输入类

/// <summary>
/// 医师查询输入
/// </summary>
public class DoctorQueryInput : BasePageInput
{
    /// <summary>
    /// 关键字（医师编码或姓名）
    /// </summary>
    public new string? Keyword { get; set; }

    /// <summary>
    /// 科室编码
    /// </summary>
    public string? KsBm { get; set; }
}

/// <summary>
/// 限价信息查询输入
/// </summary>
public class LimitPriceQueryInput : BasePageInput
{
    /// <summary>
    /// 医疗项目编码
    /// </summary>
    public string? YlxmBm { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    public string? RqLb { get; set; }
}

/// <summary>
/// 首先自付比例信息查询输入
/// </summary>
public class FirstSelfPayRatioQueryInput : BasePageInput
{
    /// <summary>
    /// 医疗项目编码
    /// </summary>
    public string? YlxmBm { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    public string? RqLb { get; set; }
}

#endregion
