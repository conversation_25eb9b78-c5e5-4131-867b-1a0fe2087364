﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品字典维护基础输入参数
/// </summary>
public class DrugDictionaryBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [Required(ErrorMessage = "药品编码不能为空")]
    public virtual string DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [Required(ErrorMessage = "药品名称不能为空")]
    public virtual string DrugName { get; set; }
    
    /// <summary>
    /// 通用名称
    /// </summary>
    public virtual string? GenericName { get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    public virtual string? ProductName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 药品分类
    /// </summary>
    public virtual string? DrugCategory { get; set; }
    
    /// <summary>
    /// 药理分类
    /// </summary>
    public virtual string? PharmacologicalClass { get; set; }
    
    /// <summary>
    /// 抗生素级别
    /// </summary>
    [Dict("AntibacterialLevel", AllowNullValue=true)]
    public virtual string? AntibacterialLevel { get; set; }
    
    /// <summary>
    /// 剂型
    /// </summary>
    public virtual string? DrugForm { get; set; }
    
    /// <summary>
    /// 用药途径
    /// </summary>
    public virtual string? DrugRoute { get; set; }
    
    /// <summary>
    /// 用药频次
    /// </summary>
    public virtual string? DrugFreq { get; set; }
    
    /// <summary>
    /// ICD10编码
    /// </summary>
    public virtual List<String> Icd10 { get; set; }
    
    /// <summary>
    /// 生产企业
    /// </summary>
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 产地
    /// </summary>
    public virtual string? PlaceOfOrigin { get; set; }
    
    /// <summary>
    /// 入库单位
    /// </summary>
    public virtual string? StorageUnit { get; set; }
    
    /// <summary>
    /// 包装规格
    /// </summary>
    public virtual string? PackageSpec { get; set; }
    
    /// <summary>
    /// 包装数量
    /// </summary>
    public virtual int? PackageQuantity { get; set; }
    
    /// <summary>
    /// 最小包装单位
    /// </summary>
    public virtual string? MinPackageUnit { get; set; }
    
    /// <summary>
    /// 剂量单位
    /// </summary>
    public virtual string? DosageUnit { get; set; }
    
    /// <summary>
    /// 剂量值
    /// </summary>
    public virtual decimal? DosageValue { get; set; }
    
    /// <summary>
    /// 含量
    /// </summary>
    public virtual decimal? ContentValue { get; set; }
    
    /// <summary>
    /// 含量单位
    /// </summary>
    public virtual string? ContentUnit { get; set; }
    
    /// <summary>
    /// 门诊规格
    /// </summary>
    public virtual string? OutpatientSpec { get; set; }
    
    /// <summary>
    /// 门诊单位
    /// </summary>
    public virtual string? OutpatientUnit { get; set; }
    
    /// <summary>
    /// 门诊包装数量
    /// </summary>
    public virtual int? OutpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 住院规格
    /// </summary>
    public virtual string? InpatientSpec { get; set; }
    
    /// <summary>
    /// 住院单位
    /// </summary>
    public virtual string? InpatientUnit { get; set; }
    
    /// <summary>
    /// 住院包装数量
    /// </summary>
    public virtual int? InpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 采购类型
    /// </summary>
    [Dict("DrugPurchaseType", AllowNullValue=true)]
    public virtual string? PurchaseType { get; set; }
    
    /// <summary>
    /// 上市许可持有人
    /// </summary>
    public virtual string? Holder { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    public virtual decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public virtual decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 每公斤进价
    /// </summary>
    public virtual decimal? PurchasePriceOfKg { get; set; }
    
    /// <summary>
    /// 每公斤零售价
    /// </summary>
    public virtual decimal? SalePriceOfKg { get; set; }
    
    /// <summary>
    /// 电子监管码
    /// </summary>
    public virtual string? RegulationCode { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 优先使用
    /// </summary>
    [Dict("DrugPriorityUse", AllowNullValue=true)]
    public virtual string? PriorityUse { get; set; }
    
    /// <summary>
    /// 药房货位
    /// </summary>
    public virtual string? PharmacyLocation { get; set; }
    
    /// <summary>
    /// 药库货位
    /// </summary>
    public virtual string? StorehouseLocation { get; set; }
    
    /// <summary>
    /// YPID
    /// </summary>
    public virtual string? Ypid { get; set; }
    
    /// <summary>
    /// 是否拆零
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsSplit { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 是否医保药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsMedicare { get; set; }
    
    /// <summary>
    /// 是否自制药
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsSelf { get; set; }
    
    /// <summary>
    /// 是否基本药物
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsBasic { get; set; }
    
    /// <summary>
    /// 是否皮试药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsSkinTest { get; set; }
    
    /// <summary>
    /// 是否国谈药
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsCountry { get; set; }
    
    /// <summary>
    /// 是否辅助药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsAssist { get; set; }
    
    /// <summary>
    /// 是否临采药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsTemporary { get; set; }
    
    /// <summary>
    /// 是否溶媒
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsSolvent { get; set; }
    
    /// <summary>
    /// 是否新冠门诊药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public virtual YesNoEnum? IsCovid { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品字典维护分页查询输入参数
/// </summary>
public class PageDrugDictionaryInput : BasePageInput
{
    /// <summary>
    /// 药品编码
    /// </summary>
    public string DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string DrugName { get; set; }
    
    /// <summary>
    /// 药品分类
    /// </summary>
    public string? DrugCategory { get; set; }
    
    /// <summary>
    /// 药理分类
    /// </summary>
    public string? PharmacologicalClass { get; set; }
    
    /// <summary>
    /// 抗生素级别
    /// </summary>
    [Dict("AntibacterialLevel", AllowNullValue=true)]
    public string? AntibacterialLevel { get; set; }
    public virtual string? DrugType { get; set; }
    /// <summary>
    /// 剂型
    /// </summary>
    public string? DrugForm { get; set; }
    
    /// <summary>
    /// 用药途径
    /// </summary>
    public string? DrugRoute { get; set; }
    
    /// <summary>
    /// 用药频次
    /// </summary>
    public string? DrugFreq { get; set; }
    
    /// <summary>
    /// ICD10编码
    /// </summary>
    public List<String>  Icd10 { get; set; }
    
    /// <summary>
    /// 生产企业
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产企业名称
    /// </summary>
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 产地
    /// </summary>
    public string? PlaceOfOrigin { get; set; }
    
    /// <summary>
    /// 入库单位
    /// </summary>
    public string? StorageUnit { get; set; }
    
    /// <summary>
    /// 包装规格
    /// </summary>
    public string? PackageSpec { get; set; }
    
    /// <summary>
    /// 包装数量
    /// </summary>
    public int? PackageQuantity { get; set; }
    
    /// <summary>
    /// 最小包装单位
    /// </summary>
    public string? MinPackageUnit { get; set; }
    
    /// <summary>
    /// 剂量单位
    /// </summary>
    public string? DosageUnit { get; set; }
    
    /// <summary>
    /// 剂量值
    /// </summary>
    public decimal? DosageValue { get; set; }
    
    /// <summary>
    /// 含量
    /// </summary>
    public decimal? ContentValue { get; set; }
    
    /// <summary>
    /// 含量单位
    /// </summary>
    public string? ContentUnit { get; set; }
    
    /// <summary>
    /// 门诊规格
    /// </summary>
    public string? OutpatientSpec { get; set; }
    
    /// <summary>
    /// 门诊单位
    /// </summary>
    public string? OutpatientUnit { get; set; }
    
    /// <summary>
    /// 门诊包装数量
    /// </summary>
    public int? OutpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 住院规格
    /// </summary>
    public string? InpatientSpec { get; set; }
    
    /// <summary>
    /// 住院单位
    /// </summary>
    public string? InpatientUnit { get; set; }
    
    /// <summary>
    /// 住院包装数量
    /// </summary>
    public int? InpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 采购类型
    /// </summary>
    [Dict("DrugPurchaseType", AllowNullValue=true)]
    public string? PurchaseType { get; set; }
    
    /// <summary>
    /// 上市许可持有人
    /// </summary>
    public long? Holder { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 每公斤进价
    /// </summary>
    public decimal? PurchasePriceOfKg { get; set; }
    
    /// <summary>
    /// 每公斤零售价
    /// </summary>
    public decimal? SalePriceOfKg { get; set; }
    
    /// <summary>
    /// 电子监管码
    /// </summary>
    public string? RegulationCode { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 优先使用
    /// </summary>
    [Dict("DrugPriorityUse", AllowNullValue=true)]
    public string? PriorityUse { get; set; }
    
    /// <summary>
    /// 药房货位
    /// </summary>
    public string? PharmacyLocation { get; set; }
    
    /// <summary>
    /// 药库货位
    /// </summary>
    public string? StorehouseLocation { get; set; }
    
    /// <summary>
    /// YPID
    /// </summary>
    public string? Ypid { get; set; }
    
    /// <summary>
    /// 是否拆零
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSplit { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 是否医保药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsMedicare { get; set; }
    
    /// <summary>
    /// 是否自制药
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSelf { get; set; }
    
    /// <summary>
    /// 是否基本药物
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsBasic { get; set; }
    
    /// <summary>
    /// 是否皮试药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSkinTest { get; set; }
    
    /// <summary>
    /// 是否国谈药
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsCountry { get; set; }
    
    /// <summary>
    /// 是否辅助药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsAssist { get; set; }
    
    /// <summary>
    /// 是否临采药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsTemporary { get; set; }
    
    /// <summary>
    /// 是否溶媒
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSolvent { get; set; }
    
    /// <summary>
    /// 是否新冠门诊药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsCovid { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品字典维护增加输入参数
/// </summary>
public class AddDrugDictionaryInput
{
    /// <summary>
    /// 药品编码
    /// </summary>
 
    public string DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [Required(ErrorMessage = "药品名称不能为空")]
    [MaxLength(200, ErrorMessage = "药品名称字符长度不能超过200")]
    public string DrugName { get; set; }
    
    /// <summary>
    /// 通用名称
    /// </summary>
    [MaxLength(200, ErrorMessage = "通用名称字符长度不能超过200")]
    public string? GenericName { get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    [MaxLength(200, ErrorMessage = "产品名称字符长度不能超过200")]
    public string? ProductName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品分类
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品分类字符长度不能超过100")]
    public string? DrugCategory { get; set; }
    
    /// <summary>
    /// 药理分类
    /// </summary>
    [MaxLength(100, ErrorMessage = "药理分类字符长度不能超过100")]
    public string? PharmacologicalClass { get; set; }
    
    /// <summary>
    /// 抗生素级别
    /// </summary>
    [Dict("AntibacterialLevel", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "抗生素级别字符长度不能超过100")]
    public string? AntibacterialLevel { get; set; }
    
    /// <summary>
    /// 剂型
    /// </summary>
    [MaxLength(100, ErrorMessage = "剂型字符长度不能超过100")]
    public string? DrugForm { get; set; }
    
    /// <summary>
    /// 用药途径
    /// </summary>
    [MaxLength(100, ErrorMessage = "用药途径字符长度不能超过100")]
    public string? DrugRoute { get; set; }
    
    /// <summary>
    /// 用药频次
    /// </summary>
    [MaxLength(100, ErrorMessage = "用药频次字符长度不能超过100")]
    public string? DrugFreq { get; set; }
    
    /// <summary>
    /// ICD10编码
    /// </summary>
    public object? Icd10 { get; set; }
    
    /// <summary>
    /// 生产企业
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 产地
    /// </summary>
    [MaxLength(200, ErrorMessage = "产地字符长度不能超过200")]
    public string? PlaceOfOrigin { get; set; }
    
    /// <summary>
    /// 入库单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "入库单位字符长度不能超过100")]
    public string? StorageUnit { get; set; }
    
    /// <summary>
    /// 包装规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "包装规格字符长度不能超过100")]
    public string? PackageSpec { get; set; }
    
    /// <summary>
    /// 包装数量
    /// </summary>
    public int? PackageQuantity { get; set; }
    
    /// <summary>
    /// 最小包装单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "最小包装单位字符长度不能超过100")]
    public string? MinPackageUnit { get; set; }
    
    /// <summary>
    /// 剂量单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "剂量单位字符长度不能超过100")]
    public string? DosageUnit { get; set; }
    
    /// <summary>
    /// 剂量值
    /// </summary>
    public decimal? DosageValue { get; set; }
    
    /// <summary>
    /// 含量
    /// </summary>
    public decimal? ContentValue { get; set; }
    
    /// <summary>
    /// 含量单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "含量单位字符长度不能超过100")]
    public string? ContentUnit { get; set; }
    
    /// <summary>
    /// 门诊规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "门诊规格字符长度不能超过100")]
    public string? OutpatientSpec { get; set; }
    
    /// <summary>
    /// 门诊单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "门诊单位字符长度不能超过100")]
    public string? OutpatientUnit { get; set; }
    
    /// <summary>
    /// 门诊包装数量
    /// </summary>
    public int? OutpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 住院规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "住院规格字符长度不能超过100")]
    public string? InpatientSpec { get; set; }
    
    /// <summary>
    /// 住院单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "住院单位字符长度不能超过100")]
    public string? InpatientUnit { get; set; }
    
    /// <summary>
    /// 住院包装数量
    /// </summary>
    public int? InpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 采购类型
    /// </summary>
    [Dict("DrugPurchaseType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "采购类型字符长度不能超过100")]
    public string? PurchaseType { get; set; }
    
    /// <summary>
    /// 上市许可持有人
    /// </summary>
    [MaxLength(100, ErrorMessage = "上市许可持有人字符长度不能超过100")]
    public string? Holder { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 每公斤进价
    /// </summary>
    public decimal? PurchasePriceOfKg { get; set; }
    
    /// <summary>
    /// 每公斤零售价
    /// </summary>
    public decimal? SalePriceOfKg { get; set; }
    
    /// <summary>
    /// 电子监管码
    /// </summary>
    [MaxLength(100, ErrorMessage = "电子监管码字符长度不能超过100")]
    public string? RegulationCode { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 优先使用
    /// </summary>
    [Dict("DrugPriorityUse", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "优先使用字符长度不能超过100")]
    public string? PriorityUse { get; set; }
    
    /// <summary>
    /// 药房货位
    /// </summary>
    [MaxLength(100, ErrorMessage = "药房货位字符长度不能超过100")]
    public string? PharmacyLocation { get; set; }
    
    /// <summary>
    /// 药库货位
    /// </summary>
    [MaxLength(100, ErrorMessage = "药库货位字符长度不能超过100")]
    public string? StorehouseLocation { get; set; }
    
    /// <summary>
    /// YPID
    /// </summary>
    [MaxLength(100, ErrorMessage = "YPID字符长度不能超过100")]
    public string? Ypid { get; set; }
    
    /// <summary>
    /// 是否拆零
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSplit { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 是否医保药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsMedicare { get; set; }
    
    /// <summary>
    /// 是否自制药
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSelf { get; set; }
    
    /// <summary>
    /// 是否基本药物
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsBasic { get; set; }
    
    /// <summary>
    /// 是否皮试药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSkinTest { get; set; }
    
    /// <summary>
    /// 是否国谈药
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsCountry { get; set; }
    
    /// <summary>
    /// 是否辅助药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsAssist { get; set; }
    
    /// <summary>
    /// 是否临采药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsTemporary { get; set; }
    
    /// <summary>
    /// 是否溶媒
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSolvent { get; set; }
    
    /// <summary>
    /// 是否新冠门诊药品
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsCovid { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品字典维护删除输入参数
/// </summary>
public class DeleteDrugDictionaryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品字典维护更新输入参数
/// </summary>
public class UpdateDrugDictionaryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>    
    [Required(ErrorMessage = "药品编码不能为空")]
    [MaxLength(50, ErrorMessage = "药品编码字符长度不能超过50")]
    public string DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>    
    [Required(ErrorMessage = "药品名称不能为空")]
    [MaxLength(200, ErrorMessage = "药品名称字符长度不能超过200")]
    public string DrugName { get; set; }
    
    /// <summary>
    /// 通用名称
    /// </summary>    
    [MaxLength(200, ErrorMessage = "通用名称字符长度不能超过200")]
    public string? GenericName { get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>    
    [MaxLength(200, ErrorMessage = "产品名称字符长度不能超过200")]
    public string? ProductName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>    
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品分类
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品分类字符长度不能超过100")]
    public string? DrugCategory { get; set; }
    
    /// <summary>
    /// 药理分类
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药理分类字符长度不能超过100")]
    public string? PharmacologicalClass { get; set; }
    
    /// <summary>
    /// 抗生素级别
    /// </summary>    
    [Dict("AntibacterialLevel", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "抗生素级别字符长度不能超过100")]
    public string? AntibacterialLevel { get; set; }
    
    /// <summary>
    /// 剂型
    /// </summary>    
    [MaxLength(100, ErrorMessage = "剂型字符长度不能超过100")]
    public string? DrugForm { get; set; }
    
    /// <summary>
    /// 用药途径
    /// </summary>    
    [MaxLength(100, ErrorMessage = "用药途径字符长度不能超过100")]
    public string? DrugRoute { get; set; }
    
    /// <summary>
    /// 用药频次
    /// </summary>    
    [MaxLength(100, ErrorMessage = "用药频次字符长度不能超过100")]
    public string? DrugFreq { get; set; }
    
    /// <summary>
    /// ICD10编码
    /// </summary>    
    public object? Icd10 { get; set; }
    
    /// <summary>
    /// 生产企业
    /// </summary>    
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 产地
    /// </summary>    
    [MaxLength(200, ErrorMessage = "产地字符长度不能超过200")]
    public string? PlaceOfOrigin { get; set; }
    
    /// <summary>
    /// 入库单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "入库单位字符长度不能超过100")]
    public string? StorageUnit { get; set; }
    
    /// <summary>
    /// 包装规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "包装规格字符长度不能超过100")]
    public string? PackageSpec { get; set; }
    
    /// <summary>
    /// 包装数量
    /// </summary>    
    public int? PackageQuantity { get; set; }
    
    /// <summary>
    /// 最小包装单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "最小包装单位字符长度不能超过100")]
    public string? MinPackageUnit { get; set; }
    
    /// <summary>
    /// 剂量单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "剂量单位字符长度不能超过100")]
    public string? DosageUnit { get; set; }
    
    /// <summary>
    /// 剂量值
    /// </summary>    
    public decimal? DosageValue { get; set; }
    
    /// <summary>
    /// 含量
    /// </summary>    
    public decimal? ContentValue { get; set; }
    
    /// <summary>
    /// 含量单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "含量单位字符长度不能超过100")]
    public string? ContentUnit { get; set; }
    
    /// <summary>
    /// 门诊规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "门诊规格字符长度不能超过100")]
    public string? OutpatientSpec { get; set; }
    
    /// <summary>
    /// 门诊单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "门诊单位字符长度不能超过100")]
    public string? OutpatientUnit { get; set; }
    
    /// <summary>
    /// 门诊包装数量
    /// </summary>    
    public int? OutpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 住院规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "住院规格字符长度不能超过100")]
    public string? InpatientSpec { get; set; }
    
    /// <summary>
    /// 住院单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "住院单位字符长度不能超过100")]
    public string? InpatientUnit { get; set; }
    
    /// <summary>
    /// 住院包装数量
    /// </summary>    
    public int? InpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 采购类型
    /// </summary>    
    [Dict("DrugPurchaseType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "采购类型字符长度不能超过100")]
    public string? PurchaseType { get; set; }
    
    /// <summary>
    /// 上市许可持有人
    /// </summary>    
    [MaxLength(100, ErrorMessage = "上市许可持有人字符长度不能超过100")]
    public string? Holder { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>    
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>    
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 每公斤进价
    /// </summary>    
    public decimal? PurchasePriceOfKg { get; set; }
    
    /// <summary>
    /// 每公斤零售价
    /// </summary>    
    public decimal? SalePriceOfKg { get; set; }
    
    /// <summary>
    /// 电子监管码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "电子监管码字符长度不能超过100")]
    public string? RegulationCode { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 优先使用
    /// </summary>    
    [Dict("DrugPriorityUse", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "优先使用字符长度不能超过100")]
    public string? PriorityUse { get; set; }
    
    /// <summary>
    /// 药房货位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药房货位字符长度不能超过100")]
    public string? PharmacyLocation { get; set; }
    
    /// <summary>
    /// 药库货位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药库货位字符长度不能超过100")]
    public string? StorehouseLocation { get; set; }
    
    /// <summary>
    /// YPID
    /// </summary>    
    [MaxLength(100, ErrorMessage = "YPID字符长度不能超过100")]
    public string? Ypid { get; set; }
    
    /// <summary>
    /// 是否拆零
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSplit { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 是否医保药品
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsMedicare { get; set; }
    
    /// <summary>
    /// 是否自制药
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSelf { get; set; }
    
    /// <summary>
    /// 是否基本药物
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsBasic { get; set; }
    
    /// <summary>
    /// 是否皮试药品
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSkinTest { get; set; }
    
    /// <summary>
    /// 是否国谈药
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsCountry { get; set; }
    
    /// <summary>
    /// 是否辅助药品
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsAssist { get; set; }
    
    /// <summary>
    /// 是否临采药品
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsTemporary { get; set; }
    
    /// <summary>
    /// 是否溶媒
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsSolvent { get; set; }
    
    /// <summary>
    /// 是否新冠门诊药品
    /// </summary>    
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsCovid { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品字典维护主键查询输入参数
/// </summary>
public class QueryByIdDrugDictionaryInput : DeleteDrugDictionaryInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataDrugDictionaryInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetDrugDictionaryStatusInput : BaseStatusInput
{
}

/// <summary>
/// 药品字典维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugDictionaryInput : BaseImportInput
{
    /// <summary>
    /// 药品编码
    /// </summary>
    [ImporterHeader(Name = "*药品编码")]
    [ExporterHeader("*药品编码", Format = "", Width = 25, IsBold = true)]
    public string DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [ImporterHeader(Name = "*药品名称")]
    [ExporterHeader("*药品名称", Format = "", Width = 25, IsBold = true)]
    public string DrugName { get; set; }
    
    /// <summary>
    /// 药品名称拼音
    /// </summary>
    [ImporterHeader(Name = "药品名称拼音")]
    [ExporterHeader("药品名称拼音", Format = "", Width = 25, IsBold = true)]
    public string? DrugNamePinyin { get; set; }
    
    /// <summary>
    /// 通用名称
    /// </summary>
    [ImporterHeader(Name = "通用名称")]
    [ExporterHeader("通用名称", Format = "", Width = 25, IsBold = true)]
    public string? GenericName { get; set; }
    
    /// <summary>
    /// 通用名称拼音
    /// </summary>
    [ImporterHeader(Name = "通用名称拼音")]
    [ExporterHeader("通用名称拼音", Format = "", Width = 25, IsBold = true)]
    public string? GenericNamePinyin { get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    [ImporterHeader(Name = "产品名称")]
    [ExporterHeader("产品名称", Format = "", Width = 25, IsBold = true)]
    public string? ProductName { get; set; }
    
    /// <summary>
    /// 产品名称拼音
    /// </summary>
    [ImporterHeader(Name = "产品名称拼音")]
    [ExporterHeader("产品名称拼音", Format = "", Width = 25, IsBold = true)]
    public string? ProductNamePinyin { get; set; }
    
    /// <summary>
    /// 药品类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类型 文本
    /// </summary>
    [Dict("DrugType")]
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string DrugTypeDictLabel { get; set; }
    
    /// <summary>
    /// 药品分类 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugCategory { get; set; }
    
    /// <summary>
    /// 药品分类 文本
    /// </summary>
    [ImporterHeader(Name = "药品分类")]
    [ExporterHeader("药品分类", Format = "", Width = 25, IsBold = true)]
    public string DrugCategoryFkDisplayName { get; set; }
    
    /// <summary>
    /// 药理分类 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? PharmacologicalClass { get; set; }
    
    /// <summary>
    /// 药理分类 文本
    /// </summary>
    [ImporterHeader(Name = "药理分类")]
    [ExporterHeader("药理分类", Format = "", Width = 25, IsBold = true)]
    public string PharmacologicalClassFkDisplayName { get; set; }
    
    /// <summary>
    /// 抗生素级别 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? AntibacterialLevel { get; set; }
    
    /// <summary>
    /// 抗生素级别 文本
    /// </summary>
    [Dict("AntibacterialLevel")]
    [ImporterHeader(Name = "抗生素级别")]
    [ExporterHeader("抗生素级别", Format = "", Width = 25, IsBold = true)]
    public string AntibacterialLevelDictLabel { get; set; }
    
    /// <summary>
    /// 剂型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugForm { get; set; }
    
    /// <summary>
    /// 剂型 文本
    /// </summary>
    [ImporterHeader(Name = "剂型")]
    [ExporterHeader("剂型", Format = "", Width = 25, IsBold = true)]
    public string DrugFormFkDisplayName { get; set; }
    
    /// <summary>
    /// 用药途径
    /// </summary>
    [ImporterHeader(Name = "用药途径")]
    [ExporterHeader("用药途径", Format = "", Width = 25, IsBold = true)]
    public string? DrugRoute { get; set; }
    
    /// <summary>
    /// 用药频次 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugFreq { get; set; }
    
    /// <summary>
    /// 用药频次 文本
    /// </summary>
    [ImporterHeader(Name = "用药频次")]
    [ExporterHeader("用药频次", Format = "", Width = 25, IsBold = true)]
    public string DrugFreqFkDisplayName { get; set; }
    
    /// <summary>
    /// ICD10编码
    /// </summary>
    [ImporterHeader(Name = "ICD10编码")]
    [ExporterHeader("ICD10编码", Format = "", Width = 25, IsBold = true)]
    public object? Icd10 { get; set; }
    
    /// <summary>
    /// 生产企业 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产企业 文本
    /// </summary>
    [ImporterHeader(Name = "生产企业")]
    [ExporterHeader("生产企业", Format = "", Width = 25, IsBold = true)]
    public string ManufacturerFkDisplayName { get; set; }
    
    /// <summary>
    /// 产地 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? PlaceOfOrigin { get; set; }
    
    /// <summary>
    /// 产地 文本
    /// </summary>
    [ImporterHeader(Name = "产地")]
    [ExporterHeader("产地", Format = "", Width = 25, IsBold = true)]
    public string PlaceOfOriginFkDisplayName { get; set; }
    
    /// <summary>
    /// 入库单位 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? StorageUnit { get; set; }
    
    /// <summary>
    /// 入库单位 文本
    /// </summary>
    [ImporterHeader(Name = "入库单位")]
    [ExporterHeader("入库单位", Format = "", Width = 25, IsBold = true)]
    public string StorageUnitFkDisplayName { get; set; }
    
    /// <summary>
    /// 包装规格
    /// </summary>
    [ImporterHeader(Name = "包装规格")]
    [ExporterHeader("包装规格", Format = "", Width = 25, IsBold = true)]
    public string? PackageSpec { get; set; }
    
    /// <summary>
    /// 包装数量
    /// </summary>
    [ImporterHeader(Name = "包装数量")]
    [ExporterHeader("包装数量", Format = "", Width = 25, IsBold = true)]
    public int? PackageQuantity { get; set; }
    
    /// <summary>
    /// 最小包装单位
    /// </summary>
    [ImporterHeader(Name = "最小包装单位")]
    [ExporterHeader("最小包装单位", Format = "", Width = 25, IsBold = true)]
    public string? MinPackageUnit { get; set; }
    
    /// <summary>
    /// 剂量单位 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DosageUnit { get; set; }
    
    /// <summary>
    /// 剂量单位 文本
    /// </summary>
    [ImporterHeader(Name = "剂量单位")]
    [ExporterHeader("剂量单位", Format = "", Width = 25, IsBold = true)]
    public string DosageUnitFkDisplayName { get; set; }
    
    /// <summary>
    /// 剂量值
    /// </summary>
    [ImporterHeader(Name = "剂量值")]
    [ExporterHeader("剂量值", Format = "", Width = 25, IsBold = true)]
    public decimal? DosageValue { get; set; }
    
    /// <summary>
    /// 含量 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public decimal? ContentValue { get; set; }
    
    /// <summary>
    /// 含量 文本
    /// </summary>
    [ImporterHeader(Name = "含量")]
    [ExporterHeader("含量", Format = "", Width = 25, IsBold = true)]
    public string ContentValueFkDisplayName { get; set; }
    
    /// <summary>
    /// 含量单位
    /// </summary>
    [ImporterHeader(Name = "含量单位")]
    [ExporterHeader("含量单位", Format = "", Width = 25, IsBold = true)]
    public string? ContentUnit { get; set; }
    
    /// <summary>
    /// 门诊规格
    /// </summary>
    [ImporterHeader(Name = "门诊规格")]
    [ExporterHeader("门诊规格", Format = "", Width = 25, IsBold = true)]
    public string? OutpatientSpec { get; set; }
    
    /// <summary>
    /// 门诊单位 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? OutpatientUnit { get; set; }
    
    /// <summary>
    /// 门诊单位 文本
    /// </summary>
    [ImporterHeader(Name = "门诊单位")]
    [ExporterHeader("门诊单位", Format = "", Width = 25, IsBold = true)]
    public string OutpatientUnitFkDisplayName { get; set; }
    
    /// <summary>
    /// 门诊包装数量
    /// </summary>
    [ImporterHeader(Name = "门诊包装数量")]
    [ExporterHeader("门诊包装数量", Format = "", Width = 25, IsBold = true)]
    public int? OutpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 住院规格
    /// </summary>
    [ImporterHeader(Name = "住院规格")]
    [ExporterHeader("住院规格", Format = "", Width = 25, IsBold = true)]
    public string? InpatientSpec { get; set; }
    
    /// <summary>
    /// 住院单位 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? InpatientUnit { get; set; }
    
    /// <summary>
    /// 住院单位 文本
    /// </summary>
    [ImporterHeader(Name = "住院单位")]
    [ExporterHeader("住院单位", Format = "", Width = 25, IsBold = true)]
    public string InpatientUnitFkDisplayName { get; set; }
    
    /// <summary>
    /// 住院包装数量
    /// </summary>
    [ImporterHeader(Name = "住院包装数量")]
    [ExporterHeader("住院包装数量", Format = "", Width = 25, IsBold = true)]
    public int? InpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 采购类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? PurchaseType { get; set; }
    
    /// <summary>
    /// 采购类型 文本
    /// </summary>
    [Dict("DrugPurchaseType")]
    [ImporterHeader(Name = "采购类型")]
    [ExporterHeader("采购类型", Format = "", Width = 25, IsBold = true)]
    public string PurchaseTypeDictLabel { get; set; }
    
    /// <summary>
    /// 上市许可持有人 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? Holder { get; set; }
    
    /// <summary>
    /// 上市许可持有人 文本
    /// </summary>
    [ImporterHeader(Name = "上市许可持有人")]
    [ExporterHeader("上市许可持有人", Format = "", Width = 25, IsBold = true)]
    public string HolderFkDisplayName { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    [ImporterHeader(Name = "进价")]
    [ExporterHeader("进价", Format = "", Width = 25, IsBold = true)]
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [ImporterHeader(Name = "零售价")]
    [ExporterHeader("零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 每公斤进价
    /// </summary>
    [ImporterHeader(Name = "每公斤进价")]
    [ExporterHeader("每公斤进价", Format = "", Width = 25, IsBold = true)]
    public decimal? PurchasePriceOfKg { get; set; }
    
    /// <summary>
    /// 每公斤零售价
    /// </summary>
    [ImporterHeader(Name = "每公斤零售价")]
    [ExporterHeader("每公斤零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? SalePriceOfKg { get; set; }
    
    /// <summary>
    /// 电子监管码
    /// </summary>
    [ImporterHeader(Name = "电子监管码")]
    [ExporterHeader("电子监管码", Format = "", Width = 25, IsBold = true)]
    public string? RegulationCode { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [ImporterHeader(Name = "批准文号")]
    [ExporterHeader("批准文号", Format = "", Width = 25, IsBold = true)]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 优先使用 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? PriorityUse { get; set; }
    
    /// <summary>
    /// 优先使用 文本
    /// </summary>
    [Dict("DrugPriorityUse")]
    [ImporterHeader(Name = "优先使用")]
    [ExporterHeader("优先使用", Format = "", Width = 25, IsBold = true)]
    public string PriorityUseDictLabel { get; set; }
    
    /// <summary>
    /// 药房货位
    /// </summary>
    [ImporterHeader(Name = "药房货位")]
    [ExporterHeader("药房货位", Format = "", Width = 25, IsBold = true)]
    public string? PharmacyLocation { get; set; }
    
    /// <summary>
    /// 药库货位
    /// </summary>
    [ImporterHeader(Name = "药库货位")]
    [ExporterHeader("药库货位", Format = "", Width = 25, IsBold = true)]
    public string? StorehouseLocation { get; set; }
    
    /// <summary>
    /// YPID
    /// </summary>
    [ImporterHeader(Name = "YPID")]
    [ExporterHeader("YPID", Format = "", Width = 25, IsBold = true)]
    public string? Ypid { get; set; }
    
    /// <summary>
    /// 是否拆零
    /// </summary>
    [ImporterHeader(Name = "是否拆零")]
    [ExporterHeader("是否拆零", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsSplit { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [ImporterHeader(Name = "国家医保编码")]
    [ExporterHeader("国家医保编码", Format = "", Width = 25, IsBold = true)]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 是否医保药品
    /// </summary>
    [ImporterHeader(Name = "是否医保药品")]
    [ExporterHeader("是否医保药品", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsMedicare { get; set; }
    
    /// <summary>
    /// 是否自制药
    /// </summary>
    [ImporterHeader(Name = "是否自制药")]
    [ExporterHeader("是否自制药", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsSelf { get; set; }
    
    /// <summary>
    /// 是否基本药物
    /// </summary>
    [ImporterHeader(Name = "是否基本药物")]
    [ExporterHeader("是否基本药物", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsBasic { get; set; }
    
    /// <summary>
    /// 是否皮试药品
    /// </summary>
    [ImporterHeader(Name = "是否皮试药品")]
    [ExporterHeader("是否皮试药品", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsSkinTest { get; set; }
    
    /// <summary>
    /// 是否国谈药
    /// </summary>
    [ImporterHeader(Name = "是否国谈药")]
    [ExporterHeader("是否国谈药", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsCountry { get; set; }
    
    /// <summary>
    /// 是否辅助药品
    /// </summary>
    [ImporterHeader(Name = "是否辅助药品")]
    [ExporterHeader("是否辅助药品", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsAssist { get; set; }
    
    /// <summary>
    /// 是否临采药品
    /// </summary>
    [ImporterHeader(Name = "是否临采药品")]
    [ExporterHeader("是否临采药品", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsTemporary { get; set; }
    
    /// <summary>
    /// 是否溶媒
    /// </summary>
    [ImporterHeader(Name = "是否溶媒")]
    [ExporterHeader("是否溶媒", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsSolvent { get; set; }
    
    /// <summary>
    /// 是否新冠门诊药品
    /// </summary>
    [ImporterHeader(Name = "是否新冠门诊药品")]
    [ExporterHeader("是否新冠门诊药品", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsCovid { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
}
