// using His.Module.Insurance.Service.Settlement.Dto.Patient;
// using His.Module.Insurance.Service.Settlement.Model.Patient;
//
//
// namespace His.Module.InsuranceSettlementForLiaocheng.Mapping.Patient;
//
// public static class QueryBasicInfoMapper
// {
//     
//     /// <summary>
//     /// 将 PatientInsuranceDto 转换为 QueryBasicInfoRequest。
//     /// </summary>
//     /// <param name="settlementDto">DTO 对象</param>
//     /// <returns>转换后的医保请求对象</returns>
//     public static QueryBasicInfoRequest toRequest(this QueryBasicInfoSettlementDto settlementDto)
//     {
//         if (settlementDto == null) return null;
//
//         var request= new QueryBasicInfoRequest
//         {  
//             UserKey=settlementDto.UserKey,
//             p_grbh = settlementDto.PersonalNo, // 个人编号
//             p_xzbz = settlementDto.InsuranceTypeCode, // 险种标志
//             p_xm = settlementDto.Name, // 姓名
//             p_yltclb = settlementDto.MedicalCategory, // 医疗统筹类别
//             
//         };
//
//         request.p_rq = settlementDto.QueryDate;;//.ToString("yyyyMMddHHmmss"); // 查询日期，格式化为字符串
//         return request;
//     }
//  
//  /// <summary>
// /// 将 QueryBasicInfoResponse 转换为 QueryBasicInfoResult。
// /// </summary>
// /// <param name="response">医保响应对象</param>
// /// <returns>转换后的 QueryBasicInfoResult 对象</returns>
// public static QueryBasicInfoResult ToResult(this QueryBasicInfoResponse response)
// {
//     if (response == null) return null;
//
//     return new QueryBasicInfoResult
//     {
//         // 姓名
//         Name = response.xm,
//
//         // 性别（1:男，2:女，9:不确定）
//         Gender = response.xb,
//
//         // 社会保障号码（社会保障号码或者身份证号）
//         SocialSecurityNumber = response.grbh,
//
//         // 支付标志（灰名单标志）（0 :灰名单，1:白名单）
//         PaymentFlag = response.zfbz,
//
//         // 支付说明（灰名单原因）（如果是白名单该值为空）
//         PaymentDescription = response.zfsm,
//
//         // 单位名称
//         CompanyName = response.dwmc,
//
//         // 医疗人员类别（内容为汉字）
//         MedicalPersonType = response.ylrylb,
//
//         // 异地标志（1:异地，0:不是异地）
//         RemoteAreaFlag = response.ydbz,
//
//         // 疾病编码（格式为：名称#m编码/...）
//         DiseaseCodes = response.mzdbjbs,
//
//         // 门诊大病备注
//         OutpatientDiseaseRemark = response.mzdbbz,
//
//         // 社保局编码
//         SocialSecurityOfficeCode = response.sbjgbh,
//
//         // 有无15天内住院记录（1:有，0:无）
//         HospitalizationRecordFlag = response.zhzybz,
//
//         // 住院记录说明
//         HospitalizationRecordDescription = response.zhzysm,
//
//         // 转出医院名称
//         TransferOutHospitalName = response.zcyymc,
//
//         // 转出医院出院日期
//         TransferOutDischargeDate = response.zccyrq,
//
//         // 人群类别（A：职工，B：居民）
//         PopulationCategory = response.rqlb,
//
//         // 出生日期
//         BirthDate = response.csrq,
//
//         // 优抚对象标志（济南专用）
//         PreferentialTreatmentFlag = response.yfdxbz,
//
//         // 优抚对象类别（济南专用）
//         PreferentialTreatmentCategory = response.yfdxlb,
//
//         // 参保地市编号（省异地用）
//         InsuranceCityCode = response.cbdsbh,
//
//         // 参保机构名称（省异地用）
//         InsuranceAgencyName = response.cbjgmc,
//
//         // 多社保局标志（0:单身份，1:多身份）
//         MultiSocialSecurityFlag = response.multisbj,
//
//         // 多社保局信息列表
//         MultiSocialSecurityInfos = response.multisbjds?.Select(m => new MultiSbjInfoDto
//         {
//             // 单位名称
//             CompanyName = m.dwmc,
//
//             // 参保状态
//             InsuranceStatus = m.cbzt,
//
//             // 险种标志
//             InsuranceTypeFlag = m.xzbz,
//
//             // 社保机构编号
//             SocialSecurityAgencyCode = m.sbjgbh,
//
//             // 参保人员类别
//             InsuredPersonCategory = m.cbrylb,
//
//             // 社保机构名称
//             SocialSecurityAgencyName = m.sbjgmc
//         }).ToList(),
//
//         // 门诊定点标志（1:是定点医院）
//         OutpatientDesignatedMedicalInstitutionFlag = response.mzddbz,
//
//         // 门诊定点说明
//         OutpatientDesignatedMedicalInstitutionDescription = response.mzddsm,
//
//         // 普通门诊慢性病备案疾病（威海专用）
//         GeneralChronicDiseaseRecord = response.ptmzjbs,
//
//         // 余额
//         Balance = response.ye,
//
//         // 救助人员类别
//         AssistancePersonCategory = response.jzrylb,
//
//         // 贫困人口标志（1:是，0:否）
//         PovertyPopulationFlag = response.pkrkbz,
//
//         // 单位性质名称
//         CompanyNatureName = response.dwxzmc,
//
//         // 居民两病备案疾病编码
//         TwoChronicDiseasesRecordCode = response.lbjbbm,
//
//         // 工伤检查信息（济南使用）
//         WorkInjuryCheckInfo = response.gsjcxx,
//
//         // 门慢的二级代码（聊城使用）
//         OutpatientChronicDiseaseSecondaryCode = response.mzmxm_ejjbbm,
//
//         // 门慢的二级名称（聊城使用）
//         OutpatientChronicDiseaseSecondaryName = response.mzmxm_ejjbmc,
//
//         // 人员ID
//         PersonId = response.ryid
//     };
// }
//
//
// }