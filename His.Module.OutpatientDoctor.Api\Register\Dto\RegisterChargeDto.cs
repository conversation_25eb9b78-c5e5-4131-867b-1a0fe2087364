namespace His.Module.OutpatientDoctor.Api.Register.Dto;

public class RegisterChargeDto
{
    
    public long?  RegisterId { get; set; }
    public long?  ChargeId { get; set; }
    /// <summary>
    /// 医保支付金额
    /// </summary>
    public decimal?  InsurancePayAmount{ get; set; }
    /// <summary>
    /// 个人支付金额
    /// </summary>
    public decimal?  SelfPayAmount { get; set; }
    public decimal?  TotalAmount { get; set; }
    public List<RegisterChargeItemDto> Items { get; set; }
}

public class RegisterChargeItemDto
{
    
    public long? ItemId { get; set; }
    /// <summary>
    /// 医保支付金额
    /// </summary>
    public decimal?  InsurancePayAmount{ get; set; }
    /// <summary>
    /// 个人支付金额
    /// </summary>
    public decimal?  SelfPayAmount { get; set; }
    public decimal?  TotalAmount { get; set; }
}