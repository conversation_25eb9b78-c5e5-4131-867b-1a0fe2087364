using His.Module.Insurance.Service.Settlement.Dto;

namespace His.Module.Insurance.Service.Settlement.Model.Outpatient;

/// <summary>
/// 门诊预结算接口请求参数（查询类）
/// </summary>
public class SettleMzPreRequest:BaseSettlementRequest
{
    /// <summary>
    /// *医疗统筹类别
    /// <para>4：门诊大病，5：意外伤害，6：普通门诊统筹，其他值见字典 YLTCLB</para>
    /// </summary>
    public string p_yltclb { get; set; }

    /// <summary>
    /// *社会保障号码
    /// </summary>
    public string p_grbh { get; set; }

    /// <summary>
    /// *姓名（参保病人的姓名）
    /// </summary>
    public string p_xm { get; set; }

    /// <summary>
    /// *性别（1:男 2:女 9:不确定）
    /// </summary>
    public string p_xb { get; set; }

    /// <summary>
    /// *病历号
    /// </summary>
    public string p_blh { get; set; }

    /// <summary>
    /// *费用日期（yyyyMMdd）
    /// </summary>
    public DateTime p_fyrq { get; set; }

    /// <summary>
    /// *主治医师编码（HIS须保证医师有资格，编码与地纬保持一致）
    /// </summary>
    public string p_ysbm { get; set; }

    /// <summary>
    /// *疾病编码
    /// <para>• yltclb=‘4’时必须传</para>
    /// <para>• yltclb=‘5’或’6’且xzbz=’C’可传空串</para>
    /// <para>• xzbz=’D’或’E’时必须传</para>
    /// <para>济南免费用药必须传，格式：jbbm:jbmc</para>
    /// </summary>
    public string p_jbbm { get; set; }

    /// <summary>
    /// 卡号
    /// <para>无卡结算：不传</para>
    /// <para>有卡结算：地纬DLL控卡时传""，HIS控卡时传实际卡号</para>
    /// </summary>
    public string p_kh { get; set; }

    /// <summary>
    /// *险种标志（医疗 C，工伤 D，生育 E）
    /// </summary>
    public string p_xzbz { get; set; }

    /// <summary>
    /// 普通门诊刷卡标志
    /// <para>门诊统筹：0，纯个账消费：1，其余情况不传</para>
    /// </summary>
    public string p_ptmzskbz { get; set; }

    /// <summary>
    /// 疾病说明（新增补充项目，用于保存疾病的相关说明）
    /// </summary>
    public string p_jbsm { get; set; }

    /// <summary>
    /// 外地就医类别（01本地定点就医，10异地治疗，其他见字典 JYLB）
    /// </summary>
    public string p_jylb { get; set; }

    /// <summary>
    /// 就医医院编码（异地治疗时必传）
    /// </summary>
    public string p_jyyybm { get; set; }

    /// <summary>
    /// 就诊卡号（枣庄增加参数）
    /// </summary>
    public string p_jzkh { get; set; }

    /// <summary>
    /// 门诊类型（济南地区使用：2普通门诊，3急诊，4急诊转住院）
    /// </summary>
    public string p_mzlx { get; set; }

    /// <summary>
    /// 识别码（潍坊持卡就医、跨省异地必传）
    /// </summary>
    public string p_sbm { get; set; }

    /// <summary>
    /// 复位码（潍坊持卡就医必传）
    /// </summary>
    public string p_fwm { get; set; }

    /// <summary>
    /// 免费用药标志（济南社区/村卫生室免费用药时传1，默认0）
    /// </summary>
    public string p_mfyybz { get; set; }

    /// <summary>
    /// 补充项目信息（通过 get_bcxm 获取需录入项）
    /// </summary>
    public List<SettleMzPreBcxm> p_bcxm_ds { get; set; }

    /// <summary>
    /// psam卡号（忻州省内异地必传）
    /// </summary>
    public string p_pasmkh { get; set; }

    /// <summary>
    /// 二维码（电子社保卡/医保电子凭证二维码或 ectoken）
    /// </summary>
    public string p_ewm { get; set; }

    /// <summary>
    /// 预产期/计生手术时间（威海、淄博 xzbz='E' 时必传）
    /// </summary>
    public DateTime? p_ycq { get; set; }

    /// <summary>
    /// 怀孕时间（威海、淄博 xzbz='E' 时必传）
    /// </summary>
    public DateTime? p_hysj { get; set; }

    /// <summary>
    /// 结婚证号（淄博 xzbz='E' 计生手术必传）
    /// </summary>
    public string p_jhzh { get; set; }

    /// <summary>
    /// 工伤发生日期（济宁工伤住院登记时使用，非必传）
    /// </summary>
    public DateTime? p_gsfsrq { get; set; }

    /// <summary>
    /// 生育合并症（济南专用，非必传）
    /// </summary>
    public string p_syhbz { get; set; }

    /// <summary>
    /// 医疗政策标识（可通过 query_ylzcbs 查询获取）
    /// </summary>
    public string p_ylzcbs { get; set; }

    /// <summary>
    /// 主要诊断（跨省异地门诊大病结算必传）
    /// </summary>
    public string p_zyzd { get; set; }

    /// <summary>
    /// *收费认证方式
    /// <para>00 手工录入身份证号</para>
    /// <para>01 医保电子凭证</para>
    /// <para>02 读居民身份证</para>
    /// <para>03 社会保障卡</para>
    /// <para>04 终端扫码</para>
    /// <para>05 终端扫脸</para>
    /// <para>06 电子社会保障卡</para>
    /// </summary>
    public string p_sfrzfs { get; set; }

    /// <summary>
    /// 门诊急诊转诊标志（1急诊，2转诊，3转诊合并急诊）
    /// </summary>
    public string p_mzjzzzbz { get; set; }

    /// <summary>
    /// 外伤标志（1:是，0:否；异地门诊外伤就医时必传）
    /// </summary>
    public string p_wsbz { get; set; }

    /// <summary>
    /// 涉及第三方标志（1:是，0:否；异地门诊外伤标志为1时必传）
    /// </summary>
    public string p_sjdsfbz { get; set; }

    /// <summary>
    /// 医疗统筹类别明细（1102:新冠门诊，其他传空）
    /// </summary>
    public string p_yltclbmx { get; set; }

    /// <summary>
    /// 门诊结算方式（德州使用）
    /// </summary>
    public string p_mzjsfs { get; set; }

    /// <summary>
    /// 意外伤害医保负担比例（德州使用，p_mzjsfs="ywsh" 时必传）
    /// </summary>
    public string p_ywshybfdbl { get; set; }

    /// <summary>
    /// 消费账户标志（1:是，0:否；默认“1”，异地就医使用）
    /// </summary>
    public string p_xfzhbz { get; set; }

    /// <summary>
    /// *就诊科室编码（2024-07-22 起门诊结算必传）
    /// </summary>
    public string p_ksbm { get; set; }

    /// <summary>
    /// 多诊断信息（多个诊断时传入）
    /// </summary>
    public List<SettleMzPreJbbm> p_jbbm_ds { get; set; }

    /// <summary>
    /// 刷脸授权码（医保三类终端刷脸返回的 authNo）
    /// </summary>
    public string p_authno { get; set; }

    /// <summary>
    /// *费用凭单信息
    /// </summary>
    public List<SettleMzPreFypd> p_fypd_ds { get; set; }
}

/// <summary>
/// 补充项目信息
/// </summary>
public class SettleMzPreBcxm
{
    public string bcxmbh { get; set; } // 补充项目编号
    public string bcxmz { get; set; }  // 补充项目值
}

/// <summary>
/// 多诊断信息
/// </summary>
public class SettleMzPreJbbm
{
    public string dzdjbbm { get; set; } // 诊断疾病编码
    /// <summary>
    /// *主诊断标志	0：否，1：是
    /// </summary>
    public string  maindiag_flag { get; set; }
    /// <summary>
    /// *诊断类别	1：西医诊断，2：中医主病诊断，3：中医主证诊断，可调用数据字典接口获取，代码编号：DIAG_TYPE
    /// </summary>
    public string      diag_type { get; set; }
    /// <summary>
    /// *诊断顺序号
    /// </summary>
    public int   diag_srt_no { get; set; }
    /// <summary>
    /// diag_dept
    /// </summary>
    public string      diag_dept { get; set; }
    /// <summary>
    /// *诊断医师编码	需传入国标医师编码
    /// </summary>
    public string  diag_dr_code { get; set; }
    /// <summary>
    /// 诊断医师姓名
    /// </summary>
    public string      diag_dr_name { get; set; }
    /// <summary>
    /// *诊断时间
    /// </summary>
    public DateTime   diag_time { get; set; }
}

/// <summary>
/// 费用凭单信息
/// </summary>
public class SettleMzPreFypd
{
    public string yyxmbm { get; set; }   // *医院项目编码
    public string yyxmmc { get; set; }    // 医院项目名称
    public double dj { get; set; }        // *单价
    public double zje { get; set; }       // *总金额（zje=dj*sl）
    public double sl { get; set; }        // *数量
    public string zxksbm { get; set; }    // *执行科室编码
    public string kdksbm { get; set; }    // *开单科室编码
    public double sxzfbl { get; set; }    // *自付比例
    public string fyfssj { get; set; }    // *费用发生时间

    public string gg { get; set; }        // 规格
    public double? bzsl { get; set; }     // 大包装的小包装数量
    public int? yyts { get; set; }        // 用药天数
    public string yysm { get; set; }      // 用药说明
    public string yzlsh { get; set; }     // 医嘱流水号
    public string sfryxm { get; set; }    // 收费人员姓名
    public string gytj { get; set; }      // 给药途径
    public double? dcyl { get; set; }     // 单次用量
    public string yypc { get; set; }      // 用药频次
    public string wpbz { get; set; }      // 外配标志（德州：1是0否）
    public string zxysbm { get; set; }    // 执行医师编码
    public string kdysbm { get; set; }    // 开单医师编码
    public string zsm { get; set; }       // 追溯码（多个用,分隔）
    public string clbz { get; set; }      // 拆零标志（0否1是）
}