--处置表
CREATE Table medical_tech.dispose (
    --主键Id
    id bigserial primary key,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    --就诊Id
    register_id bigint NOT NULL,
    --就诊流水号
    visit_no varchar(64) NOT NULL,
    --患者Id
    patient_id bigint NOT NULL,
    --患者姓名
    patient_name varchar(64) NOT NULL,
    --项目Id
    item_id bigint,
    --项目编码
    item_code varchar(64),
    --项目名称
    item_name varchar(64),
    --规格
    spec varchar(64),
    --单位
    unit varchar(64),
    --生产厂商
    manufacturer varchar(256),
    --型号
    model varchar(100),
    --单价
    price numeric(16, 4) DEFAULT 0,
    --数量
    quantity numeric(16, 4) DEFAULT 0,
    --金额
    amount numeric(16, 4) DEFAULT 0,
    --频次Id
    frequency_id bigint,
    --频次名称
    frequency_name varchar(100),
    --天数
    days integer DEFAULT 0,
    --国家医保编码
    medicine_code varchar(100),
    --国标编码
    nationalstandard_code varchar(100),
    --状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    status integer DEFAULT 1,
    --备注
    remark varchar(256),
    --是否套餐
    is_package integer DEFAULT 2,
    --门诊住院标识 0门诊 1住院
    flag integer DEFAULT 0,
    --开单时间
    billing_time timestamp,
    --开单科室Id
    billing_dept_id bigint,
    --开单科室名称
    billing_dept_name varchar(64),
    --开单医生Id
    billing_doctor_id bigint,
    --开单医生名称
    billing_doctor_name varchar(64),
    --执行时间
    execute_time timestamp,
    --执行科室Id
    execute_dept_id bigint,
    --执行科室名称
    execute_dept_name varchar(64),
    --执行科室地址
    execute_dept_address varchar(100),
    --执行医生Id
    execute_doctor_id bigint,
    --执行医生名称
    execute_doctor_name varchar(64),
    --收费人员Id
    charge_staff_id bigint,
    --收费人员名称
    charge_staff_name varchar(64),
    --收费时间
    charge_time timestamp,
    --自付比例
    self_pay_ratio numeric(4, 4),
    --自付比例是否审核 1审核 2不审核
    is_ratio_audit integer,
    --自付比例审核时间
    ratio_audit_time timestamp,
    --自付比例审核人员Id
    ratio_audit_staff_id bigint,
    --自付比例审核人员名称
    ratio_audit_staff_name varchar(64),
    --医生签名
    doctor_sign text,
    --医嘱Id
    medical_advice_id bigint,
    --处方Id
    presc_id bigint,
    --收费类别Id
    charge_category_id bigint,
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);
--表注释
COMMENT ON TABLE medical_tech.dispose IS '处置表';
--字段注释
COMMENT ON COLUMN medical_tech.dispose.id IS '主键Id';

COMMENT ON COLUMN medical_tech.dispose.apply_no IS '申请单号';

COMMENT ON COLUMN medical_tech.dispose.register_id IS '就诊Id';

COMMENT ON COLUMN medical_tech.dispose.visit_no IS '就诊流水号';

COMMENT ON COLUMN medical_tech.dispose.patient_id IS '患者Id';

COMMENT ON COLUMN medical_tech.dispose.patient_name IS '患者姓名';

COMMENT ON COLUMN medical_tech.dispose.item_id IS '项目Id';

COMMENT ON COLUMN medical_tech.dispose.item_code IS '项目编码';

COMMENT ON COLUMN medical_tech.dispose.item_name IS '项目名称';

COMMENT ON COLUMN medical_tech.dispose.spec IS '规格';

COMMENT ON COLUMN medical_tech.dispose.unit IS '单位';

COMMENT ON COLUMN medical_tech.dispose.manufacturer IS '生产厂商';

COMMENT ON COLUMN medical_tech.dispose.model IS '型号';

COMMENT ON COLUMN medical_tech.dispose.price IS '单价';

COMMENT ON COLUMN medical_tech.dispose.quantity IS '数量';

COMMENT ON COLUMN medical_tech.dispose.amount IS '金额';

COMMENT ON COLUMN medical_tech.dispose.frequency_id IS '频次Id';

COMMENT ON COLUMN medical_tech.dispose.frequency_name IS '频次名称';

COMMENT ON COLUMN medical_tech.dispose.days IS '天数';

COMMENT ON COLUMN medical_tech.dispose.medicine_code IS '国家医保编码';

COMMENT ON COLUMN medical_tech.dispose.nationalstandard_code IS '国标编码';

COMMENT ON COLUMN medical_tech.dispose.status IS '状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费';

COMMENT ON COLUMN medical_tech.dispose.remark IS '备注';

COMMENT ON COLUMN medical_tech.dispose.is_package IS '是否套餐';

COMMENT ON COLUMN medical_tech.dispose.flag IS '门诊住院标识 0门诊 1住院';

COMMENT ON COLUMN medical_tech.dispose.billing_time IS '开单时间';

COMMENT ON COLUMN medical_tech.dispose.billing_dept_id IS '开单科室Id';

COMMENT ON COLUMN medical_tech.dispose.billing_dept_name IS '开单科室名称';

COMMENT ON COLUMN medical_tech.dispose.billing_doctor_id IS '开单医生Id';

COMMENT ON COLUMN medical_tech.dispose.billing_doctor_name IS '开单医生名称';

COMMENT ON COLUMN medical_tech.dispose.execute_time IS '执行时间';

COMMENT ON COLUMN medical_tech.dispose.execute_dept_id IS '执行科室Id';

COMMENT ON COLUMN medical_tech.dispose.execute_dept_name IS '执行科室名称';

COMMENT ON COLUMN medical_tech.dispose.execute_dept_address IS '执行科室地址';

COMMENT ON COLUMN medical_tech.dispose.execute_doctor_id IS '执行医生Id';

COMMENT ON COLUMN medical_tech.dispose.execute_doctor_name IS '执行医生名称';

COMMENT ON COLUMN medical_tech.dispose.charge_staff_id IS '收费人员Id';

COMMENT ON COLUMN medical_tech.dispose.charge_staff_name IS '收费人员名称';

COMMENT ON COLUMN medical_tech.dispose.charge_time IS '收费时间';

COMMENT ON COLUMN medical_tech.dispose.self_pay_ratio IS '自付比例';

COMMENT ON COLUMN medical_tech.dispose.is_ratio_audit IS '自付比例是否审核 1审核 2不审核';

COMMENT ON COLUMN medical_tech.dispose.ratio_audit_time IS '自付比例审核时间';

COMMENT ON COLUMN medical_tech.dispose.ratio_audit_staff_id IS '自付比例审核人员Id';

COMMENT ON COLUMN medical_tech.dispose.ratio_audit_staff_name IS '自付比例审核人员名称';

COMMENT ON COLUMN medical_tech.dispose.doctor_sign IS '医生签名';

COMMENT ON COLUMN medical_tech.dispose.medical_advice_id IS '医嘱Id';

COMMENT ON COLUMN medical_tech.dispose.presc_id IS '处方Id';

COMMENT ON COLUMN medical_tech.dispose.charge_category_id IS '收费类别Id';

COMMENT ON COLUMN medical_tech.dispose.create_org_id IS '创建者部门Id';

COMMENT ON COLUMN medical_tech.dispose.create_org_name IS '创建者部门名称';

COMMENT ON COLUMN medical_tech.dispose.create_time IS '创建时间';

COMMENT ON COLUMN medical_tech.dispose.create_user_id IS '创建者Id';

COMMENT ON COLUMN medical_tech.dispose.create_user_name IS '创建者名称';

COMMENT ON COLUMN medical_tech.dispose.update_time IS '更新时间';

COMMENT ON COLUMN medical_tech.dispose.update_user_id IS '修改者Id';

COMMENT ON COLUMN medical_tech.dispose.update_user_name IS '修改者名称';

COMMENT ON COLUMN medical_tech.dispose.is_delete IS '软删除';

COMMENT ON COLUMN medical_tech.dispose.tenant_id IS '租户Id';

--处置套餐项目表
create table medical_tech.dispose_package_item (
    -- 主键Id
    id bigserial primary key,
    -- 处置Id
    dispose_id bigint NOT NULL,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    --项目Id
    item_id bigint,
    --项目编码
    item_code varchar(64),
    --项目名称
    item_name varchar(64),
    --规格
    spec varchar(64),
    --单位
    unit varchar(64),
    --生产厂商
    manufacturer varchar(256),
    --型号
    model varchar(100),
    --单价
    price numeric(16, 4) DEFAULT 0,
    --数量
    quantity numeric(16, 4) DEFAULT 0,
    --金额
    amount numeric(16, 4) DEFAULT 0,
    --收费类别Id
    charge_category_id bigint,
    --自付比例
    self_pay_ratio numeric(4, 4),
    --自付比例是否审核 1审核 2不审核
    is_ratio_audit integer,
    --自付比例审核时间
    ratio_audit_time timestamp,
    --自付比例审核人员Id
    ratio_audit_staff_id bigint,
    --自付比例审核人员名称
    ratio_audit_staff_name varchar(64),
    --备注
    remark varchar(256),
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);
--表注释
COMMENT ON TABLE medical_tech.dispose_package_item IS '处置套餐项目表';
--字段注释
COMMENT ON COLUMN medical_tech.dispose_package_item.id IS '主键Id';

COMMENT ON COLUMN medical_tech.dispose_package_item.dispose_id IS '处置Id';

COMMENT ON COLUMN medical_tech.dispose_package_item.apply_no IS '申请单号';

COMMENT ON COLUMN medical_tech.dispose_package_item.item_id IS '项目Id';

COMMENT ON COLUMN medical_tech.dispose_package_item.item_code IS '项目编码';

COMMENT ON COLUMN medical_tech.dispose_package_item.item_name IS '项目名称';

COMMENT ON COLUMN medical_tech.dispose_package_item.spec IS '规格';

COMMENT ON COLUMN medical_tech.dispose_package_item.unit IS '单位';

COMMENT ON COLUMN medical_tech.dispose_package_item.manufacturer IS '生产厂商';

COMMENT ON COLUMN medical_tech.dispose_package_item.model IS '型号';

COMMENT ON COLUMN medical_tech.dispose_package_item.price IS '单价';

COMMENT ON COLUMN medical_tech.dispose_package_item.quantity IS '数量';

COMMENT ON COLUMN medical_tech.dispose_package_item.amount IS '金额';

COMMENT ON COLUMN medical_tech.dispose_package_item.charge_category_id IS '收费类别Id';

COMMENT ON COLUMN medical_tech.dispose_package_item.self_pay_ratio IS '自付比例';

COMMENT ON COLUMN medical_tech.dispose_package_item.is_ratio_audit IS '自付比例是否审核 1审核 2不审核';

COMMENT ON COLUMN medical_tech.dispose_package_item.ratio_audit_time IS '自付比例审核时间';

COMMENT ON COLUMN medical_tech.dispose_package_item.ratio_audit_staff_id IS '自付比例审核人员Id';

COMMENT ON COLUMN medical_tech.dispose_package_item.ratio_audit_staff_name IS '自付比例审核人员名称';

COMMENT ON COLUMN medical_tech.dispose_package_item.remark IS '备注';

COMMENT ON COLUMN medical_tech.dispose_package_item.create_org_id IS '创建者部门Id';

COMMENT ON COLUMN medical_tech.dispose_package_item.create_org_name IS '创建者部门名称';

COMMENT ON COLUMN medical_tech.dispose_package_item.create_time IS '创建时间';

COMMENT ON COLUMN medical_tech.dispose_package_item.create_user_id IS '创建者Id';

COMMENT ON COLUMN medical_tech.dispose_package_item.create_user_name IS '创建者名称';

COMMENT ON COLUMN medical_tech.dispose_package_item.update_time IS '更新时间';

COMMENT ON COLUMN medical_tech.dispose_package_item.update_user_id IS '修改者Id';

COMMENT ON COLUMN medical_tech.dispose_package_item.update_user_name IS '修改者名称';

COMMENT ON COLUMN medical_tech.dispose_package_item.is_delete IS '软删除';

COMMENT ON COLUMN medical_tech.dispose_package_item.tenant_id IS '租户Id';

--检验表
create table medical_tech.lab_test (
    --主键Id
    id bigserial primary key,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    --就诊Id
    register_id bigint NOT NULL,
    --就诊流水号
    visit_no varchar(64) NOT NULL,
    --患者Id
    patient_id bigint NOT NULL,
    --患者姓名
    patient_name varchar(64) NOT NULL,
    --项目Id
    item_id bigint,
    --项目编码
    item_code varchar(64),
    --项目名称
    item_name varchar(64),
    --单位
    unit varchar(64),
    --单价
    price numeric(16, 4) DEFAULT 0,
    --数量
    quantity numeric(16, 4) DEFAULT 0,
    --金额
    amount numeric(16, 4) DEFAULT 0,
    --样本类型
    sample_type varchar(100),
    --紧急程度 0:普通,1:急,2:明晨急
    urgency_level varchar(32) DEFAULT 0,
    --国家医保编码
    medicine_code varchar(100),
    --国标编码
    nationalstandard_code varchar(100),
    --状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    status integer DEFAULT 1,
    --备注
    remark varchar(256),
    --是否套餐
    is_package integer DEFAULT 2,
    --门诊住院标识 0门诊 1住院
    flag integer DEFAULT 0,
    --开单时间
    billing_time timestamp,
    --开单科室Id
    billing_dept_id bigint,
    --开单科室名称
    billing_dept_name varchar(64),
    --开单医生Id
    billing_doctor_id bigint,
    --开单医生名称
    billing_doctor_name varchar(64),
    --执行时间
    execute_time timestamp,
    --执行科室Id
    execute_dept_id bigint,
    --执行科室名称
    execute_dept_name varchar(64),
    --执行科室地址
    execute_dept_address varchar(100),
    --执行医生Id
    execute_doctor_id bigint,
    --执行医生名称
    execute_doctor_name varchar(64),
    --收费人员Id
    charge_staff_id bigint,
    --收费人员名称
    charge_staff_name varchar(64),
    --收费时间
    charge_time timestamp,
    --自付比例
    self_pay_ratio numeric(4, 4),
    --自付比例是否审核 1审核 2不审核
    is_ratio_audit integer,
    --自付比例审核时间
    ratio_audit_time timestamp,
    --自付比例审核人员Id
    ratio_audit_staff_id bigint,
    --自付比例审核人员名称
    ratio_audit_staff_name varchar(64),
    --医生签名
    doctor_sign text,
    --医嘱Id
    medical_advice_id bigint,
    --收费类别Id
    charge_category_id bigint,
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);
--表注释
COMMENT ON TABLE medical_tech.lab_test IS '检验表';
--字段注释
COMMENT ON COLUMN medical_tech.lab_test.id IS '主键Id';

COMMENT ON COLUMN medical_tech.lab_test.apply_no IS '申请单号';

COMMENT ON COLUMN medical_tech.lab_test.register_id IS '就诊Id';

COMMENT ON COLUMN medical_tech.lab_test.visit_no IS '就诊流水号';

COMMENT ON COLUMN medical_tech.lab_test.patient_id IS '患者Id';

COMMENT ON COLUMN medical_tech.lab_test.patient_name IS '患者姓名';

COMMENT ON COLUMN medical_tech.lab_test.item_id IS '项目Id';

COMMENT ON COLUMN medical_tech.lab_test.item_code IS '项目编码';

COMMENT ON COLUMN medical_tech.lab_test.item_name IS '项目名称';

COMMENT ON COLUMN medical_tech.lab_test.unit IS '单位';

COMMENT ON COLUMN medical_tech.lab_test.price IS '单价';

COMMENT ON COLUMN medical_tech.lab_test.quantity IS '数量';

COMMENT ON COLUMN medical_tech.lab_test.amount IS '金额';

COMMENT ON COLUMN medical_tech.lab_test.sample_name IS '样本类型';

COMMENT ON COLUMN medical_tech.lab_test.urgency_level IS '紧急程度 0:普通,1:急,2:明晨急';

COMMENT ON COLUMN medical_tech.lab_test.medicine_code IS '国家医保编码';

COMMENT ON COLUMN medical_tech.lab_test.nationalstandard_code IS '国标编码';

COMMENT ON COLUMN medical_tech.lab_test.status IS '状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费';

COMMENT ON COLUMN medical_tech.lab_test.remark IS '备注';

COMMENT ON COLUMN medical_tech.lab_test.is_package IS '是否套餐';

COMMENT ON COLUMN medical_tech.lab_test.flag IS '门诊住院标识 0门诊 1住院';

COMMENT ON COLUMN medical_tech.lab_test.billing_time IS '开单时间';

COMMENT ON COLUMN medical_tech.lab_test.billing_dept_id IS '开单科室Id';

COMMENT ON COLUMN medical_tech.lab_test.billing_dept_name IS '开单科室名称';

COMMENT ON COLUMN medical_tech.lab_test.billing_doctor_id IS '开单医生Id';

COMMENT ON COLUMN medical_tech.lab_test.billing_doctor_name IS '开单医生名称';

COMMENT ON COLUMN medical_tech.lab_test.execute_time IS '执行时间';

COMMENT ON COLUMN medical_tech.lab_test.execute_dept_id IS '执行科室Id';

COMMENT ON COLUMN medical_tech.lab_test.execute_dept_name IS '执行科室名称';

COMMENT ON COLUMN medical_tech.lab_test.execute_dept_address IS '执行科室地址';

COMMENT ON COLUMN medical_tech.lab_test.execute_doctor_id IS '执行医生Id';

COMMENT ON COLUMN medical_tech.lab_test.execute_doctor_name IS '执行医生名称';

COMMENT ON COLUMN medical_tech.lab_test.charge_staff_id IS '收费人员Id';

COMMENT ON COLUMN medical_tech.lab_test.charge_staff_name IS '收费人员名称';

COMMENT ON COLUMN medical_tech.lab_test.charge_time IS '收费时间';

COMMENT ON COLUMN medical_tech.lab_test.self_pay_ratio IS '自付比例';

COMMENT ON COLUMN medical_tech.lab_test.is_ratio_audit IS '自付比例是否审核 1审核 2不审核';

COMMENT ON COLUMN medical_tech.lab_test.ratio_audit_time IS '自付比例审核时间';

COMMENT ON COLUMN medical_tech.lab_test.ratio_audit_staff_id IS '自付比例审核人员Id';

COMMENT ON COLUMN medical_tech.lab_test.ratio_audit_staff_name IS '自付比例审核人员名称';

COMMENT ON COLUMN medical_tech.lab_test.doctor_sign IS '医生签名';

COMMENT ON COLUMN medical_tech.lab_test.medical_advice_id IS '医嘱Id';

COMMENT ON COLUMN medical_tech.lab_test.charge_category_id IS '收费类别Id';

COMMENT ON COLUMN medical_tech.lab_test.create_org_id IS '创建者部门Id';

COMMENT ON COLUMN medical_tech.lab_test.create_org_name IS '创建者部门名称';

COMMENT ON COLUMN medical_tech.lab_test.create_time IS '创建时间';

COMMENT ON COLUMN medical_tech.lab_test.create_user_id IS '创建者Id';

COMMENT ON COLUMN medical_tech.lab_test.create_user_name IS '创建者名称';

COMMENT ON COLUMN medical_tech.lab_test.update_time IS '更新时间';

COMMENT ON COLUMN medical_tech.lab_test.update_user_id IS '修改者Id';

COMMENT ON COLUMN medical_tech.lab_test.update_user_name IS '修改者名称';

COMMENT ON COLUMN medical_tech.lab_test.is_delete IS '软删除';

COMMENT ON COLUMN medical_tech.lab_test.tenant_id IS '租户Id';

--检验套餐项目表
CREATE Table medical_tech.lab_test_package_item (
    -- 主键Id
    id bigserial primary key,
    --检验Id
    lab_test_id bigint NOT NULL,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    -- 项目Id
    item_id bigint,
    -- 项目编码
    item_code varchar(64),
    -- 项目名称
    item_name varchar(64),
    --单位
    unit varchar(64),
    --单价
    price numeric(16, 4) DEFAULT 0,
    --数量
    quantity numeric(16, 4) DEFAULT 0,
    --金额
    amount numeric(16, 4) DEFAULT 0,
    --收费类别Id
    charge_category_id bigint,
    --自付比例
    self_pay_ratio numeric(4, 4),
    --自付比例是否审核 1审核 2不审核
    is_ratio_audit integer,
    --自付比例审核时间
    ratio_audit_time timestamp,
    --自付比例审核人员Id
    ratio_audit_staff_id bigint,
    --自付比例审核人员名称
    ratio_audit_staff_name varchar(64),
    --备注
    remark varchar(256),
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);
--表注释
COMMENT ON TABLE medical_tech.lab_test_package_item IS '检验套餐项目表';

--字段注释
COMMENT ON COLUMN medical_tech.lab_test_package_item.id IS '主键Id';

COMMENT ON COLUMN medical_tech.lab_test_package_item.lab_test_id IS '检验Id';

COMMENT ON COLUMN medical_tech.lab_test_package_item.apply_no IS '申请单号';

COMMENT ON COLUMN medical_tech.lab_test_package_item.item_id IS '项目Id';

COMMENT ON COLUMN medical_tech.lab_test_package_item.item_code IS '项目编码';

COMMENT ON COLUMN medical_tech.lab_test_package_item.item_name IS '项目名称';

COMMENT ON COLUMN medical_tech.lab_test_package_item.unit IS '单位';

COMMENT ON COLUMN medical_tech.lab_test_package_item.price IS '单价';

COMMENT ON COLUMN medical_tech.lab_test_package_item.quantity IS '数量';

COMMENT ON COLUMN medical_tech.lab_test_package_item.amount IS '金额';

COMMENT ON COLUMN medical_tech.lab_test_package_item.charge_category_id IS '收费类别Id';

COMMENT ON COLUMN medical_tech.lab_test_package_item.self_pay_ratio IS '自付比例';

COMMENT ON COLUMN medical_tech.lab_test_package_item.is_ratio_audit IS '自付比例是否审核 1审核 2不审核';

COMMENT ON COLUMN medical_tech.lab_test_package_item.ratio_audit_time IS '自付比例审核时间';

COMMENT ON COLUMN medical_tech.lab_test_package_item.ratio_audit_staff_id IS '自付比例审核人员Id';

COMMENT ON COLUMN medical_tech.lab_test_package_item.ratio_audit_staff_name IS '自付比例审核人员名称';

COMMENT ON COLUMN medical_tech.lab_test_package_item.remark IS '备注';

COMMENT ON COLUMN medical_tech.lab_test_package_item.create_org_id IS '创建者部门Id';

COMMENT ON COLUMN medical_tech.lab_test_package_item.create_org_name IS '创建者部门名称';

COMMENT ON COLUMN medical_tech.lab_test_package_item.create_time IS '创建时间';

COMMENT ON COLUMN medical_tech.lab_test_package_item.create_user_id IS '创建者Id';

COMMENT ON COLUMN medical_tech.lab_test_package_item.create_user_name IS '创建者名称';

COMMENT ON COLUMN medical_tech.lab_test_package_item.update_time IS '更新时间';

COMMENT ON COLUMN medical_tech.lab_test_package_item.update_user_id IS '修改者Id';

COMMENT ON COLUMN medical_tech.lab_test_package_item.update_user_name IS '修改者名称';

COMMENT ON COLUMN medical_tech.lab_test_package_item.is_delete IS '软删除';

COMMENT ON COLUMN medical_tech.lab_test_package_item.tenant_id IS '租户Id';

--检查表
CREATE Table medical_tech.examination (
    --主键Id
    id bigserial primary key,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    --就诊Id
    register_id bigint NOT NULL,
    --就诊流水号
    visit_no varchar(64) NOT NULL,
    --患者Id
    patient_id bigint NOT NULL,
    --患者姓名
    patient_name varchar(64) NOT NULL,
    --检查类别Id
    check_category_id bigint,
    --检查类别名称
    check_category_name varchar(64),
    --检查部位Id
    check_point_id bigint,
    --检查部位名称
    check_point_name varchar(64),
    --检查目的
    check_objective varchar(200),
    --临床诊断
    clinical_diagnosis varchar(200),
    --病历摘要
    medical_record_summary varchar(500),
    --状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    status integer DEFAULT 1,
    --备注
    remark varchar(256),
    --门诊住院标识 0门诊 1住院
    flag integer DEFAULT 0,
    --开单时间
    billing_time timestamp,
    --开单科室Id
    billing_dept_id bigint,
    --开单科室名称
    billing_dept_name varchar(64),
    --开单医生Id
    billing_doctor_id bigint,
    --开单医生名称
    billing_doctor_name varchar(64),
    --执行时间
    execute_time timestamp,
    --执行科室Id
    execute_dept_id bigint,
    --执行科室名称
    execute_dept_name varchar(64),
    --执行科室地址
    execute_dept_address varchar(100),
    --执行医生Id
    execute_doctor_id bigint,
    --执行医生名称
    execute_doctor_name varchar(64),
    --收费人员Id
    charge_staff_id bigint,
    --收费人员名称
    charge_staff_name varchar(64),
    --收费时间
    charge_time timestamp,
    --医生签名
    doctor_sign text,
    --医嘱Id
    medical_advice_id bigint,
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);
--表注释
COMMENT ON TABLE medical_tech.examination IS '检查表';
--字段注释
COMMENT ON COLUMN medical_tech.examination.id IS '主键Id';

COMMENT ON COLUMN medical_tech.examination.apply_no IS '申请单号';

COMMENT ON COLUMN medical_tech.examination.register_id IS '就诊Id';

COMMENT ON COLUMN medical_tech.examination.visit_no IS '就诊流水号';

COMMENT ON COLUMN medical_tech.examination.patient_id IS '患者Id';

COMMENT ON COLUMN medical_tech.examination.patient_name IS '患者姓名';

COMMENT ON COLUMN medical_tech.examination.check_category_id IS '检查类别Id';

COMMENT ON COLUMN medical_tech.examination.check_category_name IS '检查类别名称';

COMMENT ON COLUMN medical_tech.examination.check_point_id IS '检查部位Id';

COMMENT ON COLUMN medical_tech.examination.check_point_name IS '检查部位名称';

COMMENT ON COLUMN medical_tech.examination.check_objective IS '检查目的';

COMMENT ON COLUMN medical_tech.examination.clinical_diagnosis IS '临床诊断';

COMMENT ON COLUMN medical_tech.examination.medical_record_summary IS '病历摘要';

COMMENT ON COLUMN medical_tech.examination.status IS '状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费';

COMMENT ON COLUMN medical_tech.examination.remark IS '备注';

COMMENT ON COLUMN medical_tech.examination.flag IS '门诊住院标识 0门诊 1住院';

COMMENT ON COLUMN medical_tech.examination.billing_time IS '开单时间';

COMMENT ON COLUMN medical_tech.examination.billing_dept_id IS '开单科室Id';

COMMENT ON COLUMN medical_tech.examination.billing_dept_name IS '开单科室名称';

COMMENT ON COLUMN medical_tech.examination.billing_doctor_id IS '开单医生Id';

COMMENT ON COLUMN medical_tech.examination.billing_doctor_name IS '开单医生名称';

COMMENT ON COLUMN medical_tech.examination.execute_time IS '执行时间';

COMMENT ON COLUMN medical_tech.examination.execute_dept_id IS '执行科室Id';

COMMENT ON COLUMN medical_tech.examination.execute_dept_name IS '执行科室名称';

COMMENT ON COLUMN medical_tech.examination.execute_dept_address IS '执行科室地址';

COMMENT ON COLUMN medical_tech.examination.execute_doctor_id IS '执行医生Id';

COMMENT ON COLUMN medical_tech.examination.execute_doctor_name IS '执行医生名称';

COMMENT ON COLUMN medical_tech.examination.charge_staff_id IS '收费人员Id';

COMMENT ON COLUMN medical_tech.examination.charge_staff_name IS '收费人员名称';

COMMENT ON COLUMN medical_tech.examination.charge_time IS '收费时间';

COMMENT ON COLUMN medical_tech.examination.doctor_sign IS '医生签名';

COMMENT ON COLUMN medical_tech.examination.medical_advice_id IS '医嘱Id';

COMMENT ON COLUMN medical_tech.examination.create_org_id IS '创建者部门Id';

COMMENT ON COLUMN medical_tech.examination.create_org_name IS '创建者部门名称';

COMMENT ON COLUMN medical_tech.examination.create_time IS '创建时间';

COMMENT ON COLUMN medical_tech.examination.create_user_id IS '创建者Id';

COMMENT ON COLUMN medical_tech.examination.create_user_name IS '创建者名称';

COMMENT ON COLUMN medical_tech.examination.update_time IS '更新时间';

COMMENT ON COLUMN medical_tech.examination.update_user_id IS '修改者Id';

COMMENT ON COLUMN medical_tech.examination.update_user_name IS '修改者名称';

COMMENT ON COLUMN medical_tech.examination.is_delete IS '软删除';

COMMENT ON COLUMN medical_tech.examination.tenant_id IS '租户Id';

--检查明细表
CREATE Table medical_tech.examination_details (
    -- 主键Id
    id bigserial primary key,
    -- 检查Id
    examination_id bigint NOT NULL,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    -- 项目Id
    item_id bigint,
    -- 项目编码
    item_code varchar(64),
    -- 项目名称
    item_name varchar(64),
    --单位
    unit varchar(64),
    --单价
    price numeric(16, 4) DEFAULT 0,
    --数量
    quantity numeric(16, 4) DEFAULT 0,
    --金额
    amount numeric(16, 4) DEFAULT 0,
    --是否套餐
    is_package integer DEFAULT 2,
    --国家医保编码
    medicine_code varchar(100),
    --国标编码
    nationalstandard_code varchar(100),
    --收费类别Id
    charge_category_id bigint,
    --自付比例
    self_pay_ratio numeric(4, 4),
    --自付比例是否审核 1审核 2不审核
    is_ratio_audit integer,
    --自付比例审核时间
    ratio_audit_time timestamp,
    --自付比例审核人员Id
    ratio_audit_staff_id bigint,
    --自付比例审核人员名称
    ratio_audit_staff_name varchar(64),
    --备注
    remark varchar(256),
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);

--表注释
COMMENT ON TABLE medical_tech.examination_details IS '检查明细表';

--字段注释
COMMENT ON COLUMN medical_tech.examination_details.id IS '主键Id';

COMMENT ON COLUMN medical_tech.examination_details.examination_id IS '检查Id';

COMMENT ON COLUMN medical_tech.examination_details.apply_no IS '申请单号';

COMMENT ON COLUMN medical_tech.examination_details.item_id IS '项目Id';

COMMENT ON COLUMN medical_tech.examination_details.item_code IS '项目编码';

COMMENT ON COLUMN medical_tech.examination_details.item_name IS '项目名称';

COMMENT ON COLUMN medical_tech.examination_details.unit IS '单位';

COMMENT ON COLUMN medical_tech.examination_details.price IS '单价';

COMMENT ON COLUMN medical_tech.examination_details.quantity IS '数量';

COMMENT ON COLUMN medical_tech.examination_details.amount IS '金额';

COMMENT ON COLUMN medical_tech.examination_details.is_package IS '是否套餐';

COMMENT ON COLUMN medical_tech.examination_details.medicine_code IS '国家医保编码';

COMMENT ON COLUMN medical_tech.examination_details.nationalstandard_code IS '国标编码';

COMMENT ON COLUMN medical_tech.examination_details.charge_category_id IS '收费类别Id';

COMMENT ON COLUMN medical_tech.examination_details.self_pay_ratio IS '自付比例';

COMMENT ON COLUMN medical_tech.examination_details.is_ratio_audit IS '自付比例是否审核 1审核 2不审核';

COMMENT ON COLUMN medical_tech.examination_details.ratio_audit_time IS '自付比例审核时间';

COMMENT ON COLUMN medical_tech.examination_details.ratio_audit_staff_id IS '自付比例审核人员Id';

COMMENT ON COLUMN medical_tech.examination_details.ratio_audit_staff_name IS '自付比例审核人员名称';

COMMENT ON COLUMN medical_tech.examination_details.remark IS '备注';

COMMENT ON COLUMN medical_tech.examination_details.create_org_id IS '创建者部门Id';

COMMENT ON COLUMN medical_tech.examination_details.create_org_name IS '创建者部门名称';

COMMENT ON COLUMN medical_tech.examination_details.create_time IS '创建时间';

COMMENT ON COLUMN medical_tech.examination_details.create_user_id IS '创建者Id';

COMMENT ON COLUMN medical_tech.examination_details.create_user_name IS '创建者名称';

COMMENT ON COLUMN medical_tech.examination_details.update_time IS '更新时间';

COMMENT ON COLUMN medical_tech.examination_details.update_user_id IS '修改者Id';

COMMENT ON COLUMN medical_tech.examination_details.update_user_name IS '修改者名称';

COMMENT ON COLUMN medical_tech.examination_details.is_delete IS '软删除';

COMMENT ON COLUMN medical_tech.examination_details.tenant_id IS '租户Id';

--检查套餐项目表

CREATE TABLE medical_tech.examination_package_item (
    -- 主键Id
    id bigserial primary key,
    -- 检查Id
    examination_id bigint NOT NULL,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    --检查明细Id
    examination_details_id bigint NOT NULL,
    -- 项目Id
    item_id bigint,
    -- 项目编码
    item_code varchar(64),
    -- 项目名称
    item_name varchar(64),
    --单位
    unit varchar(64),
    --单价
    price numeric(16, 4) DEFAULT 0,
    --数量
    quantity numeric(16, 4) DEFAULT 0,
    --金额
    amount numeric(16, 4) DEFAULT 0,
    --收费类别Id
    charge_category_id bigint,
    --自付比例
    self_pay_ratio numeric(4, 4),
    --自付比例是否审核 1审核 2不审核
    is_ratio_audit integer,
    --自付比例审核时间
    ratio_audit_time timestamp,
    --自付比例审核人员Id
    ratio_audit_staff_id bigint,
    --自付比例审核人员名称
    ratio_audit_staff_name varchar(64),
    --备注
    remark varchar(256),
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);
--表注释
COMMENT ON TABLE medical_tech.examination_package_item IS '检查套餐项目表';

--字段注释
COMMENT ON COLUMN medical_tech.examination_package_item.id IS '主键Id';

COMMENT ON COLUMN medical_tech.examination_package_item.examination_id IS '检查Id';

COMMENT ON COLUMN medical_tech.examination_package_item.apply_no IS '申请单号';

COMMENT ON COLUMN medical_tech.examination_package_item.examination_details_id IS '检查明细Id';

COMMENT ON COLUMN medical_tech.examination_package_item.item_id IS '项目Id';

COMMENT ON COLUMN medical_tech.examination_package_item.item_code IS '项目编码';

COMMENT ON COLUMN medical_tech.examination_package_item.item_name IS '项目名称';

COMMENT ON COLUMN medical_tech.examination_package_item.unit IS '单位';

COMMENT ON COLUMN medical_tech.examination_package_item.price IS '单价';

COMMENT ON COLUMN medical_tech.examination_package_item.quantity IS '数量';

COMMENT ON COLUMN medical_tech.examination_package_item.amount IS '金额';

COMMENT ON COLUMN medical_tech.examination_package_item.charge_category_id IS '收费类别Id';

COMMENT ON COLUMN medical_tech.examination_package_item.self_pay_ratio IS '自付比例';

COMMENT ON COLUMN medical_tech.examination_package_item.is_ratio_audit IS '自付比例是否审核 1审核 2不审核';

COMMENT ON COLUMN medical_tech.examination_package_item.ratio_audit_time IS '自付比例审核时间';

COMMENT ON COLUMN medical_tech.examination_package_item.ratio_audit_staff_id IS '自付比例审核人员Id';

COMMENT ON COLUMN medical_tech.examination_package_item.ratio_audit_staff_name IS '自付比例审核人员名称';

COMMENT ON COLUMN medical_tech.examination_package_item.remark IS '备注';

COMMENT ON COLUMN medical_tech.examination_package_item.create_org_id IS '创建者部门Id';

COMMENT ON COLUMN medical_tech.examination_package_item.create_org_name IS '创建者部门名称';

COMMENT ON COLUMN medical_tech.examination_package_item.create_time IS '创建时间';

COMMENT ON COLUMN medical_tech.examination_package_item.create_user_id IS '创建者Id';

COMMENT ON COLUMN medical_tech.examination_package_item.create_user_name IS '创建者名称';

COMMENT ON COLUMN medical_tech.examination_package_item.update_time IS '更新时间';

COMMENT ON COLUMN medical_tech.examination_package_item.update_user_id IS '修改者Id';

COMMENT ON COLUMN medical_tech.examination_package_item.update_user_name IS '修改者名称';

COMMENT ON COLUMN medical_tech.examination_package_item.is_delete IS '软删除';

COMMENT ON COLUMN medical_tech.examination_package_item.tenant_id IS '租户Id';

--手术表
CREATE TABLE medical_tech.surgery (
    -- 主键Id
    id bigserial primary key,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    --就诊Id
    register_id bigint NOT NULL,
    --就诊流水号
    visit_no varchar(64) NOT NULL,
    --患者Id
    patient_id bigint NOT NULL,
    --患者姓名
    patient_name varchar(64) NOT NULL,
    --项目Id
    item_id bigint,
    --项目编码
    item_code varchar(64),
    --项目名称
    item_name varchar(64),
    --单位
    unit varchar(64),
    --单价
    price numeric(16, 4) DEFAULT 0,
    --数量
    quantity numeric(16, 4) DEFAULT 0,
    --金额
    amount numeric(16, 4) DEFAULT 0,
    --国家医保编码
    medicine_code varchar(100),
    --国标编码
    nationalstandard_code varchar(100),
    --是否套餐
    is_package integer DEFAULT 2,
    --门诊住院标识 0门诊 1住院
    flag integer DEFAULT 0,
    --备注
    remark varchar(256),
    --开单时间
    billing_time timestamp,
    --开单科室Id
    billing_dept_id bigint,
    --开单科室名称
    billing_dept_name varchar(64),
    --开单医生Id
    billing_doctor_id bigint,
    --开单医生名称
    billing_doctor_name varchar(64),
    --执行时间
    execute_time timestamp,
    --执行科室Id
    execute_dept_id bigint,
    --执行科室名称
    execute_dept_name varchar(64),
    --执行科室地址
    execute_dept_address varchar(100),
    --执行医生Id
    execute_doctor_id bigint,
    --执行医生名称
    execute_doctor_name varchar(64),
    --收费人员Id
    charge_staff_id bigint,
    --收费人员名称
    charge_staff_name varchar(64),
    --收费时间
    charge_time timestamp,
    --自付比例
    self_pay_ratio numeric(4, 4),
    --自付比例是否审核 1审核 2不审核
    is_ratio_audit integer,
    --自付比例审核时间
    ratio_audit_time timestamp,
    --自付比例审核人员Id
    ratio_audit_staff_id bigint,
    --自付比例审核人员名称
    ratio_audit_staff_name varchar(64),
    --医生签名
    doctor_sign text,
    --医嘱Id
    medical_advice_id bigint,
    --收费类别Id
    charge_category_id bigint,
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);

--会诊表
CREATE Table medical_tech.consultation (
    -- 主键Id
    id bigserial primary key,
    --申请单号
    apply_no varchar(64) NOT NULL,
    --就诊Id
    register_id bigint NOT NULL,
    --就诊流水号
    visit_no varchar(64) NOT NULL,
    --患者Id
    patient_id bigint NOT NULL,
    --患者姓名
    patient_name varchar(64) NOT NULL,
    --就诊时间
    visit_time timestamp NOT NULL,
    --性别
    sex varchar(32) DEFAULT 1,
    --年龄
    age integer DEFAULT 0,
    --年龄单位
    age_unit varchar(32) DEFAULT 0,
    --门诊住院标识 0门诊 1住院
    flag integer DEFAULT 0,
    --期望会诊时间
    expected_time timestamp,
    --会诊类型  字典 ConsultationType
    type varchar(32) DEFAULT 1,
    --会诊状态  字典 ConsultationStatus
    status varchar(32) DEFAULT 1,
    --病情摘要
    clinical_summary TEXT,
    --会诊目的
    purpose TEXT,
    --会诊意见
    consultation_opinion TEXT,
    --意见填写时间
    opinion_time timestamp,
    --意见填写人Id
    opinion_staff_id bigint,
    --意见填写人名称
    opinion_staff_name varchar(64),
    --意见填写人签名
    opinion_staff_sign text,
    --申请时间
    apply_time timestamp,
    --申请科室Id
    apply_dept_id bigint,
    --申请科室名称
    apply_dept_name varchar(64),
    --申请医生Id
    apply_doctor_id bigint,
    --申请医生名称
    apply_doctor_name varchar(64),
    --申请医生签名
    apply_doctor_sign text,
    --会诊科室Id
    consultation_dept_id bigint,
    --会诊科室名称
    consultation_dept_name varchar(64),
    --会诊医生Id
    consultation_doctor_id bigint,
    --会诊医生名称
    consultation_doctor_name varchar(64),
    --会诊接受时间
    consultation_accept_time timestamp,
    --会诊结束时间
    consultation_end_time timestamp,
    --院外会诊机构名称
    outside_hospital_name varchar(64),
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);
--表注释
COMMENT ON TABLE medical_tech.consultation IS '会诊表';

--字段注释
COMMENT ON COLUMN medical_tech.consultation.id IS '主键Id';

COMMENT ON COLUMN medical_tech.consultation.apply_no IS '申请单号';

COMMENT ON COLUMN medical_tech.consultation.register_id IS '就诊Id';

COMMENT ON COLUMN medical_tech.consultation.visit_no IS '就诊流水号';

COMMENT ON COLUMN medical_tech.consultation.patient_id IS '患者Id';

COMMENT ON COLUMN medical_tech.consultation.patient_name IS '患者姓名';

COMMENT ON COLUMN medical_tech.consultation.visit_time IS '就诊时间';

COMMENT ON COLUMN medical_tech.consultation.sex IS '性别';

COMMENT ON COLUMN medical_tech.consultation.age IS '年龄';

COMMENT ON COLUMN medical_tech.consultation.age_unit IS '年龄单位';

COMMENT ON COLUMN medical_tech.consultation.flag IS '门诊住院标识 0门诊 1住院';

COMMENT ON COLUMN medical_tech.consultation.expected_time IS '期望会诊时间';

COMMENT ON COLUMN medical_tech.consultation.type IS '会诊类型  字典 ConsultationType';

COMMENT ON COLUMN medical_tech.consultation.status IS '会诊状态  字典 ConsultationStatus';

COMMENT ON COLUMN medical_tech.consultation.clinical_summary IS '病情摘要';

COMMENT ON COLUMN medical_tech.consultation.purpose IS '会诊目的';

COMMENT ON COLUMN medical_tech.consultation.consultation_opinion IS '会诊意见';

COMMENT ON COLUMN medical_tech.consultation.opinion_time IS '意见填写时间';

COMMENT ON COLUMN medical_tech.consultation.opinion_staff_id IS '意见填写人Id';

COMMENT ON COLUMN medical_tech.consultation.opinion_staff_name IS '意见填写人名称';

COMMENT ON COLUMN medical_tech.consultation.opinion_staff_sign IS '意见填写人签名';

COMMENT ON COLUMN medical_tech.consultation.apply_time IS '申请时间';

COMMENT ON COLUMN medical_tech.consultation.apply_dept_id IS '申请科室Id';

COMMENT ON COLUMN medical_tech.consultation.apply_dept_name IS '申请科室名称';

COMMENT ON COLUMN medical_tech.consultation.apply_doctor_id IS '申请医生Id';

COMMENT ON COLUMN medical_tech.consultation.apply_doctor_name IS '申请医生名称';

COMMENT ON COLUMN medical_tech.consultation.apply_doctor_sign IS '申请医生签名';

COMMENT ON COLUMN medical_tech.consultation.consultation_dept_id IS '会诊科室Id';

COMMENT ON COLUMN medical_tech.consultation.consultation_dept_name IS '会诊科室名称';

COMMENT ON COLUMN medical_tech.consultation.consultation_doctor_id IS '会诊医生Id';

COMMENT ON COLUMN medical_tech.consultation.consultation_doctor_name IS '会诊医生名称';

COMMENT ON COLUMN medical_tech.consultation.consultation_accept_time IS '会诊接受时间';

COMMENT ON COLUMN medical_tech.consultation.consultation_end_time IS '会诊结束时间';

COMMENT ON COLUMN medical_tech.consultation.outside_hospital_name IS '院外会诊机构名称';

COMMENT ON COLUMN medical_tech.consultation.create_org_id IS '创建者部门Id';

COMMENT ON COLUMN medical_tech.consultation.create_org_name IS '创建者部门名称';

COMMENT ON COLUMN medical_tech.consultation.create_time IS '创建时间';

COMMENT ON COLUMN medical_tech.consultation.create_user_id IS '创建者Id';

COMMENT ON COLUMN medical_tech.consultation.create_user_name IS '创建者名称';

COMMENT ON COLUMN medical_tech.consultation.update_time IS '更新时间';

COMMENT ON COLUMN medical_tech.consultation.update_user_id IS '修改者Id';

COMMENT ON COLUMN medical_tech.consultation.update_user_name IS '修改者名称';

COMMENT ON COLUMN medical_tech.consultation.is_delete IS '软删除';

COMMENT ON COLUMN medical_tech.consultation.tenant_id IS '租户Id';

--会诊项目表
CREATE Table medical_tech.consultation_item (
    -- 主键Id
    id bigserial primary key ,
    --会诊Id
    consultation_id bigint NOT NULL ,
    -- 申请单号
    apply_no varchar(64) NOT NULL,
    --项目Id
    item_id bigint NOT NULL ,
    --项目编码
    item_code varchar(64) NOT NULL ,
    --项目名称
    item_name varchar(64) NOT NULL ,
    --单位
    unit varchar(64),
    --单价
    price numeric(16, 4) DEFAULT 0,
    --数量
    quantity numeric(16, 4) DEFAULT 0,
    --金额
    amount numeric(16, 4) DEFAULT 0,
    --国家医保编码
    medicine_code varchar(100),
    --国标编码
    nationalstandard_code varchar(100),
    --收费类别Id
    charge_category_id bigint,
    --自付比例
    self_pay_ratio numeric(4, 4),
    --自付比例是否审核 1审核 2不审核
    is_ratio_audit integer,
    --自付比例审核时间
    ratio_audit_time timestamp,
    --自付比例审核人员Id
    ratio_audit_staff_id bigint,
    --自付比例审核人员名称
    ratio_audit_staff_name varchar(64),
    --备注
    remark varchar(256),
    --创建者部门Id
    create_org_id bigint,
    --创建者部门名称
    create_org_name varchar(64),
    --创建时间
    create_time timestamp,
    --创建者Id
    create_user_id bigint,
    --创建者名称
    create_user_name varchar(64),
    --更新时间
    update_time timestamp,
    --修改者Id
    update_user_id bigint,
    --修改者名称
    update_user_name varchar(64),
    --软删除
    is_delete boolean NOT NULL DEFAULT false,
    --租户Id
    tenant_id bigint
);
--表注释
COMMENT ON TABLE medical_tech.consultation_item IS '会诊项目表';
--字段注释
COMMENT ON COLUMN medical_tech.consultation_item.id IS '主键Id';
COMMENT ON COLUMN medical_tech.consultation_item.consultation_id IS '会诊Id';
COMMENT ON COLUMN medical_tech.consultation_item.apply_no IS '申请单号';
COMMENT ON COLUMN medical_tech.consultation_item.item_id IS '项目Id';
COMMENT ON COLUMN medical_tech.consultation_item.item_code IS '项目编码';
COMMENT ON COLUMN medical_tech.consultation_item.item_name IS '项目名称';
COMMENT ON COLUMN medical_tech.consultation_item.unit IS '单位';
COMMENT ON COLUMN medical_tech.consultation_item.price IS '单价';
COMMENT ON COLUMN medical_tech.consultation_item.quantity IS '数量';
COMMENT ON COLUMN medical_tech.consultation_item.amount IS '金额';
COMMENT ON COLUMN medical_tech.consultation_item.medicine_code IS '国家医保编码';
COMMENT ON COLUMN medical_tech.consultation_item.nationalstandard_code IS '国标编码';
COMMENT ON COLUMN medical_tech.consultation_item.charge_category_id IS '收费类别Id';
COMMENT ON COLUMN medical_tech.consultation_item.self_pay_ratio IS '自付比例';
COMMENT ON COLUMN medical_tech.consultation_item.is_ratio_audit IS '自付比例是否审核 1审核 2不审核';
COMMENT ON COLUMN medical_tech.consultation_item.ratio_audit_time IS '自付比例审核时间';
COMMENT ON COLUMN medical_tech.consultation_item.ratio_audit_staff_id IS '自付比例审核人员Id';
COMMENT ON COLUMN medical_tech.consultation_item.ratio_audit_staff_name IS '自付比例审核人员名称';
COMMENT ON COLUMN medical_tech.consultation_item.remark IS '备注';
COMMENT ON COLUMN medical_tech.consultation_item.create_org_id IS '创建者部门Id';
COMMENT ON COLUMN medical_tech.consultation_item.create_org_name IS '创建者部门名称';
COMMENT ON COLUMN medical_tech.consultation_item.create_time IS '创建时间';
COMMENT ON COLUMN medical_tech.consultation_item.create_user_id IS '创建者Id';
COMMENT ON COLUMN medical_tech.consultation_item.create_user_name IS '创建者名称';
COMMENT ON COLUMN medical_tech.consultation_item.update_time IS '更新时间';
COMMENT ON COLUMN medical_tech.consultation_item.update_user_id IS '修改者Id';
COMMENT ON COLUMN medical_tech.consultation_item.update_user_name IS '修改者名称';
COMMENT ON COLUMN medical_tech.consultation_item.is_delete IS '软删除';
COMMENT ON COLUMN medical_tech.consultation_item.tenant_id IS '租户Id';