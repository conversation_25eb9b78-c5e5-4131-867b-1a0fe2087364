namespace His.Module.OutpatientDoctor.Dto;

public class TemplatePrescriptionInput
{
    public   AddTemplatePrescriptionInput  Main { get; set; }
    public   List<AddPrescriptionDetailInput>  Details { get; set; }
}

public class AddTemplatePrescriptionInput
{
    
    public virtual long? Id { get; set; }
    
     /// <summary>
    /// `模板名称
    /// </summary> 
    public virtual string? TemplateName { get; set; }
    
    /// <summary>
    /// 门诊处方类型
    /// </summary> 
    public virtual string? PrescriptionType { get; set; }
    
    /// <summary>
    /// 1 科室模板/ 2 个人模板
    /// </summary> 
    public virtual int? TemplateScope { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary> 
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary> 
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 中药付数
    /// </summary> 
    public virtual int? HerbsQuantity { get; set; }
    
 
    /// <summary>
    /// 打印时间
    /// </summary> 
    public virtual DateTime? PrintTime { get; set; }
    
    /// <summary>
    /// 药房
    /// </summary> 
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 药房id
    /// </summary> 
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 创建者部门Id
    /// </summary> 
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary> 
    public virtual string? CreateOrgName { get; set; }
}