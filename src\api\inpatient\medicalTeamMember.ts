﻿import {useBaseApi} from '/@/api/base';

// 医疗组成员表接口服务
export const useMedicalTeamMemberApi = () => {
	const baseApi = useBaseApi("medicalTeamMember");
	return {
		// 分页查询医疗组成员表
		page: baseApi.page,
		// 查看医疗组成员表详细
		detail: baseApi.detail,
		// 新增医疗组成员表
		add: baseApi.add,
		// 更新医疗组成员表
		update: baseApi.update,
		// 删除医疗组成员表
		delete: baseApi.delete,
		// 批量删除医疗组成员表
		batchDelete: baseApi.batchDelete,
		// 导出医疗组成员表数据
		exportData: baseApi.exportData,
		// 导入医疗组成员表数据
		importData: baseApi.importData,
		// 下载医疗组成员表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 医疗组成员表实体
export interface MedicalTeamMember {
	// 主键Id
	id: number;
	// 医疗组ID
	teamId?: number;
	// 成员ID
	staffId?: number;
	// 成员名称
	staffName?: string;
	// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
	roleType: string;
	// 加入日期
	joinDate: string;
	// 离开日期
	leaveDate: string;
	// 状态(1:启用 2:停用,)
	status: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 创建用户ID
	createUserId: number;
	// 创建用户名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 更新用户ID
	updateUserId: number;
	// 更新用户名
	updateUserName: string;
	// 是否删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}