﻿using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 中医证型表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("tcm_syndrome", "中医证型表")]
public class TcmSyndrome : EntityTenant
{
    /// <summary>
    /// 中医证型编码
    /// </summary>
    [SugarColumn(ColumnName = "tcm_syndrome_code", ColumnDescription = "中医证型编码", Length = 255)]
    public virtual string? TcmSyndromeCode { get; set; }
    
    /// <summary>
    /// 中医证型名称
    /// </summary>
    [SugarColumn(ColumnName = "tcm_syndrome_name", ColumnDescription = "中医证型名称", Length = 255)]
    public virtual string? TcmSyndromeName { get; set; }
    
    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 64)]
    public virtual string? PinyinCode { get; set; }
    
    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 64)]
    public virtual string? WubiCode { get; set; }
    
    /// <summary>
    /// 版本
    /// </summary>
    [SugarColumn(ColumnName = "version", ColumnDescription = "版本", Length = 32)]
    public virtual string? Version { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }
    
}
