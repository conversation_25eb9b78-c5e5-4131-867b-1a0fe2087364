﻿using Admin.NET.Core;

namespace Admin.NET.Core.Service;

/// <summary>
/// 用户角色机构表
/// </summary>
 
public class SysUserRoleOrgInput  
{
    /// <summary>
    /// 角色Id
    /// </summary>
 
    public virtual long RoleId { get; set; }
    
    /// <summary>
    /// 角色编码
    /// </summary>
 
    public virtual string RoleCode { get; set; }
    
    /// <summary>
    /// 角色名称
    /// </summary>
 
    public virtual string RoleName { get; set; }
    
    /// <summary>
    /// 机构Id
    /// </summary> 
    public virtual long OrgId { get; set; }
    
    /// <summary>
    /// 机构编码
    /// </summary> 
    public virtual string OrgCode { get; set; }
    
    /// <summary>
    /// 机构名称
    /// </summary> 
    public virtual string OrgName { get; set; }
    
    /// <summary>
    /// 用户id
    /// </summary> 
    public virtual long? UserId { get; set; }
    
    /// <summary>
    /// 用户名称
    /// </summary>
 
    public virtual string UserName { get; set; }
     
    
}
