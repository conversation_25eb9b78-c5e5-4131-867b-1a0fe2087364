﻿using Furion.DatabaseAccessor;
using His.Module.MedicalTech.Api.Dispose;
using His.Module.MedicalTech.Api.Dispose.Dto;
using His.Module.MedicalTech.Enum;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.Shared.Api.Api.ChargeItem;
using Yitter.IdGenerator;
namespace His.Module.MedicalTech.Service;

/// <summary>
/// 处置服务 🧩
/// </summary>
[ApiDescriptionSettings(MedicalTechConst.GroupName, Order = 100)]
public class DisposeService : IDynamicApiController, ITransient, IDisposeApi
{
    private readonly SqlSugarRepository<Dispose> _disposeRep;
    private readonly SqlSugarRepository<DisposePackageItem> _disposePackageItemRep;
    private readonly IChargeItemApi _chargeItemApi;
    private readonly IChargeApi _chargeApi;

    public DisposeService(SqlSugarRepository<Dispose> disposeRep
        , IChargeItemApi chargeItemApi
        , IChargeApi chargeApi
        , SqlSugarRepository<DisposePackageItem> disposePackageItemRep)
    {
        _disposeRep = disposeRep;
        _chargeItemApi = chargeItemApi;
        _disposePackageItemRep = disposePackageItemRep;
        _chargeApi = chargeApi;
    }

    /// <summary>
    /// 分页查询处置 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询处置")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DisposeOutput>> Page(PageDisposeInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _disposeRep.AsQueryable()
            .Where(u => u.RegisterId == input.RegisterId)
            .Where(u => u.BillingDoctorId == long.Parse(App.User.FindFirst(ClaimConst.UserId).Value))
            .Where(u => u.BillingDeptId == long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value))
            .Where(u => u.BillingTime >= DateTime.Today)
            .Where(u => u.Flag == input.Flag)
            .OrderBy(u => u.ApplyNo)
            .Select<DisposeOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取处置详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取处置详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<Dispose> Detail([FromQuery] QueryByIdDisposeInput input)
    {
        return await _disposeRep.AsQueryable()
            .Includes(u => u.DisposePackageItem)
            .FirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加处置 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加处置")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost, UnitOfWork]
    public async Task Add(List<AddDisposeInput> input)
    {
        // 将输入对象映射为 Dispose 实体列
        var entities = input.Adapt<List<Dispose>>();

        // 批量获取所有关联的收费项目详细信息（优化数据库查询，避免N+1问题）
        var chargeItems = await _chargeItemApi.GetDetails([.. entities.Select(u => (long)u.ItemId)]);


        var disposePackageItems = new List<DisposePackageItem>();
        // 遍历处理每个处置实体
        foreach (var entity in entities)
        {
            // 根据 ItemId 匹配对应的收费项目
            var chargeItem = chargeItems.FirstOrDefault(u => u.Id == entity.ItemId);

            // 生成分布式唯一ID
            entity.Id = YitIdHelper.NextId();

            // 从数据库序列生成申请单号（格式化为8位数字，不足补零）
            entity.ApplyNo = await _disposeRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('apply_no_seq') As varchar), 8, '0')");

            // 填充收费项目基础信息
            entity.ItemCode = chargeItem.Code;
            entity.ItemName = chargeItem.Name;
            entity.Spec = chargeItem.Spec;
            entity.Unit = chargeItem.Unit;
            entity.Manufacturer = chargeItem.Manufacturer;
            entity.Model = chargeItem.Model;
            entity.Price = chargeItem.Price;

            // 计算总金额 = 数量 × 单价
            entity.Amount = entity.Quantity * chargeItem.Price;

            // 转换套餐标识为可空整型
            entity.IsPackage = (int?)chargeItem.Package;

            // 设置业务时间相关字段
            entity.BillingTime = DateTime.Now;

            // 从用户凭证中获取并设置计费部门信息
            entity.BillingDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId)?.Value ?? "0");
            entity.BillingDeptName = App.User.FindFirst(ClaimConst.OrgName)?.Value;

            // 从用户凭证中获取并设置开单医生信息
            entity.BillingDoctorId = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");
            entity.BillingDoctorName = App.User.FindFirst(ClaimConst.RealName)?.Value;

            // 设置收费分类和初始状态
            entity.ChargeCategoryId = chargeItem.ChargeCategoryId;
            entity.Status = 1;

            // 处理套餐项目（当收费项目是套餐时）
            if (chargeItem.Package == YesNoEnum.Y)
            {
                // 转换套餐子项为处置包项实体
                var disposePackageItem = chargeItem.ChargeItemPacks.Select(packageItem => new DisposePackageItem
                {
                    // 关联主处置记录
                    DisposeId = entity.Id,
                    ApplyNo = entity.ApplyNo,
                    // 填充套餐子项信息
                    ItemId = packageItem.Id,
                    ItemCode = packageItem.Code,
                    ItemName = packageItem.Name,
                    Spec = packageItem.Spec,
                    Unit = packageItem.Unit,
                    Manufacturer = packageItem.Manufacturer,
                    Model = packageItem.Model,
                    Price = packageItem.Price,
                    Quantity = entity.Quantity * packageItem.Quantity,
                    // 计算子项金额
                    Amount = entity.Quantity * packageItem.Quantity * packageItem.Price,
                    // 继承收费分类
                    ChargeCategoryId = packageItem.ChargeCategoryId,
                }).ToList();
                disposePackageItems.AddRange(disposePackageItem);
            }
        }

        // 批量插入主处置记录（优化数据库操作）
        await _disposeRep.InsertRangeAsync(entities);

        // 批量插入套餐子项记录
        await _disposePackageItemRep.InsertRangeAsync(disposePackageItems);

        //新增处置时同时保存到收费表
        await SaveChargeInfo(entities, disposePackageItems);
    }

    /// <summary>
    /// 更新处置 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新处置")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost, UnitOfWork]
    public async Task Update(UpdateDisposeInput input)
    {
        var record = await _disposeRep.GetFirstAsync(u => u.Id == input.Id)
            ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (record.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0001);
        }
        var entity = input.Adapt<Dispose>();
        await _disposeRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除处置 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除处置")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost, UnitOfWork]
    public async Task Delete(DeleteDisposeInput input)
    {
        var entity = await _disposeRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0002);
        }
        await _disposeRep.FakeDeleteAsync(entity);   //假删除

        // 获取并假删除套餐项目记录
        var packageItemsList = await _disposePackageItemRep.GetListAsync(u => u.DisposeId == input.Id);
        await _disposePackageItemRep.FakeDeleteAsync(packageItemsList);
        //await _disposeRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除处置 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除处置")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列 不能为空")] List<DeleteDisposeInput> input)
    {
        var exp = Expressionable.Create<Dispose>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _disposeRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _disposeRep.FakeDeleteAsync(list);   //假删除
        //return await _disposeRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置申请单状态 🔄
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置申请单状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetStatus(SetDisposeStatusInput input)
    {
        var entity = await _disposeRep.GetFirstAsync(u => u.Id == input.Id)
                ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (!MedicalTechConst.ApplyStatusValidTransitions.TryGetValue((int)entity.Status, out var allowed)
            || !allowed.Contains((int)input.Status))
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0004);
        }
        await _disposeRep.UpdateAsync(u => new Dispose()
        {
            Status = input.Status,
        }, u => u.Id == entity.Id);
    }

    // /// <summary>
    // /// 处置收费 💰
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("处置收费")]
    // [ApiDescriptionSettings(Name = "Charge"), HttpPost, UnitOfWork]
    // public async Task Charge(ChargeDisposeInput input)
    // {
    //     // 修改收费状态
    //     var result = await _chargeApi.ConfirmStatus(input);
    //     // 更新主记录状态和收费信息
    //     await _disposeRep.UpdateAsync(u => new Dispose()
    //     {
    //         Status = 2,
    //         ChargeTime = result.ChargeTime,
    //         ChargeStaffId = result.ChargeStaffId,
    //         ChargeStaffName = result.ChargeStaffName,
    //     }, u => u.Id == input.Id);
    // }

    /// <summary>
    /// 保存到收费记录
    /// </summary>
    /// <param name="disposes"></param>
    /// <param name="disposePackageItems"></param>
    /// <returns></returns>
    private async Task SaveChargeInfo(List<Dispose> disposes, List<DisposePackageItem> disposePackageItems)
    {
        var dtos = new List<OutpatientChargeDto>();
        foreach (var dispose in disposes)
        {
            // 构建 DTO 对象
            var dto = dispose.Adapt<OutpatientChargeDto>();
            dto.BillingType = "Dispose";
            dto.BillingId = dispose.Id;
            dto.BillingNo = dispose.ApplyNo;
            dto.TotalAmount = dispose.Amount;
            // 处理套餐项目
            if (dispose.IsPackage == 1)
                dto.Details = disposePackageItems.Where(u => u.DisposeId == dispose.Id).Select(item => new OutpatientChargeDetailDto
                {
                    ItemId = item.ItemId,
                    ItemCode = item.ItemCode,
                    ItemName = item.ItemName,
                    Spec = item.Spec,
                    Unit = item.Unit,
                    Price = item.Price,
                    Quantity = item.Quantity,
                    Amount = item.Amount,
                    BillingId = item.DisposeId,
                    BillingNo = item.ApplyNo,
                    BillingTime = dispose.BillingTime,
                    BillingDeptId = dispose.BillingDeptId,
                    BillingDoctorId = dispose.BillingDoctorId,
                    ExecuteDeptId = dispose.ExecuteDeptId,
                    BillingDetailId = item.Id,
                    BillingDoctorName = dispose.BillingDoctorName,
                    ChargeCategoryId = item.ChargeCategoryId,
                    PackId = dispose.ItemId,
                    PackCode = dispose.ItemCode,
                    PackName = dispose.ItemName,
                    PackNumber = (short?)dispose.Quantity,
                    PackPrice = dispose.Price,
                    ExecuteStatus = 0
                }).ToList();
            // 处理非套餐项目
            else if (dispose.IsPackage == 2)
            {
                var detail = new OutpatientChargeDetailDto
                {
                    ItemId = dispose.ItemId,
                    ItemCode = dispose.ItemCode,
                    ItemName = dispose.ItemName,
                    Spec = dispose.Spec,
                    Unit = dispose.Unit,
                    Price = dispose.Price,
                    Quantity = dispose.Quantity,
                    Amount = dispose.Amount,
                    BillingId = dispose.Id,
                    BillingNo = dispose.ApplyNo,
                    BillingTime = dispose.BillingTime,
                    BillingDeptId = dispose.BillingDeptId,
                    BillingDoctorId = dispose.BillingDoctorId,
                    ExecuteDeptId = dispose.ExecuteDeptId,
                    BillingDetailId = dispose.Id,
                    BillingDoctorName = dispose.BillingDoctorName,
                    ChargeCategoryId = dispose.ChargeCategoryId,
                    FrequencyId = dispose.FrequencyId,
                    Manufacturer = dispose.Manufacturer,
                    MedicationDays = (short?)dispose.Days,
                    ExecuteStatus = 0
                };
                dto.Details = [detail];
            }
            dtos.Add(dto);
        }
        await _chargeApi.BatchAdd(dtos);
    }




    public async Task SetStatus(SetDisposeStatusDto input)
    {
        await SetStatus(new SetDisposeStatusInput
        {
            Id = input.Id, Status = input.Status
        });
    }
}
