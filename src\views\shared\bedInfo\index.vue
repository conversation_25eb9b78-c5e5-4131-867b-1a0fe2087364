﻿<script lang="ts" setup name="bedInfo">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useBedInfoApi } from '/@/api/shared/bedInfo';
import editDialog from '/@/views/shared/bedInfo/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from "/@/components/table/importData.vue";

const bedInfoApi = useBedInfoApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
});

// 页面加载时
onMounted(async () => {
  const data = await bedInfoApi.getDropdownData(true).then(res => res.data.result) ?? {};
  state.dropdownData.bedLevel = data.bedLevel;
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const result = await bedInfoApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delBedInfo = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await bedInfoApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => { });
};

// 批量删除
const batchDelBedInfo = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await bedInfoApi.batchDelete(state.selectData.map(u => ({ id: u.id }))).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => { });
};

// 设置状态
const changeBedInfoStatus = async (row: any) => {
  await bedInfoApi.setStatus({ id: row.id, status: row.status }).then(() => ElMessage.success('状态设置成功'));
};

// 导出数据
const exportBedInfoCommand = async (command: string) => {
  try {
    state.exportLoading = true;
    if (command === 'select') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams, { selectKeyList: state.selectData.map(u => u.id) });
      await bedInfoApi.exportData(params).then(res => downloadStreamFile(res));
    } else if (command === 'current') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams);
      await bedInfoApi.exportData(params).then(res => downloadStreamFile(res));
    } else if (command === 'all') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams, { page: 1, pageSize: 99999999 });
      await bedInfoApi.exportData(params).then(res => downloadStreamFile(res));
    }
  } finally {
    state.exportLoading = false;
  }
}

handleQuery();
</script>
<template>
  <div class="bedInfo-container" v-loading="state.exportLoading">
    <el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="床位编号">
              <el-input v-model="state.tableQueryParams.bedNo" clearable placeholder="请输入床位编号" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="科室">
              <el-input v-model="state.tableQueryParams.deptName" clearable placeholder="" />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="病区">
              <el-input v-model="state.tableQueryParams.wardName" clearable placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="房间编号">
              <el-input v-model="state.tableQueryParams.roomNo" clearable placeholder="请输入房间编号" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="床位类型">
              <g-sys-dict v-model="state.tableQueryParams.bedType" code="InpatientBedType" render-as="select"
                placeholder="请选择床位类型" clearable filterable />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="床位状态">
              <g-sys-dict v-model="state.tableQueryParams.bedStatus" code="InpatientBedStatus" render-as="select"
                placeholder="请选择床位状态" clearable filterable />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="床位等级">
              <el-select clearable filterable v-model="state.tableQueryParams.bedLevelId" placeholder="请选择床位等级">
                <el-option v-for="(item, index) in state.dropdownData.bedLevel ?? []" :key="index" :value="item.value"
                  :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="排序">
              <el-input-number v-model="state.tableQueryParams.orderNo" clearable placeholder="请输入排序" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="状态">
              <g-sys-dict v-model="state.tableQueryParams.status" code="StatusEnum" render-as="select"
                placeholder="请选择状态" clearable filterable />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item>
              <el-button-group style="display: flex; align-items: center;">
                <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'bedInfo:page'"
                  v-reclick="1000"> 查询 </el-button>
                <el-button icon="ele-Refresh" @click="() => state.tableQueryParams = {}"> 重置 </el-button>
                <el-button icon="ele-ZoomIn" @click="() => state.showAdvanceQueryUI = true"
                  v-if="!state.showAdvanceQueryUI" style="margin-left:5px;"> 高级查询 </el-button>
                <el-button icon="ele-ZoomOut" @click="() => state.showAdvanceQueryUI = false"
                  v-if="state.showAdvanceQueryUI" style="margin-left:5px;"> 隐藏 </el-button>
                <el-button type="danger" style="margin-left:5px;" icon="ele-Delete" @click="batchDelBedInfo"
                  :disabled="state.selectData.length == 0" v-auth="'bedInfo:batchDelete'"> 删除 </el-button>
                <el-button type="primary" style="margin-left:5px;" icon="ele-Plus"
                  @click="editDialogRef.openDialog(null, '新增床位信息')" v-auth="'bedInfo:add'"> 新增 </el-button>
                <el-dropdown :show-timeout="70" :hide-timeout="50" @command="exportBedInfoCommand">
                  <el-button type="primary" style="margin-left:5px;" icon="ele-FolderOpened" v-reclick="20000"
                    v-auth="'bedInfo:export'"> 导出 </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="select"
                        :disabled="state.selectData.length == 0">导出选中</el-dropdown-item>
                      <el-dropdown-item command="current">导出本页</el-dropdown-item>
                      <el-dropdown-item command="all">导出全部</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <!-- <el-button type="warning" style="margin-left:5px;" icon="ele-MostlyCloudy"
                  @click="importDataRef.openDialog()" v-auth="'bedInfo:import'"> 导入 </el-button> -->
              </el-button-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="full-table" shadow="hover" style="margin-top: 5px">
      <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }"
        style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id" @sort-change="sortChange"
        border>
        <el-table-column type="selection" width="40" align="center"
          v-if="auth('bedInfo:batchDelete') || auth('bedInfo:export')" />
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column prop='bedNo' label='床位编号' show-overflow-tooltip />
        <el-table-column prop='deptName' label='科室' show-overflow-tooltip />
        <el-table-column prop='wardName' label='病区' show-overflow-tooltip />
        <el-table-column prop='roomNo' label='房间编号' show-overflow-tooltip />
        <el-table-column prop='bedType' label='床位类型' show-overflow-tooltip>
          <template #default="scope">
            <g-sys-dict v-model="scope.row.bedType" code="InpatientBedType" />
          </template>
        </el-table-column>
        <el-table-column prop='bedStatus' label='床位状态' show-overflow-tooltip>
          <template #default="scope">
            <g-sys-dict v-model="scope.row.bedStatus" code="InpatientBedStatus" />
          </template>
        </el-table-column>
        <el-table-column prop='bedLevelName' label='床位等级' :formatter="(row: any) => row.bedLevelFkDisplayName"
          show-overflow-tooltip />
        <el-table-column prop='orderNo' label='排序' show-overflow-tooltip />
        <el-table-column prop='status' label='状态' v-auth="'bedInfo:setStatus'" show-overflow-tooltip>
          <template #default="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2" size="small"
              @change="changeBedInfoStatus(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop='remark' label='备注' show-overflow-tooltip />
        <el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            <ModifyRecord :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip
          v-if="auth('bedInfo:update') || auth('bedInfo:delete')">
          <template #default="scope">
            <el-button icon="ele-Edit" size="small" text type="primary"
              @click="editDialogRef.openDialog(scope.row, '编辑床位信息')" v-auth="'bedInfo:update'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text type="danger" @click="delBedInfo(scope.row)"
              v-auth="'bedInfo:delete'"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
        @size-change="(val: any) => handleQuery({ pageSize: val })"
        @current-change="(val: any) => handleQuery({ page: val })" layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100, 200, 500]" :total="state.tableParams.total" size="small" background />
      <ImportData ref="importDataRef" :import="bedInfoApi.importData" :download="bedInfoApi.downloadTemplate"
        v-auth="'bedInfo:import'" @refresh="handleQuery" />
      <printDialog ref="printDialogRef" :title="'打印床位信息'" @reloadTable="handleQuery" />
      <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
  width: 100%;
}
</style>