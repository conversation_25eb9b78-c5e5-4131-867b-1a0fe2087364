using His.Module.Insurance.Service.Settlement.Dto;

namespace His.Module.Insurance.Service.Settlement.Model.Patient;

public class ReadCardRequest
  : PatientBaseSettlementDto
{
    /// <summary>
    /// 卡号。（原字段名：p_kh）
    /// 若由地纬DLL控制读卡器，p_kh可不传；若由his控制读卡器，p_kh为必传。
    /// </summary>
    public string p_kh { get; set; }

    /// <summary>
    /// *险种标志（具体值调用数据字典接口获取，代码编号：XZBZ）（原字段名：p_xzbz）
    /// </summary>
    public string p_xzbz { get; set; }

    /// <summary>
    /// 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传''（原字段名：p_ewm）
    /// </summary>
    public string p_ewm { get; set; }

    /// <summary>
    /// 医疗统筹类别（0为仅获取人员基本信息，1为住院，4为门诊大病(特病)，6为普通门诊，不传时，默认值为0，其他具体值调用数据字典接口获取，代码编号：YLTCLB）（原字段名：p_yltclb）
    /// </summary>
    public string p_yltclb { get; set; }

    /// <summary>
    /// 口令（口令由医保管理时，需传入口令；否则不传。（阳煤地区口令必传，如果口令没有修改过，则不会校验））（原字段名：p_kl）
    /// </summary>
    public string p_kl { get; set; }

    /// <summary>
    /// 普通门诊刷卡标志（纯个账消费传1，其他不传或传0）（原字段名：p_ptmzskbz）
    /// </summary>
    public string p_ptmzskbz { get; set; }

    /// <summary>
    /// 社会保障号码（由his控制读卡器时，跨省异地和省内异地读卡必传，跨省异地电子医保凭证时传空；三类终端刷脸或解码时，传入刷脸获取身份证号）（原字段名：p_shbzhm）
    /// </summary>
    public string p_shbzhm { get; set; }

    /// <summary>
    /// 姓名（由his控制读卡器时，跨省异地读卡必传，跨省异地电子医保凭证时传空）（原字段名：p_xm）
    /// </summary>
    public string p_xm { get; set; }

    /// <summary>
    /// 卡识别码（由his控制读卡器时，跨省异地读卡必传，跨省异地电子医保凭证时传空）（原字段名：p_sbm）
    /// </summary>
    public string p_sbm { get; set; }

    /// <summary>
    /// 卡规范版本（由his控制读卡器时，跨省异地读卡必传，跨省异地电子医保凭证时传空）（原字段名：p_gfbb）
    /// </summary>
    public string p_gfbb { get; set; }

    // /// <summary>
    // /// psam卡号（psam卡号，忻州省内异地必传）（原字段名：p_psamkh）
    // /// </summary>
    // public string p_psamkh { get; set; }

    /// <summary>
    /// 电子医保凭证业务码（传电子医保凭证二维码时需传入。传入具体的code值，如：101挂号、102住院建档、306诊间核验身份等）（原字段名：p_dzpzywm）
    /// </summary>
    public string p_dzpzywm { get; set; }

    /// <summary>
    /// 业务关联身份证号码（使用电子医保凭证时，传入业务关联人的身份证号码）（原字段名：p_ywglsfzhm）
    /// </summary>
    public string p_ywglsfzhm { get; set; }
}
