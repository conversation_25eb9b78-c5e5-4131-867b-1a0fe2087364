﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 门诊退费申请输出参数
/// </summary>
public class RefundApplyDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 退费申请ID
    /// </summary>
    public long? ChargeId { get; set; }
    
    /// <summary>
    /// 退费申请类型
    /// </summary>
    public string? ChargeType { get; set; }
    
    /// <summary>
    /// 退费申请单号
    /// </summary>
    public string? ApplyNo { get; set; }
    
    /// <summary>
    /// 退费申请时间
    /// </summary>
    public DateTime? ApplyTime { get; set; }
     
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? CardNo { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 挂号ID
    /// </summary>
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 退费原因
    /// </summary>
    public string? ApplyReason { get; set; }
    
    /// <summary>
    /// 状态 0 新增待审核 1 审核中 2 审核完成
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 审核状态 表refund_audit 状态
    /// </summary>
    public int? AuditStatus { get; set; }
    
    /// <summary>
    /// 创建机构ID
    /// </summary>
    public long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    public string? CreateOrgName { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
