﻿using Admin.NET.Core;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using System.ComponentModel.DataAnnotations;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 复诊预约记录基础输入参数
/// </summary>
public class PatientRevisitAppointmentBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required(ErrorMessage = "患者Id不能为空")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号记录Id
    /// </summary>
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string? VisitNo { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public virtual string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 预约医生Id
    /// </summary>
    public virtual long? AppointmentDoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public virtual string? AppointmentDoctorName { get; set; }
    
    /// <summary>
    /// 预约科室Id
    /// </summary>
    public virtual long? AppointmentDeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public virtual string? AppointmentDeptName { get; set; }
    
    /// <summary>
    /// 复诊时间
    /// </summary>
    public virtual DateTime? RevisitTime { get; set; }
    
    /// <summary>
    /// 复诊原因
    /// </summary>
    public virtual string? RevisitReason { get; set; }
    
    /// <summary>
    /// 复诊科室Id
    /// </summary>
    public virtual long? RevisitDeptId { get; set; }
    
    /// <summary>
    /// 复诊科室名称
    /// </summary>
    public virtual string? RevisitDeptName { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 复诊预约记录分页查询输入参数
/// </summary>
public class PagePatientRevisitAppointmentInput : BasePageInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号记录Id
    /// </summary>
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 预约医生Id
    /// </summary>
    public long? AppointmentDoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public string? AppointmentDoctorName { get; set; }
    
    /// <summary>
    /// 预约科室Id
    /// </summary>
    public long? AppointmentDeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public string? AppointmentDeptName { get; set; }
    
    /// <summary>
    /// 复诊时间范围
    /// </summary>
     public DateTime?[] RevisitTimeRange { get; set; }
    
    /// <summary>
    /// 复诊原因
    /// </summary>
    public string? RevisitReason { get; set; }
    
    /// <summary>
    /// 复诊科室Id
    /// </summary>
    public long? RevisitDeptId { get; set; }
    
    /// <summary>
    /// 复诊科室名称
    /// </summary>
    public string? RevisitDeptName { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 复诊预约记录增加输入参数
/// </summary>
public class AddPatientRevisitAppointmentInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
 
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号记录Id
    /// </summary>
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [MaxLength(64, ErrorMessage = "就诊卡号字符长度不能超过64")]
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [MaxLength(64, ErrorMessage = "身份证号字符长度不能超过64")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [MaxLength(64, ErrorMessage = "门诊号字符长度不能超过64")]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 预约医生Id
    /// </summary>
    public long? AppointmentDoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "预约医生姓名字符长度不能超过64")]
    public string? AppointmentDoctorName { get; set; }
    
    /// <summary>
    /// 预约科室Id
    /// </summary>
    public long? AppointmentDeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "预约科室名称字符长度不能超过64")]
    public string? AppointmentDeptName { get; set; }
    
    /// <summary>
    /// 复诊时间
    /// </summary>
    public DateTime? RevisitTime { get; set; }
    
    /// <summary>
    /// 复诊原因
    /// </summary>
    [MaxLength(256, ErrorMessage = "复诊原因字符长度不能超过256")]
    public string? RevisitReason { get; set; }
    
    /// <summary>
    /// 复诊科室Id
    /// </summary>
    public long? RevisitDeptId { get; set; }
    
    /// <summary>
    /// 复诊科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "复诊科室名称字符长度不能超过64")]
    public string? RevisitDeptName { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注信息字符长度不能超过256")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 复诊预约记录删除输入参数
/// </summary>
public class DeletePatientRevisitAppointmentInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 复诊预约记录更新输入参数
/// </summary>
public class UpdatePatientRevisitAppointmentInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
 
    public long? Id { get; set; }
    
    /// <summary>
    /// 患者Id
    /// </summary>    
    [Required(ErrorMessage = "患者Id不能为空")]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>    
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号记录Id
    /// </summary>    
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>    
    [MaxLength(64, ErrorMessage = "就诊卡号字符长度不能超过64")]
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>    
    [MaxLength(64, ErrorMessage = "身份证号字符长度不能超过64")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>    
    [MaxLength(64, ErrorMessage = "门诊号字符长度不能超过64")]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 预约医生Id
    /// </summary>    
    public long? AppointmentDoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>    
    [MaxLength(64, ErrorMessage = "预约医生姓名字符长度不能超过64")]
    public string? AppointmentDoctorName { get; set; }
    
    /// <summary>
    /// 预约科室Id
    /// </summary>    
    public long? AppointmentDeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>    
    [MaxLength(64, ErrorMessage = "预约科室名称字符长度不能超过64")]
    public string? AppointmentDeptName { get; set; }
    
    /// <summary>
    /// 复诊时间
    /// </summary>    
    public DateTime? RevisitTime { get; set; }
    
    /// <summary>
    /// 复诊原因
    /// </summary>    
    [MaxLength(256, ErrorMessage = "复诊原因字符长度不能超过256")]
    public string? RevisitReason { get; set; }
    
    /// <summary>
    /// 复诊科室Id
    /// </summary>    
    public long? RevisitDeptId { get; set; }
    
    /// <summary>
    /// 复诊科室名称
    /// </summary>    
    [MaxLength(64, ErrorMessage = "复诊科室名称字符长度不能超过64")]
    public string? RevisitDeptName { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>    
    [MaxLength(256, ErrorMessage = "备注信息字符长度不能超过256")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 复诊预约记录主键查询输入参数
/// </summary>
public class QueryByIdPatientRevisitAppointmentInput : DeletePatientRevisitAppointmentInput
{
}

/// <summary>
/// 复诊预约记录数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportPatientRevisitAppointmentInput : BaseImportInput
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [ImporterHeader(Name = "*患者Id")]
    [ExporterHeader("*患者Id", Format = "", Width = 25, IsBold = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [ImporterHeader(Name = "患者姓名")]
    [ExporterHeader("患者姓名", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号记录Id
    /// </summary>
    [ImporterHeader(Name = "挂号记录Id")]
    [ExporterHeader("挂号记录Id", Format = "", Width = 25, IsBold = true)]
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [ImporterHeader(Name = "就诊卡号")]
    [ExporterHeader("就诊卡号", Format = "", Width = 25, IsBold = true)]
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [ImporterHeader(Name = "身份证号")]
    [ExporterHeader("身份证号", Format = "", Width = 25, IsBold = true)]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [ImporterHeader(Name = "门诊号")]
    [ExporterHeader("门诊号", Format = "", Width = 25, IsBold = true)]
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 预约医生Id
    /// </summary>
    [ImporterHeader(Name = "预约医生Id")]
    [ExporterHeader("预约医生Id", Format = "", Width = 25, IsBold = true)]
    public long? AppointmentDoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [ImporterHeader(Name = "预约医生姓名")]
    [ExporterHeader("预约医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? AppointmentDoctorName { get; set; }
    
    /// <summary>
    /// 预约科室Id
    /// </summary>
    [ImporterHeader(Name = "预约科室Id")]
    [ExporterHeader("预约科室Id", Format = "", Width = 25, IsBold = true)]
    public long? AppointmentDeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    [ImporterHeader(Name = "预约科室名称")]
    [ExporterHeader("预约科室名称", Format = "", Width = 25, IsBold = true)]
    public string? AppointmentDeptName { get; set; }
    
    /// <summary>
    /// 复诊时间
    /// </summary>
    [ImporterHeader(Name = "复诊时间")]
    [ExporterHeader("复诊时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? RevisitTime { get; set; }
    
    /// <summary>
    /// 复诊原因
    /// </summary>
    [ImporterHeader(Name = "复诊原因")]
    [ExporterHeader("复诊原因", Format = "", Width = 25, IsBold = true)]
    public string? RevisitReason { get; set; }
    
    /// <summary>
    /// 复诊科室Id
    /// </summary>
    [ImporterHeader(Name = "复诊科室Id")]
    [ExporterHeader("复诊科室Id", Format = "", Width = 25, IsBold = true)]
    public long? RevisitDeptId { get; set; }
    
    /// <summary>
    /// 复诊科室名称
    /// </summary>
    [ImporterHeader(Name = "复诊科室名称")]
    [ExporterHeader("复诊科室名称", Format = "", Width = 25, IsBold = true)]
    public string? RevisitDeptName { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    [ImporterHeader(Name = "状态，默认1（有效）")]
    [ExporterHeader("状态，默认1（有效）", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    [ImporterHeader(Name = "备注信息")]
    [ExporterHeader("备注信息", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
