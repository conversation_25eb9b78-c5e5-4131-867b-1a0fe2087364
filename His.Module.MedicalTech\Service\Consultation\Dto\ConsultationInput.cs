﻿namespace His.Module.MedicalTech.Service;

/// <summary>
/// 会诊表基础输入参数
/// </summary>
public class ConsultationBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    public virtual string VisitNo { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string PatientName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public virtual string? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public virtual int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [Dict("AgeUnit", AllowNullValue = true)]
    public virtual string? AgeUnit { get; set; }

    /// <summary>
    /// 期望会诊时间
    /// </summary>
    public virtual DateTime? ExpectedTime { get; set; }

    /// <summary>
    /// 会诊类型
    /// </summary>
    [Dict("ConsultationType", AllowNullValue = true)]
    [Required(ErrorMessage = "会诊类型不能为空")]
    public virtual string? Type { get; set; }

    /// <summary>
    /// 病情摘要
    /// </summary>
    [Required(ErrorMessage = "病情摘要不能为空")]
    public virtual string? ClinicalSummary { get; set; }

    /// <summary>
    /// 会诊目的
    /// </summary>
    [Required(ErrorMessage = "会诊目的不能为空")]
    public virtual string? Purpose { get; set; }

    /// <summary>
    /// 申请科室Id
    /// </summary>
    public virtual long? ApplyDeptId { get; set; }

    /// <summary>
    /// 申请科室名称
    /// </summary>
    public virtual string? ApplyDeptName { get; set; }

    /// <summary>
    /// 申请医生Id
    /// </summary>
    public virtual long? ApplyDoctorId { get; set; }

    /// <summary>
    /// 申请医生名称
    /// </summary>
    public virtual string? ApplyDoctorName { get; set; }

    /// <summary>
    /// 申请医生签名
    /// </summary>
    public virtual string? ApplyDoctorSign { get; set; }

    /// <summary>
    /// 会诊科室Id
    /// </summary>
    public virtual long? ConsultationDeptId { get; set; }

    /// <summary>
    /// 会诊科室名称
    /// </summary>
    public virtual string? ConsultationDeptName { get; set; }

    /// <summary>
    /// 会诊医生Id
    /// </summary>
    public virtual long? ConsultationDoctorId { get; set; }

    /// <summary>
    /// 会诊医生名称
    /// </summary>
    public virtual string? ConsultationDoctorName { get; set; }

    /// <summary>
    /// 院外会诊机构名称
    /// </summary>
    public virtual string? OutsideHospitalName { get; set; }
}

/// <summary>
/// 会诊表分页查询输入参数
/// </summary>
public class PageConsultationInput : BasePageInput
{
    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required(ErrorMessage = "就诊Id不能为空")]
    public long RegisterId { get; set; }

    /// <summary>
    /// 0 门诊 1住院
    /// </summary>
    [Required(ErrorMessage = "门诊住院标志不能为空")]
    public int Flag { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 会诊表增加输入参数
/// </summary>
public class AddConsultationInput
{
    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required]
    public virtual long RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    [MaxLength(64, ErrorMessage = "就诊流水号字符长度不能超过64")]
    public string VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>

    public string OutpatientNo { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>

    public string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>

    public virtual long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string PatientName { get; set; }

    /// <summary>
    /// 就诊时间
    /// </summary>

    public virtual DateTime VisitTime { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [MaxLength(32, ErrorMessage = "性别字符长度不能超过32")]
    public string? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [Dict("AgeUnit", AllowNullValue = true)]
    [MaxLength(32, ErrorMessage = "年龄单位字符长度不能超过32")]
    public string? AgeUnit { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    public virtual int? Flag { get; set; }

    /// <summary>
    /// 期望会诊时间
    /// </summary>
    public DateTime? ExpectedTime { get; set; }

    /// <summary>
    /// 会诊类型
    /// </summary>
    [Dict("ConsultationType", AllowNullValue = true)]
    [Required(ErrorMessage = "会诊类型不能为空")]
    [MaxLength(32, ErrorMessage = "会诊类型字符长度不能超过32")]
    public string? Type { get; set; }

    /// <summary>
    /// 病情摘要
    /// </summary>
    [Required(ErrorMessage = "病情摘要不能为空")]
    public string? ClinicalSummary { get; set; }

    /// <summary>
    /// 会诊目的
    /// </summary>
    [Required(ErrorMessage = "会诊目的不能为空")]
    public string? Purpose { get; set; }

    /// <summary>
    /// 申请科室Id
    /// </summary>
    public long? ApplyDeptId { get; set; }

    /// <summary>
    /// 申请科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "申请科室名称字符长度不能超过64")]
    public string? ApplyDeptName { get; set; }

    /// <summary>
    /// 申请医生Id
    /// </summary>
    public long? ApplyDoctorId { get; set; }

    /// <summary>
    /// 申请医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "申请医生名称字符长度不能超过64")]
    public string? ApplyDoctorName { get; set; }

    /// <summary>
    /// 申请医生签名
    /// </summary>
    public string? ApplyDoctorSign { get; set; }

    /// <summary>
    /// 会诊科室Id
    /// </summary>
    public long? ConsultationDeptId { get; set; }

    /// <summary>
    /// 会诊科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "会诊科室名称字符长度不能超过64")]
    public string? ConsultationDeptName { get; set; }

    /// <summary>
    /// 会诊医生Id
    /// </summary>
    public long? ConsultationDoctorId { get; set; }

    /// <summary>
    /// 会诊医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "会诊医生名称字符长度不能超过64")]
    public string? ConsultationDoctorName { get; set; }

    /// <summary>
    /// 院外会诊机构名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "院外会诊机构名称字符长度不能超过64")]
    public string? OutsideHospitalName { get; set; }

    /// <summary>
    /// 收费项目Id
    /// </summary>
    [Required(ErrorMessage = "收费项目不能为空")]
    public long ItemId { get; set; }
}

/// <summary>
/// 会诊表删除输入参数
/// </summary>
public class DeleteConsultationInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 会诊表更新输入参数
/// </summary>
public class UpdateConsultationInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    [MaxLength(64, ErrorMessage = "就诊流水号字符长度不能超过64")]
    public string VisitNo { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string PatientName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [MaxLength(32, ErrorMessage = "性别字符长度不能超过32")]
    public string? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [Dict("AgeUnit", AllowNullValue = true)]
    [MaxLength(32, ErrorMessage = "年龄单位字符长度不能超过32")]
    public string? AgeUnit { get; set; }

    /// <summary>
    /// 期望会诊时间
    /// </summary>
    public DateTime? ExpectedTime { get; set; }

    /// <summary>
    /// 会诊类型
    /// </summary>
    [Dict("ConsultationType", AllowNullValue = true)]
    [Required(ErrorMessage = "会诊类型不能为空")]
    [MaxLength(32, ErrorMessage = "会诊类型字符长度不能超过32")]
    public string? Type { get; set; }

    /// <summary>
    /// 病情摘要
    /// </summary>
    [Required(ErrorMessage = "病情摘要不能为空")]
    public string? ClinicalSummary { get; set; }

    /// <summary>
    /// 会诊目的
    /// </summary>
    [Required(ErrorMessage = "会诊目的不能为空")]
    public string? Purpose { get; set; }

    /// <summary>
    /// 申请科室Id
    /// </summary>
    public long? ApplyDeptId { get; set; }

    /// <summary>
    /// 申请科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "申请科室名称字符长度不能超过64")]
    public string? ApplyDeptName { get; set; }

    /// <summary>
    /// 申请医生Id
    /// </summary>
    public long? ApplyDoctorId { get; set; }

    /// <summary>
    /// 申请医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "申请医生名称字符长度不能超过64")]
    public string? ApplyDoctorName { get; set; }

    /// <summary>
    /// 申请医生签名
    /// </summary>
    public string? ApplyDoctorSign { get; set; }

    /// <summary>
    /// 会诊科室Id
    /// </summary>
    [Required(ErrorMessage = "会诊科室不能为空")]
    public long? ConsultationDeptId { get; set; }

    /// <summary>
    /// 会诊科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "会诊科室名称字符长度不能超过64")]
    public string? ConsultationDeptName { get; set; }

    /// <summary>
    /// 会诊医生Id
    /// </summary>
    [Required(ErrorMessage = "会诊医生不能为空")]
    public long? ConsultationDoctorId { get; set; }

    /// <summary>
    /// 会诊医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "会诊医生名称字符长度不能超过64")]
    public string? ConsultationDoctorName { get; set; }

    /// <summary>
    /// 院外会诊机构名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "院外会诊机构名称字符长度不能超过64")]
    public string? OutsideHospitalName { get; set; }
}

/// <summary>
/// 会诊表主键查询输入参数
/// </summary>
public class QueryByIdConsultationInput : DeleteConsultationInput
{
}