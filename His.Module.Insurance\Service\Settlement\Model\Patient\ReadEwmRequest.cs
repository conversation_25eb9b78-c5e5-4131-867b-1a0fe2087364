namespace His.Module.Insurance.Service.Settlement.Model.Patient;
using His.Module.Insurance.Service.Settlement.Dto;

 

/// <summary>
/// 根据电子医保凭证二维码读取基本信息接口请求参数
/// </summary>
public class ReadEwmRequest:BaseSettlementRequest
{
    /// <summary>
    /// 电子医保凭证二维码（28位电子医保凭证）
    /// </summary>
    public string p_ewm { get; set; }

    /// <summary>
    /// 电子医保凭证业务码（传电子医保凭证二维码时需传入）<br/>
    /// 101：挂号<br/>
    /// 102：住院建档<br/>
    /// 103：入院登记<br/>
    /// 104：缴纳预缴金<br/>
    /// 201：问诊<br/>
    /// 202：预约检查<br/>
    /// 203：检查<br/>
    /// 204：治疗<br/>
    /// 301：结算<br/>
    /// 302：取药<br/>
    /// 303：取报告<br/>
    /// 304：打印票据和清单<br/>
    /// 305：病历材料复印<br/>
    /// 306：诊间核验身份
    /// </summary>
    public string p_dzpzywm { get; set; }
}