﻿namespace His.Module.Inpatient.Entity;

/// <summary>
/// 住院押金账户表
/// </summary>
[Tenant("*************")]
[SugarTable("inpatient_deposit_account", "住院押金账户表")]
public class InpatientDepositAccount : EntityTenantBaseData
{
    /// <summary>
    /// 入院登记ID
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_register_id", ColumnDescription = "入院登记ID")]
    public virtual long? InpatientRegisterId { get; set; }

    /// <summary>
    /// 住院号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_no", ColumnDescription = "住院号", Length = 100)]
    public virtual string InpatientNo { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }

    /// <summary>
    /// 累计缴纳总额
    /// </summary>
    [SugarColumn(ColumnName = "total_paid_amount", ColumnDescription = "累计缴纳总额", Length = 16, DecimalDigits = 2)]
    public virtual decimal? TotalPaidAmount { get; set; }

    /// <summary>
    /// 累计抵扣总额
    /// </summary>
    [SugarColumn(ColumnName = "total_used_amount", ColumnDescription = "累计抵扣总额", Length = 16, DecimalDigits = 2)]
    public virtual decimal? TotalUsedAmount { get; set; }

    /// <summary>
    /// 累计退款总额
    /// </summary>
    [SugarColumn(ColumnName = "total_refunded_amount", ColumnDescription = "累计退款总额", Length = 16, DecimalDigits = 2)]
    public virtual decimal? TotalRefundedAmount { get; set; }

    /// <summary>
    /// 实时余额
    /// </summary>
    [SugarColumn(ColumnName = "current_balance", ColumnDescription = "实时余额", Length = 16, DecimalDigits = 2)]
    public virtual decimal? CurrentBalance { get; set; }

    /// <summary>
    /// 账户状态: active, locked, closed
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "账户状态")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 账户关闭时间
    /// </summary>
    [SugarColumn(ColumnName = "closed_time", ColumnDescription = "账户关闭时间")]
    public virtual DateTime? ClosedTime { get; set; }

    /// <summary>
    /// 押金交易记录
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(DepositTransaction.AccountId))]
    public List<DepositTransaction> Transactions { get; set; }

}