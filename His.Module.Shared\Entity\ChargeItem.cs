﻿namespace His.Module.Shared.Entity;

/// <summary>
/// 收费项目表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("charge_item", "收费项目表")]
public class ChargeItem : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码", Length = 64)]
    public virtual string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 64)]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 32)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 32)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 32)]
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "规格", Length = 64)]
    public virtual string? Spec { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "单价", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Price { get; set; }

    /// <summary>
    /// 进价
    /// </summary>
    [SugarColumn(ColumnName = "purchase_price", ColumnDescription = "进价", Length = 16, DecimalDigits = 4)]
    public virtual decimal? PurchasePrice { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    [SugarColumn(ColumnName = "model", ColumnDescription = "型号", Length = 64)]
    public virtual string? Model { get; set; }

    /// <summary>
    /// 批件产品名称
    /// </summary>
    [SugarColumn(ColumnName = "approval_name", ColumnDescription = "批件产品名称", Length = 128)]
    public virtual string? ApprovalName { get; set; }

    /// <summary>
    /// 产地
    /// </summary>
    [SugarColumn(ColumnName = "producer", ColumnDescription = "产地", Length = 8)]
    public virtual string? Producer { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer", ColumnDescription = "生产厂家", Length = 256)]
    public virtual string? Manufacturer { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    [SugarColumn(ColumnName = "registration_number", ColumnDescription = "注册证号", Length = 128)]
    public virtual string? RegistrationNumber { get; set; }

    /// <summary>
    /// 物价编码
    /// </summary>
    [SugarColumn(ColumnName = "price_code", ColumnDescription = "物价编码", Length = 32)]
    public virtual string? PriceCode { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_id", ColumnDescription = "收费类别Id")]
    public virtual long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 核算类别Id
    /// </summary>
    [SugarColumn(ColumnName = "calculate_category_id", ColumnDescription = "核算类别Id")]
    public virtual long? CalculateCategoryId { get; set; }

    /// <summary>
    /// 电子发票费用类别
    /// </summary>
    [SugarColumn(ColumnName = "dzfp_charge_category", ColumnDescription = "电子发票费用类别", Length = 32)]
    public virtual string? DzfpChargeCategory { get; set; }

    /// <summary>
    /// 病案首页费用类别
    /// </summary>
    [SugarColumn(ColumnName = "basy_charge_category", ColumnDescription = "病案首页费用类别", Length = 32)]
    public virtual string? BasyChargeCategory { get; set; }

    /// <summary>
    /// 是否高值耗材  1是 2否
    /// </summary>
    [SugarColumn(ColumnName = "high_value", ColumnDescription = "是否高值耗材  1是 2否")]
    public virtual YesNoEnum HighValue { get; set; }

    /// <summary>
    /// 是否单用
    /// </summary>
    [SugarColumn(ColumnName = "use_separately", ColumnDescription = "是否单用")]
    public virtual YesNoEnum UseSeparately { get; set; }

    /// <summary>
    /// 是否上传地纬
    /// </summary>
    [SugarColumn(ColumnName = "upload_dw", ColumnDescription = "是否上传地纬")]
    public virtual YesNoEnum UploadDw { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    [SugarColumn(ColumnName = "frequency_id", ColumnDescription = "频次Id")]
    public virtual long? FrequencyId { get; set; }

    /// <summary>
    /// 样本类型
    /// </summary>
    [SugarColumn(ColumnName = "sample_type", ColumnDescription = "样本类型", Length = 32)]
    public virtual string? SampleType { get; set; }

    /// <summary>
    /// 护理等级
    /// </summary>
    [SugarColumn(ColumnName = "nurse_level", ColumnDescription = "护理等级", Length = 32)]
    public virtual string? NurseLevel { get; set; }

    /// <summary>
    /// 检查类别Id
    /// </summary>
    [SugarColumn(ColumnName = "check_category_id", ColumnDescription = "检查类别Id")]
    public virtual long? CheckCategoryId { get; set; }

    /// <summary>
    /// 检查部位Id
    /// </summary>
    [SugarColumn(ColumnName = "check_point_id", ColumnDescription = "检查部位Id")]
    public virtual long? CheckPointId { get; set; }

    /// <summary>
    /// 退费模式 0正常 1申请 2 审核
    /// </summary>
    [SugarColumn(ColumnName = "refund_mode", ColumnDescription = "退费模式 0正常 1申请 2 审核")]
    public virtual int? RefundMode { get; set; }

    /// <summary>
    /// 是否套餐 1是 2否
    /// </summary>
    [SugarColumn(ColumnName = "package", ColumnDescription = "是否套餐 1是 2否")]
    public virtual YesNoEnum Package { get; set; }

    /// <summary>
    /// 使用科室 无表示对应全部科室
    /// </summary>
    [SugarColumn(ColumnName = "use_depts", ColumnDescription = "使用科室 无表示对应全部科室")]
    public virtual long? UseDepts { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [SugarColumn(ColumnName = "usage_scope", ColumnDescription = "使用范围")]
    public virtual MedServiceCategoryEnum UsageScope { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; } = 100;
}