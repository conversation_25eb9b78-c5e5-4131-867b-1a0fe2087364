﻿using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 药品收费类别
/// </summary>
[Tenant("1300000000014")]
[SugarTable("drug_charge_category", "药品收费类别")]
public class DrugChargeCategory : EntityTenant
{
    /// <summary>
    /// 药品类别
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类别", Length = 12)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类别
    /// </summary>
    [SugarColumn(ColumnName = "drug_type_name", ColumnDescription = "药品类别", Length = 50)]
    public virtual string? DrugTypeName { get; set; }
    
    /// <summary>
    /// 收费类别
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_id", ColumnDescription = "收费类别")]
    public virtual long? ChargeCategoryId { get; set; }
    
    /// <summary>
    /// 收费类别编码
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_code", ColumnDescription = "收费类别编码", Length = 255)]
    public virtual string? ChargeCategoryCode { get; set; }
    
    /// <summary>
    /// 收费类别名称
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_name", ColumnDescription = "收费类别名称", Length = 255)]
    public virtual string? ChargeCategoryName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
