using His.Module.OutpatientDoctor.OtherModelEntity;
using His.Module.OutpatientDoctor.Service.Visit.Dto;

namespace His.Module.OutpatientDoctor.Service.Visit;

/// <summary>
/// 就诊服务服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class OutpatientVisitService(SqlSugarRepository<Register> mzRegisterRep)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 获取就诊信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取就诊信息")]
    [ApiDescriptionSettings(Name = "GetVisit"), HttpGet]
    public async Task<RegisterOutput> GetVisit([FromQuery] long? visitId, [FromQuery] string? visitNo)
    {
        var result = await mzRegisterRep.AsTenant().QueryableWithAttr<Register>()
            .LeftJoin<SysOrg>((u, a) => u.DeptId == a.Id)
            .LeftJoin<SysUser>((u, a, b) => u.DoctorId == b.Id)
            .LeftJoin<FeeCategory>((u, a, b, c) => u.FeeId == c.Id)
            .LeftJoin<RegCategory>((u, a, b, c, d) => u.RegCategoryId == d.Id)
            .LeftJoin<MedicalCardInfo>((u, a, b, c, d, e) => u.CardId == e.Id)
            .WhereIF(visitId > 0, u => u.Id == visitId)
            .WhereIF(!string.IsNullOrWhiteSpace(visitNo),
                u => u.VisitNo.Equals(visitNo, StringComparison.CurrentCultureIgnoreCase))
            .Select((u, a, b, c, d, e) => new RegisterOutput()
            {
                DeptName = a.Name,
                DoctorName = b.RealName,
                FeeName = c.Name,
                RegCategory = d.Name,
                CardNo = e.CardNo,
                Balance = e.Balance,
            }, true)
            .FirstAsync();


        return result;
    }
    /// <summary>
    /// 获取就诊信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取就诊信息")]
    [ApiDescriptionSettings(Name = "GetList"), HttpPost]
    public async Task<List<RegisterOutput>> GetList([FromBody] VisitQueryInput input)
    {
        var result = await mzRegisterRep.AsTenant().QueryableWithAttr<Register>()
            .WhereIF(input.VisitTimeRange?.Length == 2,// 就诊时间
                u => u.CreateTime >= input.VisitTimeRange[0] && u.CreateTime <= input.VisitTimeRange[1])
            .LeftJoin<SysOrg>((u, a) => u.DeptId == a.Id)
            .LeftJoin<SysUser>((u, a, b) => u.DoctorId == b.Id)
            .LeftJoin<FeeCategory>((u, a, b, c) => u.FeeId == c.Id)
            .LeftJoin<RegCategory>((u, a, b, c, d) => u.RegCategoryId == d.Id)
            .LeftJoin<MedicalCardInfo>((u, a, b, c, d, e) => u.CardId == e.Id)
            .WhereIF(input.VisitId > 0, u => u.Id == input.VisitId )
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo),
                u => u.VisitNo.Equals(input.VisitNo, StringComparison.CurrentCultureIgnoreCase))
            .WhereIF(input.CardId > 0, u => u.CardId == input.CardId )
            .WhereIF(!string.IsNullOrWhiteSpace(input.CardNo),
                (u, a, b, c, d, e)
                    => e.CardNo.Equals(input.CardNo, StringComparison.CurrentCultureIgnoreCase))
            .Select((u, a, b, c, d, e) => new RegisterOutput()
            {
                DeptName = a.Name,
                DoctorName = b.RealName,
                FeeName = c.Name,
                RegCategory = d.Name,
                CardNo = e.CardNo,
                Balance = e.Balance,
            }, true)
            .OrderBy(u => u.Id, OrderByType.Desc)
            .ToListAsync();


        return result;
    }
}