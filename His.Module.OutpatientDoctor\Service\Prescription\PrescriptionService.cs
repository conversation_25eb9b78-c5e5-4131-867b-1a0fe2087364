﻿using Furion.DatabaseAccessor;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.OutpatientDoctor.Api.Prescription;
using His.Module.OutpatientDoctor.Api.Prescription.Dto;
using His.Module.OutpatientDoctor.Dto;
using His.Module.OutpatientDoctor.OtherModelEntity;
using His.Module.Pharmacy.Api.DrugInventory;
using His.Module.Pharmacy.Api.DrugInventory.Dto;
namespace His.Module.OutpatientDoctor.Service;

/// <summary>
/// 处表服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class PrescriptionService(
    IChargeApi chargeApi,
    UserManager userManager,
    ChargeService chargeService,
    SqlSugarRepository<DrugStorage> drugStorageRep,
    SqlSugarRepository<PrescriptionMain> prescriptionMainRep,
    SqlSugarRepository<PrescriptionDetail> prescriptionDetailRep,
    IDrugInventoryApi drugInventoryApi,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient, IPrescriptionApi
{
    private readonly ISqlSugarClient _sqlSugarClient = sqlSugarClient;

    /// <summary>
    /// 患者处方列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询处方主表")]
    [ApiDescriptionSettings(Name = "ListOfPatient"), HttpPost]
    public async Task<List<PrescriptionMainOutput>> ListOfPatient(PagePrescriptionInput input)
    {
        if (string.IsNullOrWhiteSpace(input.VisitNo) && string.IsNullOrWhiteSpace(input.CardNo)
                                                     && string.IsNullOrWhiteSpace(input.OutpatientNo) &&
                                                     input.PatientId is null)
            return [];

        var query = prescriptionMainRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrescriptionNo),
                u => u.PrescriptionNo.Contains(input.PrescriptionNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName),
                u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(input.PrescriptionTimeRange?.Length == 2,
                u => u.PrescriptionTime >= input.PrescriptionTimeRange[0] &&
                     u.PrescriptionTime <= input.PrescriptionTimeRange[1])
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.StorageId != null && !input.StorageId.Equals(0L), u => u.StorageId == input.StorageId)
            .WhereIF(input.RegisterId != null, u => u.RegisterId == input.RegisterId)
            .WhereIF(input.BillingDeptId != null, u => u.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.BillingDoctorId != null, u => u.BillingDoctorId == input.BillingDoctorId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo == input.VisitNo)
            .WhereIF(!string.IsNullOrWhiteSpace(input.CardNo), u => u.CardNo == input.CardNo)
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo), u => u.OutpatientNo == input.OutpatientNo)
            .WhereIF(input.Status != null, u => input.Status.Contains(u.Status))
            .Select<PrescriptionMainOutput>();
        return await query.OrderByDescending(u => u.Id).ToListAsync();
    }

    /// <summary>
    /// 患者处方列表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询处方主表")]
    [ApiDescriptionSettings(Name = "ListOfSendDrug"), HttpPost]
    public async Task<List<PrescriptionMainForSendDrugOutput>> ListOfSendDrug(PagePrescriptionInput input)
    {
        if (string.IsNullOrWhiteSpace(input.VisitNo) && string.IsNullOrWhiteSpace(input.CardNo)
                                                     && string.IsNullOrWhiteSpace(input.OutpatientNo) &&
                                                     input.PatientId is null)
            return [];

        var orgId=long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value ?? "0");
        var storage = await drugStorageRep.AsQueryable().Where(
                    u => u.OrgId ==orgId)
                .ToListAsync()
            ;
        if (storage.Count == 0)
            throw Oops.Oh("未配置药品药房");
        input.StorageId=storage.First().Id;
        var query = prescriptionMainRep
            .AsTenant().QueryableWithAttr<PrescriptionMain>()
            .LeftJoin<Register>((u,r)=>u.RegisterId==r.Id)
            .LeftJoin<FeeCategory>((u, r, f) => r.FeeId==f.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrescriptionNo),
                u => u.PrescriptionNo.Contains(input.PrescriptionNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName),
                u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(input.PrescriptionTimeRange?.Length == 2,
                u => u.PrescriptionTime >= input.PrescriptionTimeRange[0] &&
                     u.PrescriptionTime <= input.PrescriptionTimeRange[1])
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .Where(  u => u.StorageId == input.StorageId)
            .WhereIF(input.RegisterId != null, u => u.RegisterId == input.RegisterId)
            .WhereIF(input.BillingDeptId != null, u => u.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.BillingDoctorId != null, u => u.BillingDoctorId == input.BillingDoctorId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo == input.VisitNo)
            .WhereIF(!string.IsNullOrWhiteSpace(input.CardNo), u => u.CardNo == input.CardNo)
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo), u => u.OutpatientNo == input.OutpatientNo)
            .WhereIF(input.Status != null, u => input.Status.Contains(u.Status))
            .Select((u, r,f )=> new PrescriptionMainForSendDrugOutput()
            {
                FeeId=f.Id,
                FeeName =f.Name,
                IdCardNo= r.IdCardNo,
                MedCategory=(Int16)f.MedCategory,
                MedicalInsuranceFlag=f.MedicalInsuranceFlag,
                MedicalInsuranceFlagName=f.MedicalInsuranceFlagName,
                MedicalPoolingCategory=f.MedicalPoolingCategory,
                MedicalPoolingCategoryName=f.MedicalPoolingCategoryName,
               
             
            },true);
        return await query.OrderByDescending(u => u.Id).ToListAsync();
    }

    /// <summary>
    /// 获取处方主表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取处方主表详情")]
    [ApiDescriptionSettings(Name = "Get"), HttpGet]
    public async Task<PrescriptionOutput> Get([FromQuery] QueryByIdPrescriptionMainInput input)
    {
        var main = await prescriptionMainRep.GetFirstAsync(u => u.Id == input.Id);

        var details = await prescriptionDetailRep.AsQueryable()
            .Where(u => u.PrescriptionId == input.Id)
            .Select<PrescriptionDetailOutput>()
            .ToListAsync();
        return new PrescriptionOutput { Main = main.Adapt<PrescriptionMainOutput>(), Details = details };
    }


    [ApiDescriptionSettings(IgnoreApi = true)]
    public async Task<PrescriptionMainDto> GetPrescription(long prescriptionId)
    {
        var main = await prescriptionMainRep.GetFirstAsync(u => u.Id == prescriptionId);
        if (main == null)
        {
            throw Oops.Oh("处方不存在");
        }

        var details = await prescriptionDetailRep.AsQueryable()
            .Where(u => u.PrescriptionId == prescriptionId)
            .Select<PrescriptionDetailOutput>()
            .ToListAsync();
        var r = main.Adapt<PrescriptionMainDto>();
        r.Details = details.Adapt<List<PrescriptionDetailDto>>();
        return r;
    }
   

    /// <summary>
    /// 获取处方主表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取处方主表详情")]
    [ApiDescriptionSettings(Name = "GetDetail"), HttpGet]
    public async Task<List<PrescriptionDetailOutput>> GetDetail([FromQuery] QueryByIdPrescriptionMainInput input)
    {
        var details = await prescriptionDetailRep.AsQueryable()
            .Where(u => u.PrescriptionId == input.Id)
            .Select<PrescriptionDetailOutput>()
            .ToListAsync();
        return details;
    }

    /// <summary>
    /// 增加处方表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加处方主表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<long> Add(AddPrescriptionInput input)
    {
        var entity = input.Main.Adapt<PrescriptionMain>();
        entity.PrescriptionNo =
            await prescriptionMainRep.Context.Ado.GetStringAsync(
                $"SELECT '{DateTime.Now:yyyyMM}'||LPAD(CAST(NEXTVAL('prescription_main_no_seq')As varchar),7,'0')");
        entity.PrescriptionTime = DateTime.Now;
        entity.BillingDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId)?.Value ?? "0");
        entity.BillingDeptName = App.User.FindFirst(ClaimConst.OrgName)?.Value;
        entity.BillingDoctorId = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");
        entity.BillingDoctorName = App.User.FindFirst(ClaimConst.RealName)?.Value;
        entity.Status = 1; // 默认为1
        // 默认单个药房 
        entity.StorageId = input.Details.FirstOrDefault()?.StorageId;
        entity.StorageName = input.Details.FirstOrDefault()?.StorageName;
        entity.TotalAmount = input.Details.Sum(p =>
            p.Price * p.Quantity * (entity.HerbsQuantity is null or 0 ? 1 : entity.HerbsQuantity));
        entity.Id = 0; //修改时重新插入
        var prescriptionId = await prescriptionMainRep.InsertAsync(entity) ? entity.Id : 0;

        var details = input.Details.Adapt<List<PrescriptionDetail>>();
        foreach (var item in details)
        {
            item.Id = 0; //修改时重新插入
            item.PrescriptionId = prescriptionId;
            // 处理库存
            var lockDto = item.Adapt<LockDrugInventoryInput>();
            lockDto.PrescriptionDetailId = await prescriptionDetailRep.InsertAsync(item) ? item.Id : 0;
            await drugInventoryApi.LockInventory(lockDto);
        }
        // 保存费用信息 
        await this.SaveChargeInfo( entity,details);

        return prescriptionId;
    }

    /// <summary>
    /// 更新处方主表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新处方主表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [UnitOfWork]
    public async Task<long> Update(AddPrescriptionInput input)
    {
        var record = await prescriptionMainRep.GetFirstAsync(u => u.Id == input.Main.Id);
        if (record.Status != 1)
        {
            throw new Exception("当前状态无法修改处方");
        }

        // 删除处方移除锁定库存
        await DeletePrescription(record);


        await Add(input);
        return record.Id;
        //   await prescriptionDetailRep.DeleteAsync(u => u.PrescriptionId == input.Main.Id);
        // var details = input.Details.Adapt<List<PrescriptionDetail>>();
        //
        //
        // foreach (var item in details)
        // {
        //     item.PrescriptionId = input.Main.Id;
        //     if (item.Id.Equals(0L))
        //     {
        //         await prescriptionDetailRep.InsertAsync(item);
        //     }
        //     else
        //     {
        //         await prescriptionDetailRep.AsUpdateable(item)     
        //             .ExecuteCommandAsync();
        //
        //         // await prescriptionDetailRep.AsUpdateable(item).IgnoreColumns(ignoreAllNullColumns:true)    
        //         //     .ExecuteCommandAsync();
        //
        //     }
        // }
        //
        // var entity = input.Main.Adapt<PrescriptionMain>();
        // entity.TotalAmount = input.Details.Sum(p =>
        //     p.Price * p.Quantity * (entity.HerbsQuantity is null or 0 ? 1 : entity.HerbsQuantity));
        // await prescriptionMainRep.AsUpdateable(entity)     
        //     .ExecuteCommandAsync();
        //   // await prescriptionMainRep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns:true)    
        //   //    .ExecuteCommandAsync();
        // return input.Main.Id ?? 0;
    }

    [NonAction]
    [ApiDescriptionSettings(IgnoreApi = true)]
    private async Task DeletePrescription(PrescriptionMain record)
    {
        if (record.Status > 1)
        {
            throw new Exception("当前状态禁止删除");
        }

        // 删除重新插入
        await prescriptionDetailRep.FakeDeleteAsync(record);
        var details = await prescriptionDetailRep.AsQueryable().Where(u => u.PrescriptionId == record.Id)
            .ToListAsync();

        foreach (var item in details)
        {
            // 移除锁定库存
            var removeDto = item.Adapt<LockDrugInventoryInput>();
            removeDto.PrescriptionDetailId = item.Id;
            await drugInventoryApi.RemoveLockInventory(removeDto);
        }

        await prescriptionDetailRep.FakeDeleteAsync(details);
        // 删除费用明细
        await chargeService.Delete(record.Id);
    }

    /// <summary>
    /// 删除处方主表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除处方主表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeletePrescriptionMainInput input)
    {
        var entity = await prescriptionMainRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status is 1 or 0)
        {
            await DeletePrescription(entity);  
        }
        else
            throw Oops.Oh("当前状态禁止删除");


        //await _prescriptionMainRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 删除处方明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除处方明细表")]
    [ApiDescriptionSettings(Name = "DeleteDetail"), HttpPost]
    public async Task DeleteDetail(DeletePrescriptionDetailInput input)
    {
        var detail = await prescriptionDetailRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        var main = await prescriptionMainRep.GetFirstAsync(u => u.Id == detail.PrescriptionId) ??
                   throw Oops.Oh(ErrorCodeEnum.D1002);
        if (main.Status is 1 or 0)
        {
            // 移除锁定库存

            var removeDto = detail.Adapt<LockDrugInventoryInput>();
            removeDto.PrescriptionDetailId = detail.Id;
            await drugInventoryApi.RemoveLockInventory(removeDto);
            await prescriptionDetailRep.FakeDeleteAsync(detail); //假删除
        }
        else
            throw Oops.Oh("当前状态禁止删除");

        //await _prescriptionDetailRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 处方发药时调用，修改处方状态
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns> 
    [ApiDescriptionSettings(IgnoreApi = true)]
    public async Task<bool> PrescriptionSendDrug(PrescriptionSendDrugDto dto)
    {
        var main = await prescriptionMainRep.GetFirstAsync(u => u.Id == dto.PrescriptionId);
        if (main == null)
        {
            throw Oops.Oh("未查询到处方信息！");
        }

        if (main.Status != 2)
        {
            throw Oops.Oh("当前状态[未收费]无法进行发药操作");
        }

        var r = await prescriptionMainRep.UpdateAsync(
            u =>
                new PrescriptionMain() { Status = 3, SendUserId = dto.SendUserId },
            u => u.Id == dto.PrescriptionId
                 && u.Status == 2
        );
        if (!r)
        {
            throw Oops.Oh($"发药失败，请稍后重试！");
        }

        // 回写收费执行科室
        await chargeApi.ExecuteStatus(new OutpatientChargeExecuteDto()
        {
            ChargeId = main.ChargeMainId,
            ExecuteDeptId = dto.ExecuteDeptId, // 执行科室
            ExecuteDoctorId = dto.SendUserId, // 执行医生 
            ExecuteTime = DateTime.Now // 发药时间
        });
        return r;
    }

    /// <summary>
    /// 退药 后修改处方状态，再退费 
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(IgnoreApi = true)]
    public async Task<bool> PrescriptionRefundDrug(PrescriptionRefundDrugDto dto)
    {
        var main = await prescriptionMainRep.GetFirstAsync(u => u.Id == dto.PrescriptionId);
        if (main == null)
        {
            throw Oops.Oh("未查询到处方信息！");
        }

        if (main.Status != 3 && main.Status != 2)
        {
            throw Oops.Oh("当前状态非已收费或已发药状态，无法进行退费操作");
        }
       
        var r = await prescriptionMainRep.UpdateAsync(
            u =>
                new PrescriptionMain() { Status =5   , RefundUserId = dto.RefundUserId,RefundReason = dto.Reason},
            u => u.Id == dto.PrescriptionId
 
        );
        if (!r)
        {
            throw Oops.Oh($"退药失败！");
        }

 
        return r;
    }

    [DisplayName("处方计费")]
    [ApiDescriptionSettings(Name = "Charge"), HttpPost, UnitOfWork]
    public async Task<OutpatientChargeResult> Charge(OutpatientChargeConfirmDto input)
    {
        // 修改收费状态
        var result = await chargeApi.ConfirmStatus(input);

        if (input.BillingId ==0)
        {
            input.BillingId = result.BillingId;
        }

        // 修改处方状态
        await prescriptionMainRep.UpdateAsync(u =>
            new PrescriptionMain()
            {
                Status = 2,
                ChargeStaffId = userManager.UserId,
                ChargeTime =result.ChargeTime
            }, u => u.Id == input.BillingId);
        return result;

    }

    public async Task SetStatus(QueryByIdPrescriptionMainInput input)
    {
        // 修改处方状态
        await prescriptionMainRep.UpdateAsync(u =>
            new PrescriptionMain
            {
                Status = 2, ChargeStaffId = userManager.UserId, ChargeTime = DateTime.Now
            }, u => u.Id == input.Id);
    }
     private async Task<OutpatientChargeResult> SaveChargeInfo( PrescriptionMain main, List<PrescriptionDetail> prescriptionDetails)
    {
         

        var dto = new OutpatientChargeDto();

        dto.PatientId = main.PatientId;
        dto.RegisterId = main.RegisterId;
        dto.TotalAmount = prescriptionDetails.Sum(u =>
            u.Quantity * u.Price * (main.HerbsQuantity is 0 or null ? 1 : main.HerbsQuantity));
        dto.Details = [];
        // 新增
        dto.CardNo = main.CardNo;
        dto.VisitNo = main.VisitNo;
        dto.OutpatientNo = main.OutpatientNo;
        dto.ExecuteDeptId = main.StorageId;
        // dto.ExecuteDoctorId = main.ExecuteDoctorId;
        dto.BillingDeptId = main.BillingDeptId;
        dto.BillingDoctorId = main.BillingDoctorId;
        dto.BillingTime = main.PrescriptionTime;
        dto.BillingType = "Prescription";
        dto.BillingId = main.Id;
        dto.BillingNo = main.PrescriptionNo;
        dto.InvoiceNumber = await chargeApi.GetInvoiceNumber();
        
        foreach (var prescriptionDetail in prescriptionDetails)
        {
            var chargeDetail = prescriptionDetail.Adapt<OutpatientChargeDetailDto>();
            chargeDetail.ChargeCategoryCode = "00" + prescriptionDetail.DrugType;

            chargeDetail.ItemId = prescriptionDetail.DrugId;
            chargeDetail.ItemName = prescriptionDetail.DrugName;
            chargeDetail.ItemCode = prescriptionDetail.DrugCode;
            chargeDetail.Spec = prescriptionDetail.Spec;
            // chargeDetail.Unit = prescriptionDetail.Unit;// 如果需要设置单位，需取消注释并确保字段存在
            chargeDetail.Quantity = Convert.ToDecimal(prescriptionDetail.Quantity);
            chargeDetail.Amount = prescriptionDetail.Quantity * prescriptionDetail.Price *
                                  (main.HerbsQuantity is 0 or null ? 1 : main.HerbsQuantity);
            chargeDetail.Price = prescriptionDetail.Price;

            chargeDetail.PharmacyId = prescriptionDetail.StorageId;
            chargeDetail.HerbsQuantity = main.HerbsQuantity;
            chargeDetail.BillingNo = main.PrescriptionNo;
            chargeDetail.BillingTime = main.PrescriptionTime;
            chargeDetail.BillingId = main.Id;
            chargeDetail.BillingDetailId = prescriptionDetail.Id;
            chargeDetail.ExecuteDeptId = prescriptionDetail.StorageId; // 执行科室药房
            // chargeDetail.ExecuteDoctorId = prescriptionDetail.SendUserId; // 发药药师（如果需要）
            chargeDetail.BillingDeptId = main.BillingDeptId; // 开单科室
            chargeDetail.BillingDoctorId = main.BillingDoctorId; // 开单医生
            dto.Details.Add(chargeDetail);
        }

        var result = await chargeApi.Add(dto);
        await prescriptionMainRep.UpdateAsync(u =>
            new PrescriptionMain()
            {
                Status = 1,//默认未收费
 
                ChargeMainId = result.ChargeId
            }, u => u.Id == main.Id);
        return result;
    }
}