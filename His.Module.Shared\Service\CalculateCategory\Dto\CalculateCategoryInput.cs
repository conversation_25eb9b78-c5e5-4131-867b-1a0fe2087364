﻿namespace His.Module.Shared.Service;

/// <summary>
/// 核算类别分页查询输入参数
/// </summary>
public class PageCalculateCategoryInput : BasePageInput
{
    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }
}

/// <summary>
/// 核算类别增加输入参数
/// </summary>
public class AddCalculateCategoryInput : CalculateCategory
{
}

/// <summary>
/// 核算类别删除输入参数
/// </summary>
public class DeleteCalculateCategoryInput : BaseIdInput
{
}

/// <summary>
/// 核算类别更新输入参数
/// </summary>
public class UpdateCalculateCategoryInput : AddCalculateCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public override long Id { get; set; }
}

/// <summary>
/// 核算类别主键查询输入参数
/// </summary>
public class QueryByIdCalculateCategoryInput : DeleteCalculateCategoryInput
{
}

/// <summary>
/// 设置核算类别状态输入参数
/// </summary>
public class SetStatusCalculateCategoryInput : BaseIdInput
{
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }
}