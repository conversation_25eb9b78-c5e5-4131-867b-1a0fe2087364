﻿import {useBaseApi} from '/@/api/base';

// 床位信息接口服务
export const useBedInfoApi = () => {
	const baseApi = useBaseApi("bedInfo");
	return {
		// 分页查询床位信息
		page: baseApi.page,
		// 查看床位信息详细
		detail: baseApi.detail,
		// 新增床位信息
		add: baseApi.add,
		// 更新床位信息
		update: baseApi.update,
		// 设置床位信息状态
		setStatus: baseApi.setStatus,
		// 删除床位信息
		delete: baseApi.delete,
		// 批量删除床位信息
		batchDelete: baseApi.batchDelete,
		// 导出床位信息数据
		exportData: baseApi.exportData,
		// 导入床位信息数据
		importData: baseApi.importData,
		// 下载床位信息数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
		// 获取下拉列表数据
		getDropdownData: (fromPage: Boolean = false, cancel: boolean = false) => baseApi.dropdownData({ fromPage }, cancel),
	}
}

// 床位信息实体
export interface BedInfo {
	// 主键Id
	id: number;
	// 床位编号
	bedNo?: string;
	// 部门id
	deptId?: number;
	// 部门名称
	deptName?: string;
	// 病房id
	wardId?: number;
	// 病房名称
	wardName?: string;
	// 房间编号
	roomNo: string;
	// 床位类型
	bedType: string;
	// 床位状态
	bedStatus: string;
	// 床位等级
	bedLevel: string;
	// 排序
	orderNo?: number;
	// 状态
	status: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}