using His.Module.MedicalTech;

namespace His.Module.OutpatientDoctor.Service.Dto;

 
/// <summary>
/// 检查增加输入参数
/// </summary>
public class TemplateExaminationInput
{
    
    public long? Id { get; set; }
    
    /// <summary>
    /// 检查名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "template_name", ColumnDescription = "检查名称", Length = 64)]
    public virtual string TemplateName { get; set; }
    /// <summary>
    /// 1 全院/ 2 科室/3 个人模板"
    /// </summary>
    [SugarColumn(ColumnName = "template_scope", ColumnDescription = "1 全院/ 2 科室/3 个人模板")]
    public virtual int? TemplateScope { get; set; }
    /// <summary>
    /// 检查类别Id
    /// </summary>
    [Required(ErrorMessage = "检查类别Id不能为空")]
    public long? CheckCategoryId { get; set; }

    /// <summary>
    /// 检查类别名称
    /// </summary>
    [Required(ErrorMessage = "检查类别名称不能为空")]
    [MaxLength(64, ErrorMessage = "检查类别名称字符长度不能超过64")]
    public string? CheckCategoryName { get; set; }

    /// <summary>
    /// 检查部位Id
    /// </summary>
    public long? CheckPointId { get; set; }

    /// <summary>
    /// 检查部位名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "检查部位名称字符长度不能超过64")]
    public string? CheckPointName { get; set; }

    /// <summary>
    /// 检查目的
    /// </summary>
    [MaxLength(200, ErrorMessage = "检查目的字符长度不能超过200")]
    public string? CheckObjective { get; set; }

 
 

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注字符长度不能超过256")]
    public string? Remark { get; set; }

    /// <summary>
    /// 门诊住院标识
    /// </summary>
    public int? Flag { get; set; }
 

    /// <summary>
    /// 执行科室Id
    /// </summary>
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "执行科室名称字符长度不能超过64")]
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    [MaxLength(100, ErrorMessage = "执行科室地址字符长度不能超过100")]
    public string? ExecuteDeptAddress { get; set; }
 

    /// <summary>
    /// 检查明细列表
    /// </summary>
    public List<AddExaminationDetailsInput> ExaminationDetails { get; set; }
}
