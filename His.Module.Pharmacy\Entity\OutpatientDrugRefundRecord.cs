﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 门诊退药记录表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("outpatient_drug_refund_record", "门诊退药记录表")]
public class OutpatientDrugRefundRecord : EntityTenant
{
    /// <summary>
    /// 退药单号
    /// </summary>
    [SugarColumn(ColumnName = "refund_no", ColumnDescription = "退药单号", Length = 100)]
    public virtual string? RefundNo { get; set; }
    
    /// <summary>
    /// 关联的发药记录ID
    /// </summary>
    [SugarColumn(ColumnName = "send_record_id", ColumnDescription = "关联的发药记录ID")]
    public virtual long? SendRecordId { get; set; }
    
    /// <summary>
    /// 退药人ID
    /// </summary>
    [SugarColumn(ColumnName = "refund_user_id", ColumnDescription = "退药人ID")]
    public virtual long? RefundUserId { get; set; }
    
    /// <summary>
    /// 退药人名称
    /// </summary>
    [SugarColumn(ColumnName = "refund_user_name", ColumnDescription = "退药人名称", Length = 100)]
    public virtual string? RefundUserName { get; set; }
    
    /// <summary>
    /// 退药时间
    /// </summary>
    [SugarColumn(ColumnName = "refund_time", ColumnDescription = "退药时间")]
    public virtual DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退药申请id
    /// </summary>
    [SugarColumn(ColumnName = "refund_apply_id", ColumnDescription = "退药申请id")]
    public virtual long? RefundApplyId { get; set; }
    
    /// <summary>
    /// 审核完成时间
    /// </summary>
    [SugarColumn(ColumnName = "audit_time", ColumnDescription = "审核完成时间")]
    public virtual DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 退药原因
    /// </summary>
    [SugarColumn(ColumnName = "reason", ColumnDescription = "退药原因", Length = 0)]
    public virtual string? Reason { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "药房ID")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "药房名称", Length = 100)]
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者名称", Length = 100)]
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 100)]
    public virtual string? VisitNo { get; set; }
    
    /// <summary>
    /// 就诊id
    /// </summary>
    [SugarColumn(ColumnName = "visit_id", ColumnDescription = "就诊id")]
    public virtual long? VisitId { get; set; }
    
    /// <summary>
    /// 卡号
    /// </summary>
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "卡号", Length = 100)]
    public virtual string? CardNo { get; set; }
    
    /// <summary>
    /// 卡id
    /// </summary>
    [SugarColumn(ColumnName = "card_id", ColumnDescription = "卡id")]
    public virtual long? CardId { get; set; }
    
    /// <summary>
    /// 处方ID
    /// </summary>
    [SugarColumn(ColumnName = "prescription_id", ColumnDescription = "处方ID")]
    public virtual long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    [SugarColumn(ColumnName = "prescription_detail_id", ColumnDescription = "处方明细ID")]
    public virtual long? PrescriptionDetailId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品ID")]
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 100)]
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "药品规格", Length = 100)]
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "药品单位", Length = 100)]
    public virtual string? Unit { get; set; }
    /// <summary>
    /// 退药数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "发药数量")]
    public virtual decimal? Quantity { get; set; }
    /// <summary>
    /// 退药数量
    /// </summary>
    [SugarColumn(ColumnName = "refund_quantity", ColumnDescription = "退药数量")]
    public virtual decimal? RefundQuantity { get; set; }
    
        
    /// <summary>
    /// 库存ID
    /// </summary>
    [SugarColumn(ColumnName = "inventory_id", ColumnDescription = "库存ID")]
    public virtual long? InventoryId { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "零售价", Length = 16, DecimalDigits=4)]
    public virtual decimal? Price { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>
    [SugarColumn(ColumnName = "refund_amount", ColumnDescription = "总退药金额", Length = 20, DecimalDigits=4)]
    public virtual decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnName = "batch_no", ColumnDescription = "批号", Length = 100)]
    public virtual string? BatchNo { get; set; }
    
}
