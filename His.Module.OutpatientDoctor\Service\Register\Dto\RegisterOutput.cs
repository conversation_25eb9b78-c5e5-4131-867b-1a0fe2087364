﻿using His.Module.Shared.Api.Enum;

namespace His.Module.OutpatientDoctor.Service;

public class RegisterOutput : Register
{
    /// <summary>
	/// 科室名称
	/// </summary>
	public string DeptName { get; set; }

    /// <summary>
    /// 医生名称
    /// </summary>
    public string Doctor<PERSON>ame { get; set; }

    /// <summary>
    /// 费别
    /// </summary>
    public string FeeName { get; set; }

    /// <summary>
    /// 号别
    /// </summary>
    public string RegCategory { get; set; }

   
    
    
    /// <summary>
    /// 余额
    /// </summary> 
    public decimal? Balance { get; set; }
     
    /// <summary>
    /// 医疗类别
    /// </summary>
 
    public   MedCategoryEnum? MedCategory { get; set; }
 

   
    /// <summary>
    /// 医疗统筹类别
    /// </summary> 
    public   string? MedicalPoolingCategory { get; set; }
    /// <summary>
    /// 医疗统筹类别名称
    /// </summary> 
    public   string? MedicalPoolingCategoryName { get; set; }
    
    /// <summary>
    /// 医疗统筹类别
    /// </summary> 
    public   string? MedicalInsuranceFlag { get; set; }
    /// <summary>
    /// 医疗统筹类别名称
    /// </summary> 
    public   string? MedicalInsuranceFlagName { get; set; }
}