﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 特殊处理记服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class StorageSpecialRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<StorageSpecialRecord> _storageSpecialRecordRep;
    private readonly SqlSugarRepository<StorageSpecialDetail> _storageSpecialDetailRep;
    private readonly SqlSugarRepository<DrugStorage> _drugStorageRep;
    private readonly InventoryService _inventoryService;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public StorageSpecialRecordService(
        SqlSugarRepository<StorageSpecialRecord> storageSpecialRecordRep,
        SqlSugarRepository<StorageSpecialDetail> storageSpecialDetailRep,
        SqlSugarRepository<DrugStorage> drugStorageRep,
        InventoryService inventoryService,
        ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _storageSpecialRecordRep = storageSpecialRecordRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
        _storageSpecialDetailRep = storageSpecialDetailRep;
        _drugStorageRep = drugStorageRep;
        _inventoryService = inventoryService;
    }

    /// <summary>
    /// 分页查询特殊处理记 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询特殊处理记")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<StorageSpecialRecordOutput>> Page(PageStorageSpecialRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _storageSpecialRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.HandleNo.Contains(input.Keyword) || u.DrugType.Contains(input.Keyword) ||
                     u.HandleType.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.HandleNo), u => u.HandleNo.Contains(input.HandleNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.HandleType), u => u.HandleType.Contains(input.HandleType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.StorageId != null, u => u.StorageId == input.StorageId)
            .WhereIF(input.SupplierId != null, u => u.SupplierId == input.SupplierId)
            .WhereIF(input.HandleTimeRange?.Length == 2,
                u => u.HandleTime >= input.HandleTimeRange[0] && u.HandleTime <= input.HandleTimeRange[1])
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<DrugStorage>((u, storage) => u.StorageId == storage.Id)
            .LeftJoin<EnterpriseDictionary>((u, storage, supplier) => u.SupplierId == supplier.Id)
            .Select((u, storage, supplier) => new StorageSpecialRecordOutput
            {
                Id = u.Id,
                StorageId = u.StorageId,
                StorageFkDisplayName = $"{storage.StorageName}",
                StorageCode = u.StorageCode,
                StorageName = u.StorageName,
                HandleNo = u.HandleNo,
                DrugType = u.DrugType,
                SupplierId = u.SupplierId,
                SupplierFkDisplayName = $"{supplier.EnterpriseName}",
                SupplierCode = u.SupplierCode,
                SupplierName = u.SupplierName,
                HandleType = u.HandleType,
                HandleTime = u.HandleTime,
                TotalPurchasePrice = u.TotalPurchasePrice,
                TotalSalePrice = u.TotalSalePrice,
                Remark = u.Remark,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取特殊处理记详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取特殊处理记详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<StorageSpecialRecord> Detail([FromQuery] QueryByIdStorageSpecialRecordInput input)
    {
        return await _storageSpecialRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加特殊处理记 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加特殊处理记")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<long> Add(AddStorageSpecialRecordInput input)
    {
        var entity = input.Adapt<StorageSpecialRecord>();
        entity.HandleNo =
            await _storageSpecialRecordRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('storage_handle_no_seq')As varchar),7,'0')");
        entity.HandleTime = DateTime.Now;
        entity.Status = 0;
        var recordId = await _storageSpecialRecordRep.InsertAsync(entity) ? entity.Id : 0;

      var storage=  await _drugStorageRep.GetFirstAsync(u => u.Id == entity.StorageId);
      entity.StorageCode= storage.StorageCode;
      entity.StorageName= storage.StorageName;
        await SaveDetail(input.Details.Adapt<List<StorageSpecialDetail>>(), entity);
        await _storageSpecialRecordRep.UpdateAsync(u
            => new StorageSpecialRecord()
            {
                TotalSalePrice = entity.TotalSalePrice, HandleTime = DateTime.Now,
                TotalPurchasePrice = entity.TotalPurchasePrice
            }, u => u.Id == entity.Id);
        return recordId;
    }

    async Task SaveDetail(List<StorageSpecialDetail> details, StorageSpecialRecord entity)
    {
        await _storageSpecialDetailRep.DeleteAsync(u => u.HandleId == entity.Id);
        foreach (var item in details)
        {
            item.HandleNo = entity.HandleNo;
            item.HandleId = entity.Id;
            item.Id = 0;
            if (item.Quantity == null || item.Quantity == 0)
            {
                throw Oops.Oh("数量不能为0");
            }
        }

        var totalSalePrice = details.Sum(p => p.TotalSalePrice);
        var totalPurchasePrice = details.Sum(p => p.TotalPurchasePrice);
        entity.TotalSalePrice = totalSalePrice;
        entity.TotalPurchasePrice = totalPurchasePrice;
        await _storageSpecialDetailRep.InsertRangeAsync(details);
    }

    /// <summary>
    /// 提交处理单 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("提交处理单")]
    [ApiDescriptionSettings(Name = "Submit"), HttpPost]
    [UnitOfWork]
    public async Task<bool> Submit(SubmitStorageSpecialRecordInput input)
    {
        var result = await _storageSpecialRecordRep.GetFirstAsync(u => u.Id == input.Id);

        if (result.Status == 0)
        {
            // 是否需要审核
            var storage = await _drugStorageRep.GetFirstAsync(u => u.Id == result.StorageId);
            if (storage.PurchaseAudit != 1)
            {
                //直接更新库存
                var details = await
                    _storageSpecialDetailRep.GetListAsync(u => u.HandleId == input.Id);
                foreach (var detail in details)
                {
                    await _inventoryService.UpdateInventoryAsync(detail, result);
                }
            }

            return await _storageSpecialRecordRep.UpdateAsync(u
                => new StorageSpecialRecord() { Status = 1, HandleTime = DateTime.Now }, u => u.Id == input.Id);
        }
        else
            throw Oops.Oh("当前状态禁止提交");
    }

    /// <summary>
    /// 更新特殊处理记 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新特殊处理记")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [UnitOfWork]
    public async Task Update(UpdateStorageSpecialRecordInput input)
    {
        var entity = input.Adapt<StorageSpecialRecord>();
        var result = await _storageSpecialRecordRep.GetFirstAsync(u => u.Id == input.Id);
        if (result.Status != 0)
            throw Oops.Oh("当前状态禁止修改");
        await _storageSpecialDetailRep.DeleteAsync(u => u.HandleId == entity.Id);

        await SaveDetail(input.Details.Adapt<List<StorageSpecialDetail>>(), entity);
        //药房
        var storage=  await _drugStorageRep.GetFirstAsync(u => u.Id == entity.StorageId);
        entity.StorageCode= storage.StorageCode;
        entity.StorageName= storage.StorageName;
        await _storageSpecialRecordRep.AsUpdateable(entity)
            .IgnoreColumns(u => new
            {
                u.StorageCode,
                u.StorageName,
                u.SupplierCode,
                u.SupplierName,
            })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除特殊处理记 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除特殊处理记")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeleteStorageSpecialRecordInput input)
    {
        var entity = await _storageSpecialRecordRep.GetFirstAsync(u => u.Id == input.Id);
        if (entity.Status != 0)
            throw Oops.Oh("当前状态禁止删除");

        await _storageSpecialRecordRep.FakeDeleteAsync(entity); //假删除

        var details = await _storageSpecialDetailRep.GetListAsync(u => u.HandleId == input.Id);
        await _storageSpecialDetailRep.FakeDeleteAsync(details);
        //await _storageSpecialRecordRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除特殊处理记 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除特殊处理记")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete(
        [Required(ErrorMessage = "主键列表不能为空")] List<DeleteStorageSpecialRecordInput> input)
    {
        var exp = Expressionable.Create<StorageSpecialRecord>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _storageSpecialRecordRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _storageSpecialRecordRep.FakeDeleteAsync(list); //假删除
        //return await _storageSpecialRecordRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataStorageSpecialRecordInput input)
    {
        var storageIdData = await _storageSpecialRecordRep.Context.Queryable<DrugStorage>()
            .InnerJoinIF<StorageSpecialRecord>(input.FromPage, (u, r) => u.Id == r.StorageId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.StorageName}"
            }).ToListAsync();
        // var supplierIdData = await _storageSpecialRecordRep.Context.Queryable<EnterpriseDictionary>()
        //     .InnerJoinIF<StorageSpecialRecord>(input.FromPage, (u, r) => u.Id == r.SupplierId)
        //     .Select(u => new {
        //         Value = u.Id,
        //         Label = $"{u.EnterpriseName}"
        //     }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "storageId", storageIdData },
            // { "supplierId", supplierIdData },
        };
    }

    /// <summary>
    /// 导出特殊处理记记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出特殊处理记记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageStorageSpecialRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportStorageSpecialRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var handleTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "SpecialHandle" })
            .Result.ToDictionary(x => x.Value, x => x.Label);
        var statusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" })
            .Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e =>
        {
            e.HandleTypeDictLabel = handleTypeDictMap.GetValueOrDefault(e.HandleType ?? "", e.HandleType);
            //  e.StatusDictLabel = statusDictMap.GetValueOrDefault(e.Status ?? "", e.Status);
        });
        return ExcelHelper.ExportTemplate(list, "特殊处理记导出记录");
    }

    /// <summary>
    /// 下载特殊处理记数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载特殊处理记数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportStorageSpecialRecordOutput>(), "特殊处理记导入模板", (_, info) =>
        {
            if (nameof(ExportStorageSpecialRecordOutput.StorageFkDisplayName) == info.Name)
                return _storageSpecialRecordRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}")
                    .Distinct().ToList();
            if (nameof(ExportStorageSpecialRecordOutput.SupplierFkDisplayName) == info.Name)
                return _storageSpecialRecordRep.Context.Queryable<EnterpriseDictionary>()
                    .Select(u => $"{u.EnterpriseName}").Distinct().ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入特殊处理记记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入特殊处理记记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var handleTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "SpecialHandle" })
                .Result.ToDictionary(x => x.Label!, x => x.Value);
            var statusDictMap = _sysDictTypeService
                .GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportStorageSpecialRecordInput, StorageSpecialRecord>(file,
                (list, markerErrorAction) =>
                {
                    _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                    {
                        // 链接 库房
                        var storageIdLabelList = pageItems.Where(x => x.StorageFkDisplayName != null)
                            .Select(x => x.StorageFkDisplayName).Distinct().ToList();
                        if (storageIdLabelList.Any())
                        {
                            var storageIdLinkMap = _storageSpecialRecordRep.Context.Queryable<DrugStorage>()
                                .Where(u => storageIdLabelList.Contains($"{u.StorageName}")).ToList()
                                .ToDictionary(u => $"{u.StorageName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.StorageId = storageIdLinkMap.GetValueOrDefault(e.StorageFkDisplayName ?? "");
                                if (e.StorageId == null) e.Error = "库房链接失败";
                            });
                        }

                        // 链接 供应商
                        var supplierIdLabelList = pageItems.Where(x => x.SupplierFkDisplayName != null)
                            .Select(x => x.SupplierFkDisplayName).Distinct().ToList();
                        if (supplierIdLabelList.Any())
                        {
                            var supplierIdLinkMap = _storageSpecialRecordRep.Context.Queryable<EnterpriseDictionary>()
                                .Where(u => supplierIdLabelList.Contains($"{u.EnterpriseName}")).ToList()
                                .ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.SupplierId = supplierIdLinkMap.GetValueOrDefault(e.SupplierFkDisplayName ?? "");
                                if (e.SupplierId == null) e.Error = "供应商链接失败";
                            });
                        }

                        // 映射字典值
                        foreach (var item in pageItems)
                        {
                            if (string.IsNullOrWhiteSpace(item.HandleTypeDictLabel)) continue;
                            item.HandleType = handleTypeDictMap.GetValueOrDefault(item.HandleTypeDictLabel);
                            if (item.HandleType == null) item.Error = "处理类型字典映射失败";
                            // if (string.IsNullOrWhiteSpace(item.StatusDictLabel)) continue;
                            // item.Status = statusDictMap.GetValueOrDefault(item.StatusDictLabel);
                            if (item.Status == null) item.Error = "状态字典映射失败";
                        }

                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x => { return true; }).Adapt<List<StorageSpecialRecord>>();

                        var storageable = _storageSpecialRecordRep.Context.Storageable(rows)
                            .SplitError(it => it.Item.HandleNo?.Length > 100, "特殊处理单号长度不能超过100个字符")
                            .SplitError(it => it.Item.DrugType?.Length > 100, "药品类型长度不能超过100个字符")
                            .SplitError(it => it.Item.HandleType?.Length > 100, "处理类型长度不能超过100个字符")
                            .SplitError(it => it.Item.Remark?.Length > 100, "备注长度不能超过100个字符")
                            .SplitInsert(_ => true)
                            .ToStorage();

                        storageable.BulkCopy();
                        storageable.BulkUpdate();

                        // 标记错误信息
                        markerErrorAction.Invoke(storageable, pageItems, rows);
                    });
                });

            return stream;
        }
    }
}