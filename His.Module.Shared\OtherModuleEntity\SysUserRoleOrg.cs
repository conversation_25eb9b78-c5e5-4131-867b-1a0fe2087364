namespace His.Module.Shared.OtherModuleEntity;


/// <summary>
/// 用户角色机构表
/// </summary>
[Tenant("1300000000001")]
[SugarTable("sys_user_role_org", "用户角色机构表")]
public class SysUserRoleOrg : EntityTenant
{
    /// <summary>
    /// 角色Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "role_id", ColumnDescription = "角色Id")]
    public virtual long RoleId { get; set; }
    
    /// <summary>
    /// 角色编码
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "role_code", ColumnDescription = "角色编码", Length = 32)]
    public virtual string RoleCode { get; set; }
    
    /// <summary>
    /// 角色名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "role_name", ColumnDescription = "角色名称", Length = 32)]
    public virtual string RoleName { get; set; }
    
    /// <summary>
    /// 机构Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "org_id", ColumnDescription = "机构Id")]
    public virtual long OrgId { get; set; }
    
    /// <summary>
    /// 机构编码
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "org_code", ColumnDescription = "机构编码", Length = 32)]
    public virtual string OrgCode { get; set; }
    
    /// <summary>
    /// 机构名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "org_name", ColumnDescription = "机构名称", Length = 32)]
    public virtual string OrgName { get; set; }
    
    /// <summary>
    /// 用户id
    /// </summary>
    [SugarColumn(ColumnName = "user_id", ColumnDescription = "用户id")]
    public virtual long? UserId { get; set; }
    
    /// <summary>
    /// 用户名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "user_name", ColumnDescription = "用户名称", Length = 32)]
    public virtual string UserName { get; set; }
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
