﻿using Admin.NET.Core.Service;
using His.Module.Shared.Api.Api.ChargeItem;
using His.Module.Shared.Api.Api.ChargeItem.Dto;
using Microsoft.AspNetCore.Http;
using NewLife;
using System.Data;
namespace His.Module.Shared.Service;

/// <summary>
/// 收费项目服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class ChargeItemService : IDynamicApiController, ITransient, IChargeItemApi
{
    private readonly SqlSugarRepository<ChargeItem> _chargeItemRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public ChargeItemService(SqlSugarRepository<ChargeItem> chargeItemRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _chargeItemRep = chargeItemRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询收费项目 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询收费项目")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<ChargeItemOutput>> Page(PageChargeItemInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.Code = input.Code?.Trim();
        input.Name = input.Name?.Trim().ToLower();
        var query = _chargeItemRep.AsTenant().QueryableWithAttr<ChargeItem>()
            .LeftJoin<SysOrg>((u, sysOrg) => u.UseDepts == sysOrg.Id)
            .LeftJoin<ChargeCategory>((u, sysOrg, chargeCategory) => u.ChargeCategoryId == chargeCategory.Id)
            .LeftJoin<CalculateCategory>((u, sysOrg, chargeCategory, calculateCategory) => u.CalculateCategoryId == calculateCategory.Id)
            .LeftJoin<Frequency>((u, sysOrg, chargeCategory, calculateCategory, frequency) => u.FrequencyId == frequency.Id)
            .LeftJoin<CheckCategory>((u, sysOrg, chargeCategory, calculateCategory, frequency, checkCategory) => u.CheckCategoryId == checkCategory.Id)
            .LeftJoin<CheckPoint>((u, sysOrg, chargeCategory, calculateCategory, frequency, checkCategory, checkPoint) => u.CheckPointId == checkPoint.Id)
            .Where(u => u.Package == input.Package)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
            || u.Name.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .WhereIF(input.ChargeCategoryId != null, u => u.ChargeCategoryId == input.ChargeCategoryId)
            .WhereIF(input.UseDepts != null && input.UseDepts > 0, u => u.UseDepts == input.UseDepts)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select((u, sysOrg, chargeCategory, calculateCategory, frequency, checkCategory, checkPoint) => new ChargeItemOutput
            {
                Id = u.Id,
                Code = u.Code,
                Name = u.Name,
                PinyinCode = u.PinyinCode,
                WubiCode = u.WubiCode,
                Unit = u.Unit,
                Spec = u.Spec,
                Price = u.Price,
                PurchasePrice = u.PurchasePrice,
                Model = u.Model,
                ApprovalName = u.ApprovalName,
                Producer = u.Producer,
                Manufacturer = u.Manufacturer,
                RegistrationNumber = u.RegistrationNumber,
                PriceCode = u.PriceCode,
                ChargeCategoryId = u.ChargeCategoryId,
                ChargeCategoryFkDisplayName = $"{chargeCategory.Name}",
                CalculateCategoryId = u.CalculateCategoryId,
                CalculateCategoryFkDisplayName = $"{calculateCategory.Name}",
                DzfpChargeCategory = u.DzfpChargeCategory,
                BasyChargeCategory = u.BasyChargeCategory,
                HighValue = u.HighValue,
                UseSeparately = u.UseSeparately,
                UploadDw = u.UploadDw,
                FrequencyId = u.FrequencyId,
                FrequencyFkDisplayName = $"{frequency.Name}",
                SampleType = u.SampleType,
                NurseLevel = u.NurseLevel,
                CheckCategoryId = u.CheckCategoryId,
                CheckCategoryFkDisplayName = $"{checkCategory.Name}",
                RefundMode = u.RefundMode,
                Package = u.Package,
                UseDepts = u.UseDepts,
                UseDeptsFkDisplayName = $"{sysOrg.Name}",
                UsageScope = u.UsageScope,
                Remark = u.Remark,
                Status = u.Status,
                OrderNo = u.OrderNo,
                CreateTime = u.CreateTime,
                UpdateTime = u.UpdateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
                CheckPointId = u.CheckPointId,
                CheckPointFkDisplayName = $"{checkPoint.Name}",
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取收费项目详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取收费项目详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<ChargeItem> Detail([FromQuery] QueryByIdChargeItemInput input)
    {
        return await _chargeItemRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 添加收费项目数据
    /// </summary>
    /// <returns></returns>
    [DisplayName("添加收费项目数据")]
    [ApiDescriptionSettings(Name = "AddChargeItem"), HttpPost]
    public async Task<bool> AddChargeItem()
    {
        var dataTable = await _sqlSugarClient.Ado.GetDataTableAsync("SELECT * FROM \"shared\".\"XT_DICNODRUGITEM\"");
        var list = new List<ChargeItem>();
        foreach (DataRow item in dataTable.Rows)
        {
            var entity = new ChargeItem
            {
                Code = item["NITEMID"].ToString(),
                Name = item["VITEMNAME"].ToString(),
                Unit = "件",
                Spec = item["VGUIGE"].ToString(),
                Price = item["NNODRUGITEMPRICE"].ToDecimal(),
                PurchasePrice = item["NJHPRICE"].ToDecimal(),
                Model = item["VMODEL"].ToString(),
                ApprovalName = item["VPJCPMC"].ToString(),
                Producer = item["VCDM"].ToString(),
                Manufacturer = item["VITEM_MANUFACTURER"].ToString(),
                RegistrationNumber = item["VITEM_REG_NO"].ToString(),
                PriceCode = item["WJCODE"].ToString(),
                ChargeCategoryId = 658665256792133,
                CalculateCategoryId = 658658955128901,
                DzfpChargeCategory = "60128",
                BasyChargeCategory = "1",
                HighValue = item["NISGZCL"] == null
                ? YesNoEnum.N
                : item["NISGZCL"].ToString() == "0"
                ? YesNoEnum.N
                : YesNoEnum.Y,
                UseSeparately = item["ISDY"] == null
                ? YesNoEnum.N
                : item["ISDY"].ToString() == "0"
                ? YesNoEnum.N
                : YesNoEnum.Y,
                UploadDw = item["NISSCDW"] == null
                ? YesNoEnum.N
                : item["NISSCDW"].ToString() == "0"
                ? YesNoEnum.N
                : YesNoEnum.Y,
                CheckCategoryId = 658665256792135,
                RefundMode = 0,
                Package = item["NISPACK"] == null
                ? YesNoEnum.N
                : item["NISPACK"].ToString() == "0"
                ? YesNoEnum.N
                : YesNoEnum.Y,
                UsageScope = System.Enum.Parse<MedServiceCategoryEnum>(item["NOPHZID"].ToString()),
            };
            entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
            entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
            list.Add(entity);
        }

        return await _chargeItemRep.InsertRangeAsync(list);
    }

    /// <summary>
    /// 根据条件查询收费项目列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("根据条件查询收费项目列表")]
    [ApiDescriptionSettings(Name = "List"), HttpPost]
    [SkipPermission]
    public async Task<SqlSugarPagedList<ChargeItemOutput>> List(ChargeItemListInput input)
    {
        // 处理输入的关键字，去除空格并转换为小写
        input.Keyword = input.Keyword?.Trim().ToLower();
        // 初始化处方类型对象
        var prescriptionType = new PrescriptionType();
        // 如果输入的处方类型编码不为空，则根据编码查询对应的处方类型
        if (!string.IsNullOrWhiteSpace(input.PrescriptionTypeCode))
        {
            prescriptionType = await _chargeItemRep.ChangeRepository<SqlSugarRepository<PrescriptionType>>()
            .GetFirstAsync(u => u.Code == input.PrescriptionTypeCode) ?? throw Oops.Oh(SharedErrorCodeEnum.S0002);// 如果未找到，抛出异常
        }
        // 构建查询条件，关联多个表并筛选数据
        var chargeItems = await _chargeItemRep.AsTenant().QueryableWithAttr<ChargeItem>()
            .LeftJoin<SysOrg>((u, sysOrg) => u.UseDepts == sysOrg.Id)
            .LeftJoin<ChargeCategory>((u, sysOrg, chargeCategory) => u.ChargeCategoryId == chargeCategory.Id)
            .LeftJoin<CalculateCategory>((u, sysOrg, chargeCategory, calculateCategory) => u.CalculateCategoryId == calculateCategory.Id)
            .LeftJoin<Frequency>((u, sysOrg, chargeCategory, calculateCategory, frequency) => u.FrequencyId == frequency.Id)
            .LeftJoin<CheckCategory>((u, sysOrg, chargeCategory, calculateCategory, frequency, checkCategory) => u.CheckCategoryId == checkCategory.Id)
            .LeftJoin<CheckPoint>((u, sysOrg, chargeCategory, calculateCategory, frequency, checkCategory, checkPoint) => u.CheckPointId == checkPoint.Id)
            .Where(u => u.Status == StatusEnum.Enable)// 筛选启用状态的收费项目
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
            || u.Name.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))// 根据关键字筛选
            .WhereIF(prescriptionType.ChargeCategorys is not null && prescriptionType.ChargeCategorys.Count > 0, u => prescriptionType.ChargeCategorys.Contains((long)u.ChargeCategoryId))// 根据处方类型筛选
            .WhereIF(input.ChargeCategoryId is not null && input.ChargeCategoryId > 0, u => u.ChargeCategoryId == input.ChargeCategoryId)// 根据收费类别筛选
            .WhereIF(!string.IsNullOrWhiteSpace(input.ChargeCategoryCode), (u, sysOrg, chargeCategory, calculateCategory, frequency, checkCategory, checkPoint)
                =>  u.ChargeCategoryId == chargeCategory.Id&& chargeCategory.Code == input.ChargeCategoryCode)
            .WhereIF(input.ExaminationTypeId.HasValue, u => u.CheckCategoryId == input.ExaminationTypeId)
            .Select((u, sysOrg, chargeCategory, calculateCategory, frequency, checkCategory, checkPoint) => new ChargeItemOutput
            {
                // 映射查询结果到输出对象
                Id = u.Id,
                Code = u.Code,
                Name = u.Name,
                PinyinCode = u.PinyinCode,
                WubiCode = u.WubiCode,
                Unit = u.Unit,
                Spec = u.Spec,
                Price = u.Price,
                PurchasePrice = u.PurchasePrice,
                Model = u.Model,
                ApprovalName = u.ApprovalName,
                Producer = u.Producer,
                Manufacturer = u.Manufacturer,
                RegistrationNumber = u.RegistrationNumber,
                PriceCode = u.PriceCode,
                ChargeCategoryId = u.ChargeCategoryId,
                ChargeCategoryFkDisplayName = $"{chargeCategory.Name}",
                CalculateCategoryId = u.CalculateCategoryId,
                CalculateCategoryFkDisplayName = $"{calculateCategory.Name}",
                DzfpChargeCategory = u.DzfpChargeCategory,
                BasyChargeCategory = u.BasyChargeCategory,
                HighValue = u.HighValue,
                UseSeparately = u.UseSeparately,
                UploadDw = u.UploadDw,
                FrequencyId = u.FrequencyId,
                FrequencyFkDisplayName = $"{frequency.Name}",
                SampleType = u.SampleType,
                NurseLevel = u.NurseLevel,
                CheckCategoryId = u.CheckCategoryId,
                CheckCategoryFkDisplayName = $"{checkCategory.Name}",
                RefundMode = u.RefundMode,
                Package = u.Package,
                UseDepts = u.UseDepts,
                UseDeptsFkDisplayName = $"{sysOrg.Name}",
                UsageScope = u.UsageScope,
                Remark = u.Remark,
                Status = u.Status,
                OrderNo = u.OrderNo,
                CreateTime = u.CreateTime,
                UpdateTime = u.UpdateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
                CheckPointId = u.CheckPointId,
                CheckPointFkDisplayName = $"{checkPoint.Name}",
            })
            .OrderBuilder(input)// 根据输入参数构建排序规则
            .ToPagedListAsync(input.Page, input.PageSize);// 分页查询
        // 如果查询结果中包含套餐项目，处理其关联的子项目
        var packageIds = chargeItems.Items.Where(x => x.Package == YesNoEnum.Y).Select(x => x.Id).ToList();
        if (packageIds.Count == 0) return chargeItems;
        // 查询所有套餐关联的子项目
        var allPacks = await _chargeItemRep.ChangeRepository<SqlSugarRepository<ChargeItemPack>>()
            .AsQueryable()
            .Where(u => packageIds.Contains((long)u.PackId))
            .ToListAsync();
        // 获取所有关联的收费项目
        var relatedItemIds = allPacks.Select(p => p.ChargeItemId).Distinct().ToList();
        var relatedItems = relatedItemIds.Count != 0
            ? await _chargeItemRep.GetListAsync(u => relatedItemIds.Contains(u.Id))
            : [];
        // 构建子项目字典
        var itemDict = relatedItems.ToDictionary(i => i.Id, i => i.Adapt<ChargeItemOutput>());
        var packGroups = allPacks.GroupBy(p => p.PackId);
        // 为每个套餐项目添加其子项目列表
        foreach (var item in chargeItems.Items)
            if (item.Package == YesNoEnum.Y)
                item.ChargeItemPacks = packGroups
                    .FirstOrDefault(g => g.Key == item.Id)?
                    .Select(pack =>
                    {
                        if (!itemDict.TryGetValue((long)pack.ChargeItemId, out var relatedItem)) return null;
                        relatedItem.Quantity = pack.ChargeItemQuantity;
                        return relatedItem;
                    })
                    .Where(x => x != null)
                    .ToList();
        return chargeItems;
    }

    /// <summary>
    /// 获取收费项目详情（支持批量查询）
    /// </summary>
    /// <param name="ids">收费项目ID集合</param>
    /// <returns></returns>
    [HttpPost]
    public async Task<List<ChargeItemDto>> GetDetails(List<long> ids)
    {
        if (ids == null || ids.Count == 0) return [];

        // 批量获取主收费项目
        var chargeItems = await _chargeItemRep.GetListAsync(u => ids.Contains(u.Id));
        if (chargeItems.Count == 0) return [];

        // 获取所有套餐关联关系
        var packIds = chargeItems.Where(x => x.Package == YesNoEnum.Y).Select(x => x.Id).ToList();
        var allPacks = await _chargeItemRep.ChangeRepository<SqlSugarRepository<ChargeItemPack>>()
            .AsQueryable()
            .Where(u => packIds.Contains((long)u.PackId))
            .ToListAsync();

        // 批量获取关联收费项目
        var relatedItemIds = allPacks.Select(p => p.ChargeItemId).Distinct().ToList();
        var relatedItems = relatedItemIds.Count != 0
            ? await _chargeItemRep.GetListAsync(u => relatedItemIds.Contains(u.Id))
            : [];

        // 构建数据结构
        var itemDict = relatedItems.ToDictionary(i => i.Id, i => i.Adapt<ChargeItemDto>());
        var packGroups = allPacks.GroupBy(p => p.PackId);

        // 构建返回结果
        return [.. chargeItems.Select(mainItem =>
        {
            var dto = mainItem.Adapt<ChargeItemDto>();
            if (mainItem.Package == YesNoEnum.Y)
            {
                dto.ChargeItemPacks = packGroups
                    .FirstOrDefault(g => g.Key == mainItem.Id)?
                    .Select(pack =>
                    {
                        if (!itemDict.TryGetValue((long)pack.ChargeItemId, out var item)) return null;
                        item.Quantity = pack.ChargeItemQuantity;
                        return item;
                    })
                    .Where(x => x != null)
                    .ToList() ?? [];
            }
            return dto;
        })];
    }


    /// <summary>
    /// 增加收费项目 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加收费项目")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddChargeItemInput input)
    {
        var entity = input.Adapt<ChargeItem>();
        entity.Code = await _chargeItemRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('charge_item_code_seq')As varchar),8,'0')");
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        return await _chargeItemRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新收费项目 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新收费项目")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateChargeItemInput input)
    {
        var entity = input.Adapt<ChargeItem>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _chargeItemRep.AsUpdateable(entity)
        .IgnoreColumns(u => new
        {
            u.Code,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除收费项目 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除收费项目")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteChargeItemInput input)
    {
        var entity = await _chargeItemRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _chargeItemRep.FakeDeleteAsync(entity);   //假删除
        //await _chargeItemRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除收费项目 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除收费项目")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteChargeItemInput> input)
    {
        var exp = Expressionable.Create<ChargeItem>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _chargeItemRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _chargeItemRep.FakeDeleteAsync(list);   //假删除
        //return await _chargeItemRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置收费项目状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置收费项目状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetChargeItemStatus(SetChargeItemStatusInput input)
    {
        await _chargeItemRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataChargeItemInput input)
    {
        var chargeCategoryIdData = await _chargeItemRep.Context.Queryable<ChargeCategory>()
            .Where(u => u.Status == StatusEnum.Enable)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.Name}"
            }).ToListAsync();
        var calculateCategoryIdData = await _chargeItemRep.Context.Queryable<CalculateCategory>()
            .Where(u => u.Status == StatusEnum.Enable)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.Name}"
            }).ToListAsync();
        var frequencyIdData = await _chargeItemRep.Context.Queryable<Frequency>()
            .Where(u => u.Status == StatusEnum.Enable)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.Name}"
            }).ToListAsync();
        var checkCategoryIdData = await _chargeItemRep.Context.Queryable<CheckCategory>()
            .Where(u => u.Status == StatusEnum.Enable)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.Name}"
            }).ToListAsync();
        var checkPointIdData = await _chargeItemRep.Context.Queryable<CheckPoint>()
            .Where(u => u.Status == StatusEnum.Enable)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.Name}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "chargeCategoryId", chargeCategoryIdData },
            { "calculateCategoryId", calculateCategoryIdData },
            { "frequencyId", frequencyIdData },
            { "checkCategoryId", checkCategoryIdData },
            { "checkPointId", checkPointIdData },
        };
    }

    /// <summary>
    /// 导出收费项目记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出收费项目记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageChargeItemInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportChargeItemOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var dzfpChargeCategoryDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "ElectronicInvoiceFeeCategory" }).Result.ToDictionary(x => x.Value, x => x.Label);
        var basyChargeCategoryDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "MedicalRecordFeeCategory" }).Result.ToDictionary(x => x.Value, x => x.Label);
        var sampleTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "SampleType" }).Result.ToDictionary(x => x.Value, x => x.Label);
        var nurseLevelDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "NurseLevel" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e =>
        {
            e.DzfpChargeCategoryDictLabel = dzfpChargeCategoryDictMap.GetValueOrDefault(e.DzfpChargeCategory ?? "", e.DzfpChargeCategory);
            e.BasyChargeCategoryDictLabel = basyChargeCategoryDictMap.GetValueOrDefault(e.BasyChargeCategory ?? "", e.BasyChargeCategory);
            e.SampleTypeDictLabel = sampleTypeDictMap.GetValueOrDefault(e.SampleType ?? "", e.SampleType);
            e.NurseLevelDictLabel = nurseLevelDictMap.GetValueOrDefault(e.NurseLevel ?? "", e.NurseLevel);
        });
        return ExcelHelper.ExportTemplate(list, "收费项目导出记录");
    }

    /// <summary>
    /// 下载收费项目数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载收费项目数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportChargeItemOutput>(), "收费项目导入模板", (_, info) =>
        {
            if (nameof(ExportChargeItemOutput.ChargeCategoryFkDisplayName) == info.Name) return _chargeItemRep.Context.Queryable<ChargeCategory>().Select(u => $"{u.Name}").Distinct().ToList();
            if (nameof(ExportChargeItemOutput.CalculateCategoryFkDisplayName) == info.Name) return _chargeItemRep.Context.Queryable<CalculateCategory>().Select(u => $"{u.Name}").Distinct().ToList();
            if (nameof(ExportChargeItemOutput.FrequencyFkDisplayName) == info.Name) return _chargeItemRep.Context.Queryable<Frequency>().Select(u => $"{u.Name}").Distinct().ToList();
            if (nameof(ExportChargeItemOutput.CheckCategoryFkDisplayName) == info.Name) return _chargeItemRep.Context.Queryable<CheckCategory>().Select(u => $"{u.Name}").Distinct().ToList();
            if (nameof(ExportChargeItemOutput.CheckPointFkDisplayName) == info.Name) return _chargeItemRep.Context.Queryable<CheckPoint>().Select(u => $"{u.Name}").Distinct().ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入收费项目记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入收费项目记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var dzfpChargeCategoryDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "ElectronicInvoiceFeeCategory" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var basyChargeCategoryDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "MedicalRecordFeeCategory" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var sampleTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "SampleType" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var nurseLevelDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "NurseLevel" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportChargeItemInput, ChargeItem>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 收费类别
                    var chargeCategoryIdLabelList = pageItems.Where(x => x.ChargeCategoryFkDisplayName != null).Select(x => x.ChargeCategoryFkDisplayName).Distinct().ToList();
                    if (chargeCategoryIdLabelList.Any())
                    {
                        var chargeCategoryIdLinkMap = _chargeItemRep.Context.Queryable<ChargeCategory>().Where(u => chargeCategoryIdLabelList.Contains($"{u.Name}")).ToList().ToDictionary(u => $"{u.Name}", u => u.Id);
                        pageItems.ForEach(e =>
                        {
                            e.ChargeCategoryId = chargeCategoryIdLinkMap.GetValueOrDefault(e.ChargeCategoryFkDisplayName ?? "");
                            if (e.ChargeCategoryId == null) e.Error = "收费类别链接失败";
                        });
                    }
                    // 链接 核算类别
                    var calculateCategoryIdLabelList = pageItems.Where(x => x.CalculateCategoryFkDisplayName != null).Select(x => x.CalculateCategoryFkDisplayName).Distinct().ToList();
                    if (calculateCategoryIdLabelList.Any())
                    {
                        var calculateCategoryIdLinkMap = _chargeItemRep.Context.Queryable<CalculateCategory>().Where(u => calculateCategoryIdLabelList.Contains($"{u.Name}")).ToList().ToDictionary(u => $"{u.Name}", u => u.Id);
                        pageItems.ForEach(e =>
                        {
                            e.CalculateCategoryId = calculateCategoryIdLinkMap.GetValueOrDefault(e.CalculateCategoryFkDisplayName ?? "");
                            if (e.CalculateCategoryId == null) e.Error = "核算类别链接失败";
                        });
                    }
                    // 链接 频次
                    var frequencyIdLabelList = pageItems.Where(x => x.FrequencyFkDisplayName != null).Select(x => x.FrequencyFkDisplayName).Distinct().ToList();
                    if (frequencyIdLabelList.Any())
                    {
                        var frequencyIdLinkMap = _chargeItemRep.Context.Queryable<Frequency>().Where(u => frequencyIdLabelList.Contains($"{u.Name}")).ToList().ToDictionary(u => $"{u.Name}", u => u.Id);
                        pageItems.ForEach(e =>
                        {
                            e.FrequencyId = frequencyIdLinkMap.GetValueOrDefault(e.FrequencyFkDisplayName ?? "");
                            if (e.FrequencyId == null) e.Error = "频次链接失败";
                        });
                    }
                    // 链接 检查类别
                    var checkCategoryIdLabelList = pageItems.Where(x => x.CheckCategoryFkDisplayName != null).Select(x => x.CheckCategoryFkDisplayName).Distinct().ToList();
                    if (checkCategoryIdLabelList.Any())
                    {
                        var checkCategoryIdLinkMap = _chargeItemRep.Context.Queryable<CheckCategory>().Where(u => checkCategoryIdLabelList.Contains($"{u.Name}")).ToList().ToDictionary(u => $"{u.Name}", u => u.Id);
                        pageItems.ForEach(e =>
                        {
                            e.CheckCategoryId = checkCategoryIdLinkMap.GetValueOrDefault(e.CheckCategoryFkDisplayName ?? "");
                            if (e.CheckCategoryId == null) e.Error = "检查类别链接失败";
                        });
                    }
                    // 链接 检查部位
                    var checkPointIdLabelList = pageItems.Where(x => x.CheckPointFkDisplayName != null).Select(x => x.CheckPointFkDisplayName).Distinct().ToList();
                    if (checkPointIdLabelList.Any())
                    {
                        var checkPointIdLinkMap = _chargeItemRep.Context.Queryable<CheckPoint>().Where(u => checkPointIdLabelList.Contains($"{u.Name}")).ToList().ToDictionary(u => $"{u.Name}", u => u.Id);
                        pageItems.ForEach(e =>
                        {
                            e.CheckPointId = checkPointIdLinkMap.GetValueOrDefault(e.CheckPointFkDisplayName ?? "");
                            if (e.CheckPointId == null) e.Error = "检查部位链接失败";
                        });
                    }

                    // 映射字典值
                    foreach (var item in pageItems)
                    {
                        if (string.IsNullOrWhiteSpace(item.DzfpChargeCategoryDictLabel)) continue;
                        item.DzfpChargeCategory = dzfpChargeCategoryDictMap.GetValueOrDefault(item.DzfpChargeCategoryDictLabel);
                        if (item.DzfpChargeCategory == null) item.Error = "电子发票费用类别字典映射失败";
                        if (string.IsNullOrWhiteSpace(item.BasyChargeCategoryDictLabel)) continue;
                        item.BasyChargeCategory = basyChargeCategoryDictMap.GetValueOrDefault(item.BasyChargeCategoryDictLabel);
                        if (item.BasyChargeCategory == null) item.Error = "病案首页费用类别字典映射失败";
                        if (string.IsNullOrWhiteSpace(item.SampleTypeDictLabel)) continue;
                        item.SampleType = sampleTypeDictMap.GetValueOrDefault(item.SampleTypeDictLabel);
                        if (item.SampleType == null) item.Error = "样本类型字典映射失败";
                        if (string.IsNullOrWhiteSpace(item.NurseLevelDictLabel)) continue;
                        item.NurseLevel = nurseLevelDictMap.GetValueOrDefault(item.NurseLevelDictLabel);
                        if (item.NurseLevel == null) item.Error = "护理等级字典映射失败";
                    }

                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.ChargeCategoryId == null)
                        {
                            x.Error = "收费类别不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.CalculateCategoryId == null)
                        {
                            x.Error = "核算类别不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.UseSeparately == null)
                        {
                            x.Error = "是否单用不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.UploadDw == null)
                        {
                            x.Error = "是否上传地纬不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.UsageScope == null)
                        {
                            x.Error = "使用范围不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<ChargeItem>>();

                    var storageable = _chargeItemRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Code?.Length > 64, "编码长度不能超过64个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.Name), "名称不能为空")
                        .SplitError(it => it.Item.Name?.Length > 64, "名称长度不能超过64个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 32, "拼音码长度不能超过32个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 32, "五笔码长度不能超过32个字符")
                        .SplitError(it => it.Item.Unit?.Length > 32, "单位长度不能超过32个字符")
                        .SplitError(it => it.Item.Spec?.Length > 64, "规格长度不能超过64个字符")
                        .SplitError(it => it.Item.Price == null, "单价不能为空")
                        .SplitError(it => it.Item.Model?.Length > 64, "型号长度不能超过64个字符")
                        .SplitError(it => it.Item.ApprovalName?.Length > 128, "批件产品名称长度不能超过128个字符")
                        .SplitError(it => it.Item.Producer?.Length > 8, "产地长度不能超过8个字符")
                        .SplitError(it => it.Item.Manufacturer?.Length > 256, "生产厂家长度不能超过256个字符")
                        .SplitError(it => it.Item.RegistrationNumber?.Length > 128, "注册证号长度不能超过128个字符")
                        .SplitError(it => it.Item.PriceCode?.Length > 32, "物价编码长度不能超过32个字符")
                        .SplitError(it => it.Item.ChargeCategoryId == null, "收费类别不能为空")
                        .SplitError(it => it.Item.CalculateCategoryId == null, "核算类别不能为空")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.DzfpChargeCategory), "电子发票费用类别不能为空")
                        .SplitError(it => it.Item.DzfpChargeCategory?.Length > 32, "电子发票费用类别长度不能超过32个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.BasyChargeCategory), "病案首页费用类别不能为空")
                        .SplitError(it => it.Item.BasyChargeCategory?.Length > 32, "病案首页费用类别长度不能超过32个字符")
                        .SplitError(it => it.Item.SampleType?.Length > 32, "样本类型长度不能超过32个字符")
                        .SplitError(it => it.Item.NurseLevel?.Length > 32, "护理等级长度不能超过32个字符")
                        .SplitError(it => it.Item.Remark?.Length > 256, "备注长度不能超过256个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}