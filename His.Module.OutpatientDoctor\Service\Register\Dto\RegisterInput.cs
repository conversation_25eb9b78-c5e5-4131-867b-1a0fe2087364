﻿namespace His.Module.OutpatientDoctor.Service;

/// <summary>
/// 门诊挂号分页查询输入参数
/// </summary>
public class RegisterInput : BasePageInput
{
    /// <summary>
    /// 就诊流水号
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// 挂号科室
    /// </summary>
    public long? DeptId { get; set; }
    /// <summary>
    /// 医生
    /// </summary>
    public long? DoctorId { get; set; }
    

    /// <summary>
    /// 模糊查询关键字
    /// </summary>
    public string? Keyword { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
    
    
}

/// <summary>
/// 挂号输入参数
/// </summary>
public class AddMzRegisterInput : Register
{
    /// <summary>
    /// 号别id
    /// </summary>
    [Required(ErrorMessage = "号别id不能为空")]
    public override long? RegCategoryId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [Required(ErrorMessage = "患者姓名不能为空")]
    public override string PatientName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [Required(ErrorMessage = "性别不能为空")]
    public override GenderEnum Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    [Required(ErrorMessage = "年龄不能为空")]
    public override int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [Required(ErrorMessage = "年龄单位不能为空")]
    public override string AgeUnit { get; set; }

    /// <summary>
    /// 费别
    /// </summary>
    [Required(ErrorMessage = "费别不能为空")]
    public override long? FeeId { get; set; }

    /// <summary>
    /// 科室
    /// </summary>
    [Required(ErrorMessage = "科室不能为空")]
    public override long? DeptId { get; set; }

    /// <summary>
    /// 医生
    /// </summary>
    [Required(ErrorMessage = "医生不能为空")]
    public override long? DoctorId { get; set; }

    /// <summary>
	/// 就诊卡号
	/// </summary>

    public string CardNo { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>

    public string IdCardNo { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal? TotalAmount { get; set; }

    // /// <summary>
    // /// 支付方式
    // /// </summary>
    // public long? PaymentMethod { get; set; }
    /// <summary>
    /// 支付方式
    /// </summary>
    public long? PaymentMethod { get; set; }
    /// <summary>
    /// 个人支付
    /// </summary>
    public decimal? PersonalPayment { get; set; }

    /// <summary>
    /// 挂号时间
    /// </summary> 
    public DateTime? RegTime { get; set; }
}

/// <summary>
/// 退号输入参数
/// </summary>
public class RefundMzRegisterInput : BaseIdInput
{
}

/// <summary>
/// 更新挂号信息输入参数
/// </summary>
public class UpdateMzRegisterInput : AddMzRegisterInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public override long Id { get; set; }
}

/// <summary>
/// 更新挂号信息输入参数
/// </summary>
public class SeeVisitInput  
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long Id { get; set; }
}

public class EndVisitInput : SeeVisitInput
{
}

public class DeleteRegisterInput  
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public   long Id { get; set; }
}
/// <summary>
/// 门诊挂号主键查询输入参数
/// </summary>
public class QueryByIdMzRegisterInput : RefundMzRegisterInput
{
}

public class QueryByStatusMzRegisterInput
{
    /// <summary>
    /// 挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败
    /// </summary>
    public RegStatusEnum? Status { get; set; }
}