namespace His.Module.Financial.Service;

/// <summary>
/// 收款员结算查询输入
/// </summary>
public class CashierSettlementQueryInput : BasePageInput
{
    /// <summary>
    /// 收款员ID
    /// </summary>
    public long? CashierId { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 结算类型 1=日结 2=月结
    /// </summary>
    public int? SettlementType { get; set; }
}

/// <summary>
/// 收款员统计输入
/// </summary>
public class CashierStatisticsInput
{
    /// <summary>
    /// 收款员ID
    /// </summary>
    public long? CashierId { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 结算类型 1=日结 2=月结，null=不检查结算状态
    /// </summary>
    public int? SettlementType { get; set; }
}

/// <summary>
/// 收款员日结输入
/// </summary>
public class CashierDailySettlementInput
{
    /// <summary>
    /// 收款员ID
    /// </summary>
    public long CashierId { get; set; }

    /// <summary>
    /// 结算日期
    /// </summary>
    public DateTime SettlementDate { get; set; }

    /// <summary>
    /// 强制结算（忽略异常数据）
    /// </summary>
    public bool ForceSettle { get; set; } = false;

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 收款员月结输入
/// </summary>
public class CashierMonthlySettlementInput
{
    /// <summary>
    /// 收款员ID
    /// </summary>
    public long CashierId { get; set; }

    /// <summary>
    /// 结算年月
    /// </summary>
    public DateTime SettlementMonth { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 数据补录输入
/// </summary>
public class DataSupplementInput
{
    /// <summary>
    /// 结算ID
    /// </summary>
    public long SettlementId { get; set; }

    /// <summary>
    /// 补录的业务数据ID列表
    /// </summary>
    public List<long> BusinessIds { get; set; } = new List<long>();

    /// <summary>
    /// 业务数据类型 1=收费 2=退费 3=充值 4=退卡
    /// </summary>
    public int BusinessType { get; set; }

    /// <summary>
    /// 补录原因
    /// </summary>
    public string? SupplementReason { get; set; }

    /// <summary>
    /// 是否强制补录（忽略校验错误）
    /// </summary>
    public bool ForceUpdate { get; set; } = false;
}

/// <summary>
/// 冲正处理输入
/// </summary>
public class ReversalProcessInput
{
    /// <summary>
    /// 原业务数据ID
    /// </summary>
    public long OriginalBusinessId { get; set; }

    /// <summary>
    /// 业务数据类型 1=收费 2=退费 3=充值 4=退卡
    /// </summary>
    public int BusinessType { get; set; }

    /// <summary>
    /// 冲正原因
    /// </summary>
    public string ReversalReason { get; set; } = string.Empty;

    /// <summary>
    /// 冲正金额
    /// </summary>
    public decimal ReversalAmount { get; set; }

    /// <summary>
    /// 是否影响已结算数据
    /// </summary>
    public bool AffectSettledData { get; set; }

    /// <summary>
    /// 相关结算ID（如果影响已结算数据）
    /// </summary>
    public long? RelatedSettlementId { get; set; }
}

/// <summary>
/// 数据校验输入
/// </summary>
public class DataValidationInput
{
    /// <summary>
    /// 收款员ID
    /// </summary>
    public long CashierId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 校验类型 1=基础数据校验 2=金额一致性校验 3=完整性校验 4=全面校验
    /// </summary>
    public int ValidationType { get; set; } = 4;

    /// <summary>
    /// 是否修复发现的问题
    /// </summary>
    public bool AutoFix { get; set; } = false;
}

/// <summary>
/// 收款员统计输出
/// </summary>
public class CashierStatisticsOutput
{
    /// <summary>
    /// 收款员ID
    /// </summary>
    public long CashierId { get; set; }

    /// <summary>
    /// 收款员姓名
    /// </summary>
    public string? CashierName { get; set; }

    /// <summary>
    /// 统计开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 统计结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总收费金额
    /// </summary>
    public decimal TotalChargeAmount { get; set; }

    /// <summary>
    /// 总退费金额
    /// </summary>
    public decimal TotalRefundAmount { get; set; }

    /// <summary>
    /// 净收费金额（收费-退费）
    /// </summary>
    public decimal NetChargeAmount { get; set; }

    /// <summary>
    /// 就诊卡充值金额
    /// </summary>
    public decimal TotalRechargeAmount { get; set; }

    /// <summary>
    /// 就诊卡退卡金额
    /// </summary>
    public decimal TotalCardRefundAmount { get; set; }

    /// <summary>
    /// 净充值金额（充值-退卡）
    /// </summary>
    public decimal NetRechargeAmount { get; set; }

    /// <summary>
    /// 收费笔数
    /// </summary>
    public int ChargeCount { get; set; }

    /// <summary>
    /// 退费笔数
    /// </summary>
    public int RefundCount { get; set; }

    /// <summary>
    /// 充值笔数
    /// </summary>
    public int RechargeCount { get; set; }

    /// <summary>
    /// 退卡笔数
    /// </summary>
    public int CardRefundCount { get; set; }

    /// <summary>
    /// 退费率（退费金额/收费金额）
    /// </summary>
    public decimal RefundRate { get; set; }

    /// <summary>
    /// 日均收费金额（仅月结使用）
    /// </summary>
    public decimal? DailyAvgAmount { get; set; }

    /// <summary>
    /// 日均收费笔数（仅月结使用）
    /// </summary>
    public decimal? DailyAvgCount { get; set; }

    /// <summary>
    /// 工作天数（仅月结使用）
    /// </summary>
    public int? WorkDays { get; set; }

    /// <summary>
    /// 支付方式统计
    /// </summary>
    public List<PayMethodStatistics> PayMethodStatistics { get; set; } = new List<PayMethodStatistics>();

    /// <summary>
    /// 收费明细
    /// </summary>
    public List<ChargeDetailStatistics> ChargeDetails { get; set; } = new List<ChargeDetailStatistics>();

    /// <summary>
    /// 退费明细
    /// </summary>
    public List<RefundDetailStatistics> RefundDetails { get; set; } = new List<RefundDetailStatistics>();

    /// <summary>
    /// 充值明细
    /// </summary>
    public List<RechargeDetailStatistics> RechargeDetails { get; set; } = new List<RechargeDetailStatistics>();

    /// <summary>
    /// 退卡明细
    /// </summary>
    public List<CardRefundDetailStatistics> CardRefundDetails { get; set; } = new List<CardRefundDetailStatistics>();

    /// <summary>
    /// 异常数据列表
    /// </summary>
    public List<ExceptionDataInfo> ExceptionData { get; set; } = new List<ExceptionDataInfo>();

    /// <summary>
    /// 数据校验结果
    /// </summary>
    public DataValidationResult ValidationResult { get; set; } = new DataValidationResult();

    /// <summary>
    /// 是否已结算（根据指定的结算类型检查）
    /// </summary>
    public bool IsSettled { get; set; }
}

/// <summary>
/// 支付方式统计
/// </summary>
public class PayMethodStatistics
{
    /// <summary>
    /// 支付方式ID
    /// </summary>
    public long PayMethodId { get; set; }

    /// <summary>
    /// 支付方式名称
    /// </summary>
    public string? PayMethodName { get; set; }

    /// <summary>
    /// 收费金额
    /// </summary>
    public decimal ChargeAmount { get; set; }

    /// <summary>
    /// 退费金额
    /// </summary>
    public decimal RefundAmount { get; set; }

    /// <summary>
    /// 净收费金额
    /// </summary>
    public decimal NetChargeAmount { get; set; }

    /// <summary>
    /// 充值金额
    /// </summary>
    public decimal RechargeAmount { get; set; }

    /// <summary>
    /// 退卡金额
    /// </summary>
    public decimal CardRefundAmount { get; set; }

    /// <summary>
    /// 净充值金额
    /// </summary>
    public decimal NetRechargeAmount { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 收费笔数
    /// </summary>
    public int ChargeCount { get; set; }

    /// <summary>
    /// 退费笔数
    /// </summary>
    public int RefundCount { get; set; }

    /// <summary>
    /// 充值笔数
    /// </summary>
    public int RechargeCount { get; set; }

    /// <summary>
    /// 退卡笔数
    /// </summary>
    public int CardRefundCount { get; set; }

    /// <summary>
    /// 总笔数
    /// </summary>
    public int TotalCount { get; set; }
}

/// <summary>
/// 收费明细统计
/// </summary>
public class ChargeDetailStatistics
{

    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime ChargeTime { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 费别名称
    /// </summary>
    public string? FeeName { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    public string OutpatientNo { get; set; }

    /// <summary>
    /// 收费金额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    public string? PayMethod { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal? PayAmount { get; set; }

    /// <summary>
    /// 社保支付方式
    /// </summary>
    public string? SocialSecurityPayMethod { get; set; }

    /// <summary>
    /// 社保支付金额
    /// </summary>
    public decimal? SocialSecurityPayAmount { get; set; }

}

/// <summary>
/// 退费明细统计
/// </summary>
public class RefundDetailStatistics
{

    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// 退费发票号
    /// </summary>
    public string? RefundInvoiceNumber { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime ChargeTime { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 费别名称
    /// </summary>
    public string? FeeName { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    public string OutpatientNo { get; set; }

    /// <summary>
    /// 收费金额
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    public string? PayMethod { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal? PayAmount { get; set; }

    /// <summary>
    /// 社保支付方式
    /// </summary>
    public string? SocialSecurityPayMethod { get; set; }

    /// <summary>
    /// 社保支付金额
    /// </summary>
    public decimal? SocialSecurityPayAmount { get; set; }

    /// <summary>
    /// 退费原因
    /// </summary>
    public string? RefundReason { get; set; }
}

/// <summary>
/// 充值明细统计
/// </summary>
public class RechargeDetailStatistics
{
    /// <summary>
    /// 充值ID
    /// </summary>
    public long RechargeId { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? CardNo { get; set; }

    /// <summary>
    /// 充值金额
    /// </summary>
    public decimal PayAmount { get; set; }

    /// <summary>
    /// 充值时间
    /// </summary>
    public DateTime RechargeTime { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    public string? PayMethodName { get; set; }
}

/// <summary>
/// 退卡明细统计
/// </summary>
public class CardRefundDetailStatistics
{
    /// <summary>
    /// 退卡ID
    /// </summary>
    public long CardRefundId { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? CardNo { get; set; }

    /// <summary>
    /// 退卡金额
    /// </summary>
    public decimal RefundAmount { get; set; }

    /// <summary>
    /// 退卡时间
    /// </summary>
    public DateTime RefundTime { get; set; }

    /// <summary>
    /// 退卡原因
    /// </summary>
    public string? RefundReason { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    public string? PayMethodName { get; set; }
}

/// <summary>
/// 异常数据信息
/// </summary>
public class ExceptionDataInfo
{
    /// <summary>
    /// 异常类型 1=跨日期数据 2=状态异常 3=金额异常 4=其他
    /// </summary>
    public int ExceptionType { get; set; }

    /// <summary>
    /// 异常描述
    /// </summary>
    public string? ExceptionDesc { get; set; }

    /// <summary>
    /// 业务数据ID
    /// </summary>
    public long BusinessId { get; set; }

    /// <summary>
    /// 业务数据类型 1=收费 2=退费 3=充值 4=退卡
    /// </summary>
    public int BusinessType { get; set; }

    /// <summary>
    /// 异常详情
    /// </summary>
    public string? ExceptionDetail { get; set; }

    /// <summary>
    /// 建议处理方式
    /// </summary>
    public string? SuggestedAction { get; set; }
}

/// <summary>
/// 数据校验结果
/// </summary>
public class DataValidationResult
{
    /// <summary>
    /// 校验是否通过
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// 校验错误列表
    /// </summary>
    public List<string> ValidationErrors { get; set; } = new List<string>();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new List<string>();

    /// <summary>
    /// 数据完整性检查结果
    /// </summary>
    public bool DataIntegrityCheck { get; set; } = true;

    /// <summary>
    /// 金额一致性检查结果
    /// </summary>
    public bool AmountConsistencyCheck { get; set; } = true;
}

/// <summary>
/// 操作日志记录输入参数
/// </summary>
public class LogOperationInput
{
    /// <summary>
    /// 操作类型
    /// </summary>
    [Required(ErrorMessage = "操作类型不能为空")]
    public int OperationType { get; set; }

    /// <summary>
    /// 操作描述
    /// </summary>
    [Required(ErrorMessage = "操作描述不能为空")]
    public string OperationDesc { get; set; } = string.Empty;

    /// <summary>
    /// 业务数据ID
    /// </summary>
    [Required(ErrorMessage = "业务数据ID不能为空")]
    public long BusinessId { get; set; }

    /// <summary>
    /// 操作前数据
    /// </summary>
    public object? BeforeData { get; set; }

    /// <summary>
    /// 操作后数据
    /// </summary>
    public object? AfterData { get; set; }

    /// <summary>
    /// 操作原因
    /// </summary>
    public string? OperationReason { get; set; }

    /// <summary>
    /// 操作结果 1=成功 2=失败
    /// </summary>
    public int OperationResult { get; set; } = 1;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作耗时（毫秒）
    /// </summary>
    public long OperationDuration { get; set; } = 0;
}

/// <summary>
/// 详细操作日志记录输入参数（内部使用）
/// </summary>
public class LogOperationDetailInput
{
    /// <summary>
    /// 结算ID（可为0）
    /// </summary>
    public long SettlementId { get; set; }

    /// <summary>
    /// 操作类型 1=创建 2=修改 3=撤销 4=重新计算 5=数据补录 6=冲正处理
    /// </summary>
    [Required(ErrorMessage = "操作类型不能为空")]
    public int OperationType { get; set; }

    /// <summary>
    /// 操作描述
    /// </summary>
    [Required(ErrorMessage = "操作描述不能为空")]
    public string OperationDesc { get; set; } = string.Empty;

    /// <summary>
    /// 操作前数据
    /// </summary>
    public object? BeforeData { get; set; }

    /// <summary>
    /// 操作后数据
    /// </summary>
    public object? AfterData { get; set; }

    /// <summary>
    /// 操作原因
    /// </summary>
    public string? OperationReason { get; set; }

    /// <summary>
    /// 影响的业务数据ID列表
    /// </summary>
    public List<long>? AffectedBusinessIds { get; set; }

    /// <summary>
    /// 操作结果 1=成功 2=失败
    /// </summary>
    public int OperationResult { get; set; } = 1;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作耗时（毫秒）
    /// </summary>
    public long OperationDuration { get; set; } = 0;
}

/// <summary>
/// 获取操作日志输入参数
/// </summary>
public class GetOperationLogsInput
{
    /// <summary>
    /// 结算ID
    /// </summary>
    [Required(ErrorMessage = "结算ID不能为空")]
    public long SettlementId { get; set; }
}

/// <summary>
/// 获取收款员操作日志输入参数
/// </summary>
public class GetCashierOperationLogsInput
{
    /// <summary>
    /// 收款员ID
    /// </summary>
    [Required(ErrorMessage = "收款员ID不能为空")]
    public long CashierId { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    [Required(ErrorMessage = "开始日期不能为空")]
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    [Required(ErrorMessage = "结束日期不能为空")]
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 操作类型（可选）
    /// </summary>
    public int? OperationType { get; set; }
}