﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

public class UserOutput 
{
    public  long Id { get; set; }
    /// <summary>
    /// 账号
    /// </summary>
    public  string Account { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public  string Password { get; set; }

    /// <summary>
    /// 真实姓名
    /// </summary>
    public  string RealName { get; set; }

    /// <summary>
    /// 真实姓名
    /// </summary>
    public  string  Name { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 性别-男_1、女_2
    /// </summary>
    public GenderEnum Sex { get; set; } = GenderEnum.Male;

    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    public string? Nation { get; set; }

    /// <summary>
    /// 手机号码
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    public CardTypeEnum CardType { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 文化程度
    /// </summary>
    public CultureLevelEnum CultureLevel { get; set; }

    /// <summary>
    /// 政治面貌
    /// </summary>
    public string? PoliticalOutlook { get; set; }

    /// <summary>
    /// 毕业院校
    /// </summary>COLLEGE
    public string? College { get; set; }

    /// <summary>
    /// 办公电话
    /// </summary>
    public string? OfficePhone { get; set; }

    /// <summary>
    /// 紧急联系人
    /// </summary>
    public string? EmergencyContact { get; set; }

    /// <summary>
    /// 紧急联系人电话
    /// </summary>
    public string? EmergencyPhone { get; set; }

    /// <summary>
    /// 紧急联系人地址
    /// </summary>
    public string? EmergencyAddress { get; set; }

    /// <summary>
    /// 个人简介
    /// </summary>
    public string? Introduction { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int OrderNo { get; set; } = 100;

    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; } = StatusEnum.Enable;
    
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum AcSignStatus { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 账号类型
    /// </summary>
    public AccountTypeEnum AccountType { get; set; } = AccountTypeEnum.NormalUser;

    /// <summary>
    /// 直属机构Id
    /// </summary>
    public long OrgId { get; set; }

    /// <summary>
    /// 直属机构
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OrgId))]
    public SysOrg SysOrg { get; set; }

    /// <summary>
    /// 直属主管Id
    /// </summary>
    public long? ManagerUserId { get; set; }

    /// <summary>
    /// 直属主管
    /// </summary>
    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    [Navigate(NavigateType.OneToOne, nameof(ManagerUserId))]
    public SysUser ManagerUser { get; set; }

    /// <summary>
    /// 职位Id
    /// </summary>
    public long PosId { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    [Navigate(NavigateType.OneToOne, nameof(PosId))]
    public SysPos SysPos { get; set; }

    /// <summary>
    /// 工号
    /// </summary>
    public string? JobNum { get; set; }

    /// <summary>
    /// 职级
    /// </summary>
    public string? PosLevel { get; set; }

    /// <summary>
    /// 职称
    /// </summary>
    public string? PosTitle { get; set; }

    /// <summary>
    /// 擅长领域
    /// </summary>
    public string? Expertise { get; set; }

    /// <summary>
    /// 办公区域
    /// </summary>
    public string? OfficeZone { get; set; }

    /// <summary>
    /// 办公室
    /// </summary>
    public string? Office { get; set; }

    /// <summary>
    /// 入职日期
    /// </summary>
    public DateTime? JoinDate { get; set; }

    /// <summary>
    /// 最新登录Ip
    /// </summary>
    public string? LastLoginIp { get; set; }

    /// <summary>
    /// 最新登录地点
    /// </summary>
    public string? LastLoginAddress { get; set; }

    /// <summary>
    /// 最新登录时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 最新登录设备
    /// </summary>
    public string? LastLoginDevice { get; set; }

    /// <summary>
    /// 电子签名
    /// </summary>
    public string? Signature { get; set; }

    /// <summary>
    /// 抗生素处方权
    /// </summary>
    public int AntibacterialPermission { get; set; } = 0;

    /// <summary>
    /// 麻醉处方权
    /// </summary>
    public int NarcoticPermission { get; set; } = 0;

    /// <summary>
    /// 精神药品处方权
    /// </summary>
    public int PsychotropicPermission { get; set; } = 0;

    /// <summary>
    /// 草药处方权
    /// </summary>
    public int HerbsPermission { get; set; } = 0;

    /// <summary>
    /// 处方权限
    /// </summary>
    public int PrescriptionPermission { get; set; } = 0;

    /// <summary>
    /// 行政科室
    /// </summary>
    public int AdministrativeDept { get; set; } = 0;

    /// <summary>
    /// 离职日期
    /// </summary>
    public DateTime? LeaveDate { get; set; }

    /// <summary>
    /// 医保编号
    /// </summary>
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 机构名称
    /// </summary>
    public string OrgName { get; set; }

    /// <summary>
    /// 职位名称
    /// </summary>
    public string PosName { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string RoleName { get; set; }

    /// <summary>
    /// 域用户
    /// </summary>
    public string DomainAccount { get; set; }
}
