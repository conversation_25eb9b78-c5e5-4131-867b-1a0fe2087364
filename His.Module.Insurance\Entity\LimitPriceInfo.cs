﻿using Admin.NET.Core;

namespace His.Module.Insurance.Entity;

/// <summary>
/// 限价信息
/// </summary>
[Tenant("1300000000013")]
[SugarTable("limit_price_info", "限价信息")]
public class LimitPriceInfo : EntityTenant
{
    /// <summary>
    /// 医疗项目编码
    /// </summary>
    [SugarColumn(ColumnName = "ylxm_bm", ColumnDescription = "医疗项目编码", Length = 50)]
    public virtual string? YlxmBm { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    [SugarColumn(ColumnName = "rq_lb", ColumnDescription = "人群类别", Length = 10)]
    public virtual string? RqLb { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    [SugarColumn(ColumnName = "yltc_lb", ColumnDescription = "医疗统筹类别", Length = 10)]
    public virtual string? YltcLb { get; set; }

    /// <summary>
    /// 待遇人员类别
    /// </summary>
    [SugarColumn(ColumnName = "dyry_lb", ColumnDescription = "待遇人员类别", Length = 10)]
    public virtual string? DyryLb { get; set; }

    /// <summary>
    /// 起始日期
    /// </summary>
    [SugarColumn(ColumnName = "qsrq", ColumnDescription = "起始日期")]
    public virtual DateTime? Qsrq { get; set; }

    /// <summary>
    /// 终止日期
    /// </summary>
    [SugarColumn(ColumnName = "zzrq", ColumnDescription = "终止日期")]
    public virtual DateTime? Zzrq { get; set; }

    /// <summary>
    /// 限价
    /// </summary>
    [SugarColumn(ColumnName = "xj", ColumnDescription = "限价")]
    public virtual decimal? Xj { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "bz", ColumnDescription = "备注", Length = 500)]
    public virtual string? Bz { get; set; }

    /// <summary>
    /// 医院编码
    /// </summary>
    [SugarColumn(ColumnName = "yy_bm", ColumnDescription = "医院编码", Length = 50)]
    public virtual string? YyBm { get; set; }

    /// <summary>
    /// 医院级别
    /// </summary>
    [SugarColumn(ColumnName = "yy_jb", ColumnDescription = "医院级别", Length = 10)]
    public virtual string? YyJb { get; set; }

    /// <summary>
    /// 医疗机构性质
    /// </summary>
    [SugarColumn(ColumnName = "yljg_xz", ColumnDescription = "医疗机构性质", Length = 10)]
    public virtual string? YljgXz { get; set; }

    /// <summary>
    /// 同步序号
    /// </summary>
    [SugarColumn(ColumnName = "sxh", ColumnDescription = "同步序号")]
    public virtual long? Sxh { get; set; }

    /// <summary>
    /// 最后同步时间
    /// </summary>
    [SugarColumn(ColumnName = "last_sync_time", ColumnDescription = "最后同步时间")]
    public virtual DateTime? LastSyncTime { get; set; }
}
