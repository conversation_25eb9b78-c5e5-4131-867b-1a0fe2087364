﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Inpatient;

/// <summary>
/// 医疗组维护基础输入参数
/// </summary>
public class MedicalTeamBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 医疗组名称
    /// </summary>
    [Required(ErrorMessage = "医疗组名称不能为空")]
    public virtual string TeamName { get; set; }
    
    /// <summary>
    /// 所属科室ID
    /// </summary>
    [Required(ErrorMessage = "所属科室ID不能为空")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [Required(ErrorMessage = "不能为空")]
    public virtual string DeptName { get; set; }
    
    /// <summary>
    /// 医疗组类型 字典医疗组
    /// </summary>
    [Required(ErrorMessage = "医疗组类型 字典医疗组不能为空")]
    public virtual string TeamType { get; set; }
    
    /// <summary>
    /// 组长ID
    /// </summary>
    public virtual long? TeamLeaderId { get; set; }
    
    /// <summary>
    /// 组长
    /// </summary>
    public virtual string? TeamLeaderName { get; set; }
    
    /// <summary>
    /// 成立日期
    /// </summary>
    public virtual DateTime? EstablishDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 医疗组维护分页查询输入参数
/// </summary>
public class PageMedicalTeamInput : BasePageInput
{
    /// <summary>
    /// 医疗组名称
    /// </summary>
    public string TeamName { get; set; }
    
    /// <summary>
    /// 所属科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string DeptName { get; set; }
    
    /// <summary>
    /// 医疗组类型 字典医疗组
    /// </summary>
    public string TeamType { get; set; }
    
    /// <summary>
    /// 组长ID
    /// </summary>
    public long? TeamLeaderId { get; set; }
    
    /// <summary>
    /// 组长
    /// </summary>
    public string? TeamLeaderName { get; set; }
    
    /// <summary>
    /// 成立日期范围
    /// </summary>
     public DateTime?[] EstablishDateRange { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 医疗组维护增加输入参数
/// </summary>
public class AddMedicalTeamInput
{
    /// <summary>
    /// 医疗组名称
    /// </summary>
    [Required(ErrorMessage = "医疗组名称不能为空")]
    [MaxLength(100, ErrorMessage = "医疗组名称字符长度不能超过100")]
    public string TeamName { get; set; }
    
    /// <summary>
    /// 所属科室ID
    /// </summary>
    [Required(ErrorMessage = "所属科室 不能为空")]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [Required(ErrorMessage = "不能为空")] 
    public string DeptName { get; set; }
    
    /// <summary>
    /// 医疗组类型 字典医疗组
    /// </summary> 
    [MaxLength(100, ErrorMessage = "医疗组类型 字典医疗组字符长度不能超过100")]
    public string TeamType { get; set; }
    
    /// <summary>
    /// 组长ID
    /// </summary>
    public long? TeamLeaderId { get; set; }
    
    /// <summary>
    /// 组长
    /// </summary>
    [MaxLength(20, ErrorMessage = "组长字符长度不能超过20")]
    public string? TeamLeaderName { get; set; }
    
    /// <summary>
    /// 成立日期
    /// </summary>
    public DateTime? EstablishDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 医疗组维护删除输入参数
/// </summary>
public class DeleteMedicalTeamInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 医疗组维护更新输入参数
/// </summary>
public class UpdateMedicalTeamInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 医疗组名称
    /// </summary>    
    [Required(ErrorMessage = "医疗组名称不能为空")]
    [MaxLength(100, ErrorMessage = "医疗组名称字符长度不能超过100")]
    public string TeamName { get; set; }
    
    /// <summary>
    /// 所属科室ID
    /// </summary>    
    [Required(ErrorMessage = "所属科室ID不能为空")]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>    
    [Required(ErrorMessage = "不能为空")]
    [MaxLength(100, ErrorMessage = "字符长度不能超过100")]
    public string DeptName { get; set; }
    
    /// <summary>
    /// 医疗组类型 字典医疗组
    /// </summary>    
    [Required(ErrorMessage = "医疗组类型 字典医疗组不能为空")]
    [MaxLength(100, ErrorMessage = "医疗组类型 字典医疗组字符长度不能超过100")]
    public string TeamType { get; set; }
    
    /// <summary>
    /// 组长ID
    /// </summary>    
    public long? TeamLeaderId { get; set; }
    
    /// <summary>
    /// 组长
    /// </summary>    
    [MaxLength(20, ErrorMessage = "组长字符长度不能超过20")]
    public string? TeamLeaderName { get; set; }
    
    /// <summary>
    /// 成立日期
    /// </summary>    
    public DateTime? EstablishDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 医疗组维护主键查询输入参数
/// </summary>
public class QueryByIdMedicalTeamInput : DeleteMedicalTeamInput
{
}

/// <summary>
/// 医疗组维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportMedicalTeamInput : BaseImportInput
{
    /// <summary>
    /// 医疗组名称
    /// </summary>
    [ImporterHeader(Name = "*医疗组名称")]
    [ExporterHeader("*医疗组名称", Format = "", Width = 25, IsBold = true)]
    public string TeamName { get; set; }
    
    /// <summary>
    /// 所属科室ID
    /// </summary>
    [ImporterHeader(Name = "*所属科室ID")]
    [ExporterHeader("*所属科室ID", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [ImporterHeader(Name = "*")]
    [ExporterHeader("*", Format = "", Width = 25, IsBold = true)]
    public string DeptName { get; set; }
    
    /// <summary>
    /// 医疗组类型 字典医疗组
    /// </summary>
    [ImporterHeader(Name = "*医疗组类型 字典医疗组")]
    [ExporterHeader("*医疗组类型 字典医疗组", Format = "", Width = 25, IsBold = true)]
    public string TeamType { get; set; }
    
    /// <summary>
    /// 组长ID
    /// </summary>
    [ImporterHeader(Name = "组长ID")]
    [ExporterHeader("组长ID", Format = "", Width = 25, IsBold = true)]
    public long? TeamLeaderId { get; set; }
    
    /// <summary>
    /// 组长
    /// </summary>
    [ImporterHeader(Name = "组长")]
    [ExporterHeader("组长", Format = "", Width = 25, IsBold = true)]
    public string? TeamLeaderName { get; set; }
    
    /// <summary>
    /// 成立日期
    /// </summary>
    [ImporterHeader(Name = "成立日期")]
    [ExporterHeader("成立日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? EstablishDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    [ImporterHeader(Name = "状态(1:启用 2:停用,)")]
    [ExporterHeader("状态(1:启用 2:停用,)", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
// public class AddMedicalTeamMemberInput
// {
//     /// <summary>
//     /// 医疗组名称
//     /// </summary>
//     [Required(ErrorMessage = "医疗组为空")] 
//     public long TeamId { get; set; }
//     
//     /// <summary>
//     /// 所属科室ID
//     /// </summary>
//     [Required(ErrorMessage = "所属科室 不能为空")]
//     public long? DeptId { get; set; }
//     
//     /// <summary>
//     /// 
//     /// </summary> 
//     public string DeptName { get; set; }
//      
//     /// <summary>
//     /// 成员ID
//     /// </summary>
//     [Required]
//   
//     public virtual long StaffId { get; set; }
//     
//     /// <summary>
//     /// 成员名称
//     /// </summary>
//     [Required] 
//     public virtual string StaffName { get; set; }
//     
//     /// <summary>
//     /// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
//     /// </summary>
//     [Required] 
//     public virtual string RoleType { get; set; }
// }
