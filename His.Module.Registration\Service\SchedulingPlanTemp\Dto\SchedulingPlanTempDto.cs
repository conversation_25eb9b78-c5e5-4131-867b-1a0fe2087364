﻿namespace His.Module.Registration;

/// <summary>
/// 医生排班计划输出参数
/// </summary>
public class SchedulingPlanTempDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 医生id
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 时间段id
    /// </summary>
    public long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    public string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    public string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 号别id
    /// </summary>
    public long? RegCategoryId { get; set; }
    
    /// <summary>
    /// 挂号类别名称
    /// </summary>
    public string? RegCategoryName { get; set; }
    
    /// <summary>
    /// 限号数
    /// </summary>
    public int? RegLimit { get; set; }
    
    /// <summary>
    /// 限预约号数
    /// </summary>
    public int? AppLimit { get; set; }
    
    /// <summary>
    /// 已挂号数
    /// </summary>
    public int? RegNumber { get; set; }
    
    /// <summary>
    /// 已预约号数
    /// </summary>
    public int? AppNumber { get; set; }
 
    
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 星期几
    /// </summary>
    public string? WeekDay { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public string? RoomName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
