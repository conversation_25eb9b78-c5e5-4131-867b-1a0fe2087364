﻿namespace His.Module.OutpatientDoctor.Enum;

/// <summary>
/// 收费状态枚举
/// </summary>
[Description("收费状态枚举")]
public enum ChargeStatusEnum
{
    /// <summary>
	/// 未收费
	/// </summary>
	[Description("未收费")]
    NotCharged = 0,

    /// <summary>
    /// 已收费
    /// </summary>
    [Description("已收费")]
    Charged = 1,

    /// <summary>
    /// 取药
    /// </summary>
    [Description("取药")]
    Dispensary = 2,

    /// <summary>
    /// 退药
    /// </summary>
    [Description("退药")]
    DrugRepercussion = 3,

    /// <summary>
    /// 退费
    /// </summary>
    [Description("退费")]
    Refund = 4,

    /// <summary>
    /// 红冲
    /// </summary>
    [Description("红冲")]
    HongChong = 5,
}