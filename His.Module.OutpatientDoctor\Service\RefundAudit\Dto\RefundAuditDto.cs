﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 退费审核表输出参数
/// </summary>
public class RefundAuditDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 退费申请ID
    /// </summary>
    public long? ApplyId { get; set; }
    
    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 审核部门ID
    /// </summary>
    public long? AuditDeptId { get; set; }
    
    /// <summary>
    /// 审核部门编码
    /// </summary>
    public string? AuditDeptCode { get; set; }
    
    /// <summary>
    /// 审核部门名称
    /// </summary>
    public string? AuditDeptName { get; set; }
    
    /// <summary>
    /// 审核人ID
    /// </summary>
    public long? AuditUserId { get; set; }
    
    /// <summary>
    /// 审核人编码
    /// </summary>
    public string? AuditUserCode { get; set; }
    
    /// <summary>
    /// 审核人名称
    /// </summary>
    public string? AuditUserName { get; set; }
    
    /// <summary>
    /// 审核原因
    /// </summary>
    public string? AuditReason { get; set; }
    
    /// <summary>
    /// 审核状态
    /// </summary>
    public int? AuditStatus { get; set; }
    
    /// <summary>
    /// 创建机构ID
    /// </summary>
    public long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    public string? CreateOrgName { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}

public class InitAuditRecordDto
{
 public   long? ApplyId { get; set; }
 public   long? ChargeId { get; set; }
}