﻿using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 预交金建议表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("advance_payment_suggestion", "预交金建议表")]
public class AdvancePaymentSuggestion : EntityTenant
{
    /// <summary>
    /// 诊断编码
    /// </summary>
    [SugarColumn(ColumnName = "diagnosis_code", ColumnDescription = "诊断编码", Length = 128)]
    public virtual string? DiagnosisCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "diagnosis_name", ColumnDescription = "诊断名称", Length = 255)]
    public virtual string? DiagnosisName { get; set; }
    
    /// <summary>
    /// 病种名称
    /// </summary>
    [SugarColumn(ColumnName = "disease_type_name", ColumnDescription = "病种名称", Length = 255)]
    public virtual string? DiseaseTypeName { get; set; }
    
    /// <summary>
    /// 职工
    /// </summary>
    [SugarColumn(ColumnName = "employee", ColumnDescription = "职工", Length = 16, DecimalDigits=2)]
    public virtual decimal? Employee { get; set; }
    
    /// <summary>
    /// 居民
    /// </summary>
    [SugarColumn(ColumnName = "resident", ColumnDescription = "居民", Length = 16, DecimalDigits=2)]
    public virtual decimal? Resident { get; set; }
    
    /// <summary>
    /// 自费
    /// </summary>
    [SugarColumn(ColumnName = "self_funded", ColumnDescription = "自费", Length = 16, DecimalDigits=2)]
    public virtual decimal? SelfFunded { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }
    
}
