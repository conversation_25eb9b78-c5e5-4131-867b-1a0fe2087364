﻿using His.Module.Patient.Api.Enum;

namespace His.Module.Patient.Api.Api.Dto;

public class CardInfoDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>

    public string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>

    public long PatientId { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>

    public BusinessTypeEnum BusinessType { get; set; }

    /// <summary>
    /// 密码
    /// </summary>

    public string? Password { get; set; }

    /// <summary>
    /// 余额
    /// </summary>

    public decimal? Balance { get; set; }

    /// <summary>
    /// 卡状态 0挂失;1正常;2 退卡
    /// </summary>

    public CardStatusEnum Status { get; set; } = CardStatusEnum.Normal;

    /// <summary>
    /// 是否储值
    /// </summary>

    public bool? IsStored { get; set; }

    /// <summary>
    /// 备注
    /// </summary>

    public string? Remark { get; set; }

    /// <summary>
    /// 指定使用科室，多个以“,”分割
    /// </summary>

    public string? UseDepts { get; set; }

    /// <summary>
    /// 指定充值方式，多个以“,”分割
    /// </summary>

    public string? ChargeModes { get; set; }
}