﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 处方主表服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class PrescriptionMainService : IDynamicA<PERSON><PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<PrescriptionMain> _prescriptionMainRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public PrescriptionMainService(SqlSugarRepository<PrescriptionMain> prescriptionMainRep, ISqlSugarClient sqlSugarClient)
    {
        _prescriptionMainRep = prescriptionMainRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询处方主表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询处方主表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<PrescriptionMainOutput>> Page(PagePrescriptionMainInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _prescriptionMainRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.PrescriptionNo.Contains(input.Keyword) || u.PrescriptionType.Contains(input.Keyword) || u.PrescriptionName.Contains(input.Keyword) || u.OutpatientPrescriptionType.Contains(input.Keyword) || u.PatientName.Contains(input.Keyword) || u.BillingDoctorSign.Contains(input.Keyword) || u.Remark.Contains(input.Keyword) || u.DiagnosticCode.Contains(input.Keyword) || u.DiagnosticName.Contains(input.Keyword) || u.Diagnostic1Code.Contains(input.Keyword) || u.Diagnostic1Name.Contains(input.Keyword) || u.Diagnostic2Code.Contains(input.Keyword) || u.Diagnostic2Name.Contains(input.Keyword) || u.TcmDiagnosticCode.Contains(input.Keyword) || u.TcmDiagnosticName.Contains(input.Keyword) || u.HerbsDecoction.Contains(input.Keyword) || u.RefundInvoiceNumber.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrescriptionNo), u => u.PrescriptionNo.Contains(input.PrescriptionNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrescriptionType), u => u.PrescriptionType.Contains(input.PrescriptionType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrescriptionName), u => u.PrescriptionName.Contains(input.PrescriptionName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.WstrnMdcnPrescriptionType), u => u.OutpatientPrescriptionType.Contains(input.WstrnMdcnPrescriptionType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BillingDoctorSign), u => u.BillingDoctorSign.Contains(input.BillingDoctorSign.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DiagnosticCode), u => u.DiagnosticCode.Contains(input.DiagnosticCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DiagnosticName), u => u.DiagnosticName.Contains(input.DiagnosticName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Diagnostic1Code), u => u.Diagnostic1Code.Contains(input.Diagnostic1Code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Diagnostic1Name), u => u.Diagnostic1Name.Contains(input.Diagnostic1Name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Diagnostic2Code), u => u.Diagnostic2Code.Contains(input.Diagnostic2Code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Diagnostic2Name), u => u.Diagnostic2Name.Contains(input.Diagnostic2Name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TcmDiagnosticCode), u => u.TcmDiagnosticCode.Contains(input.TcmDiagnosticCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TcmDiagnosticName), u => u.TcmDiagnosticName.Contains(input.TcmDiagnosticName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.HerbsDecoction), u => u.HerbsDecoction.Contains(input.HerbsDecoction.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RefundInvoiceNumber), u => u.RefundInvoiceNumber.Contains(input.RefundInvoiceNumber.Trim()))
            .WhereIF(input.PrescriptionTimeRange?.Length == 2, u => u.PrescriptionTime >= input.PrescriptionTimeRange[0] && u.PrescriptionTime <= input.PrescriptionTimeRange[1])
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.RegisterId != null, u => u.RegisterId == input.RegisterId)
            .WhereIF(input.BillingDeptId != null, u => u.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.BillingDoctorId != null, u => u.BillingDoctorId == input.BillingDoctorId)
            .WhereIF(input.ChargeStaffId != null, u => u.ChargeStaffId == input.ChargeStaffId)
            .WhereIF(input.ChargeTimeRange?.Length == 2, u => u.ChargeTime >= input.ChargeTimeRange[0] && u.ChargeTime <= input.ChargeTimeRange[1])
            .WhereIF(input.RefundStaffId != null, u => u.RefundStaffId == input.RefundStaffId)
            .WhereIF(input.RefundTimeRange?.Length == 2, u => u.RefundTime >= input.RefundTimeRange[0] && u.RefundTime <= input.RefundTimeRange[1])
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .WhereIF(input.IsPrint != null, u => u.IsPrint == input.IsPrint)
            .WhereIF(input.HerbsQuantity != null, u => u.HerbsQuantity == input.HerbsQuantity)
            .WhereIF(input.IsDecoction != null, u => u.IsDecoction == input.IsDecoction)
            .WhereIF(input.PrintTimeRange?.Length == 2, u => u.PrintTime >= input.PrintTimeRange[0] && u.PrintTime <= input.PrintTimeRange[1])
            .WhereIF(input.ChargeMainId != null, u => u.ChargeMainId == input.ChargeMainId)
            .Select<PrescriptionMainOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取处方主表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取处方主表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<PrescriptionMain> Detail([FromQuery] QueryByIdPrescriptionMainInput input)
    {
        return await _prescriptionMainRep.GetFirstAsync(u => u.Id == input.Id);
    }
 
}