namespace His.Module.Insurance.Service.Settlement.Dto;

public class BaseSettlementResponse
{
    /// <summary>
    /// 执行代码（0为成功，其他为失败；执行成功时才返回下面的数据）
    /// </summary>
    public string resultcode { get; set; }
    
    /// <summary>
    /// 执行信息
    /// </summary>
    public string resulttext { get; set; }

    /// <summary>
    /// 便于后续接口使用
    /// </summary>
    public string UserKey { get; set; }
    public bool IsSuccess
    {
        get
        {
            return resultcode == "0";
            
        }

    }

}