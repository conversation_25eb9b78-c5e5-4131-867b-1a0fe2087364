﻿using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;

namespace His.Module.Registration.Service.ClinicRoom.Dto;

/// <summary>
/// 诊室维护基础输入参数
/// </summary>
public class ClinicRoomBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public virtual string? Name { get; set; }
    
    /// <summary>
    /// 诊室编码
    /// </summary>
    public virtual string? Code { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    public virtual string? IpAddress { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 诊室维护分页查询输入参数
/// </summary>
public class PageClinicRoomInput : BasePageInput
{
    /// <summary>
    /// 诊室名称
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊室编码
    /// </summary>
    public string? Code { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 诊室维护增加输入参数
/// </summary>
public class AddClinicRoomInput
{
    /// <summary>
    /// 诊室名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "诊室名称字符长度不能超过255")]
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊室编码
    /// </summary>
    [MaxLength(255, ErrorMessage = "诊室编码字符长度不能超过255")]
    public string? Code { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "科室名称字符长度不能超过255")]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    [MaxLength(255, ErrorMessage = "ip地址字符长度不能超过255")]
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 诊室维护删除输入参数
/// </summary>
public class DeleteClinicRoomInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 诊室维护更新输入参数
/// </summary>
public class UpdateClinicRoomInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>    
    [MaxLength(255, ErrorMessage = "诊室名称字符长度不能超过255")]
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊室编码
    /// </summary>    
    [MaxLength(255, ErrorMessage = "诊室编码字符长度不能超过255")]
    public string? Code { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>    
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>    
    [MaxLength(255, ErrorMessage = "科室名称字符长度不能超过255")]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>    
    [MaxLength(255, ErrorMessage = "ip地址字符长度不能超过255")]
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 诊室维护主键查询输入参数
/// </summary>
public class QueryByIdClinicRoomInput : DeleteClinicRoomInput
{
}

/// <summary>
/// 诊室维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportClinicRoomInput : BaseImportInput
{
    /// <summary>
    /// 诊室名称
    /// </summary>
    [ImporterHeader(Name = "诊室名称")]
    [ExporterHeader("诊室名称", Format = "", Width = 25, IsBold = true)]
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊室编码
    /// </summary>
    [ImporterHeader(Name = "诊室编码")]
    [ExporterHeader("诊室编码", Format = "", Width = 25, IsBold = true)]
    public string? Code { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    [ImporterHeader(Name = "科室id")]
    [ExporterHeader("科室id", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [ImporterHeader(Name = "科室名称")]
    [ExporterHeader("科室名称", Format = "", Width = 25, IsBold = true)]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    [ImporterHeader(Name = "ip地址")]
    [ExporterHeader("ip地址", Format = "", Width = 25, IsBold = true)]
    public string? IpAddress { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
