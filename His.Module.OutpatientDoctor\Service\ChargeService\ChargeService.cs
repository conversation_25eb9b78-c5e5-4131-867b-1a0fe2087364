using Furion.DatabaseAccessor;
using His.Module.Financial.Api.MedicalCard;
using His.Module.Financial.Api.MedicalCard.Dto;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.OutpatientDoctor.Service.Dto;
using His.Module.Patient.Api.Api;
using Yitter.IdGenerator;
namespace His.Module.OutpatientDoctor.Service;

/// <summary>
/// 计费服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class ChargeService(
    UserManager userManager,
    SqlSugarRepository<ChargeMain> chargeMainRep,
    SqlSugarRepository<ChargeDetail> chargeDetailRep,
    ICardInfoApi cardInfoApi,
    IMedicalCardPaymentApi medicalCardPaymentApi,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient, IChargeApi
{
    private readonly ISqlSugarClient _sqlSugarClient = sqlSugarClient;


    /// <summary>
    /// 获取门诊收费记录 ℹ️
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取门诊收费记录")]
    [ApiDescriptionSettings(Name = "Get"), HttpGet]
    public async Task<ChargeMain> Get([FromQuery] long id)
    {
        return await chargeMainRep.GetFirstAsync(u => u.Id == id);
    }

    /// <summary>
    /// 获取门诊收费明细 ℹ️
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取门诊收费明细")]
    [ApiDescriptionSettings(Name = "GetDetail"), HttpGet]
    public async Task<List<ChargeDetail>> GetDetail([FromQuery] long id)
    {
        return await chargeDetailRep.AsQueryable().Where(u => u.ChargeId == id)
                .ToListAsync()
            ;
    }


    /// <summary>
    /// 获取门诊收费明细 ℹ️
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取门诊收费明细")]
    [ApiDescriptionSettings(Name = "GetChargeList"), HttpPost]
    public async Task<List<OutpatientChargeDetailDto>> GetChargeList([FromBody]  long?[] chargeIds)
    {
        var list = await
            chargeDetailRep.AsQueryable().Where(u => chargeIds.Contains(u.ChargeId)).ToListAsync();
        return list.Adapt<List<OutpatientChargeDetailDto>>();
    
    }

    /// <summary>
    /// 计费 ➕
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [DisplayName("计费")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<OutpatientChargeResult> Add(OutpatientChargeDto dto)
    {
        //构造收费主表实体
        var main = dto.Adapt<ChargeMain>();

        if (string.IsNullOrWhiteSpace(dto.InvoiceNumber))
            main.InvoiceNumber = await GetInvoiceNumber();
        else
            main.InvoiceNumber = dto.InvoiceNumber;


        main.PayMethod1Id = -1;
        main.PayAmount1 = 0;
        // main.   PayMethod2Id = input.PaymentMethod;
        // main.  PayAmount2 = input.PersonalPayment;
        main.Status = ChargeStatusEnum.NotCharged;//提前插入费用表，但不计费
        main.Type = 1;


        //插入收费主表
        await chargeMainRep.InsertAsync(main);
        var details = dto.Details.Adapt<List<ChargeDetail>>();
        var output = new OutpatientChargeResult
        {
            ChargeId = main.Id,
            ChargeStaffId = main.CreateUserId,
            ChargeTime = main.CreateTime,
            ChargeStaffName = main.CreateUserName,
            Details = []
        };

        foreach (var item in details)
        {
            item.ChargeId = main.Id;
            item.ExecuteStatus = YesNoEnum.N;
            item.Withdrawal = YesNoEnum.N;
            item.PayMethod1Id = main.PayMethod1Id;
            item.PayAmount1 = main.PayAmount1;
            item.PayMethod2Id = main.PayMethod2Id;
            item.PayAmount2 = main.PayAmount2;
            item.Status = ChargeStatusEnum.Charged;
            await chargeDetailRep.InsertAsync(item);
            output.Details.Add(new OutpatientChargeDetailsOutput()
            {
                ChargeDetailId = item.Id, ChargeId = item.ChargeId, ItemId = item.ItemId
            });
        }

        // var cardInfoEntity = await cardInfoApi.Detail(dto.CardId);
        // //扣除费用
        // //处理卡扣款情况
        // if (dto.PaymentMethod ==658657048305733)// 650961344696389)
        // {
        //     //卡扣款
        //     await cardInfoApi.CardPay(new CardPayInput()
        //     {
        //         Id = cardInfoEntity.Id,
        //         InvoiceNumber = main.InvoiceNumber,
        //         PayAmount = main.PayAmount2,
        //         PayMethodId = dto.PaymentMethod,
        //         // PayMethodId =  main.PayMethod1Id, 
        //     });
        // }


        return output;
    }

    /// <summary>
    /// 批量计费 ➕
    /// </summary>
    /// <param name="dtos"></param>
    /// <returns></returns>
    [DisplayName("批量计费")]
    [ApiDescriptionSettings(Name = "BatchAdd")][HttpPost]
    [UnitOfWork]
    public async Task BatchAdd(List<OutpatientChargeDto> dtos)
    {
        var chargeMainList = new List<ChargeMain>();
        var chargeDetailList = new List<ChargeDetail>();

        foreach (var dto in dtos)
        {
            //构造收费主表实体
            var main = dto.Adapt<ChargeMain>();
            main.Id = YitIdHelper.NextId();
            main.PayMethod1Id = -1;
            main.PayAmount1 = 0;
            main.Status = ChargeStatusEnum.NotCharged; //提前插入费用表，但不计费
            main.Type = 1;
            chargeMainList.Add(main);
            var details = dto.Details.Adapt<List<ChargeDetail>>();
            foreach (var item in details)
            {
                item.Id = YitIdHelper.NextId();
                item.ChargeId = main.Id;
                item.ExecuteStatus = YesNoEnum.N;
                item.Withdrawal = YesNoEnum.N;
                item.PayMethod1Id = main.PayMethod1Id;
                item.PayAmount1 = main.PayAmount1;
                item.PayMethod2Id = main.PayMethod2Id;
                item.PayAmount2 = main.PayAmount2;
                item.Status = ChargeStatusEnum.NotCharged;
                chargeDetailList.Add(item);
            }
        }
        await chargeMainRep.InsertRangeAsync(chargeMainList);
        await chargeDetailRep.InsertRangeAsync(chargeDetailList);
    }
    
    
    
    
    

    [ApiDescriptionSettings(IgnoreApi = true), UnitOfWork]
    public async Task<bool> Refund(OutpatientChargeRefundDto dto)
    {
        var chargeMain = await chargeMainRep.GetFirstAsync(x => x.Id == dto.ChargeId);
        // 卡交易记录
        await medicalCardPaymentApi.Refund(new MedicalCardPaymentRefundDto()
        {
            PatientId = chargeMain.PatientId,
            CardNo = chargeMain.CardNo, 
            PayAmount = chargeMain.PayAmount2, 
            VisitNo = chargeMain.VisitNo,
        });

        // 插入一条负值 ，

 
        var chargeDetail = await chargeDetailRep.AsQueryable().Where(x => x.ChargeId == dto.ChargeId).ToListAsync();
        var refundMain = chargeMain.Adapt<ChargeMain>();
        refundMain.ResetNull(); 
        refundMain.OriginalRecordId = dto.ChargeId;
        refundMain.PayAmount1 = -chargeMain.PayAmount1;
        refundMain.PayAmount2 = -chargeMain.PayAmount2; // 退款金额
        refundMain.Status = ChargeStatusEnum.Refund;
        refundMain.RefundReason = dto.Reason; // 退款原因
        await chargeMainRep.InsertAsync(refundMain);
        foreach (var detail in chargeDetail)
        {
            var refundDetail = detail.Adapt<ChargeDetail>();
            refundDetail.ResetNull(); 
            refundDetail.OriginalDetailsId = detail.Id;
            refundDetail.PayAmount1 = -detail.PayAmount1;
            refundDetail.PayAmount2 = -detail.PayAmount2; // 退款金额
            refundDetail.ChargeId = refundMain.Id;
            refundDetail.Status = ChargeStatusEnum.Refund;
            refundDetail.RefundReason = dto.Reason; // 退款原因
            await chargeDetailRep.InsertAsync(refundDetail);
        }
        // 修改原始记录

        await chargeDetailRep.UpdateAsync(
            u => new ChargeDetail() { Status = ChargeStatusEnum.Refund, }
            , u => u.ChargeId == dto.ChargeId
        );
        return await chargeMainRep.UpdateAsync(
            u => new ChargeMain() { Status = ChargeStatusEnum.Refund, }
            , u => u.Id == dto.ChargeId
        );
    }

    /// <summary>
    /// 确认收费
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [DisplayName("确认收费")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<OutpatientChargeResult> ConfirmStatus(OutpatientChargeConfirmDto dto)
    {
        var main = await chargeMainRep.GetFirstAsync(x => x.Id == dto.ChargeId);

        if (main == null)
            throw new Exception("未找到该收费单");
        
        var now= DateTime.Now;
        await chargeDetailRep.UpdateAsync(
            u => new ChargeDetail()
            {
                ChargeTime =  now,
                ChargeUserId = userManager.UserId,
                ChargeUserName = userManager.RealName,
                ChargeDeptId = userManager.OrgId,
                ChargeDeptName = userManager.OrgName,
                Status =ChargeStatusEnum.Charged,
            },
            u => u.ChargeId == dto.ChargeId
        );
        var r= await chargeMainRep.UpdateAsync(
            u => new ChargeMain()
            {
                ChargeTime =  now,
                ChargeUserId = userManager.UserId,
                ChargeUserName = userManager.RealName,
                ChargeDeptId = userManager.OrgId,
                ChargeDeptName = userManager.OrgName,
                Status =ChargeStatusEnum.Charged,
            },
            u => u.Id == dto.ChargeId
        );
        
        var result=new OutpatientChargeResult()
        {
            BillingId=main.BillingId ?? 0,
            ChargeId = dto.ChargeId,
            ChargeTime = now,
            ChargeStaffId = userManager.UserId,
            ChargeStaffName = userManager.RealName,
            Details = []
        };
        return result;
    }

    /// <summary>
    /// 批量确认收费
    /// </summary>
    /// <param name="dtos"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [DisplayName("批量确认收费")]
    [ApiDescriptionSettings(Name = "BatchConfirmStatus")][HttpPost]
    [UnitOfWork]
    public async Task BatchConfirmStatus(List<OutpatientChargeConfirmDto> dtos)
    {
        var chargeIds = dtos.Select(u => u.ChargeId).ToList();
        var mains = await chargeMainRep.GetListAsync(x => chargeIds.Contains(x.Id));
        var now = DateTime.Now;
        var userId = userManager.UserId;
        var userName = userManager.RealName;
        var orgId = userManager.OrgId;
        var orgName = userManager.OrgName;
        var invoiceNumber = await GetInvoiceNumber();
        foreach (var dto in dtos)
        {
            var main = mains.First(u => u.Id == dto.ChargeId);
            if (main == null)
                throw Oops.Oh($"未找到收费单id:{dto.ChargeId}");
        }
        await chargeDetailRep.UpdateAsync(
            u => new ChargeDetail
            {

                ChargeTime = now,
                ChargeUserId = userId,
                ChargeUserName = userName,
                ChargeDeptId = orgId,
                ChargeDeptName = orgName,
                Status = ChargeStatusEnum.Charged
            },
            u => chargeIds.Contains(u.ChargeId)
        );
        await chargeMainRep.UpdateAsync(
            u => new ChargeMain
            {
                InvoiceNumber = invoiceNumber,
                ChargeTime = now,
                ChargeUserId = userId,
                ChargeUserName = userName,
                ChargeDeptId = orgId,
                ChargeDeptName = orgName,
                Status = ChargeStatusEnum.Charged
            },
            u => chargeIds.Contains(u.Id)
        );
        //卡扣费
        await medicalCardPaymentApi.Deduction(new MedicalCardPaymentDeductionDto
        {
            PatientId = mains.First().PatientId, PayAmount = mains.Sum(u => u.TotalAmount)
        });
    }

    
    
    
    public async Task<bool> ExecuteStatus(OutpatientChargeExecuteDto dto)
    {
        var main = await chargeMainRep.GetFirstAsync(x => x.Id == dto.ChargeId);

        if (main == null)
            throw new Exception("未找到该收费单");

        await chargeDetailRep.UpdateAsync(
            u => new ChargeDetail()
            {
                ExecuteTime = dto.ExecuteTime,
                ExecuteStatus = YesNoEnum.Y,
                ExecuteDeptId = dto.ExecuteDeptId,
                ExecuteDoctorId = dto.ExecuteDoctorId,
                // ExecuteTime = dto.ExecuteTime
            },
            u => u.ChargeId == dto.ChargeId
        );
        return await chargeMainRep.UpdateAsync(
            u => new ChargeMain()
            {
                ExecuteDeptId = dto.ExecuteDeptId,
                ExecuteDoctorId = dto.ExecuteDoctorId,
                ExecuteTime = dto.ExecuteTime,
                ExecuteStatus = YesNoEnum.Y,
            },
            u => u.Id == dto.ChargeId
        );
    }

    public async Task<string> GetInvoiceNumber()
    {
        return await chargeMainRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('charge_main_in_num_seq')As varchar),8,'0')");
    }
    

    /// <summary>
    /// 查询已计费数据用于退费申请
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询已计费数据用于退费申请")]
    [ApiDescriptionSettings(Name = "ListOfRefund"), HttpPost]
    public async Task<List<ChargeOfRefundOutput>> ListOfRefund(QueryChargeInput input)
    {
        var query = chargeMainRep.AsQueryable()
            .Where(  u => u.Status==ChargeStatusEnum.Charged)
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), (c) => c.VisitNo == input.VisitNo.Trim())
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo),
                (c) => c.OutpatientNo == input.OutpatientNo.Trim())
            .LeftJoin<RefundApply>((c, r) => c.Id == r.ChargeId)
            .Select((c, r) => new ChargeOfRefundOutput()
            {
                ChargeId = c.Id,
                RefundApplyId = r.Id,
                ApplyReason = r.ApplyReason,
                CreateTime = c.CreateTime,
                Id = c.Id,
                ApplyTime = r.ApplyTime,
                ApplyUserName = r.CreateUserName,
                ApplyStatus = r.Status,
                AuditStatus = r.AuditStatus,
            }, true);
        var list = await query.ToListAsync();
        if (list.IsNullOrEmpty()) return list;
        var ids = list.Select(p => p.Id).ToList();
        var details = await chargeDetailRep.AsQueryable()
            .InnerJoin<ChargeMain>((d, m) => d.ChargeId == m.Id && m.Status==ChargeStatusEnum.Charged)
            .WhereIF(ids.Count > 0, (d, m) => ids.Contains(m.Id)).ToListAsync();
        // 遍历list 将details 赋值给list
        list.ForEach(item =>
        {
            item.Details = details.Where(u => u.ChargeId == item.Id).ToList().Adapt<List<ChargeDetailOutput>>();
        });


        return list;
    }
    /// <summary>
    /// 删除费用数据 ❌
    /// </summary>
    /// <param name="chargeId"></param>
    /// <returns></returns>
  
    [NonAction]
    [ApiDescriptionSettings(IgnoreApi = true)]
    public async Task Delete(long? chargeId)
    {
        var entity = await chargeMainRep.GetFirstAsync(x => x.Id == chargeId) ?? 
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        await chargeDetailRep.FakeDeleteAsync(entity);
       

        var details= await chargeDetailRep.GetListAsync(x => x.ChargeId == chargeId);
        await chargeDetailRep.FakeDeleteAsync(details);
   
    }
    
}