using His.Module.Financial.Const;
using His.Module.Financial.Entity;
using System.Text.Json;
namespace His.Module.Financial.Service;

/// <summary>
/// 收款员操作日志服务 📝
/// </summary>
[ApiDescriptionSettings(FinancialConst.GroupName, Order = 150)]
public class CashierOperationLogService(
    SqlSugarRepository<CashierSettlementLog> cashierSettlementLogRep)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 记录收款员操作日志
    /// </summary>
    /// <param name="input">操作日志记录输入参数</param>
    /// <returns></returns>
    public async Task LogOperation(LogOperationDetailInput input)
    {
        var log = new CashierSettlementLog
        {
            SettlementId = input.SettlementId,
            OperationType = input.OperationType,
            OperationDesc = input.OperationDesc,
            OperationReason = input.OperationReason,
            OperationResult = input.OperationResult,
            OperationDuration = input.OperationDuration,
            ErrorMessage = input.ErrorMessage,
            BeforeData = input.BeforeData != null ? JsonSerializer.Serialize(input.BeforeData) : null,
            AfterData = input.AfterData != null ? JsonSerializer.Serialize(input.AfterData) : null,
            AffectedBusinessIds = input.AffectedBusinessIds != null && input.AffectedBusinessIds.Count > 0
                ? JsonSerializer.Serialize(input.AffectedBusinessIds) : null
        };
        await cashierSettlementLogRep.InsertAsync(log);

    }

    /// <summary>
    /// 记录收费操作日志 💰
    /// </summary>
    /// <param name="input">操作日志记录输入参数</param>
    /// <returns></returns>
    [DisplayName("记录收费操作日志")]
    [ApiDescriptionSettings(Name = "LogChargeOperation")][HttpPost]
    public async Task LogChargeOperation(LogOperationInput input)
    {
        await LogOperation(new LogOperationDetailInput
        {
            SettlementId = 0,
            OperationType = input.OperationType,
            OperationDesc = input.OperationDesc,
            BeforeData = input.BeforeData,
            AfterData = input.AfterData,
            OperationReason = input.OperationReason,
            AffectedBusinessIds = [input.BusinessId],
            OperationResult = input.OperationResult,
            ErrorMessage = input.ErrorMessage,
            OperationDuration = input.OperationDuration
        });
    }

    /// <summary>
    /// 记录充值操作日志 💳
    /// </summary>
    /// <param name="input">操作日志记录输入参数</param>
    /// <returns></returns>
    [DisplayName("记录充值操作日志")]
    [ApiDescriptionSettings(Name = "LogRechargeOperation")][HttpPost]
    public async Task LogRechargeOperation(LogOperationInput input)
    {
        await LogOperation(new LogOperationDetailInput
        {
            SettlementId = 0,
            OperationType = input.OperationType,
            OperationDesc = input.OperationDesc,
            BeforeData = input.BeforeData,
            AfterData = input.AfterData,
            OperationReason = input.OperationReason,
            AffectedBusinessIds = [input.BusinessId],
            OperationResult = input.OperationResult,
            ErrorMessage = input.ErrorMessage,
            OperationDuration = input.OperationDuration
        });
    }

    /// <summary>
    /// 获取操作日志列表 📋
    /// </summary>
    /// <param name="input">获取操作日志输入参数</param>
    /// <returns></returns>
    [DisplayName("获取操作日志")]
    [ApiDescriptionSettings(Name = "GetOperationLogs")][HttpPost]
    public async Task<List<CashierSettlementLog>> GetOperationLogs(GetOperationLogsInput input)
    {

        return await cashierSettlementLogRep.AsQueryable()
            .Where(u => u.SettlementId == input.SettlementId)
            .OrderByDescending(u => u.CreateTime)
            .ToListAsync();

    }

    /// <summary>
    /// 获取收款员操作日志 👤
    /// </summary>
    /// <param name="input">获取收款员操作日志输入参数</param>
    /// <returns></returns>
    [DisplayName("获取收款员操作日志")]
    [ApiDescriptionSettings(Name = "GetCashierOperationLogs")][HttpPost]
    public async Task<List<CashierSettlementLog>> GetCashierOperationLogs(GetCashierOperationLogsInput input)
    {

        return await cashierSettlementLogRep.AsQueryable()
            .Where(u => u.CreateUserId == input.CashierId)
            .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
            .WhereIF(input.OperationType.HasValue, u => u.OperationType == input.OperationType)
            .OrderByDescending(u => u.CreateTime)
            .ToListAsync();

    }
}