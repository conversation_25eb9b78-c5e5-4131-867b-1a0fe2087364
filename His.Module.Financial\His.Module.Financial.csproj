﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
	  <NoWarn>1701;1702;1591;8632</NoWarn>
	  <DocumentationFile></DocumentationFile>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <GenerateDocumentationFile>True</GenerateDocumentationFile>
	  <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.NET.Core\Admin.NET.Core.csproj" />
    <ProjectReference Include="..\His.Module.Financial.Api\His.Module.Financial.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Configuration\Financial.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Remove="sql\cashier_settlement.sql" />
    <None Remove="Service\CashierSettlement\结算状态检查功能说明.md" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="sql\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Service\CashierSettlement\CashierSettlementServiceTest.cs" />
  </ItemGroup>

</Project>
