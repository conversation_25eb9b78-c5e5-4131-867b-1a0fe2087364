﻿using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 医疗组基本信息表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("medical_team", "医疗组基本信息表")]
public class MedicalTeam : EntityTenant
{
    /// <summary>
    /// 医疗组名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "team_name", ColumnDescription = "医疗组名称", Length = 100)]
    public virtual string TeamName { get; set; }
    
    /// <summary>
    /// 所属科室ID
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "所属科室ID")]
    public virtual long DeptId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "", Length = 100)]
    public virtual string DeptName { get; set; }
    
    /// <summary>
    /// 医疗组类型 字典医疗组
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "team_type", ColumnDescription = "医疗组类型 字典医疗组", Length = 100)]
    public virtual string TeamType { get; set; }
    
    /// <summary>
    /// 组长ID
    /// </summary>
    [SugarColumn(ColumnName = "team_leader_id", ColumnDescription = "组长ID")]
    public virtual long? TeamLeaderId { get; set; }
    
    /// <summary>
    /// 组长
    /// </summary>
    [SugarColumn(ColumnName = "team_leader_name", ColumnDescription = "组长", Length = 20)]
    public virtual string? TeamLeaderName { get; set; }
    
    /// <summary>
    /// 成立日期
    /// </summary>
    [SugarColumn(ColumnName = "establish_date", ColumnDescription = "成立日期")]
    public virtual DateTime? EstablishDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态(1:启用 2:停用,)")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
     
    
 
    
}
