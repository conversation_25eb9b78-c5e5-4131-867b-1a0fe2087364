﻿using Admin.NET.Core;

namespace His.Module.Insurance.Entity;

/// <summary>
/// 首先自付比例信息
/// </summary>
[Tenant("1300000000013")]
[SugarTable("first_self_pay_ratio_info", "首先自付比例信息")]
public class FirstSelfPayRatioInfo : EntityTenant
{
    /// <summary>
    /// 医疗项目编码
    /// </summary>
    [SugarColumn(ColumnName = "ylxm_bm", ColumnDescription = "医疗项目编码", Length = 50)]
    public virtual string? YlxmBm { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    [SugarColumn(ColumnName = "rq_lb", ColumnDescription = "人群类别", Length = 10)]
    public virtual string? RqLb { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    [SugarColumn(ColumnName = "yltc_lb", ColumnDescription = "医疗统筹类别", Length = 10)]
    public virtual string? YltcLb { get; set; }

    /// <summary>
    /// 待遇人员类别
    /// </summary>
    [SugarColumn(ColumnName = "dyry_lb", ColumnDescription = "待遇人员类别", Length = 10)]
    public virtual string? DyryLb { get; set; }

    /// <summary>
    /// 起始日期
    /// </summary>
    [SugarColumn(ColumnName = "qsrq", ColumnDescription = "起始日期")]
    public virtual DateTime? Qsrq { get; set; }

    /// <summary>
    /// 终止日期
    /// </summary>
    [SugarColumn(ColumnName = "zzrq", ColumnDescription = "终止日期")]
    public virtual DateTime? Zzrq { get; set; }

    /// <summary>
    /// 首先自付比例
    /// </summary>
    [SugarColumn(ColumnName = "sxzf_bl", ColumnDescription = "首先自付比例")]
    public virtual decimal? SxzfBl { get; set; }

    /// <summary>
    /// 说明
    /// </summary>
    [SugarColumn(ColumnName = "sm", ColumnDescription = "说明", Length = 500)]
    public virtual string? Sm { get; set; }

    /// <summary>
    /// 险种标志
    /// </summary>
    [SugarColumn(ColumnName = "xz_bz", ColumnDescription = "险种标志", Length = 10)]
    public virtual string? XzBz { get; set; }

    /// <summary>
    /// 同步序号
    /// </summary>
    [SugarColumn(ColumnName = "sxh", ColumnDescription = "同步序号")]
    public virtual long? Sxh { get; set; }

    /// <summary>
    /// 最后同步时间
    /// </summary>
    [SugarColumn(ColumnName = "last_sync_time", ColumnDescription = "最后同步时间")]
    public virtual DateTime? LastSyncTime { get; set; }
}
