using His.Module.Inpatient.Entity;
using His.Module.Shared.Service.BasicInfo.Dto;

namespace His.Module.Shared.Service.BasicInfo;
 
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100,
    Name = "BasicInfo")]
public class InpatientBasicInfoService(
    SqlSugarRepository<MedicalTeam> medicalTeamRep,
    SqlSugarRepository<MedicalTeamMember> medicalTeamMemberRep,
        SqlSugarRepository<BedInfo> bedInfoRep)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 获取医疗团队列表
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetMedicalTeamList")]
    [DisplayName("获取医疗团队列表")]
    [SkipPermission]
    public async Task<List<MedicalTeam>> GetMedicalTeamList(QueryMedicalTeamInput input )
    {
        return  await medicalTeamRep.AsQueryable().Where(x=>x.DeptId==input.DeptId
            && x.Status == 1
            
            )
            .ToListAsync();
    }
    /// <summary>
    /// 获取医疗团队列表
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetMedicalTeamMemberList")]
    [DisplayName("获取医疗团队下的成员")] 
    [SkipPermission]
    public async Task<List<MedicalTeamMember>> GetMedicalTeamMemberList(QuerymedicalTeamMemberInput input )
    {
        return  await medicalTeamMemberRep. AsQueryable().Where(x=>x.TeamId==input.TeamId
                                                                  && x.Status == 1  )
            .WhereIF(!string.IsNullOrEmpty(input.RoleType),x=>x.RoleType==input.RoleType)
           // .WhereIF(string.IsNullOrEmpty(input.RoleType),x=>x.RoleType==input.RoleType)
            .ToListAsync();
    }
    /// <summary>
    /// 获取可用床位
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetEnableWardBedList")]
    [DisplayName("获取可用病区床位列表")]
    [SkipPermission]
    public async Task<List<BedInfo>> GetEnableWardBedList(QueryEnableWardBedInput input )
    {
        return  await bedInfoRep.AsQueryable().Where(x=>x.Status == 1 &&x.BedStatus == "N"
            )
            .WhereIF(input.DeptId!=null,x=>x.DeptId==input.DeptId)
            .WhereIF(input.WardId!=null,x=>x.WardId == input.WardId)
            .ToListAsync();
        
        
    }
}