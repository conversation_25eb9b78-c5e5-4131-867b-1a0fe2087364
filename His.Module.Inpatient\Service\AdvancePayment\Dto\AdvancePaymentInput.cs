﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Inpatient;

/// <summary>
/// 预交金基础输入参数
/// </summary>
public class AdvancePaymentBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 收据号
    /// </summary>
    public virtual string? VoucherNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 住院登记号
    /// </summary>
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    public virtual string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    public virtual string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    public virtual string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 保险号码
    /// </summary>
    public virtual string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预缴金额
    /// </summary>
    public virtual decimal? AdvanceAmount { get; set; }
    
    /// <summary>
    /// 大写金额
    /// </summary>
    public virtual string? AdvanceAmountChinese { get; set; }
    
    /// <summary>
    /// 缴费时间
    /// </summary>
    public virtual DateTime? AdvanceTime { get; set; }
    
    /// <summary>
    /// 付款方式
    /// </summary>
    public virtual string? PaymentMethod { get; set; }
    
    /// <summary>
    /// 付款记录ID
    /// </summary>
    public virtual long? PaymentRecordId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 预交金分页查询输入参数
/// </summary>
public class PageAdvancePaymentInput : BasePageInput
{
    /// <summary>
    /// 收据号
    /// </summary>
    public string? VoucherNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 住院登记号
    /// </summary>
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    public string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 保险号码
    /// </summary>
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预缴金额
    /// </summary>
    public decimal? AdvanceAmount { get; set; }
    
    /// <summary>
    /// 大写金额
    /// </summary>
    public string? AdvanceAmountChinese { get; set; }
    
    /// <summary>
    /// 缴费时间范围
    /// </summary>
     public DateTime?[] AdvanceTimeRange { get; set; }
    
    /// <summary>
    /// 付款方式
    /// </summary>
    public string? PaymentMethod { get; set; }
    
    /// <summary>
    /// 付款记录ID
    /// </summary>
    public long? PaymentRecordId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 预交金增加输入参数
/// </summary>
public class AddAdvancePaymentInput
{
    /// <summary>
    /// 收据号
    /// </summary>
    [MaxLength(100, ErrorMessage = "收据号字符长度不能超过100")]
    public string? VoucherNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 住院登记号
    /// </summary>
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [MaxLength(100, ErrorMessage = "住院号字符长度不能超过100")]
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [MaxLength(100, ErrorMessage = "住院流水号字符长度不能超过100")]
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    [MaxLength(100, ErrorMessage = "病案号字符长度不能超过100")]
    public string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 保险号码
    /// </summary>
    [MaxLength(100, ErrorMessage = "保险号码字符长度不能超过100")]
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预缴金额
    /// </summary>
    public decimal? AdvanceAmount { get; set; }
    
    /// <summary>
    /// 大写金额
    /// </summary>
    [MaxLength(100, ErrorMessage = "大写金额字符长度不能超过100")]
    public string? AdvanceAmountChinese { get; set; }
    
    /// <summary>
    /// 缴费时间
    /// </summary>
    public DateTime? AdvanceTime { get; set; }
    
    /// <summary>
    /// 付款方式
    /// </summary>
    [MaxLength(100, ErrorMessage = "付款方式字符长度不能超过100")]
    public string? PaymentMethod { get; set; }
    
    /// <summary>
    /// 付款记录ID
    /// </summary>
    public long? PaymentRecordId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
}

/// <summary>
/// 预交金删除输入参数
/// </summary>
public class DeleteAdvancePaymentInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 预交金更新输入参数
/// </summary>
public class UpdateAdvancePaymentInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 收据号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "收据号字符长度不能超过100")]
    public string? VoucherNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>    
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 住院登记号
    /// </summary>    
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "住院号字符长度不能超过100")]
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "住院流水号字符长度不能超过100")]
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "病案号字符长度不能超过100")]
    public string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 保险号码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "保险号码字符长度不能超过100")]
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预缴金额
    /// </summary>    
    public decimal? AdvanceAmount { get; set; }
    
    /// <summary>
    /// 大写金额
    /// </summary>    
    [MaxLength(100, ErrorMessage = "大写金额字符长度不能超过100")]
    public string? AdvanceAmountChinese { get; set; }
    
    /// <summary>
    /// 缴费时间
    /// </summary>    
    public DateTime? AdvanceTime { get; set; }
    
    /// <summary>
    /// 付款方式
    /// </summary>    
    [MaxLength(100, ErrorMessage = "付款方式字符长度不能超过100")]
    public string? PaymentMethod { get; set; }
    
    /// <summary>
    /// 付款记录ID
    /// </summary>    
    public long? PaymentRecordId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    public int? Status { get; set; }
    
}

/// <summary>
/// 预交金主键查询输入参数
/// </summary>
public class QueryByIdAdvancePaymentInput : DeleteAdvancePaymentInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataAdvancePaymentInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 预交金数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportAdvancePaymentInput : BaseImportInput
{
    /// <summary>
    /// 收据号
    /// </summary>
    [ImporterHeader(Name = "收据号")]
    [ExporterHeader("收据号", Format = "", Width = 25, IsBold = true)]
    public string? VoucherNo { get; set; }
    
    /// <summary>
    /// 患者ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者ID 文本
    /// </summary>
    [ImporterHeader(Name = "患者ID")]
    [ExporterHeader("患者ID", Format = "", Width = 25, IsBold = true)]
    public string PatientFkDisplayName { get; set; }
    
    /// <summary>
    /// 住院登记号 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 住院登记号 文本
    /// </summary>
    [ImporterHeader(Name = "住院登记号")]
    [ExporterHeader("住院登记号", Format = "", Width = 25, IsBold = true)]
    public string RegisterFkDisplayName { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [ImporterHeader(Name = "住院号")]
    [ExporterHeader("住院号", Format = "", Width = 25, IsBold = true)]
    public string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [ImporterHeader(Name = "住院流水号")]
    [ExporterHeader("住院流水号", Format = "", Width = 25, IsBold = true)]
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    [ImporterHeader(Name = "病案号")]
    [ExporterHeader("病案号", Format = "", Width = 25, IsBold = true)]
    public string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 保险号码
    /// </summary>
    [ImporterHeader(Name = "保险号码")]
    [ExporterHeader("保险号码", Format = "", Width = 25, IsBold = true)]
    public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预缴金额
    /// </summary>
    [ImporterHeader(Name = "预缴金额")]
    [ExporterHeader("预缴金额", Format = "", Width = 25, IsBold = true)]
    public decimal? AdvanceAmount { get; set; }
    
    /// <summary>
    /// 大写金额
    /// </summary>
    [ImporterHeader(Name = "大写金额")]
    [ExporterHeader("大写金额", Format = "", Width = 25, IsBold = true)]
    public string? AdvanceAmountChinese { get; set; }
    
    /// <summary>
    /// 缴费时间
    /// </summary>
    [ImporterHeader(Name = "缴费时间")]
    [ExporterHeader("缴费时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? AdvanceTime { get; set; }
    
    /// <summary>
    /// 付款方式
    /// </summary>
    [ImporterHeader(Name = "付款方式")]
    [ExporterHeader("付款方式", Format = "", Width = 25, IsBold = true)]
    public string? PaymentMethod { get; set; }
    
    /// <summary>
    /// 付款记录ID
    /// </summary>
    [ImporterHeader(Name = "付款记录ID")]
    [ExporterHeader("付款记录ID", Format = "", Width = 25, IsBold = true)]
    public long? PaymentRecordId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
}
