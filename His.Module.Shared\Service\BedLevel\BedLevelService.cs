﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.Shared.Entity;
using Microsoft.AspNetCore.Http;

namespace His.Module.Shared.Service;

/// <summary>
/// 床位等级服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class BedLevelService(
    SqlSugarRepository<BedLevel> bedLevelRep,
    SqlSugarRepository<BedInfo> bedInfoRep,
    SqlSugarRepository<BedLevelChargeItem> bedLevelChargeItemRep,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 分页查询床位等级 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询床位等级")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<BedLevelOutput>> Page(PageBedLevelInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = bedLevelRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.LevelName.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.LevelName), u => u.LevelName.Contains(input.LevelName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<BedLevelOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取床位等级详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取床位等级详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<BedLevel> Detail([FromQuery] QueryByIdBedLevelInput input)
    {
        return await bedLevelRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加床位等级 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加床位等级")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost,UnitOfWork]
    public async Task<long> Add(AddBedLevelInput input)
    {
        var entity = input.Adapt<BedLevel>();
        entity.Amount = input.ChargeItems.Sum(p => p.Amount);
        var r = await bedLevelRep.InsertAsync(entity);
        var items = input.ChargeItems.Adapt<List<BedLevelChargeItem>>();
        items.ForEach(p =>
        {
            p.LevelId = entity.Id;
            p.LevelName = entity.LevelName;
            p.Status = 1;
        });
        await bedLevelChargeItemRep.InsertRangeAsync(items);
        return entity.Id;
    }

    /// <summary>
    /// 更新床位等级 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新床位等级")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost,UnitOfWork]
    public async Task Update(UpdateBedLevelInput input)
    {
        var entity = input.Adapt<BedLevel>();
        await bedLevelChargeItemRep.AsDeleteable().Where(p=>p.LevelId==entity.Id).ExecuteCommandAsync();
        await bedLevelRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
        var items = input.ChargeItems.Adapt<List<BedLevelChargeItem>>();
        items.ForEach(p => p.LevelId = entity.Id);
        await bedLevelChargeItemRep.InsertRangeAsync(items);
    }

    /// <summary>
    /// 删除床位等级 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除床位等级")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost,UnitOfWork]
    public async Task Delete(DeleteBedLevelInput input)
    {
        if (await bedInfoRep.IsAnyAsync(u => u.BedLevelId == input.Id))
        {
            throw Oops.Oh("该床位等级有床位再使用，不能删除");
        }

        var entity = await bedLevelRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await bedLevelRep.FakeDeleteAsync(entity); //假删除
        var chargeItem=await bedLevelChargeItemRep.GetListAsync(u=>u.LevelId==input.Id);
        await bedLevelChargeItemRep.FakeDeleteAsync(chargeItem); //

        //await _bedLevelRep.DeleteAsync(entity);   //真删除
    }
    /// <summary>
    /// 删除床位等级 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除床位费")]
    [ApiDescriptionSettings(Name = "DeleteChargeItem"), HttpPost,UnitOfWork]
    public async Task DeleteChargeItem(DeleteBedLevelInput input)
    {
        
  
        await bedLevelChargeItemRep.DeleteByIdAsync(input.Id);
        //await _bedLevelRep.DeleteAsync(entity);   //真删除
    }
    /// <summary>
    /// 批量删除床位等级 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除床位等级")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteBedLevelInput> input)
    {
     
        foreach (var row in input)
        {

           await this.Delete(row);
        }
        return 1;
        // var exp = Expressionable.Create<BedLevel>();
        // foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        // var list = await bedLevelRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
        //
        // return await bedLevelRep.FakeDeleteAsync(list); //假删除
        //return await _bedLevelRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetBedLevelStatus(SetBedLevelStatusInput input)
    {
        await bedLevelRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    /// <summary>
    /// 导出床位等级记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出床位等级记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageBedLevelInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportBedLevelOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "床位等级导出记录");
    }

    /// <summary>
    /// 下载床位等级数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载床位等级数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportBedLevelOutput>(), "床位等级导入模板");
    }

    /// <summary>
    /// 导入床位等级记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入床位等级记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportBedLevelInput, BedLevel>(file, (list, markerErrorAction) =>
            {
                sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        return true;
                    }).Adapt<List<BedLevel>>();

                    var storageable = bedLevelRep.Context.Storageable(rows)
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.LevelName), "床位等级名称不能为空")
                        .SplitError(it => it.Item.LevelName?.Length > 100, "床位等级名称长度不能超过100个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}