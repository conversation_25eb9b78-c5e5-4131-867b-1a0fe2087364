﻿namespace His.Module.Pharmacy;

/// <summary>
/// 出库管理基础输入参数
/// </summary>
public class StorageOutRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 出库单号
    /// </summary>
    public virtual string? StorageOutNo { get; set; }
    
    /// <summary>
    /// 出库类型
    /// </summary>
    [Dict("StorageOutType", AllowNullValue=true)]
    public virtual string? StorageOutType { get; set; }
    
    /// <summary>
    /// 出库日期
    /// </summary>
    public virtual DateTime? StorageOutTime { get; set; }
    
    /// <summary>
    /// 目标科室
    /// </summary>
    public virtual long? TargetDeptId { get; set; }
    
    /// <summary>
    /// 领用人ID
    /// </summary>
    public virtual long? TargetUserId { get; set; }
    
    /// <summary>
    /// 领用人编码
    /// </summary>
    public virtual string? TargetUserCode { get; set; }
    
    /// <summary>
    /// 领用人名称
    /// </summary>
    public virtual string? TargetUserName { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    public virtual string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 出库管理分页查询输入参数
/// </summary>
public class PageStorageOutRecordInput : BasePageInput
{
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 出库单号
    /// </summary>
    public string? StorageOutNo { get; set; }
    
    /// <summary>
    /// 出库类型
    /// </summary>
    [Dict("StorageOutType", AllowNullValue=true)]
    public string? StorageOutType { get; set; }
    
    /// <summary>
    /// 出库日期范围
    /// </summary>
     public DateTime?[] StorageOutTimeRange { get; set; }
    
    /// <summary>
    /// 目标科室
    /// </summary>
    public long? TargetDeptId { get; set; }
    
    /// <summary>
    /// 领用人ID
    /// </summary>
    public long? TargetUserId { get; set; }
    
    /// <summary>
    /// 领用人编码
    /// </summary>
    public string? TargetUserCode { get; set; }
    
    /// <summary>
    /// 领用人名称
    /// </summary>
    public string? TargetUserName { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 出库管理增加输入参数
/// </summary>
public class AddStorageOutRecordInput
{
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "库房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 出库单号
    /// </summary>
    [MaxLength(100, ErrorMessage = "出库单号字符长度不能超过100")]
    public string? StorageOutNo { get; set; }
    
    /// <summary>
    /// 出库类型
    /// </summary>
    [Dict("StorageOutType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "出库类型字符长度不能超过100")]
    public string? StorageOutType { get; set; }
    
    /// <summary>
    /// 出库日期
    /// </summary>
    public DateTime? StorageOutTime { get; set; }
    
    /// <summary>
    /// 目标科室
    /// </summary>
    public long? TargetDeptId { get; set; }

    /// <summary>
    /// 目标科室名称
    /// </summary>
    public string? TargetDeptName { get; set; }
    
    /// <summary>
    /// 领用人ID
    /// </summary>
    public long? TargetUserId { get; set; }
    
    /// <summary>
    /// 领用人编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "领用人编码字符长度不能超过100")]
    public string? TargetUserCode { get; set; }
    
    /// <summary>
    /// 领用人名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "领用人名称字符长度不能超过100")]
    public string? TargetUserName { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    [MaxLength(100, ErrorMessage = "发票号字符长度不能超过100")]
    public string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(100, ErrorMessage = "备注字符长度不能超过100")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    public List<AddStorageOutDetailInput> Details { get; set; }
    
}

/// <summary>
/// 出库管理删除输入参数
/// </summary>
public class DeleteStorageOutRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 出库管理更新输入参数
/// </summary>
public class UpdateStorageOutRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>    
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "库房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>    
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 出库单号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "出库单号字符长度不能超过100")]
    public string? StorageOutNo { get; set; }
    
    /// <summary>
    /// 出库类型
    /// </summary>    
    [Dict("StorageOutType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "出库类型字符长度不能超过100")]
    public string? StorageOutType { get; set; }
    
    /// <summary>
    /// 出库日期
    /// </summary>    
    public DateTime? StorageOutTime { get; set; }
    
    /// <summary>
    /// 目标科室
    /// </summary>    
    public long? TargetDeptId { get; set; }
    
    /// <summary>
    /// 领用人ID
    /// </summary>    
    public long? TargetUserId { get; set; }
    
    /// <summary>
    /// 领用人编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "领用人编码字符长度不能超过100")]
    public string? TargetUserCode { get; set; }
    
    /// <summary>
    /// 领用人名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "领用人名称字符长度不能超过100")]
    public string? TargetUserName { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>    
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>    
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "发票号字符长度不能超过100")]
    public string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(100, ErrorMessage = "备注字符长度不能超过100")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    
    public List<UpdateStorageOutDetailInput> Details { get; set; }
    
}

/// <summary>
/// 出库管理主键查询输入参数
/// </summary>
public class QueryByIdStorageOutRecordInput : DeleteStorageOutRecordInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataStorageOutRecordInput
{
    /// <summary>
    /// 目标部门过滤条件，用于筛选下拉数据中的目标部门信息
    /// </summary>
    public string TargetDeptFilter { get; set; }
    
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 出库管理数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportStorageOutRecordInput : BaseImportInput
{
    /// <summary>
    /// 库房 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房 文本
    /// </summary>
    [ImporterHeader(Name = "库房")]
    [ExporterHeader("库房", Format = "", Width = 25, IsBold = true)]
    public string StorageFkDisplayName { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [ImporterHeader(Name = "库房编码")]
    [ExporterHeader("库房编码", Format = "", Width = 25, IsBold = true)]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类型 文本
    /// </summary>
    [Dict("DrugType")]
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string DrugTypeDictLabel { get; set; }
    
    /// <summary>
    /// 出库单号
    /// </summary>
    [ImporterHeader(Name = "出库单号")]
    [ExporterHeader("出库单号", Format = "", Width = 25, IsBold = true)]
    public string? StorageOutNo { get; set; }
    
    /// <summary>
    /// 出库类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? StorageOutType { get; set; }
    
    /// <summary>
    /// 出库类型 文本
    /// </summary>
    [Dict("StorageOutType")]
    [ImporterHeader(Name = "出库类型")]
    [ExporterHeader("出库类型", Format = "", Width = 25, IsBold = true)]
    public string StorageOutTypeDictLabel { get; set; }
    
    /// <summary>
    /// 出库日期
    /// </summary>
    [ImporterHeader(Name = "出库日期")]
    [ExporterHeader("出库日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? StorageOutTime { get; set; }
    
    /// <summary>
    /// 目标科室 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? TargetDeptId { get; set; }
    
    /// <summary>
    /// 目标科室 文本
    /// </summary>
    [ImporterHeader(Name = "目标科室")]
    [ExporterHeader("目标科室", Format = "", Width = 25, IsBold = true)]
    public string TargetDeptFkDisplayName { get; set; }
    
    /// <summary>
    /// 领用人ID
    /// </summary>
    [ImporterHeader(Name = "领用人ID")]
    [ExporterHeader("领用人ID", Format = "", Width = 25, IsBold = true)]
    public long? TargetUserId { get; set; }
    
    /// <summary>
    /// 领用人编码
    /// </summary>
    [ImporterHeader(Name = "领用人编码")]
    [ExporterHeader("领用人编码", Format = "", Width = 25, IsBold = true)]
    public string? TargetUserCode { get; set; }
    
    /// <summary>
    /// 领用人名称
    /// </summary>
    [ImporterHeader(Name = "领用人名称")]
    [ExporterHeader("领用人名称", Format = "", Width = 25, IsBold = true)]
    public string? TargetUserName { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    [ImporterHeader(Name = "总进价")]
    [ExporterHeader("总进价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [ImporterHeader(Name = "总零售价")]
    [ExporterHeader("总零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    [ImporterHeader(Name = "发票号")]
    [ExporterHeader("发票号", Format = "", Width = 25, IsBold = true)]
    public string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 状态 文本
    /// </summary>
    [Dict("StorageInOutStatus")]
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public string StatusDictLabel { get; set; }
    
}
