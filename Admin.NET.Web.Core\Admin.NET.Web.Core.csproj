<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <NoWarn>1701;1702;1591</NoWarn>
    <DocumentationFile></DocumentationFile>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <Copyright>Admin.NET</Copyright>
    <Description>Admin.NET 通用权限开发平台</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IGeekFan.AspNetCore.Knife4jUI" Version="0.0.16" />
    <PackageReference Include="System.Security.Cryptography.Pkcs" Version="9.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.NET.Application\Admin.NET.Application.csproj" />
    <ProjectReference Include="..\His.Module.Financial\His.Module.Financial.csproj" />
    <ProjectReference Include="..\His.Module.InpatientDoctor\His.Module.InpatientDoctor.csproj" />
    <ProjectReference Include="..\His.Module.InpatientNurse\His.Module.InpatientNurse.csproj" />
    <ProjectReference Include="..\His.Module.Inpatient\His.Module.Inpatient.csproj" />
    <ProjectReference Include="..\His.Module.InsuranceSettlementForLiaocheng\His.Module.InsuranceSettlementForLiaocheng.csproj" />
    <ProjectReference Include="..\His.Module.Insurance\His.Module.Insurance.csproj" />
    <ProjectReference Include="..\His.Module.MedicalTech\His.Module.MedicalTech.csproj" />
    <ProjectReference Include="..\His.Module.Nursing\His.Module.Nursing.csproj" />
    <ProjectReference Include="..\His.Module.OutpatientDoctor\His.Module.OutpatientDoctor.csproj" />
    <ProjectReference Include="..\His.Module.Patient\His.Module.Patient.csproj" />
    <ProjectReference Include="..\His.Module.Pharmacy\His.Module.Pharmacy.csproj" />
    <ProjectReference Include="..\His.Module.Registration.Api\His.Module.Registration.Api.csproj" />
    <ProjectReference Include="..\His.Module.Registration\His.Module.Registration.csproj" />
    <ProjectReference Include="..\His.Module.Report\His.Module.Report.csproj" />
    <ProjectReference Include="..\His.Module.Shared\His.Module.Shared.csproj" />
  </ItemGroup>

</Project>
