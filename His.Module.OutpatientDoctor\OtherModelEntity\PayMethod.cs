﻿namespace His.Module.OutpatientDoctor.OtherModelEntity;

/// <summary>
/// 支付方式表
/// </summary>
[SugarTable(null, "支付方式表")]
[Tenant("1300000000014")]
public class PayMethod : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码", Length = 64)]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 64)]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 64)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 64)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    [SugarColumn(ColumnName = "type", ColumnDescription = "类型", Length = 16)]
    public virtual string? Type { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public string? Remark { get; set; }

    /// <summary>
    /// 挂号价格
    /// </summary>
    [SugarColumn(ColumnName = "reg_price", ColumnDescription = "挂号价格", Length = 5, DecimalDigits = 2)]
    public decimal? RegPrice { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public int? OrderNo { get; set; } = 100;
}