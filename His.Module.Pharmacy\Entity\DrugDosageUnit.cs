﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品剂量单位表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_dosage_unit", "药品剂量单位表")]
public class DrugDosageUnit : EntityTenant
{
    /// <summary>
    /// 剂量单位名称
    /// </summary>
    [SugarColumn(ColumnName = "unit_name", ColumnDescription = "剂量单位名称", Length = 100)]
    public virtual string? UnitName { get; set; }
    
    /// <summary>
    /// 剂量单位名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "unit_pinyin", ColumnDescription = "剂量单位名称拼音", Length = 100)]
    public virtual string? UnitPinyin { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "1 启用 2 停用")]
    public virtual int? Status { get; set; }
    
}
