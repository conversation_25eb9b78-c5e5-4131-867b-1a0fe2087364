﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;
 

/// <summary>
/// 门诊发药记录表分页查询输入参数
/// </summary>
public class PageOutpatientDrugSendRecordInput : BasePageInput
{
    /// <summary>
    /// 发药单号
    /// </summary>
    public string? SendNo { get; set; }
    
    /// <summary>
    /// 发药人ID
    /// </summary>
    public long? SendUserId { get; set; }
    
    /// <summary>
    /// 发药人名称
    /// </summary>
    public string? SendUserName { get; set; }
    
    /// <summary>
    /// 发药时间范围
    /// </summary>
     public DateTime?[] SendTimeRange { get; set; }
    
    /// <summary>
    /// 审核人ID
    /// </summary>
    public long? AuditUserId { get; set; }
    
    /// <summary>
    /// 审核人名称
    /// </summary>
    public string? AuditUserName { get; set; }
    
    /// <summary>
    /// 审核时间范围
    /// </summary>
     public DateTime?[] AuditTimeRange { get; set; }
    
    /// <summary>
    /// 调配人员ID
    /// </summary>
    public long? PickUserId { get; set; }
    
    /// <summary>
    /// 调配人员名称
    /// </summary>
    public string? PickUserName { get; set; }
    
    /// <summary>
    /// 调配时间范围
    /// </summary>
     public DateTime?[] PickTimeRange { get; set; }
    
    /// <summary>
    /// 核对人员ID
    /// </summary>
    public long? CheckUserId { get; set; }
    
    /// <summary>
    /// 核对人员名称
    /// </summary>
    public string? CheckUserName { get; set; }
    
    /// <summary>
    /// 核对时间范围
    /// </summary>
     public DateTime?[] CheckTimeRange { get; set; }
    
    /// <summary>
    /// 药房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号ID
    /// </summary>
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 处方ID
    /// </summary>
    public long? PrescriptionId { get; set; }
    /// <summary>
    /// 处方明细ID
    /// </summary>
    public virtual long? PrescriptionDetailId { get; set; }
    /// <summary>
    /// 处方号
    /// </summary>
    public string? PrescriptionNo { get; set; }
    
    /// <summary>
    /// 处方时间范围
    /// </summary>
     public DateTime?[] PrescriptionTimeRange { get; set; }
    
    /// <summary>
    /// 处方类型
    /// </summary>
    public string? PrescriptionType { get; set; }
    
    /// <summary>
    /// 库存ID
    /// </summary>
    public long? InventoryId { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生名称
    /// </summary>
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }
    
    /// <summary>
    /// 收费人员ID
    /// </summary>
    public long? ChargeStaffId { get; set; }
    
    /// <summary>
    /// 收费ID
    /// </summary>
    public long? ChargeId { get; set; }
    
    /// <summary>
    /// 收费时间范围
    /// </summary>
     public DateTime?[] ChargeTimeRange { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    public string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 发药数量
    /// </summary>
    public decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单次剂量
    /// </summary>
    public decimal? SingleDose { get; set; }
    
    /// <summary>
    /// 单次剂量单位
    /// </summary>
    public string? SingleDoseUnit { get; set; }
    
    /// <summary>
    /// 用药途径ID
    /// </summary>
    public long? MedicationRoutesId { get; set; }
    
    /// <summary>
    /// 用药频次ID
    /// </summary>
    public long? FrequencyId { get; set; }
    
    /// <summary>
    /// 用药天数
    /// </summary>
    public Int16? MedicationDays { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? Price { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? Amount { get; set; }
    
    /// <summary>
    /// 草药付数
    /// </summary>
    public int? HerbsQuantity { get; set; }
    
    /// <summary>
    /// 煎药方法
    /// </summary>
    [Dict("DecoctionMethod", AllowNullValue=true)]
    public string? DecoctionMethod { get; set; }
    
    /// <summary>
    /// 是否代煎
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue=true)]
    public YesNoEnum? IsDecoction { get; set; }
    
    /// <summary>
    /// 退药数量
    /// </summary>
    public decimal? RefundQuantity { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>
    public decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期范围
    /// </summary>
     public DateTime?[] ProductionDateRange { get; set; }
    
    /// <summary>
    /// 有效期范围
    /// </summary>
     public DateTime?[] ExpirationDateRange { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 药品通用名编码
    /// </summary>
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂家
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}
 

 
 

/// <summary>
/// 门诊发药记录表主键查询输入参数
/// </summary>
public class QueryByIdOutpatientDrugSendRecordInput  
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataOutpatientDrugSendRecordInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 门诊发药记录表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportOutpatientDrugSendRecordInput : BaseImportInput
{
    /// <summary>
    /// 发药单号
    /// </summary>
    [ImporterHeader(Name = "发药单号")]
    [ExporterHeader("发药单号", Format = "", Width = 25, IsBold = true)]
    public string? SendNo { get; set; }
    
    /// <summary>
    /// 发药人ID
    /// </summary>
    [ImporterHeader(Name = "发药人ID")]
    [ExporterHeader("发药人ID", Format = "", Width = 25, IsBold = true)]
    public long? SendUserId { get; set; }
    
    /// <summary>
    /// 发药人名称
    /// </summary>
    [ImporterHeader(Name = "发药人名称")]
    [ExporterHeader("发药人名称", Format = "", Width = 25, IsBold = true)]
    public string? SendUserName { get; set; }
    
    /// <summary>
    /// 发药时间
    /// </summary>
    [ImporterHeader(Name = "发药时间")]
    [ExporterHeader("发药时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? SendTime { get; set; }
    
    /// <summary>
    /// 审核人ID
    /// </summary>
    [ImporterHeader(Name = "审核人ID")]
    [ExporterHeader("审核人ID", Format = "", Width = 25, IsBold = true)]
    public long? AuditUserId { get; set; }
    
    /// <summary>
    /// 审核人名称
    /// </summary>
    [ImporterHeader(Name = "审核人名称")]
    [ExporterHeader("审核人名称", Format = "", Width = 25, IsBold = true)]
    public string? AuditUserName { get; set; }
    
    /// <summary>
    /// 审核时间
    /// </summary>
    [ImporterHeader(Name = "审核时间")]
    [ExporterHeader("审核时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 调配人员ID
    /// </summary>
    [ImporterHeader(Name = "调配人员ID")]
    [ExporterHeader("调配人员ID", Format = "", Width = 25, IsBold = true)]
    public long? PickUserId { get; set; }
    
    /// <summary>
    /// 调配人员名称
    /// </summary>
    [ImporterHeader(Name = "调配人员名称")]
    [ExporterHeader("调配人员名称", Format = "", Width = 25, IsBold = true)]
    public string? PickUserName { get; set; }
    
    /// <summary>
    /// 调配时间
    /// </summary>
    [ImporterHeader(Name = "调配时间")]
    [ExporterHeader("调配时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? PickTime { get; set; }
    
    /// <summary>
    /// 核对人员ID
    /// </summary>
    [ImporterHeader(Name = "核对人员ID")]
    [ExporterHeader("核对人员ID", Format = "", Width = 25, IsBold = true)]
    public long? CheckUserId { get; set; }
    
    /// <summary>
    /// 核对人员名称
    /// </summary>
    [ImporterHeader(Name = "核对人员名称")]
    [ExporterHeader("核对人员名称", Format = "", Width = 25, IsBold = true)]
    public string? CheckUserName { get; set; }
    
    /// <summary>
    /// 核对时间
    /// </summary>
    [ImporterHeader(Name = "核对时间")]
    [ExporterHeader("核对时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? CheckTime { get; set; }
    
    /// <summary>
    /// 药房 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房 文本
    /// </summary>
    [ImporterHeader(Name = "药房")]
    [ExporterHeader("药房", Format = "", Width = 25, IsBold = true)]
    public string StorageFkDisplayName { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>
    [ImporterHeader(Name = "药房编码")]
    [ExporterHeader("药房编码", Format = "", Width = 25, IsBold = true)]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [ImporterHeader(Name = "患者ID")]
    [ExporterHeader("患者ID", Format = "", Width = 25, IsBold = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    [ImporterHeader(Name = "患者名称")]
    [ExporterHeader("患者名称", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号ID
    /// </summary>
    [ImporterHeader(Name = "挂号ID")]
    [ExporterHeader("挂号ID", Format = "", Width = 25, IsBold = true)]
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    [ImporterHeader(Name = "就诊号")]
    [ExporterHeader("就诊号", Format = "", Width = 25, IsBold = true)]
    public string? VisitNo { get; set; }
 
    /// <summary>
    /// 处方号
    /// </summary>
    [ImporterHeader(Name = "处方号")]
    [ExporterHeader("处方号", Format = "", Width = 25, IsBold = true)]
    public string? PrescriptionNo { get; set; }
    
    /// <summary>
    /// 处方时间
    /// </summary>
    [ImporterHeader(Name = "处方时间")]
    [ExporterHeader("处方时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? PrescriptionTime { get; set; }
    
    /// <summary>
    /// 处方类型
    /// </summary>
    [ImporterHeader(Name = "处方类型")]
    [ExporterHeader("处方类型", Format = "", Width = 25, IsBold = true)]
    public string? PrescriptionType { get; set; }
    
    /// <summary>
    /// 库存ID
    /// </summary>
    [ImporterHeader(Name = "库存ID")]
    [ExporterHeader("库存ID", Format = "", Width = 25, IsBold = true)]
    public long? InventoryId { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    [ImporterHeader(Name = "科室ID")]
    [ExporterHeader("科室ID", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [ImporterHeader(Name = "科室名称")]
    [ExporterHeader("科室名称", Format = "", Width = 25, IsBold = true)]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    [ImporterHeader(Name = "医生ID")]
    [ExporterHeader("医生ID", Format = "", Width = 25, IsBold = true)]
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生名称
    /// </summary>
    [ImporterHeader(Name = "医生名称")]
    [ExporterHeader("医生名称", Format = "", Width = 25, IsBold = true)]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    [ImporterHeader(Name = "发票号")]
    [ExporterHeader("发票号", Format = "", Width = 25, IsBold = true)]
    public string? InvoiceNumber { get; set; }
    
    /// <summary>
    /// 收费人员ID
    /// </summary>
    [ImporterHeader(Name = "收费人员ID")]
    [ExporterHeader("收费人员ID", Format = "", Width = 25, IsBold = true)]
    public long? ChargeStaffId { get; set; }
    
    /// <summary>
    /// 收费ID
    /// </summary>
    [ImporterHeader(Name = "收费ID")]
    [ExporterHeader("收费ID", Format = "", Width = 25, IsBold = true)]
    public long? ChargeId { get; set; }
    
    /// <summary>
    /// 收费时间
    /// </summary>
    [ImporterHeader(Name = "收费时间")]
    [ExporterHeader("收费时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? ChargeTime { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [ImporterHeader(Name = "药品ID")]
    [ExporterHeader("药品ID", Format = "", Width = 25, IsBold = true)]
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [ImporterHeader(Name = "药品编码")]
    [ExporterHeader("药品编码", Format = "", Width = 25, IsBold = true)]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [ImporterHeader(Name = "药品名称")]
    [ExporterHeader("药品名称", Format = "", Width = 25, IsBold = true)]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类型 文本
    /// </summary>
    [Dict("DrugType")]
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string DrugTypeDictLabel { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    [ImporterHeader(Name = "药品规格")]
    [ExporterHeader("药品规格", Format = "", Width = 25, IsBold = true)]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    [ImporterHeader(Name = "药品单位")]
    [ExporterHeader("药品单位", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 发药数量
    /// </summary>
    [ImporterHeader(Name = "发药数量")]
    [ExporterHeader("发药数量", Format = "", Width = 25, IsBold = true)]
    public decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单次剂量
    /// </summary>
    [ImporterHeader(Name = "单次剂量")]
    [ExporterHeader("单次剂量", Format = "", Width = 25, IsBold = true)]
    public decimal? SingleDose { get; set; }
    
    /// <summary>
    /// 单次剂量单位
    /// </summary>
    [ImporterHeader(Name = "单次剂量单位")]
    [ExporterHeader("单次剂量单位", Format = "", Width = 25, IsBold = true)]
    public string? SingleDoseUnit { get; set; }
    
    /// <summary>
    /// 用药途径ID
    /// </summary>
    [ImporterHeader(Name = "用药途径ID")]
    [ExporterHeader("用药途径ID", Format = "", Width = 25, IsBold = true)]
    public long? MedicationRoutesId { get; set; }
    
    /// <summary>
    /// 用药频次ID
    /// </summary>
    [ImporterHeader(Name = "用药频次ID")]
    [ExporterHeader("用药频次ID", Format = "", Width = 25, IsBold = true)]
    public long? FrequencyId { get; set; }
    
    /// <summary>
    /// 用药天数
    /// </summary>
    [ImporterHeader(Name = "用药天数")]
    [ExporterHeader("用药天数", Format = "", Width = 25, IsBold = true)]
    public Int16? MedicationDays { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [ImporterHeader(Name = "零售价")]
    [ExporterHeader("零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? Price { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [ImporterHeader(Name = "总零售价")]
    [ExporterHeader("总零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? Amount { get; set; }
    
    /// <summary>
    /// 草药付数
    /// </summary>
    [ImporterHeader(Name = "草药付数")]
    [ExporterHeader("草药付数", Format = "", Width = 25, IsBold = true)]
    public int? HerbsQuantity { get; set; }
    
    /// <summary>
    /// 煎药方法 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DecoctionMethod { get; set; }
    
    /// <summary>
    /// 煎药方法 文本
    /// </summary>
    [Dict("DecoctionMethod")]
    [ImporterHeader(Name = "煎药方法")]
    [ExporterHeader("煎药方法", Format = "", Width = 25, IsBold = true)]
    public string DecoctionMethodDictLabel { get; set; }
    
    /// <summary>
    /// 是否代煎
    /// </summary>
    [ImporterHeader(Name = "是否代煎")]
    [ExporterHeader("是否代煎", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? IsDecoction { get; set; }
    
    /// <summary>
    /// 退药数量
    /// </summary>
    [ImporterHeader(Name = "退药数量")]
    [ExporterHeader("退药数量", Format = "", Width = 25, IsBold = true)]
    public decimal? RefundQuantity { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>
    [ImporterHeader(Name = "总退药金额")]
    [ExporterHeader("总退药金额", Format = "", Width = 25, IsBold = true)]
    public decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [ImporterHeader(Name = "批号")]
    [ExporterHeader("批号", Format = "", Width = 25, IsBold = true)]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    [ImporterHeader(Name = "生产日期")]
    [ExporterHeader("生产日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    [ImporterHeader(Name = "有效期")]
    [ExporterHeader("有效期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [ImporterHeader(Name = "批准文号")]
    [ExporterHeader("批准文号", Format = "", Width = 25, IsBold = true)]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 药品通用名编码
    /// </summary>
    [ImporterHeader(Name = "药品通用名编码")]
    [ExporterHeader("药品通用名编码", Format = "", Width = 25, IsBold = true)]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂家 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家 文本
    /// </summary>
    [ImporterHeader(Name = "生产厂家")]
    [ExporterHeader("生产厂家", Format = "", Width = 25, IsBold = true)]
    public string ManufacturerFkDisplayName { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    [ImporterHeader(Name = "生产厂家名称")]
    [ExporterHeader("生产厂家名称", Format = "", Width = 25, IsBold = true)]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
}
