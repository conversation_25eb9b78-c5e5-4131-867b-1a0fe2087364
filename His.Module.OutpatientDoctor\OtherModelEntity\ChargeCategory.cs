﻿namespace His.Module.OutpatientDoctor.OtherModelEntity;

/// <summary>
/// 收费类别表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("charge_category", "收费类别表")]
public class ChargeCategory : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码", Length = 32)]
    public virtual string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 32)]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 20)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 20)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 提成
    /// </summary>
    [SugarColumn(ColumnName = "commission", ColumnDescription = "提成", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Commission { get; set; }

    /// <summary>
    /// 记账属性 0执行科室 1病房护士
    /// </summary>
    [SugarColumn(ColumnName = "account_attribute", ColumnDescription = "记账属性 0执行科室 1病房护士")]
    public virtual short? AccountAttribute { get; set; }

    /// <summary>
    /// 类型 0药品 1非药品 2卫材
    /// </summary>
    [SugarColumn(ColumnName = "type", ColumnDescription = "类型 0药品 1非药品 2卫材")]
    public virtual short? Type { get; set; }

    /// <summary>
    /// 医保类型 01药品 02检验 03检查 04治疗 05手术麻醉 06医用耗材 07服务设施 08血费 09其他
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_type", ColumnDescription = "医保类型 01药品 02检验 03检查 04治疗 05手术麻醉 06医用耗材 07服务设施 08血费 09其他", Length = 16)]
    public virtual string? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
}