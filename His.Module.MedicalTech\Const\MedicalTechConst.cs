﻿namespace His.Module.MedicalTech.Const;

/// <summary>
/// 医技管理相关常量
/// </summary>
[Const("医技管理相关常量")]
public class MedicalTechConst
{
    /// <summary>
    /// API分组名称
    /// </summary>
    public const string GroupName = "MedicalTech";

    /// <summary>
    /// 申请单状态有效转换 字典 ApplyStatus
    /// </summary>
    public static readonly Dictionary<int, List<int>> ApplyStatusValidTransitions = new()
    {
        [1] = [2],        // 申请 → 已收费
        [2] = [3, 4],     // 已收费 → 执行、已退费
        [3] = [2],        // 执行 → 已收费
        [4] = [],         // 已退费
        [5] = [6],        // 审核 → 预扣费
        [6] = [2, 7],        // 预扣费 → 已收费、作废
        [7] = []          // 作废 →
    };
}