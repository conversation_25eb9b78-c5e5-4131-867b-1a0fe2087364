﻿using Furion.DatabaseAccessor;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.OutpatientDoctor.Api.Prescription;
using His.Module.OutpatientDoctor.Api.Prescription.Dto;
using His.Module.OutpatientDoctor.Dto;
using His.Module.Pharmacy.Api.DrugInventory.Dto; 
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;

namespace His.Module.OutpatientDoctor.Service;

/// <summary>
/// 处表服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class TemplateService(
    TemplatePrescriptionService templatePrescriptionService,

    SqlSugarRepository<TemplatePrescriptionMain> templatePrescriptionMainRep,
    SqlSugarRepository<TemplatePrescriptionDetail> templatePrescriptionDetailRep,

    ISqlSugarClient sqlSugarClient)
    : IDynamic<PERSON><PERSON><PERSON>ontroller, ITransient
{

 

    /// <summary>
    /// 增加处方表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询处方模板列表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    [UnitOfWork]
    public async Task<SqlSugarPagedList<TemplatePrescriptionMain>> Page(PageTemplateInput input)
    {

       var list=await templatePrescriptionMainRep.AsQueryable()
            .ToPagedListAsync(input.Page, input.PageSize);
        
        return list;
    } 
 
 
 
}