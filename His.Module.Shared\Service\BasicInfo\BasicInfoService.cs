﻿using Admin.NET.Core.Service;
using His.Module.Shared.Service.BasicInfo.Dto;
using RegionInput = His.Module.Shared.Service.BasicInfo.Dto.RegionInput;
using UserInput = His.Module.Shared.Service.BasicInfo.Dto.UserInput;

namespace His.Module.Shared.Service.BasicInfo;

/// <summary>
/// 基础信息服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public   class BasicInfoService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<CalculateCategory> _calculateCategoryRep;

    private readonly SqlSugarRepository<ChargeCategory> _chargeCategoryRep;

    private readonly SqlSugarRepository<CheckCategory> _checkCategoryRep;

    private readonly SqlSugarRepository<CheckPoint> _checkPointRep;

    private readonly SqlSugarRepository<FeeCategory> _feeCategoryRep;

    private readonly SqlSugarRepository<Frequency> _frequencyRep;

    private readonly SqlSugarRepository<Icd10> _icd10Rep;

    private readonly SqlSugarRepository<MedicationRoutes> _medicationRoutesRep;

    private readonly SqlSugarRepository<PayMethod> _payMethodRep;

    private readonly SqlSugarRepository<PrescriptionType> _prescriptionTypeRep;

    private readonly SqlSugarRepository<RegCategory> _regCategoryRep;

    private readonly SqlSugarRepository<SysOrg> _sysOrgRep;

    private readonly SysOrgService _sysOrgService;

    private readonly UserManager _userManager;

    private readonly SqlSugarRepository<SysUser> _sysUserRep;

    private readonly SqlSugarRepository<SysRegion> _sysRegionRep;

    private readonly SqlSugarRepository<TcmDiagnosis> _tcmDiagnosisRep;

    private readonly SqlSugarRepository<TcmSyndrome> _tcmSyndromeRep;
    private readonly SqlSugarRepository<SysOrgStructDetail> _sysOrgStructDetailRep;
    public BasicInfoService(
        SqlSugarRepository<SysOrg> sysOrgRep,
        SqlSugarRepository<Frequency> frequencyRep,
        SqlSugarRepository<CalculateCategory> calculateCategoryRep,
        SqlSugarRepository<ChargeCategory> chargeCategoryRep,
        SqlSugarRepository<CheckCategory> checkCategoryRep,
        SqlSugarRepository<CheckPoint> checkPointRep,
        SqlSugarRepository<FeeCategory> feeCategoryRep,
        SqlSugarRepository<Icd10> icd10Rep,
        SqlSugarRepository<MedicationRoutes> medicationRoutesRep,
        SqlSugarRepository<PayMethod> payMethodRep,
        SqlSugarRepository<PrescriptionType> prescriptionTypeRep,
        SqlSugarRepository<RegCategory> regCategoryRep,
        SysOrgService sysOrgService,
        UserManager userManager,
        SqlSugarRepository<SysUser> sysUserRep,
        SqlSugarRepository<SysRegion> sysRegionRep,
        SqlSugarRepository<TcmSyndrome> tcmSyndromeRep,
        SqlSugarRepository<TcmDiagnosis> tcmDiagnosisRep,
        SqlSugarRepository<SysOrgStructDetail> sysOrgStructDetailRep
    )
    {
        _sysOrgRep = sysOrgRep;
        _frequencyRep = frequencyRep;
        _calculateCategoryRep = calculateCategoryRep;
        _chargeCategoryRep = chargeCategoryRep;
        _checkCategoryRep = checkCategoryRep;
        _checkPointRep = checkPointRep;
        _feeCategoryRep = feeCategoryRep;
        _icd10Rep = icd10Rep;
        _medicationRoutesRep = medicationRoutesRep;
        _payMethodRep = payMethodRep;
        _prescriptionTypeRep = prescriptionTypeRep;
        _regCategoryRep = regCategoryRep;
        _sysOrgService = sysOrgService;
        _userManager = userManager;
        _sysUserRep = sysUserRep;
        _sysRegionRep = sysRegionRep;
        _tcmDiagnosisRep = tcmDiagnosisRep;
        _tcmSyndromeRep = tcmSyndromeRep;
        _sysOrgStructDetailRep = sysOrgStructDetailRep;
    }

    /// <summary>
    /// 获取科室列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetDepartments")]
    [DisplayName("获取科室列表")]
    [SkipPermission]
    public async Task<List<SysOrg>> GetDepartments([FromQuery] DepartmentInput input)
    {
        var tenantId = long.Parse(App.User.FindFirst(ClaimConst.TenantId)?.Value ?? "0");
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _sysOrgRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable && u.TenantId==tenantId)
            .WhereIF(input.ParentId > 0, u => u.Pid == input.ParentId)
            .WhereIF(input.OrgTypes != null, u => input.OrgTypes.Contains(u.Type))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }


    /// <summary>
    /// 获取住院科室列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetInpatientDepartments")]
    [DisplayName("获取住院科室列表")]
    [SkipPermission]
    public async Task<List<SysOrg>> GetInpatientDepartments([FromQuery] DepartmentInput input)
    {
        input.OrgTypes = ["InpatientDept"];
        return await GetDepartments(input);
    }


    /// <summary>
    /// 根据父Id获取病区列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetInpatientWards")]
    [DisplayName("根据父Id获取病区列表")]
    [SkipPermission]
    public async Task<List<SysOrg>> GetInpatientWards([FromQuery] DepartmentInput input)
    {
        input.OrgTypes = ["InpatientWard"];
        return await GetDepartments(input);
    }

    /// <summary>
    /// 获取频次列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetFrequencies")]
    [DisplayName("获取频次列表")]
    [SkipPermission]
    public async Task<List<Frequency>> GetFrequencies([FromQuery] FrequencyInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _frequencyRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取核算类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetCalculateCategories")]
    [DisplayName("获取核算类别列表")]
    [SkipPermission]
    public async Task<List<CalculateCategory>> GetCalculateCategories([FromQuery] CalculateCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _calculateCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取收费类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetChargeCategories")]
    [DisplayName("获取收费类别列表")]
    [SkipPermission]
    public async Task<List<ChargeCategory>> GetChargeCategories([FromQuery] ChargeCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _chargeCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取检查类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetCheckCategories")]
    [DisplayName("获取检查类别列表")]
    [SkipPermission]
    public async Task<List<CheckCategory>> GetCheckCategories([FromQuery] CheckCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _checkCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取检查部位列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetCheckPoints")]
    [DisplayName("获取检查部位列表")]
    [SkipPermission]
    public async Task<List<CheckPoint>> GetCheckPoints([FromQuery] CheckPointInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _checkPointRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取费用类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetFeeCategories")]
    [DisplayName("获取费用类别列表")]
    [SkipPermission]
    public async Task<List<FeeCategory>> GetFeeCategories([FromQuery] FeeCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _feeCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(input.UsageScope is not null, u => u.UsageScope == (MedServiceCategoryEnum?)input.UsageScope)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取ICD10列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetIcd10s")]
    [DisplayName("获取ICD10列表")]
    [SkipPermission]
    public async Task<List<Icd10>> GetIcd10s([FromQuery] Icd10Input input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _icd10Rep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable && u.Level == 5)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Take(20) //前20条
            .ToListAsync();
    }

    /// <summary>
    /// 获取中医诊断 
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetTcmDiagnosis")]
    [DisplayName("获取中医诊断列表")]
    [SkipPermission]
    public async Task<List<TcmDiagnosis>> GetTcmDiagnosis([FromQuery] Icd10Input input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _tcmDiagnosisRep.AsQueryable()
            .Where(u => u.Status == 1)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.TcmDiagnosisCode.Contains(input.Keyword)
                                                                     || u.TcmDiagnosisName.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Take(20) //前20条
            .ToListAsync();
    }

    /// <summary>
    /// 获取中医证型列表  
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetTcmSyndrome")]
    [DisplayName("获取中医证型列表")]
    [SkipPermission]
    public async Task<List<TcmSyndrome>> GetTcmSyndrome([FromQuery] Icd10Input input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _tcmSyndromeRep.AsQueryable()
            .Where(u => u.Status == 1)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.TcmSyndromeCode.Contains(input.Keyword)
                                                                     || u.TcmSyndromeName.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Take(20) //前20条
            .ToListAsync();
    }

    /// <summary>
    /// 获取给药途径列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetMedicationRoutes")]
    [DisplayName("获取给药途径列表")]
    [SkipPermission]
    public async Task<List<MedicationRoutes>> GetMedicationRoutes([FromQuery] MedicationRoutesInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _medicationRoutesRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.RouteCode.Contains(input.Keyword)
                                                                     || u.RouteName.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取支付方式列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetPayMethods")]
    [DisplayName("获取支付方式列表")]
    [SkipPermission]
    public async Task<List<PayMethod>> GetPayMethods([FromQuery] Dto.PayMethodInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _payMethodRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取处方类型列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetPrescriptionTypes")]
    [DisplayName("获取处方类型列表")]
    [SkipPermission]
    public async Task<List<PrescriptionType>> GetPrescriptionTypes([FromQuery] PrescriptionTypeInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _prescriptionTypeRep.AsQueryable()
            .Where(u => u.Status == (int?)StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取挂号类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetRegCategories")]
    [DisplayName("获取挂号类别列表")]
    [SkipPermission]
    public async Task<List<RegCategory>> GetRegCategories([FromQuery] RegCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        return await _regCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
                                                                     || u.Name.Contains(input.Keyword)
                                                                     || u.PinyinCode.Contains(input.Keyword)
                                                                     || u.WubiCode.Contains(input.Keyword))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 获取用户列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetUsers")]
    [DisplayName("获取用户列表")]
    [SkipPermission]
    public async Task<List<UserOutput>> GetUsers([FromQuery] UserInput input)
    {
        // 获取用户拥有的机构集合
        var userOrgIdList = await _sysOrgService.GetUserOrgIdList();
        List<long> orgList = null;
        var query = _sysUserRep.AsQueryable()
            .LeftJoin<SysOrg>((u, a) => u.OrgId == a.Id)
            .LeftJoin<SysPos>((u, a, b) => u.PosId == b.Id);
            // .LeftJoin<SysUserRole>((u, a, b, r) => u.Id == r.UserId)
            // .WhereIF(input.RoleId > 0, (u, a, b, r) => r.RoleId == input.RoleId);
        if (input.OrgId > 0) // 指定机构查询时
        {
            return await query.InnerJoin<SysUserRoleOrg>((u, a, b, c) => u.Id == c.UserId && c.OrgId == input.OrgId
                )
                .WhereIF(input.RoleId > 0, (u, a, b, c)=> c.RoleId == input.RoleId)
                .Where(u => u.AccountType != AccountTypeEnum.SuperAdmin
                            && u.Status == StatusEnum.Enable)
                .OrderBy(u => u.OrderNo)
                .Select(
                    (u, a, b, c) => new UserOutput
                    {  Name = u.RealName,
                        OrgName = a.Name,
                        PosName = b.Name,
                        RoleName =
                            SqlFunc.Subqueryable<SysUserRole>().LeftJoin<SysRole>((m, n) => m.RoleId == n.Id)
                                .Where(m => m.UserId == u.Id).SelectStringJoin((m, n) => n.Name, ","),
                        DomainAccount = SqlFunc.Subqueryable<SysUserLdap>().Where(m => m.UserId == u.Id)
                            .Select(m => m.Account)
                    }, true).ToListAsync();
            ;
        }
        else // 各管理员只能看到自己机构下的用户列表
        {
            orgList = _userManager.SuperAdmin ? null : userOrgIdList;
            return await query
                .LeftJoin<SysUserRole>((u, a, b, r) => u.Id == r.UserId)
                .WhereIF(input.RoleId > 0, (u, a, b, r) => r.RoleId == input.RoleId)
                .Where(u => u.AccountType != AccountTypeEnum.SuperAdmin
                                          && u.Status == StatusEnum.Enable)
                .WhereIF(orgList != null, u => orgList.Contains(u.OrgId))
                .OrderBy(u => u.OrderNo)
                .Select(
                    (u, a, b,r) => new UserOutput
                    {
                        Name = u.RealName,
                        OrgName = a.Name,
                        PosName = b.Name,
                        RoleName =
                            SqlFunc.Subqueryable<SysUserRole>().LeftJoin<SysRole>((m, n) => m.RoleId == n.Id)
                                .Where(m => m.UserId == u.Id).SelectStringJoin((m, n) => n.Name, ","),
                        DomainAccount = SqlFunc.Subqueryable<SysUserLdap>().Where(m => m.UserId == u.Id)
                            .Select(m => m.Account)
                    }, true)
                .ToListAsync();
        }
    }

    /// <summary>
    /// 获取门诊医生
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetOutpatientDoctor")]
    [DisplayName("获取门诊医生")]
    [SkipPermission]
    public async Task<List<UserOutput>> GetOutpatientDoctor([FromQuery] UserInput input)
    {
        input.RoleId =*************** ;// 医生角色id 
        return await GetUsers(input);
    }


    /// <summary>
    /// 获取住院医生
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetInpatientDoctor")]
    [DisplayName("获取住院医生")]
    [SkipPermission]
    public async Task<List<UserOutput>> GetInpatientDoctor([FromQuery] UserInput input)
    {
        input.RoleId = ***************; // 医生角色id 
        return await GetUsers(input);
    }


    /// <summary>
    /// 获取行政区域树列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetRegionTree")]
    [DisplayName("获取行政区域树列表")]
    [SkipPermission]
    public async Task<List<RegionOutput>> GetRegionTree([FromQuery] RegionInput input)
    {
        var regions = await _sysRegionRep.AsQueryable().Select(u => new SysRegion
        {
            Id = u.Id, Pid = u.Pid, Name = u.Name, Code = u.Code
        }).OrderBy(u => u.Id).ToListAsync();
        // 将所有地区按照父ID进行分组，以优化查找性能
        var regionsByParentId = regions.GroupBy(r => r.Pid).ToDictionary(g => g.Key, g => g.ToList());
        return BuildTreeInternal(0, regionsByParentId);
    }

    /// <summary>
    /// 构造树
    /// </summary>
    /// <param name="parentId"></param>
    /// <param name="regionsByParentId"></param>
    /// <returns></returns>
    private static List<RegionOutput> BuildTreeInternal(long parentId,
        Dictionary<long, List<SysRegion>> regionsByParentId)
    {
        if (!regionsByParentId.TryGetValue(parentId, out var value)) return null;

        var children = new List<RegionOutput>();
        foreach (var region in value)
        {
            var childNode = new RegionOutput
            {
                Value = region.Code, Label = region.Name, Children = BuildTreeInternal(region.Id, regionsByParentId)
            };
            // 如果Children为空，则设置为null以满足输出格式要求
            if (childNode.Children == null || childNode.Children.Count == 0) childNode.Children = null;
            children.Add(childNode);
        }

        return children;
    }
    
    /// <summary>
    /// 根据科室结构(分类) 获取科室列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "GetDeptByStructCode")]
    [DisplayName("获取处方类型列表")]
    [SkipPermission]
    public async Task<List<SysOrg>> GetDeptByStructCode([FromQuery] QueryDeptByStructInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        
        return  await _sysOrgRep.AsTenant().QueryableWithAttr<SysOrg>()
            .InnerJoin<SysOrgStructDetail>((org,detail)=>org.Id==detail.DeptId)
            .Where((org,detail) =>
                detail.StructCode == input.StructCode &&
                org.Status ==  StatusEnum.Enable && (detail.Status == (int?)StatusEnum.Enable ||detail.Status == 0))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), (org,detail) => detail.StructCode.Contains(input.Keyword)
                                                                     || detail.StructName.Contains(input.Keyword)
                                                                     || detail.DeptName.Contains(input.Keyword) )
           .OrderBy((org,detail) => org.Id )
            .Select((org,detail) => org)
            .ToListAsync();
 
    }
}