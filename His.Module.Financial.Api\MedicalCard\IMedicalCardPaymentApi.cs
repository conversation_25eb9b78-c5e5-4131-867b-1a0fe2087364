using His.Module.Financial.Api.MedicalCard.Dto;

namespace His.Module.Financial.Api.MedicalCard;

public interface IMedicalCardPaymentApi
{
     /// <summary>
     /// 卡充值
     /// </summary>
     /// <param name="input"></param>
     /// <returns></returns>
     public Task<MedicalCardPaymentOutput> Recharge(MedicalCardPaymentRechargeDto input);
     
     /// <summary>
     /// 退费
     /// </summary>
     /// <param name="input"></param>
     /// <returns></returns>
     public Task<MedicalCardPaymentOutput> Refund(MedicalCardPaymentRefundDto input);
     
     /// <summary>
     /// 卡扣费
     /// </summary>
     /// <param name="input"></param>
     /// <returns></returns>
     public Task<MedicalCardPaymentOutput> Deduction(MedicalCardPaymentDeductionDto input);
     
     
     
     
     /// <summary>
     /// 退卡余额
     /// </summary>
     /// <param name="input"></param>
     /// <returns></returns>
     public Task<MedicalCardPaymentOutput> CardRefundBalance(MedicalCardPaymentRefundBalanceDto input);
     
     
    /// <summary>
    /// 卡红冲
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
     public Task<MedicalCardPaymentOutput> RedInvoice(MedicalCardPaymentRedInvoiceDto input);
     
     
     
    
}