﻿namespace His.Module.Pharmacy;

/// <summary>
/// 出库管理输出参数
/// </summary>
public class StorageOutRecordOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }    
    
    /// <summary>
    /// 库房 描述
    /// </summary>
    public string StorageFkDisplayName { get; set; } 
    
    /// <summary>
    /// 库房编码
    /// </summary>
    public string? StorageCode { get; set; }    
    
    /// <summary>
    /// 药品类型
    /// </summary>
    public string? DrugType { get; set; }    
    
    /// <summary>
    /// 出库单号
    /// </summary>
    public string? StorageOutNo { get; set; }    
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }    
    
    /// <summary>
    /// 供应商ID 描述
    /// </summary>
    public string SupplierFkDisplayName { get; set; } 
    
    /// <summary>
    /// 供应商编码
    /// </summary>
    public string? SupplierCode { get; set; }    
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    public string? SupplierName { get; set; }    
    
    /// <summary>
    /// 出库类型
    /// </summary>
    public string? StorageOutType { get; set; }    
    
    /// <summary>
    /// 出库日期
    /// </summary>
    public DateTime? StorageOutTime { get; set; }    
    
    /// <summary>
    /// 目标科室
    /// </summary>
    public long? TargetDeptId { get; set; }    
    
    /// <summary>
    /// 目标科室 描述
    /// </summary>
    public string TargetDeptFkDisplayName { get; set; } 
    
    /// <summary>
    /// 目标科室编码
    /// </summary>
    public string? TargetDeptCode { get; set; }    
    
    /// <summary>
    /// 目标科室名称
    /// </summary>
    public string? TargetDeptName { get; set; }    
    
    /// <summary>
    /// 领用人ID
    /// </summary>
    public long? TargetUserId { get; set; }    
    
    /// <summary>
    /// 领用人编码
    /// </summary>
    public string? TargetUserCode { get; set; }    
    
    /// <summary>
    /// 领用人名称
    /// </summary>
    public string? TargetUserName { get; set; }    
    
    /// <summary>
    /// 总进价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }    
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }    
    
    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNo { get; set; }    
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 出库管理数据导入模板实体
/// </summary>
public class ExportStorageOutRecordOutput : ImportStorageOutRecordInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
