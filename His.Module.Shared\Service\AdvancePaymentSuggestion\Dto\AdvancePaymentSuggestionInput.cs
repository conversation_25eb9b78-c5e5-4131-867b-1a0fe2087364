﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Shared;

/// <summary>
/// 预交金建议基础输入参数
/// </summary>
public class AdvancePaymentSuggestionBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 诊断编码
    /// </summary>
    public virtual string? DiagnosisCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    public virtual string? DiagnosisName { get; set; }
    
    /// <summary>
    /// 病种名称
    /// </summary>
    public virtual string? DiseaseTypeName { get; set; }
    
    /// <summary>
    /// 职工
    /// </summary>
    public virtual decimal? Employee { get; set; }
    
    /// <summary>
    /// 居民
    /// </summary>
    public virtual decimal? Resident { get; set; }
    
    /// <summary>
    /// 自费
    /// </summary>
    public virtual decimal? SelfFunded { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }
    
}

/// <summary>
/// 预交金建议分页查询输入参数
/// </summary>
public class PageAdvancePaymentSuggestionInput : BasePageInput
{
    /// <summary>
    /// 诊断编码
    /// </summary>
    public string? DiagnosisCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosisName { get; set; }
    
    /// <summary>
    /// 病种名称
    /// </summary>
    public string? DiseaseTypeName { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 预交金建议增加输入参数
/// </summary>
public class AddAdvancePaymentSuggestionInput
{
    /// <summary>
    /// 诊断编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "诊断编码字符长度不能超过128")]
    public string? DiagnosisCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "诊断名称字符长度不能超过255")]
    public string? DiagnosisName { get; set; }
    
    /// <summary>
    /// 病种名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "病种名称字符长度不能超过255")]
    public string? DiseaseTypeName { get; set; }
    
    /// <summary>
    /// 职工
    /// </summary>
    public decimal? Employee { get; set; }
    
    /// <summary>
    /// 居民
    /// </summary>
    public decimal? Resident { get; set; }
    
    /// <summary>
    /// 自费
    /// </summary>
    public decimal? SelfFunded { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }
    
}

/// <summary>
/// 预交金建议删除输入参数
/// </summary>
public class DeleteAdvancePaymentSuggestionInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 预交金建议更新输入参数
/// </summary>
public class UpdateAdvancePaymentSuggestionInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 诊断编码
    /// </summary>    
    [MaxLength(128, ErrorMessage = "诊断编码字符长度不能超过128")]
    public string? DiagnosisCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>    
    [MaxLength(255, ErrorMessage = "诊断名称字符长度不能超过255")]
    public string? DiagnosisName { get; set; }
    
    /// <summary>
    /// 病种名称
    /// </summary>    
    [MaxLength(255, ErrorMessage = "病种名称字符长度不能超过255")]
    public string? DiseaseTypeName { get; set; }
    
    /// <summary>
    /// 职工
    /// </summary>    
    public decimal? Employee { get; set; }
    
    /// <summary>
    /// 居民
    /// </summary>    
    public decimal? Resident { get; set; }
    
    /// <summary>
    /// 自费
    /// </summary>    
    public decimal? SelfFunded { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>    
    public int? OrderNo { get; set; }
    
}

/// <summary>
/// 预交金建议主键查询输入参数
/// </summary>
public class QueryByIdAdvancePaymentSuggestionInput : DeleteAdvancePaymentSuggestionInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetAdvancePaymentSuggestionStatusInput : BaseStatusInput
{
}

/// <summary>
/// 预交金建议数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportAdvancePaymentSuggestionInput : BaseImportInput
{
    /// <summary>
    /// 诊断编码
    /// </summary>
    [ImporterHeader(Name = "诊断编码")]
    [ExporterHeader("诊断编码", Format = "", Width = 25, IsBold = true)]
    public string? DiagnosisCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    [ImporterHeader(Name = "诊断名称")]
    [ExporterHeader("诊断名称", Format = "", Width = 25, IsBold = true)]
    public string? DiagnosisName { get; set; }
    
    /// <summary>
    /// 病种名称
    /// </summary>
    [ImporterHeader(Name = "病种名称")]
    [ExporterHeader("病种名称", Format = "", Width = 25, IsBold = true)]
    public string? DiseaseTypeName { get; set; }
    
    /// <summary>
    /// 职工
    /// </summary>
    [ImporterHeader(Name = "职工")]
    [ExporterHeader("职工", Format = "", Width = 25, IsBold = true)]
    public decimal? Employee { get; set; }
    
    /// <summary>
    /// 居民
    /// </summary>
    [ImporterHeader(Name = "居民")]
    [ExporterHeader("居民", Format = "", Width = 25, IsBold = true)]
    public decimal? Resident { get; set; }
    
    /// <summary>
    /// 自费
    /// </summary>
    [ImporterHeader(Name = "自费")]
    [ExporterHeader("自费", Format = "", Width = 25, IsBold = true)]
    public decimal? SelfFunded { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }
    
}
