﻿namespace His.Module.Inpatient;

/// <summary>
/// 入院登记记录基础输入参数
/// </summary>
public class InpatientRegisterBaseInput
{
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string MedicalCardNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public virtual long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    public virtual int? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public virtual int? Age { get; set; }
    
    /// <summary>
    /// 年龄单位
    /// </summary>
    public virtual string? AgeUnit { get; set; }
    
    /// <summary>
    /// 出生日期
    /// </summary>
    public virtual DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public virtual int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public virtual string? IdCardNo { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    public virtual string? Phone { get; set; }
    
    /// <summary>
    /// 联系人姓名
    /// </summary>
    public virtual string? ContactName { get; set; }
    
    /// <summary>
    /// 联系人关系
    /// </summary>
    public virtual string? ContactRelationship { get; set; }
    
    /// <summary>
    /// 联系人地址
    /// </summary>
    public virtual string? ContactAddress { get; set; }
    
    /// <summary>
    /// 联系人电话号码
    /// </summary>
    public virtual string? ContactPhone { get; set; }
    
    /// <summary>
    /// 现居住地省
    /// </summary>
    public virtual int? ResidenceProvince { get; set; }
    
    /// <summary>
    /// 现居住地市
    /// </summary>
    public virtual int? ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>
    public virtual int? ResidenceCounty { get; set; }
    
    /// <summary>
    /// 详细现居住地
    /// </summary>
    public virtual string? ResidenceAddress { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public virtual DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 病区ID
    /// </summary>
    public virtual long? WardId { get; set; }

    /// <summary>
    /// 病区名称
    /// </summary>
    public virtual string? WardName { get; set; }
    
    /// <summary>
    /// 诊疗组ID
    /// </summary>
    public virtual long? TeamId { get; set; }
    
    /// <summary>
    /// 诊疗组名称
    /// </summary>
    public virtual string? TeamName { get; set; }

    /// <summary>
    /// 医生ID
    /// </summary>
    public virtual long? DoctorId { get; set; }

    /// <summary>
    /// 医生姓名
    /// </summary>
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>
    public virtual long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>
    public virtual string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    public virtual string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>
    public virtual string? SettlementCategory { get; set; }

    /// <summary>
    /// 入院诊断编号
    /// </summary>
    public virtual string? AdmissionDiagnosisCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public virtual string? AdmissionDiagnosisName { get; set; }
    
    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    public virtual string? PregnancyRiskLevel { get; set; }
    
    /// <summary>
    /// 高危因素
    /// </summary>
    public virtual string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

}

/// <summary>
/// 入院登记记录分页查询输入参数
/// </summary>
public class PageInpatientRegisterInput : BasePageInput
{
    /// <summary>
    /// 住院流水号
    /// </summary>
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    public string? InpatientNo { get; set; }

    /// <summary>
    /// 住院次数
    /// </summary>
    public int? InpatientCount { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    public int? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }
    
    /// <summary>
    /// 年龄单位
    /// </summary>
    public string? AgeUnit { get; set; }

    /// <summary>
    /// 出生日期范围
    /// </summary>
    public DateTime?[] BirthdayRange { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    public string? Phone { get; set; }
    
    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string? ContactName { get; set; }
    
    /// <summary>
    /// 联系人关系
    /// </summary>
    public string? ContactRelationship { get; set; }
    
    /// <summary>
    /// 联系人地址
    /// </summary>
    public string? ContactAddress { get; set; }
    
    /// <summary>
    /// 联系人电话号码
    /// </summary>
    public string? ContactPhone { get; set; }
    
    /// <summary>
    /// 现居住地省
    /// </summary>
    public int? ResidenceProvince { get; set; }
    
    /// <summary>
    /// 现居住地市
    /// </summary>
    public int? ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>
    public int? ResidenceCounty { get; set; }
    
    /// <summary>
    /// 详细现居住地
    /// </summary>
    public string? ResidenceAddress { get; set; }

    /// <summary>
    /// 入院时间范围
    /// </summary>
    public DateTime?[] InpatientTimeRange { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 病区ID
    /// </summary>
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    public string? WardName { get; set; }
    
    /// <summary>
    /// 诊疗组ID
    /// </summary>
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 诊疗组名称
    /// </summary>
    public string? TeamName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>
    public long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>
    public string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>
    public string? SettlementCategory { get; set; }
    
    /// <summary>
    /// 入院诊断编号
    /// </summary>
    public string? AdmissionDiagnosisCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public string? AdmissionDiagnosisName { get; set; }
    
    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    public string? PregnancyRiskLevel { get; set; }
    
    /// <summary>
    /// 高危因素
    /// </summary>
    public string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 入院登记记录增加输入参数
/// </summary>
public class AddInpatientRegisterInput
{
    /// <summary>
    /// 预约记录Id
    /// </summary>
    public long? AppointmentId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [Required(ErrorMessage = "就诊卡号不能为空")]
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [Required(ErrorMessage = "患者ID不能为空")]
    public long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [Required(ErrorMessage = "患者姓名不能为空")]
    public string PatientName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public int? Sex { get; set; }
    
    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }
    
    /// <summary>
    /// 年龄单位
    /// </summary>
    public string? AgeUnit { get; set; }
    
    /// <summary>
    /// 出生日期
    /// </summary>
    public DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>

    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>

    public string? ContactRelationship { get; set; }
    
    /// <summary>
    /// 联系人地址
    /// </summary>

    public string? ContactAddress { get; set; }
    
    /// <summary>
    /// 联系人电话号码
    /// </summary>

    public string? ContactPhone { get; set; }
    
    /// <summary>
    /// 现居住地省
    /// </summary>
    public int? ResidenceProvince { get; set; }
    
    /// <summary>
    /// 现居住地市
    /// </summary>
    public int? ResidenceCity { get; set; }
    
    /// <summary>
    /// 现居住地县
    /// </summary>
    public int? ResidenceCounty { get; set; }
    
    /// <summary>
    /// 详细现居住地
    /// </summary>
    public string? ResidenceAddress { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>
    public DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>

    public string? DeptName { get; set; }
    
    /// <summary>
    /// 病区ID
    /// </summary>
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    public string? WardName { get; set; }
    
    /// <summary>
    /// 诊疗组ID
    /// </summary>
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 诊疗组名称
    /// </summary>
    public string? TeamName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>

    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>
    public long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>

    public string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>

    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>

    public string? SettlementCategory { get; set; }
    
    /// <summary>
    /// 入院诊断编号
    /// </summary>

    public string? AdmissionDiagnosisCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>

    public string? AdmissionDiagnosisName { get; set; }
    
    /// <summary>
    /// 妊娠风险评估
    /// </summary>

    public string? PregnancyRiskLevel { get; set; }
    
    /// <summary>
    /// 高危因素
    /// </summary>

    public string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>

    public string? Remark { get; set; }
    
}

/// <summary>
/// 入院登记记录删除输入参数
/// </summary>
public class DeleteInpatientRegisterInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 入院登记记录更新输入参数
/// </summary>
public class UpdateInpatientRegisterInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "住院流水号字符长度不能超过100")]
    public string? InpatientSerialNo { get; set; }

    /// <summary>
    /// 住院号
    /// </summary>
    [MaxLength(100, ErrorMessage = "住院号字符长度不能超过100")]
    public string? InpatientNo { get; set; }

    /// <summary>
    /// 住院次数
    /// </summary>
    public int? InpatientCount { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [MaxLength(100, ErrorMessage = "就诊卡号字符长度不能超过100")]
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>    
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>    
    [MaxLength(100, ErrorMessage = "患者姓名字符长度不能超过100")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>    
    public int? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>    
    public int? Age { get; set; }
    
    /// <summary>
    /// 年龄单位
    /// </summary>    
    [MaxLength(32, ErrorMessage = "年龄单位字符长度不能超过32")]
    public string? AgeUnit { get; set; }
    
    /// <summary>
    /// 出生日期
    /// </summary>    
    public DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>    
    public int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>    
    [MaxLength(32, ErrorMessage = "身份证号字符长度不能超过32")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>    
    [MaxLength(16, ErrorMessage = "电话号码字符长度不能超过16")]
    public string? Phone { get; set; }
    
    /// <summary>
    /// 联系人姓名
    /// </summary>    
    [MaxLength(32, ErrorMessage = "联系人姓名字符长度不能超过32")]
    public string? ContactName { get; set; }
    
    /// <summary>
    /// 联系人关系
    /// </summary>    
    [MaxLength(16, ErrorMessage = "联系人关系字符长度不能超过16")]
    public string? ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>    
    [MaxLength(64, ErrorMessage = "联系人地址字符长度不能超过64")]
    public string? ContactAddress { get; set; }
    
    /// <summary>
    /// 联系人电话号码
    /// </summary>    
    [MaxLength(16, ErrorMessage = "联系人电话号码字符长度不能超过16")]
    public string? ContactPhone { get; set; }
    
    /// <summary>
    /// 现居住地省
    /// </summary>    
    public int? ResidenceProvince { get; set; }
    
    /// <summary>
    /// 现居住地市
    /// </summary>    
    public int? ResidenceCity { get; set; }
    
    /// <summary>
    /// 现居住地县
    /// </summary>    
    public int? ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>    
    [MaxLength(128, ErrorMessage = "详细现居住地字符长度不能超过128")]
    public string? ResidenceAddress { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    public DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>    
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "科室名称字符长度不能超过100")]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 病区ID
    /// </summary>    
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "病区名称字符长度不能超过100")]
    public string? WardName { get; set; }
    
    /// <summary>
    /// 诊疗组ID
    /// </summary>    
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 诊疗组名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "诊疗组名称字符长度不能超过100")]
    public string? TeamName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>    
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>    
    [MaxLength(100, ErrorMessage = "医生姓名字符长度不能超过100")]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>    
    public long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "接诊医生名称字符长度不能超过100")]
    public string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>    
    [MaxLength(32, ErrorMessage = "入院途径字符长度不能超过32")]
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>    
    [MaxLength(100, ErrorMessage = "结算类别字符长度不能超过100")]
    public string? SettlementCategory { get; set; }
    
    /// <summary>
    /// 入院诊断编号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "入院诊断编号字符长度不能超过100")]
    public string? AdmissionDiagnosisCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "入院诊断名称字符长度不能超过100")]
    public string? AdmissionDiagnosisName { get; set; }
    
    /// <summary>
    /// 妊娠风险评估
    /// </summary>    
    [MaxLength(32, ErrorMessage = "妊娠风险评估字符长度不能超过32")]
    public string? PregnancyRiskLevel { get; set; }
    
    /// <summary>
    /// 高危因素
    /// </summary>    
    [MaxLength(256, ErrorMessage = "高危因素字符长度不能超过256")]
    public string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }

}

/// <summary>
/// 入院登记记录主键查询输入参数
/// </summary>
public class QueryByIdInpatientRegisterInput : DeleteInpatientRegisterInput
{
}

/// <summary>
/// 入院登记记录数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportInpatientRegisterInput : BaseImportInput
{
    /// <summary>
    /// 住院流水号
    /// </summary>
    [ImporterHeader(Name = "住院流水号")]
    [ExporterHeader("住院流水号", Format = "", Width = 25, IsBold = true)]
    public string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [ImporterHeader(Name = "住院号")]
    [ExporterHeader("住院号", Format = "", Width = 25, IsBold = true)]
    public string? InpatientNo { get; set; }

    /// <summary>
    /// 住院次数
    /// </summary>
    [ImporterHeader(Name = "住院次数")]
    [ExporterHeader("住院次数", Format = "", Width = 25, IsBold = true)]
    public int? InpatientCount { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [ImporterHeader(Name = "就诊卡号")]
    [ExporterHeader("就诊卡号", Format = "", Width = 25, IsBold = true)]
    public string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [ImporterHeader(Name = "患者ID")]
    [ExporterHeader("患者ID", Format = "", Width = 25, IsBold = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [ImporterHeader(Name = "患者姓名")]
    [ExporterHeader("患者姓名", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [ImporterHeader(Name = "性别")]
    [ExporterHeader("性别", Format = "", Width = 25, IsBold = true)]
    public int? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    [ImporterHeader(Name = "年龄")]
    [ExporterHeader("年龄", Format = "", Width = 25, IsBold = true)]
    public int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [ImporterHeader(Name = "年龄单位")]
    [ExporterHeader("年龄单位", Format = "", Width = 25, IsBold = true)]
    public string? AgeUnit { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    [ImporterHeader(Name = "出生日期")]
    [ExporterHeader("出生日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [ImporterHeader(Name = "证件类型")]
    [ExporterHeader("证件类型", Format = "", Width = 25, IsBold = true)]
    public int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [ImporterHeader(Name = "身份证号")]
    [ExporterHeader("身份证号", Format = "", Width = 25, IsBold = true)]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    [ImporterHeader(Name = "电话号码")]
    [ExporterHeader("电话号码", Format = "", Width = 25, IsBold = true)]
    public string? Phone { get; set; }
    
    /// <summary>
    /// 联系人姓名
    /// </summary>
    [ImporterHeader(Name = "联系人姓名")]
    [ExporterHeader("联系人姓名", Format = "", Width = 25, IsBold = true)]
    public string? ContactName { get; set; }
    
    /// <summary>
    /// 联系人关系
    /// </summary>
    [ImporterHeader(Name = "联系人关系")]
    [ExporterHeader("联系人关系", Format = "", Width = 25, IsBold = true)]
    public string? ContactRelationship { get; set; }
    
    /// <summary>
    /// 联系人地址
    /// </summary>
    [ImporterHeader(Name = "联系人地址")]
    [ExporterHeader("联系人地址", Format = "", Width = 25, IsBold = true)]
    public string? ContactAddress { get; set; }
    
    /// <summary>
    /// 联系人电话号码
    /// </summary>
    [ImporterHeader(Name = "联系人电话号码")]
    [ExporterHeader("联系人电话号码", Format = "", Width = 25, IsBold = true)]
    public string? ContactPhone { get; set; }
    
    /// <summary>
    /// 现居住地省
    /// </summary>
    [ImporterHeader(Name = "现居住地省")]
    [ExporterHeader("现居住地省", Format = "", Width = 25, IsBold = true)]
    public int? ResidenceProvince { get; set; }
    
    /// <summary>
    /// 现居住地市
    /// </summary>
    [ImporterHeader(Name = "现居住地市")]
    [ExporterHeader("现居住地市", Format = "", Width = 25, IsBold = true)]
    public int? ResidenceCity { get; set; }
    
    /// <summary>
    /// 现居住地县
    /// </summary>
    [ImporterHeader(Name = "现居住地县")]
    [ExporterHeader("现居住地县", Format = "", Width = 25, IsBold = true)]
    public int? ResidenceCounty { get; set; }
    
    /// <summary>
    /// 详细现居住地
    /// </summary>
    [ImporterHeader(Name = "详细现居住地")]
    [ExporterHeader("详细现居住地", Format = "", Width = 25, IsBold = true)]
    public string? ResidenceAddress { get; set; }
    
    /// <summary>
    /// 入院时间
    /// </summary>
    [ImporterHeader(Name = "入院时间")]
    [ExporterHeader("入院时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    [ImporterHeader(Name = "科室ID")]
    [ExporterHeader("科室ID", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [ImporterHeader(Name = "科室名称")]
    [ExporterHeader("科室名称", Format = "", Width = 25, IsBold = true)]
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 病区ID
    /// </summary>
    [ImporterHeader(Name = "病区ID")]
    [ExporterHeader("病区ID", Format = "", Width = 25, IsBold = true)]
    public long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    [ImporterHeader(Name = "病区名称")]
    [ExporterHeader("病区名称", Format = "", Width = 25, IsBold = true)]
    public string? WardName { get; set; }
    
    /// <summary>
    /// 诊疗组ID
    /// </summary>
    [ImporterHeader(Name = "诊疗组ID")]
    [ExporterHeader("诊疗组ID", Format = "", Width = 25, IsBold = true)]
    public long? TeamId { get; set; }
    
    /// <summary>
    /// 诊疗组名称
    /// </summary>
    [ImporterHeader(Name = "诊疗组名称")]
    [ExporterHeader("诊疗组名称", Format = "", Width = 25, IsBold = true)]
    public string? TeamName { get; set; }

    /// <summary>
    /// 医生ID
    /// </summary>
    [ImporterHeader(Name = "医生ID")]
    [ExporterHeader("医生ID", Format = "", Width = 25, IsBold = true)]
    public long? DoctorId { get; set; }

    /// <summary>
    /// 医生姓名
    /// </summary>
    [ImporterHeader(Name = "医生姓名")]
    [ExporterHeader("医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>
    [ImporterHeader(Name = "接诊医生id")]
    [ExporterHeader("接诊医生id", Format = "", Width = 25, IsBold = true)]
    public long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>
    [ImporterHeader(Name = "接诊医生名称")]
    [ExporterHeader("接诊医生名称", Format = "", Width = 25, IsBold = true)]
    public string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    [ImporterHeader(Name = "入院途径")]
    [ExporterHeader("入院途径", Format = "", Width = 25, IsBold = true)]
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>
    [ImporterHeader(Name = "结算类别")]
    [ExporterHeader("结算类别", Format = "", Width = 25, IsBold = true)]
    public string? SettlementCategory { get; set; }
    
    /// <summary>
    /// 入院诊断编号
    /// </summary>
    [ImporterHeader(Name = "入院诊断编号")]
    [ExporterHeader("入院诊断编号", Format = "", Width = 25, IsBold = true)]
    public string? AdmissionDiagnosisCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    [ImporterHeader(Name = "入院诊断名称")]
    [ExporterHeader("入院诊断名称", Format = "", Width = 25, IsBold = true)]
    public string? AdmissionDiagnosisName { get; set; }
    
    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    [ImporterHeader(Name = "妊娠风险评估")]
    [ExporterHeader("妊娠风险评估", Format = "", Width = 25, IsBold = true)]
    public string? PregnancyRiskLevel { get; set; }
    
    /// <summary>
    /// 高危因素
    /// </summary>
    [ImporterHeader(Name = "高危因素")]
    [ExporterHeader("高危因素", Format = "", Width = 25, IsBold = true)]
    public string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
