﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药理分类表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("pharmacological_class", "药理分类表")]
public class PharmacologicalClass : EntityTenant
{
    /// <summary>
    /// 药理分类编码
    /// </summary>
    [SugarColumn(ColumnName = "class_code", ColumnDescription = "药理分类编码", Length = 100)]
    public virtual string? ClassCode { get; set; }
    
    /// <summary>
    /// 药理分类名称
    /// </summary>
    [SugarColumn(ColumnName = "class_name", ColumnDescription = "药理分类名称", Length = 100)]
    public virtual string? ClassName { get; set; }
    
    /// <summary>
    /// 父级分类ID
    /// </summary>
    [SugarColumn(ColumnName = "parent_id", ColumnDescription = "父级分类ID")]
    public virtual long? ParentId { get; set; }
    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态（1 启用 2 停用）")]
    public virtual int? Status { get; set; }
    
}
