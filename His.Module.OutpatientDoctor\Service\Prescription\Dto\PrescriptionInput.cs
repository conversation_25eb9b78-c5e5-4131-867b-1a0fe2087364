namespace His.Module.OutpatientDoctor.Dto;

public class AddPrescriptionInput
{
    public   AddPrescriptionMainInput  Main { get; set; }
    public   List<AddPrescriptionDetailInput>  Details { get; set; }
}

public class PagePrescriptionInput
{
    /// <summary>
    /// 处方号
    /// </summary>
    [MaxLength(64, ErrorMessage = "处方号字符长度不能超过64")]
    public string? PrescriptionNo { get; set; }
    
    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }
    /// <summary>
    /// 药房id
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string? PatientName { get; set; }

    /// <summary>
    /// 挂号Id
    /// </summary>
    public long? RegisterId { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }
    
    /// <summary>
    /// 处方时间范围
    /// </summary>
    public DateTime?[] PrescriptionTimeRange { get; set; }
    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary> 
    //public virtual int? Status { get; set; }
    public virtual List<int?> Status { get; set; }
    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string?  CardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
    public string?  OutpatientNo { get; set; }

}