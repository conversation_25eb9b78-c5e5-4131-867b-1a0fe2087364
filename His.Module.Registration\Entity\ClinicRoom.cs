﻿using Admin.NET.Core;
namespace His.Module.Registration.Entity;

/// <summary>
/// 诊室表
/// </summary>
[Tenant("1300000000004")]
[SugarTable("clinic_room", "诊室表")]
public class ClinicRoom : EntityTenant
{
    /// <summary>
    /// 诊室名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "诊室名称", Length = 255)]
    public virtual string? Name { get; set; }
    
    /// <summary>
    /// 诊室编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "诊室编码", Length = 255)]
    public virtual string? Code { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室id")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "科室名称", Length = 255)]
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    [SugarColumn(ColumnName = "ip_address", ColumnDescription = "ip地址", Length = 255)]
    public virtual string? IpAddress { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
