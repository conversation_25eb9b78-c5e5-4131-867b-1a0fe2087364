﻿namespace His.Module.MedicalTech.Service;

/// <summary>
/// 处置表输出参数
/// </summary>
public class DisposeOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>
    public string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    public long RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    public string VisitNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string PatientName { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 生产厂商
    /// </summary>
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    public string? FrequencyName { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public int? Days { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>
    public string? NationalstandardCode { get; set; }

    /// <summary>
    /// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    public int? IsPackage { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    public int? Flag { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    public DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    public string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    public string? ExecuteDeptAddress { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    public long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    public string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费人员名称
    /// </summary>
    public string? ChargeStaffName { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    public decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    public int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    public DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人员Id
    /// </summary>
    public long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人员名称
    /// </summary>
    public string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 医生签名
    /// </summary>
    public string? DoctorSign { get; set; }

    /// <summary>
    /// 医嘱Id
    /// </summary>
    public long? MedicalAdviceId { get; set; }

    /// <summary>
    /// 处方Id
    /// </summary>
    public long? PrescId { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 创建者部门Id
    /// </summary>
    public long? CreateOrgId { get; set; }

    /// <summary>
    /// 创建者部门名称
    /// </summary>
    public string? CreateOrgName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
}