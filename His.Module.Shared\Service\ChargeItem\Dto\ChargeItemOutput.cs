﻿namespace His.Module.Shared.Service;

/// <summary>
/// 收费项目输出参数
/// </summary>
public class ChargeItemOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    public string? WubiCode { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 进价
    /// </summary>
    public decimal? PurchasePrice { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    /// 批件产品名称
    /// </summary>
    public string? ApprovalName { get; set; }

    /// <summary>
    /// 产地
    /// </summary>
    public string? Producer { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    public string? RegistrationNumber { get; set; }

    /// <summary>
    /// 物价编码
    /// </summary>
    public string? PriceCode { get; set; }

    /// <summary>
    /// 收费类别
    /// </summary>
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 收费类别 描述
    /// </summary>
    public string ChargeCategoryFkDisplayName { get; set; }

    /// <summary>
    /// 核算类别
    /// </summary>
    public long? CalculateCategoryId { get; set; }

    /// <summary>
    /// 核算类别 描述
    /// </summary>
    public string CalculateCategoryFkDisplayName { get; set; }

    /// <summary>
    /// 电子发票费用类别
    /// </summary>
    public string? DzfpChargeCategory { get; set; }

    /// <summary>
    /// 病案首页费用类别
    /// </summary>
    public string? BasyChargeCategory { get; set; }

    /// <summary>
    /// 是否高值耗材
    /// </summary>
    public YesNoEnum HighValue { get; set; }

    /// <summary>
    /// 是否单用
    /// </summary>
    public YesNoEnum UseSeparately { get; set; }

    /// <summary>
    /// 是否上传地纬
    /// </summary>
    public YesNoEnum UploadDw { get; set; }

    /// <summary>
    /// 频次
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次 描述
    /// </summary>
    public string FrequencyFkDisplayName { get; set; }

    /// <summary>
    /// 样本类型
    /// </summary>
    public string? SampleType { get; set; }

    /// <summary>
    /// 护理等级
    /// </summary>
    public string? NurseLevel { get; set; }

    /// <summary>
    /// 检查类别
    /// </summary>
    public long? CheckCategoryId { get; set; }

    /// <summary>
    /// 检查类别 描述
    /// </summary>
    public string CheckCategoryFkDisplayName { get; set; }

    /// <summary>
    /// 退费模式
    /// </summary>
    public int? RefundMode { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    public YesNoEnum Package { get; set; }

    /// <summary>
    /// 使用科室
    /// </summary>
    public long? UseDepts { get; set; }

    /// <summary>
    /// 使用科室 描述
    /// </summary>
    public string UseDeptsFkDisplayName { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    public MedServiceCategoryEnum UsageScope { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    /// 检查部位
    /// </summary>
    public long? CheckPointId { get; set; }

    /// <summary>
    /// 检查部位 描述
    /// </summary>
    public string CheckPointFkDisplayName { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 套餐所包含的收费项目
    /// </summary>
    public List<ChargeItemOutput>? ChargeItemPacks { get; set; }
}

/// <summary>
/// 收费项目数据导入模板实体
/// </summary>
public class ExportChargeItemOutput : ImportChargeItemInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}