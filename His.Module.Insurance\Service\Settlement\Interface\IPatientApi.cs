using His.Module.Insurance.Service.Settlement.Dto;
using His.Module.Insurance.Service.Settlement.Dto.Patient;
using His.Module.Insurance.Service.Settlement.Model.Patient;
using ReadCardRequest = His.Module.Insurance.Service.Settlement.Model.Patient.ReadCardRequest;

namespace His.Module.Insurance.Service.Settlement.Interface;

/// <summary>
/// 个人信息获取
/// </summary>

public interface IPatientApi:ITransient
{
    
    public bool IsLocal (PatientBaseSettlementDto patient);
    
    /// <summary>
    /// 3.2.1获取人员基本信息（无卡）
    ///接口名称：query_basic_info
    /// 接口作用：在收到HIS的获取申请后，连接社保中心，获取个人基本信息。
    /// 接口类型：查询类
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public Task<QueryBasicInfoResponse> QueryBasicInfo(QueryBasicInfoRequest request);
    
    /// <summary>
    ///3.2.2读卡获取人员基本信息
    ///      接口名称：read_card
    ///    接口作用：读取卡片信息，取得人员相关信息。
    ///  接口类型：查询类
    /// </summary>
    
    public Task<ReadCardResponse> ReadCard(ReadCardRequest dto);
    
    /// <summary>
    /// 3.2.3查询个人账户余额信息
    ///接口名称：get_sscard_balance
       /// 接口作用：查询个人账户余额信息，只有省直、东营等银行管理账户使用，济南、淄博无法使用，需通过read_card获取余额。
  ///  接口类型：查询类
  ///  </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public Task<QueryBalanceResponse> GetBalance(QueryBalanceRequest request);
    /// <summary>
    /// 3.2.5根据电子医保凭证获取个人身份证号和姓名
    ///   接口名称：read_ewm
    ///  接口作用：根据电子医保凭证二维码获取个人身份证号码和姓名。
    /// 接口类型：查询类
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public Task<ReadEwmResponse> ReadEwm (ReadEwmRequest request);
    
 
    
    
}