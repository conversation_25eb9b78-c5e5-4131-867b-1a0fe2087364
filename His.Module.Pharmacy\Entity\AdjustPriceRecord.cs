﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品调价记录表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("adjust_price_record", "药品调价记录表")]
public class AdjustPriceRecord : EntityTenant
{
    /// <summary>
    /// 药品ID
    /// </summary>
    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品ID")]
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 100)]
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "规格", Length = 100)]
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 100)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量")]
    public virtual int? Quantity { get; set; }
    
    /// <summary>
    /// 旧零售价
    /// </summary>
    [SugarColumn(ColumnName = "old_sale_price", ColumnDescription = "旧零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? OldSalePrice { get; set; }
    
    /// <summary>
    /// 新零售价
    /// </summary>
    [SugarColumn(ColumnName = "new_sale_price", ColumnDescription = "新零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? NewSalePrice { get; set; }
     
    
    
    /// <summary>
    /// 旧总零售价
    /// </summary>
    [SugarColumn(ColumnName = "total_old_sale_price", ColumnDescription = "总零售价", Length = 20, DecimalDigits=4)]
    public decimal? TotalOldSalePrice { get; set; }

    /// <summary>
    /// 新总零售价
    /// </summary>
    [SugarColumn(ColumnName = "total_new_sale_price", ColumnDescription = "总零售价", Length = 20, DecimalDigits=4)]
    public decimal? TotalNewSalePrice { get; set; }
    
    /// <summary>
    /// 调价金额差额
    /// </summary>
    [SugarColumn(ColumnName = "adjust_price", ColumnDescription = "调价金额差额", Length = 20, DecimalDigits=4)]
    public decimal? AdjustPrice { get; set; }
    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnName = "batch_no", ColumnDescription = "批号", Length = 100)]
    public virtual string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnName = "production_date", ColumnDescription = "生产日期")]
    public virtual DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    [SugarColumn(ColumnName = "expiration_date", ColumnDescription = "有效期")]
    public virtual DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [SugarColumn(ColumnName = "approval_number", ColumnDescription = "批准文号", Length = 100)]
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "国家医保编码", Length = 100)]
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 生产厂商ID
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_id", ColumnDescription = "生产厂商ID")]
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_name", ColumnDescription = "生产厂商名称", Length = 100)]
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 调价时间
    /// </summary>
    [SugarColumn(ColumnName = "adjust_time", ColumnDescription = "调价时间")]
    public virtual DateTime? AdjustTime { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
}
