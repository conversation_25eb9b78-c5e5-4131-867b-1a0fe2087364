﻿namespace His.Module.Shared.Service.BasicInfo.Dto;

public class BasicInfoInput
{
    /// <summary>
    /// 关键字
    /// </summary>
    public string? Keyword { get; set; }
}

public class DepartmentInput : BasicInfoInput
{
    /// <summary>
    /// 机构类型列表
    /// </summary>
   // [Required(ErrorMessage = "机构类型不能为空")]
    public List<string> OrgTypes { get; set; }
    
    /// <summary>
    /// 父级Id
    /// </summary>
    public long? ParentId { get; set; }
 
}

/// <summary>
/// 频次查询输入参数
/// </summary>
public class FrequencyInput : BasicInfoInput
{
}

/// <summary>
/// 核算类别查询输入参数
/// </summary>
public class CalculateCategoryInput : BasicInfoInput
{
}

/// <summary>
/// 收费类别查询输入参数
/// </summary>
public class ChargeCategoryInput : BasicInfoInput
{
}

/// <summary>
/// 检查类别查询输入参数
/// </summary>
public class CheckCategoryInput : BasicInfoInput
{
}

/// <summary>
/// 检查部位查询输入参数
/// </summary>
public class CheckPointInput : BasicInfoInput
{
}

/// <summary>
/// 费用类别查询输入参数
/// </summary>
public class FeeCategoryInput : BasicInfoInput
{
    /// <summary>
    /// 使用范围
    /// </summary>
    public int? UsageScope { get; set; }
}

/// <summary>
/// ICD10查询输入参数
/// </summary>
public class Icd10Input : BasicInfoInput
{
}

/// <summary>
/// 给药途径查询输入参数
/// </summary>
public class MedicationRoutesInput : BasicInfoInput
{
}

/// <summary>
/// 支付方式查询输入参数
/// </summary>
public class PayMethodInput : BasicInfoInput
{
}

/// <summary>
/// 处方类型查询输入参数
/// </summary>
public class PrescriptionTypeInput : BasicInfoInput
{
}
/// <summary>
/// 根据科室结构查询科室输入参数
/// </summary>
public class QueryDeptByStructInput : BasicInfoInput
{
    public string? StructCode { get; set; }
}
/// <summary>
/// 挂号类别查询输入参数
/// </summary>
public class RegCategoryInput : BasicInfoInput
{
}

public class UserInput : BasicInfoInput
{
    /// <summary>
    /// 机构Id
    /// </summary>
   // [Required(ErrorMessage = "机构Id不能为空")] 不能限制科室
    public long OrgId { get; set; }
    
    /// <summary>
    /// 按角色查询
    /// </summary>
    public long RoleId { get; set; }
}
/// <summary>
/// 行政区域查询输入参数
/// </summary>
public class RegionInput : BasicInfoInput
{
}