<template>
	<div class="appointment-create-container" v-loading="state.loading">
		<div class="form-container">
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" inline-message :rules="rules">
				<el-card class="full-table" shadow="hover" style="margin-top: 5px">
					<template #header>
						<div class="card-header">
							<el-tag class="ml-2">患者基本信息</el-tag>
						</div>
					</template>
					<el-row style="flex: 1">
						<el-form-item v-show="false">
							<el-input v-model="state.ruleForm.id" />
						</el-form-item>
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb5">
							<el-form-item label="卡号" prop="cardNo">
								<el-input v-model="state.ruleForm.cardNo" @keyup.enter="handleCardNoEnter" style="width: 200px" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="姓名" prop="patientName">
								<el-input v-model="state.ruleForm.patientName" placeholder="请输入姓名" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="性别" prop="sex">
								<g-sys-dict v-model="state.ruleForm.sex" code="GenderEnum" placeholder="请选择性别" render-as="select" clearable filterable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="年龄" prop="age">
								<el-input v-model="state.ruleForm.age" type="number" placeholder="请输入年龄" @input="handleCalculateBirthDate">
									<template #append>
										<g-sys-dict v-model="state.ruleForm.ageUnit" code="AgeUnit" @change="handleCalculateBirthDate" render-as="select" placeholder="岁" style="width: 52px" />
									</template>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="出生日期" prop="birthday">
								<el-date-picker v-model="state.ruleForm.birthday" type="date" placeholder="出生日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleCalculateAgeAndUnit" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="证件类型" prop="cardType">
								<g-sys-dict v-model="state.ruleForm.cardType" code="CardTypeEnum" placeholder="请选择证件类型" render-as="select" clearable filterable @change="handleCardTypeChange" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="证件号" prop="idCardNo">
								<el-input v-model="state.ruleForm.idCardNo" placeholder="请输入证件号" clearable @blur="handleIdCardNoBlur" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="民族" prop="nation">
								<g-sys-dict v-model="state.ruleForm.nation" code="NationEnum" placeholder="请选择民族" render-as="select" value-type="string" clearable filterable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="电话号码" prop="phone">
								<el-input v-model="state.ruleForm.phone" placeholder="请输入电话号码" clearable @blur="handlePhoneBlur" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="职业" prop="occupation">
								<g-sys-dict v-model="state.ruleForm.occupation" code="Occupation" placeholder="请选择职业" render-as="select" clearable filterable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="国籍" prop="nationality">
								<g-sys-dict v-model="state.ruleForm.nationality" code="Nationality" placeholder="请选择国籍" render-as="select" clearable filterable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="籍贯" prop="nativePlace">
								<el-cascader v-model="state.nativePlace" :options="state.regionList" placeholder="请选择省市区县" @change="nativePlaceChange" clearable class="w100" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="出生地" prop="birthPlace">
								<el-cascader v-model="state.birthPlace" :options="state.regionList" placeholder="请选择省市区县" @change="birthPlaceChange" clearable class="w100" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="现住址" prop="address">
								<el-cascader v-model="state.address" :options="state.regionList" placeholder="请选择省市区县" @change="addressChange" clearable class="w100" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="详细现住址" prop="residenceAddress">
								<el-input v-model="state.ruleForm.residenceAddress" placeholder="请输入现住址详细地址" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="工作单位" prop="workPlace">
								<el-input v-model="state.ruleForm.workPlace" placeholder="请输入工作单位" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="单位电话" prop="workPlacePhone">
								<el-input v-model="state.ruleForm.workPlacePhone" placeholder="请输入单位电话" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="工作地址" prop="workAddress">
								<el-cascader v-model="state.workAddress" :options="state.regionList" placeholder="请选择省市区县" @change="workAddressChange" clearable class="w100" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="详细工作地址" prop="workAddress">
								<el-input v-model="state.ruleForm.workAddress" placeholder="请输入工作地址详细地址" clearable />
							</el-form-item>
						</el-col>
					</el-row>
				</el-card>
				<el-card class="full-table" shadow="hover" style="margin-top: 5px">
					<template #header>
						<div class="card-header">
							<el-tag class="ml-2">联系人信息</el-tag>
						</div>
					</template>
					<el-row style="flex: 1">
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="联系人姓名" prop="contactName">
								<el-input v-model="state.ruleForm.contactName" placeholder="请输入联系人姓名" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="与联系人关系" prop="contactRelationship">
								<g-sys-dict v-model="state.ruleForm.contactRelationship" code="DomesticRelation" placeholder="请选择与联系人关系" render-as="select" clearable filterable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="联系人电话" prop="contactPhone">
								<el-input v-model="state.ruleForm.contactPhone" placeholder="请输入联系人电话" clearable @blur="handleContactPhoneBlur" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="联系人地址" prop="contactAddress">
								<el-input v-model="state.ruleForm.contactAddress" placeholder="请输入联系人地址" clearable />
							</el-form-item>
						</el-col>
					</el-row>
				</el-card>
				<el-card class="full-table" shadow="hover" style="margin-top: 5px">
					<template #header>
						<div class="card-header">
							<el-tag class="ml-2">登记信息</el-tag>
						</div>
					</template>
					<el-row style="flex: 1">
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="入院时间" prop="inpatientTime">
								<el-date-picker
									v-model="state.ruleForm.inpatientTime"
									type="datetime"
									placeholder="请选择入院时间"
									:default-time="new Date('2000-01-01 00:00:00')"
									value-format="YYYY-MM-DD HH:mm:ss"
								/>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="接诊医生" prop="receivingDoctorId">
								<PinyinSelect v-model="state.ruleForm.receivingDoctorId" @change="receivingDoctorChange" placeholder="请选择接诊医生" :options="state.receivingDoctorList" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="住院科室" prop="deptId">
								<PinyinSelect v-model="state.ruleForm.deptId" @change="inpatientDeptChange" placeholder="请选择住院科室" :options="state.inpatientDeptList" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="病区" prop="wardId">
								<PinyinSelect v-model="state.ruleForm.wardId" @change="wardChange" placeholder="请选择病区" :options="state.wardList" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="诊疗小组" prop="teamId">
								<PinyinSelect v-model="state.ruleForm.teamId" @change="teamChange" labelField="teamName" placeholder="请选择诊疗小组" :options="state.teamList" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="主治医生" prop="doctorId">
								<PinyinSelect v-model="state.ruleForm.doctorId" @change="doctorChange" labelField="staffName" valueField="staffId" placeholder="请选择主治医生" :options="state.doctorList" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="入院诊断" prop="admissionDiagnosisCode">
								<el-select
									filterable
									v-model="state.ruleForm.admissionDiagnosisCode"
									remote
									reserve-keyword
									:remote-method="icd10RemoteMethod"
									:loading="state.icd10Loading"
									@change="diagnosticCodeChange"
									placeholder="请选择入院诊断"
								>
									<el-option v-if="state.admissionDiagnosisList.length == 0" :value="state.ruleForm.admissionDiagnosisCode ?? ''" :label="state.ruleForm.admissionDiagnosisName ?? ''"> </el-option>
									<el-option v-else v-for="item in state.admissionDiagnosisList" :key="item.code" :label="item.name" :value="item.code" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="结算类别" prop="settlementCategory">
								<PinyinSelect v-model="state.ruleForm.settlementCategory" valueField="code" placeholder="请选择结算类别" :options="state.settlementCategoryList" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="入院途径" prop="inpatientWay">
								<g-sys-dict v-model="state.ruleForm.inpatientWay" code="InpatientWayType" render-as="select" placeholder="请选择入院途径" clearable filterable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="预交金" prop="advancePayment">
								<el-input-number v-model="state.ruleForm.advancePayment" placeholder="请输入预交金金额" :min="1" controls-position="right" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="妊娠风险等级" prop="pregnancyRiskLevel">
								<g-sys-dict v-model="state.ruleForm.pregnancyRiskLevel" code="PregnancyRiskLevel" render-as="select" placeholder="请选择妊娠风险等级" clearable filterable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="高危因素" prop="highRiskFactors">
								<el-input v-model="state.ruleForm.highRiskFactors" placeholder="请输入高危因素" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
							<el-form-item label="备注" prop="remark">
								<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" clearable />
							</el-form-item>
						</el-col>
					</el-row>
				</el-card>
			</el-form>
		</div>
		<el-row style="margin-top: auto" class="foot">
			<el-col :span="24">
				<el-card style="margin-top: 5px" :body-style="{ padding: '0px' }" shadow="never">
					<div class="button-container">
						<el-button-group>
							<el-button type="primary" :icon="'ele-Plus'" @click="submit"> 提交 </el-button>
							<el-button type="success" icon="ele-Reading">读卡</el-button>
							<el-button type="info" icon="ele-Refresh" @click="resetForm">清屏</el-button>
							<el-button type="info" icon="ele-Printer">打印</el-button>
						</el-button-group>
					</div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>
<script lang="ts" setup name="appointment-create">
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import PinyinSelect from '/@/components/pinyinSelect/index.vue';

import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import { useMedicalCardInfoApi } from '/@/api/patient/medicalCardInfo';
import { useInpatientRegisterApi } from '/@/api/inpatient/inpatientRegister';
import { useAppointmentRecordApi } from '/@/api/inpatient/appointmentRecord';
import { calculateAgeAndUnit, calculateBirthDate, extractInfoFromIdCard, verifyPhone, verifyIdCard } from '/@/utils/toolsValidate';
import { useRoute } from 'vue-router';

//API服务
const basicInfoApi = useBasicInfoApi();
const medicalCardInfoApi = useMedicalCardInfoApi();
const inpatientRegisterApi = useInpatientRegisterApi();
const appointmentRecordApi = useAppointmentRecordApi();

// 路由对象
const route = useRoute();

const ruleFormRef = ref();
const state = reactive({
	loading: false,
	icd10Loading: false,
	ruleForm: {} as any,
	address: [] as any, //现住址选中的值
	workAddress: [] as any, //工作地址选中的值
	nativePlace: [] as any, //籍贯选中的值
	birthPlace: [] as any, //出生地选中的值
	regionList: [] as any[], //省市区县集合
	//接诊医生集合
	receivingDoctorList: [] as any[],
	//住院科室集合
	inpatientDeptList: [] as any[],
	//病区集合
	wardList: [] as any[],
	//诊疗小组集合
	teamList: [] as any[],
	//主治医生集合
	doctorList: [] as any[],
	//入院诊断集合
	admissionDiagnosisList: [] as any[],
	//结算类别集合
	settlementCategoryList: [] as any[],
});

// 页面加载时
onMounted(async () => {
	//获取行政区域列表
	await basicInfoApi.getRegionTree({}).then((res) => (state.regionList = res.data.result ?? []));
	//获取接诊医生列表
	await basicInfoApi.getOutpatientDoctor({}).then((res) => (state.receivingDoctorList = res.data.result ?? []));
	//获取住院科室列表
	await basicInfoApi.getInpatientDepartments({}).then((res) => (state.inpatientDeptList = res.data.result ?? []));
	//获取结算类别(住院)列表
	await basicInfoApi.getFeeCategories({ usageScope: 2 }).then((res) => (state.settlementCategoryList = res.data.result ?? []));

	// 如果是从预约记录跳转过来，根据预约ID查询预约记录并回写表单
	const appointmentId = route.query.appointmentId;
	const type = route.query.type;
	if (appointmentId && type === 'appointment') {
		await loadAppointmentData(appointmentId);
	}
});
//提交
const submit = () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await inpatientRegisterApi.add(values);
			ElMessage.success('提交成功');
			resetForm();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};

// 重置表单
const resetForm = () => {
	ruleFormRef.value?.resetFields();
	state.address = null;
	state.workAddress = null;
	state.nativePlace = null;
	state.birthPlace = null;
	state.ruleForm.ageUnit = null;
};

// 入院诊断远程搜索方法
const icd10RemoteMethod = async (query: string) => {
	const res = await basicInfoApi.getIcd10s({ keyword: query });
	state.admissionDiagnosisList = (res.data.result ?? []).map((item: any) => ({ code: item.code, name: item.name }));
};

/**
 * 通用地址选择处理函数
 * @param value 级联选择器的值
 * @param prefix 字段前缀
 */
const handleAddressChange = (value: any, prefix: string) => {
	if (value == undefined) return;

	state.ruleForm[`${prefix}Province`] = value[0];
	state.ruleForm[`${prefix}City`] = value[1];
	state.ruleForm[`${prefix}County`] = value[2];
};

//选中籍贯触发事件
const nativePlaceChange = (value: any) => {
	handleAddressChange(value, 'nativePlace');
};

//选中出生地触发事件
const birthPlaceChange = (value: any) => {
	handleAddressChange(value, 'birthplace');
};

//选中现住址触发事件
const addressChange = (value: any) => {
	handleAddressChange(value, 'residence');
};

//选中工作地址触发事件
const workAddressChange = (value: any) => {
	handleAddressChange(value, 'work');
};
//选中接诊医生触发事件
const receivingDoctorChange = (value: string) => {
	// 找到选中的对象
	const selectedOption = state.receivingDoctorList.find((item) => item.id === value);
	if (selectedOption) {
		state.ruleForm.receivingDoctorName = selectedOption.name;
	}
};
//选中住院科室触发事件
const inpatientDeptChange = async (value: string) => {
	if (value) {
		//获取病区列表
		await basicInfoApi.getInpatientWards({ parentId: value }).then((res) => (state.wardList = res.data.result ?? []));
		//获取诊疗小组列表
		await basicInfoApi.getMedicalTeamList({ deptId: value }).then((res) => (state.teamList = res.data.result ?? []));
		// 找到选中的对象
		const selectedOption = state.inpatientDeptList.find((item) => item.id === value);
		if (selectedOption) {
			state.ruleForm.deptName = selectedOption.name;
		}
		state.ruleForm.wardId = '';
		state.ruleForm.teamId = '';
	} else {
		state.wardList = [];
		state.teamList = [];
	}
};
//选中病区触发事件
const wardChange = (value: string) => {
	// 找到选中的对象
	const selectedOption = state.wardList.find((item) => item.id === value);
	if (selectedOption) {
		state.ruleForm.wardName = selectedOption.name;
	}
};
//选中诊疗小组触发事件
const teamChange = async (value: string) => {
	if (value) {
		//获取主治医生列表
		await basicInfoApi.getMedicalTeamMemberList({ teamId: value }).then((res) => (state.doctorList = res.data.result ?? []));
		//找到选中的对象
		const selectedOption = state.teamList.find((item) => item.id === value);
		if (selectedOption) {
			state.ruleForm.teamName = selectedOption.teamName;
		}
	} else {
		state.doctorList = [];
	}
};
//选中主治医生触发事件
const doctorChange = (value: string) => {
	// 找到选中的对象
	const selectedOption = state.doctorList.find((item) => item.staffId === value);
	if (selectedOption) {
		state.ruleForm.doctorName = selectedOption.staffName;
	}
};
//选中入院诊断触发事件
const diagnosticCodeChange = (value: string) => {
	// 找到选中的对象
	const selectedOption = state.admissionDiagnosisList.find((item) => item.code === value);
	if (selectedOption) {
		state.ruleForm.admissionDiagnosisName = selectedOption.name;
	}
};
//选中结算类别触发事件
const settlementCategoryChange = (value: string) => {
	// 找到选中的对象
	const selectedOption = state.settlementCategoryList.find((item) => item.id === value);
	if (selectedOption) {
		state.ruleForm.settlementCategoryName = selectedOption.name;
	}
};
/**
 * 地址验证函数
 * @param _rule 验证规则
 * @param _value 验证值
 * @param callback 回调函数
 * @param addressField 地址字段名
 */
const validateAddress = (_rule: any, _value: any, callback: any) => {
	// 检查地址是否有效：不为undefined且长度大于0且第一个元素不为undefined
	if (state.address != undefined && state.address.length > 0 && state.address[0] == undefined) {
		callback(new Error('请选择现住址！'));
	} else {
		callback();
	}
};
//自行添加其他规则
const rules = ref<FormRules>({
	patientName: [{ required: true, message: '请输入姓名！', trigger: 'blur' }],
	businessType: [{ required: true, message: '请选择业务类型！', trigger: 'change' }],
	sex: [{ required: true, message: '请选择性别！', trigger: 'change' }],
	age: [{ required: true, message: '请输入年龄！', trigger: 'blur' }],
	birthday: [{ required: true, message: '请选择出生日期！', trigger: 'change' }],
	cardType: [{ required: true, message: '请选择证件类型！', trigger: 'change' }],
	idCardNo: [
		{ required: true, message: '请输入证件号！', trigger: 'blur' },
		{
			validator: (rule, value, callback) => {
				// 只有当证件类型为身份证(0)时才进行验证
				if (value && state.ruleForm.cardType === 0 && !verifyIdCard(value)) {
					callback(new Error('请输入正确的身份证号码'));
				} else {
					callback();
				}
			},
			trigger: 'blur',
		},
	],
	phone: [
		{ required: true, message: '请输入电话号码！', trigger: 'blur' },
		{
			validator: (rule, value, callback) => {
				if (value && !verifyPhone(value)) {
					callback(new Error('请输入正确的手机号码'));
				} else {
					callback();
				}
			},
			trigger: 'blur',
		},
	],
	address: [{ validator: validateAddress, required: true, trigger: 'change' }],
	residenceAddress: [{ required: true, message: '请输入详细现住址！', trigger: 'blur' }],
	//联系人姓名
	contactName: [{ required: true, message: '请输入联系人姓名！', trigger: 'blur' }],
	//联系人关系
	contactRelationship: [{ required: true, message: '请选择联系人关系！', trigger: 'change' }],
	//联系人电话
	contactPhone: [
		{ required: true, message: '请输入联系人电话！', trigger: 'blur' },
		{
			validator: (rule, value, callback) => {
				if (value && !verifyPhone(value)) {
					callback(new Error('请输入正确的手机号码'));
				} else {
					callback();
				}
			},
			trigger: 'blur',
		},
	],
	//入院时间
	inpatientTime: [{ required: true, message: '请选择入院时间！', trigger: 'change' }],
	//接诊医生
	receivingDoctorId: [{ required: true, message: '请选择接诊医生！', trigger: 'change' }],
	//住院科室
	deptId: [{ required: true, message: '请选择住院科室！', trigger: 'change' }],
	//病区
	wardId: [{ required: true, message: '请选择病区！', trigger: 'change' }],
	//诊疗小组
	teamId: [{ required: true, message: '请选择诊疗小组！', trigger: 'change' }],
	//主治医生
	doctorId: [{ required: true, message: '请选择主治医生！', trigger: 'change' }],
	//入院诊断
	admissionDiagnosisCode: [{ required: true, message: '请选择入院诊断！', trigger: 'change' }],
	//结算类别
	settlementCategory: [{ required: true, message: '请选择结算类别！', trigger: 'change' }],
	//入院途径
	inpatientWay: [{ required: true, message: '请选择入院途径！', trigger: 'change' }],
});

//卡号回车
const handleCardNoEnter = () => {
	console.log('回车键被按下' + state.ruleForm.cardNo.length);
	if (state.ruleForm.cardNo.length == 18) {
		// 身份证号
		getCardInfoByCardId(state.ruleForm.cardNo, '');
	} else if (state.ruleForm.cardNo.length == 8) {
		// 就诊卡号
		getCardInfoByCardId('', state.ruleForm.cardNo);
	} else if (state.ruleForm.cardNo.length == 8) {
		// 健康聊城
		//getCardInfoByCardId(state.ruleForm.cardNo)
	} else if (state.ruleForm.cardNo.length == 8) {
		// 医保电子凭证
		//getCardInfoByCardId(state.ruleForm.cardNo)
	}
};
//根据身份证号查询卡信息
const getCardInfoByCardId = async (idCardNo: string, cardNo: string) => {
	var res = await medicalCardInfoApi.cardInfoByIdOrCardNo({ idCardNo: idCardNo, cardNo: cardNo });
	// 更新表单信息
	state.ruleForm = res.data.result;
	state.address = [state.ruleForm.residenceProvince?.toString(), state.ruleForm.residenceCity?.toString(), state.ruleForm.residenceCounty?.toString()];
	state.workAddress = [state.ruleForm.workProvince?.toString(), state.ruleForm.workCity?.toString(), state.ruleForm.workCounty?.toString()];
	state.nativePlace = [state.ruleForm.nativePlaceProvince?.toString(), state.ruleForm.nativePlaceCity?.toString(), state.ruleForm.nativePlaceCounty?.toString()];
	state.birthPlace = [state.ruleForm.birthplaceProvince?.toString(), state.ruleForm.birthplaceCity?.toString(), state.ruleForm.birthplaceCounty?.toString()];
	state.ruleForm.medicalCardNo = res.data.result.cardNo;
	// 如果有出生日期，计算年龄
	if (state.ruleForm.birthday) {
		handleCalculateAgeAndUnit();
	}
	// 如果有年龄但没有出生日期，计算出生日期
	else if (state.ruleForm.age && !state.ruleForm.birthday) {
		handleCalculateBirthDate();
	}
};

/**
 * 根据出生日期计算年龄和年龄单位
 */
const handleCalculateAgeAndUnit = () => {
	if (!state.ruleForm.birthday) return;

	const result = calculateAgeAndUnit(state.ruleForm.birthday);
	state.ruleForm.age = result.age;
	state.ruleForm.ageUnit = result.ageUnit;
};

/**
 * 根据年龄和年龄单位计算出生日期
 */
const handleCalculateBirthDate = () => {
	if (!state.ruleForm.age || state.ruleForm.age <= 0) return;

	state.ruleForm.birthday = calculateBirthDate(state.ruleForm.age, state.ruleForm.ageUnit);
};

/**
 * 证件类型变更事件
 */
const handleCardTypeChange = () => {
	if (state.ruleForm.idCardNo) {
		if (state.ruleForm.cardType === 0) {
			handleExtractInfoFromIdCard();
		}
		// 无论证件类型是什么，都触发证件号的验证
		ruleFormRef.value.validateField('idCardNo');
	}
};

/**
 * 证件号失焦事件
 */
const handleIdCardNoBlur = () => {
	if (state.ruleForm.idCardNo) {
		// 如果是身份证，提取信息
		if (state.ruleForm.cardType === 0) {
			handleExtractInfoFromIdCard();
		}
		// 触发证件号的验证
		ruleFormRef.value.validateField('idCardNo');
	}
};

/**
 * 从身份证号中提取信息
 */
const handleExtractInfoFromIdCard = () => {
	const result = extractInfoFromIdCard(state.ruleForm.idCardNo);
	if (result) {
		state.ruleForm.birthday = result.birthday;
		state.ruleForm.sex = result.sex;
		handleCalculateAgeAndUnit();
	}
};

/**
 * 联系人电话号码失焦事件
 */
const handleContactPhoneBlur = () => {
	if (state.ruleForm.contactPhone) {
		// 使用表单验证方式显示错误，触发表单项的验证
		ruleFormRef.value.validateField('contactPhone');
	}
};

/**
 * 电话号码失焦事件
 */
const handlePhoneBlur = () => {
	if (state.ruleForm.phone) {
		// 使用表单验证方式显示错误，触发表单项的验证
		ruleFormRef.value.validateField('phone');
	}
};

/**
 * 根据预约ID查询预约记录并回写表单
 */
const loadAppointmentData = async (appointmentId: any) => {
	try {
		state.loading = true;
		const res = await appointmentRecordApi.detail(appointmentId);
		const appointmentData = res.data.result;
		if (appointmentData) {
			state.ruleForm = appointmentData;
			state.ruleForm.appointmentId = appointmentId;
			//设置入院时间为当前时间
			state.ruleForm.inpatientTime = new Date();
			state.address = [state.ruleForm.residenceProvince?.toString(), state.ruleForm.residenceCity?.toString(), state.ruleForm.residenceCounty?.toString()];
			state.workAddress = [state.ruleForm.workProvince?.toString(), state.ruleForm.workCity?.toString(), state.ruleForm.workCounty?.toString()];
			state.nativePlace = [state.ruleForm.nativePlaceProvince?.toString(), state.ruleForm.nativePlaceCity?.toString(), state.ruleForm.nativePlaceCounty?.toString()];
			state.birthPlace = [state.ruleForm.birthplaceProvince?.toString(), state.ruleForm.birthplaceCity?.toString(), state.ruleForm.birthplaceCounty?.toString()];
			state.ruleForm.medicalCardNo = appointmentData.medicalCardNo;
			// 如果有科室ID，加载对应的病区和诊疗小组
			if (appointmentData.deptId) {
				await inpatientDeptChange(appointmentData.deptId);
				await teamChange(appointmentData.teamId);
			}
			ElMessage.success('预约记录加载成功');
		}
	} catch (error) {
		console.error('加载预约记录失败:', error);
	} finally {
		state.loading = false;
	}
};

//将属性或者函数暴露给父组件
defineExpose({});
</script>
<style scoped>
.appointment-create-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	/* 使容器占满整个视口高度 */
}

.form-container {
	flex: 1;
	/* 表单容器占据剩余空间 */
	overflow-y: auto;
	/* 增加垂直滚动条 */
	padding: 0px;
	/* 可选：添加内边距 */
}

.foot {
	position: sticky;
	bottom: 0;
	background-color: white;
	/* 背景色防止内容穿透 */
	z-index: 1000;
	/* 确保底部内容在最上层 */
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}

:deep(.el-card__header) {
	padding: 5px;
}

:deep(.el-card__body) {
	padding: 10px;
}

.button-container {
	display: flex;
	justify-content: flex-end;
	padding: 10px;
	/* 可选：添加一些内边距 */
}

.mb5 {
	margin-bottom: 5px;
}

.search-header {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 10px;
}

.search-label {
	font-weight: bold;
	min-width: 70px;
}
</style>
