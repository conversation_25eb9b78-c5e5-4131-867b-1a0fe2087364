﻿using His.Module.Patient.Api.Enum;
namespace His.Module.Patient.Service;

/// <summary>
/// 就诊卡管理输出参数
/// </summary>
public class MedicalCardInfoOutput : PatientInfo
{
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 患者唯一号
    /// </summary>
    public override string PatientNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public long PatientId { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public BusinessTypeEnum BusinessType { get; set; }

    /// <summary>
    /// 使用科室
    /// </summary>
    public string UseDepts { get; set; }

    /// <summary>
    /// 充值方式
    /// </summary>
    public string ChargeModes { get; set; }

    /// <summary>
    /// 余额
    /// </summary>
    public decimal? Balance { get; set; }

    /// <summary>
    /// 卡状态
    /// </summary>
    public CardStatusEnum Status { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
}