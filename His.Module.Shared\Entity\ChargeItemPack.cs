﻿namespace His.Module.Shared.Entity;

/// <summary>
/// 收费项目套餐表
/// </summary>
[SugarTable("charge_item_pack", "收费项目套餐表")]
[Tenant("1300000000014")]
public class ChargeItemPack : EntityTenant
{
    /// <summary>
    /// 套餐Id
    /// </summary>
    [SugarColumn(ColumnName = "pack_id", ColumnDescription = "套餐Id")]
    public long? PackId { get; set; }

    /// <summary>
    /// 收费项目Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_item_id", ColumnDescription = "单项Id")]
    public long? ChargeItemId { get; set; }

    /// <summary>
    /// 收费项目数量
    /// </summary>
    [SugarColumn(ColumnName = "charge_item_quantity", ColumnDescription = "收费项目数量")]
    public Int16? ChargeItemQuantity { get; set; }
}