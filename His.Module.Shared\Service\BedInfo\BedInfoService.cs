﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor; 
using His.Module.Shared.Entity;
using Microsoft.AspNetCore.Http;

namespace His.Module.Shared.Service;

/// <summary>
/// 床位信息服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class BedInfoService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<BedInfo> _bedInfoRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public BedInfoService(SqlSugarRepository<BedInfo> bedInfoRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _bedInfoRep = bedInfoRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询床位信息 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询床位信息")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<BedInfoOutput>> Page(PageBedInfoInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _bedInfoRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.BedNo.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.WardName.Contains(input.Keyword) || u.RoomNo.Contains(input.Keyword) || u.BedType.Contains(input.Keyword) || u.BedStatus.Contains(input.Keyword) || u.BedLevelName.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BedNo), u => u.BedNo.Contains(input.BedNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeptName), u => u.DeptName.Contains(input.DeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.WardName), u => u.WardName.Contains(input.WardName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RoomNo), u => u.RoomNo.Contains(input.RoomNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BedType), u => u.BedType.Contains(input.BedType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BedStatus), u => u.BedStatus.Contains(input.BedStatus.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BedLevelName), u => u.BedLevelName.Contains(input.BedLevelName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.WardId != null, u => u.WardId == input.WardId)
            .WhereIF(input.OrderNo != null, u => u.OrderNo == input.OrderNo)
            .WhereIF(input.Status.HasValue, u => u.Status == (int)input.Status)
            .LeftJoin<BedLevel>((u, bedLevel) => u.BedLevelId == bedLevel.Id)
            .Select((u, bedLevel) => new BedInfoOutput
            {
                Id = u.Id,
                BedNo = u.BedNo,
                DeptId = u.DeptId,
                DeptName = u.DeptName,
                WardId = u.WardId,
                WardName = u.WardName,
                RoomNo = u.RoomNo,
                BedType = u.BedType,
                BedStatus = u.BedStatus,
                BedLevelName = u.BedLevelName,
                BedLevelFkDisplayName = $"{bedLevel.LevelName}",
                OrderNo = u.OrderNo,
                Status = (StatusEnum) u.Status,
                Remark = u.Remark,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取床位信息详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取床位信息详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<BedInfo> Detail([FromQuery] QueryByIdBedInfoInput input)
    {
        return await _bedInfoRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加床位信息 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加床位信息")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddBedInfoInput input)
    {
        var entity = input.Adapt<BedInfo>();
        return await _bedInfoRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新床位信息 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新床位信息")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateBedInfoInput input)
    {
        var entity = input.Adapt<BedInfo>();
        await _bedInfoRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除床位信息 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除床位信息")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteBedInfoInput input)
    {
        var entity = await _bedInfoRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _bedInfoRep.FakeDeleteAsync(entity);   //假删除
        //await _bedInfoRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除床位信息 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除床位信息")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteBedInfoInput> input)
    {
        var exp = Expressionable.Create<BedInfo>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _bedInfoRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _bedInfoRep.FakeDeleteAsync(list);   //假删除
        //return await _bedInfoRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置床位信息状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置床位信息状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetBedInfoStatus(SetBedInfoStatusInput input)
    {
        await _bedInfoRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataBedInfoInput input)
    {
        var bedLevelData = await _bedInfoRep.Context.Queryable<BedLevel>()
            .InnerJoinIF<BedInfo>(input.FromPage, (u, r) => u.Id == r.BedLevelId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.LevelName}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "bedLevel", bedLevelData },
        };
    }
    
    /// <summary>
    /// 导出床位信息记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出床位信息记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageBedInfoInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportBedInfoOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var bedTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "InpatientBedType" }).Result.ToDictionary(x => x.Value, x => x.Label);
        var bedStatusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "InpatientBedStatus" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.BedTypeDictLabel = bedTypeDictMap.GetValueOrDefault(e.BedType ?? "", e.BedType);
            e.BedStatusDictLabel = bedStatusDictMap.GetValueOrDefault(e.BedStatus ?? "", e.BedStatus);
        });
        return ExcelHelper.ExportTemplate(list, "床位信息导出记录");
    }
    
    /// <summary>
    /// 下载床位信息数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载床位信息数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportBedInfoOutput>(), "床位信息导入模板", (_, info) =>
        {
            if (nameof(ExportBedInfoOutput.BedLevelFkDisplayName) == info.Name) return _bedInfoRep.Context.Queryable<BedLevel>().Select(u => $"{u.LevelName}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入床位信息记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入床位信息记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var bedTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "InpatientBedType" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var bedStatusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "InpatientBedStatus" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportBedInfoInput, BedInfo>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 床位等级
                    var bedLevelLabelList = pageItems.Where(x => x.BedLevelFkDisplayName != null).Select(x => x.BedLevelFkDisplayName).Distinct().ToList();
                    if (bedLevelLabelList.Any()) {
                        var bedLevelLinkMap = _bedInfoRep.Context.Queryable<BedLevel>().Where(u => bedLevelLabelList.Contains($"{u.LevelName}")).ToList().ToDictionary(u => $"{u.LevelName}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.BedLevelId = bedLevelLinkMap.GetValueOrDefault(e.BedLevelFkDisplayName ?? "");
                            if (e.BedLevelName == null) e.Error = "床位等级链接失败";
                        });
                    }
                    
                    // 映射字典值
                    foreach(var item in pageItems) {
                        if (string.IsNullOrWhiteSpace(item.BedTypeDictLabel)) continue;
                        item.BedType = bedTypeDictMap.GetValueOrDefault(item.BedTypeDictLabel);
                        if (item.BedType == null) item.Error = "床位类型字典映射失败";
                        if (string.IsNullOrWhiteSpace(item.BedStatusDictLabel)) continue;
                        item.BedStatus = bedStatusDictMap.GetValueOrDefault(item.BedStatusDictLabel);
                        if (item.BedStatus == null) item.Error = "床位状态字典映射失败";
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.DeptId == null){
                            x.Error = "科室id不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.WardId == null){
                            x.Error = "病区id不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.OrderNo == null){
                            x.Error = "排序不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<BedInfo>>();
                    
                    var storageable = _bedInfoRep.Context.Storageable(rows)
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.BedNo), "床位编号不能为空")
                        .SplitError(it => it.Item.BedNo?.Length > 100, "床位编号长度不能超过100个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.DeptName), "科室名称不能为空")
                        .SplitError(it => it.Item.DeptName?.Length > 100, "科室名称长度不能超过100个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.WardName), "病区名称不能为空")
                        .SplitError(it => it.Item.WardName?.Length > 100, "病区名称长度不能超过100个字符")
                        .SplitError(it => it.Item.RoomNo?.Length > 100, "房间编号长度不能超过100个字符")
                        .SplitError(it => it.Item.BedType?.Length > 100, "床位类型长度不能超过100个字符")
                        .SplitError(it => it.Item.BedStatus?.Length > 100, "床位状态长度不能超过100个字符")
                        .SplitError(it => it.Item.BedLevelName?.Length > 100, "床位等级长度不能超过100个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
