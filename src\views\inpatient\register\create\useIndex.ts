import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useInpatientRegisterApi } from '/@/api/inpatient/inpatientRegister';
import { useFeeCategoryApi } from '/@/api/shared/feeCategory';

import { useRouter } from 'vue-router';
import { SysRegionApi } from '/@/api-services/api';
import { getAPI } from '/@/utils/axios-utils';
import { useAdvancePaymentApi } from '/@/api/inpatient/management/advancePayment';

export function useInpatientRegisterCreate() {
	const router = useRouter();

	const ruleFormRef = ref();
	const inpatientRegisterApi = useInpatientRegisterApi();
	const feeCategoryApi = useFeeCategoryApi();
	const printDialogRef = ref();

	const state = reactive({
		ruleForm: {} as any,
		loading: false,
		stores: {},
		dropdownData: {} as any,

		addressDicData: [] as any[],
		inpatientDiagnosticOptionData: [] as any[], // 入院诊断检索到的数据
		secondaryDiagnosticOptionData: [] as any[], // 出院诊断检索到的数据
		advancePaymentInfo: {} as any, // 预交金
	});

	const register = async () => {
		ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
			if (isValid) {
				state.loading = true;
				if (!state.ruleForm.feeName) {
					state.ruleForm.feeName = state.dropdownData.feeCategoryData.filter((item: any) => item.id === state.ruleForm.feeId)[0].name;
				}
				let values = state.ruleForm;

				console.log('ruleForm', JSON.parse(JSON.stringify(values)));

				const data = {
					rregisterInfo: values,
					advancePaymentInfo: state.advancePaymentInfo,
				};

				await inpatientRegisterApi[state.ruleForm.id ? 'update' : 'add'](data)
					.then(async (res) => {
						// 重新获取详情数据
						ElMessage.success('保存成功');
						await inpatientRegisterApi.detail(res.data.result).then((d) => {
							ruleFormRef.value.resetFields(); // 重置表单验证状态
							state.ruleForm = {}; // 重置表单数据
							state.ruleForm = d.data.result;
						});
						// if (res.data.result) {
						//   ElMessage.success('保存成功');
						//    ruleFormRef.value.resetFields(); // 重置表单验证状态
						//    state.ruleForm = {}; // 重置表单数据
						// } else {
						// }
						state.loading = false;
					})
					.catch((err) => {
						state.loading = false;
					});
			} else {
				ElMessage({
					message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
					type: 'error',
				});
			}
		});
	};

	onMounted(async () => {
		state.dropdownData.feeCategoryData = await feeCategoryApi.page({ pageSize: 100, status: 1, usageScope: 2 }).then((res) => res.data.result?.items ?? []);
		if (router.options.history.state) {
			// 预约记录
			state.ruleForm = router.options.history.state;

			console.log('appointmentrecord', state.ruleForm);

			// 入院诊断
			state.ruleForm.inpatientDiagnosticCode = state.ruleForm.diagnosticCode;
			state.ruleForm.inpatientDiagnosticName = state.ruleForm.diagnosticName;
			// 入院途径 2
			state.ruleForm.inpatientWay = '2';

			// 主治医生
			state.ruleForm.mainDoctorId = state.ruleForm.doctorId;
			// 出生地
			state.ruleForm.birthplace = [String(state.ruleForm.birthplaceProvince), String(state.ruleForm.birthplaceCity), String(state.ruleForm.birthplaceCounty)];

			// 现居住地
			state.ruleForm.residence = [String(state.ruleForm.residenceProvince), String(state.ruleForm.residenceCity), String(state.ruleForm.residenceCounty)];

			// 工作地址
			state.ruleForm.workAddress = [String(state.ruleForm.workProvince), String(state.ruleForm.workCity), String(state.ruleForm.workCounty)];
			// 籍贯
			state.ruleForm.nativePlace = [String(state.ruleForm.nativePlaceProvince), String(state.ruleForm.nativePlaceCity), String(state.ruleForm.nativePlaceCounty)];
			setDefaultData();
		}
	});
	const inpatientDiagnosticSearchMethod = async (query: string) => {
		if (query && query.length > 1) {
			await inpatientRegisterApi
				.getDiagnosticMap(query)
				.then((res) => {
					state.inpatientDiagnosticOptionData = res.data.result;
				})
				.catch((err) => {
					state.inpatientDiagnosticOptionData = [];
				});
		} else {
			state.inpatientDiagnosticOptionData = [];
		}
	};
	const secondaryDiagnosticSearchMethod = async (query: string) => {
		if (query && query.length > 1) {
			await inpatientRegisterApi
				.getDiagnosticMap(query)
				.then((res) => {
					state.secondaryDiagnosticOptionData = res.data.result;
				})
				.catch((err) => {
					state.secondaryDiagnosticOptionData = [];
				});
		} else {
			state.secondaryDiagnosticOptionData = [];
		}
	};
	const setDefaultData = () => {
		state.ruleForm.inpatientTime = new Date();
		state.ruleForm.arrearsLimit = 1000;
		// 默认允许欠费
		console.log('allowArrears', state.ruleForm.allowArrears);
		state.ruleForm.allowArrears = true;
	};
	const handleQuery = async (params: any = {}) => {
		let addressDicData = await getAPI(SysRegionApi).apiSysRegionTreeGet();
		const data = (await inpatientRegisterApi.getDropdownData(true).then((res) => res.data.result)) ?? {};
		state.dropdownData.depts = data.depts;
		state.dropdownData.doctors = data.doctors;
		state.addressDicData = addressDicData.data.result ?? [];
	};

	const calculateAgeAndUnit = () => {
		if (!state.ruleForm.birthday) return;

		const birthDate = new Date(state.ruleForm.birthday);
		const today = new Date();

		let age = today.getFullYear() - birthDate.getFullYear();
		const monthDiff = today.getMonth() - birthDate.getMonth();

		if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
			age--;
		}

		if (age < 1) {
			let months = (today.getFullYear() - birthDate.getFullYear()) * 12 + (today.getMonth() - birthDate.getMonth());
			if (today.getDate() < birthDate.getDate()) {
				months--;
			}

			if (months < 1) {
				const timeDiff = today.getTime() - birthDate.getTime();
				const days = Math.floor(timeDiff / (1000 * 3600 * 24));
				state.ruleForm.age = days;
				state.ruleForm.ageUnit = '2'; // 天
			} else {
				state.ruleForm.age = months;
				state.ruleForm.ageUnit = '1'; // 月
			}
		} else {
			state.ruleForm.age = age;
			state.ruleForm.ageUnit = '0'; // 岁
		}
	};

	const calculateBirthDate = () => {
		if (!state.ruleForm.age || isNaN(state.ruleForm.age)) return;

		const today = new Date();
		let birthDate;

		switch (state.ruleForm.ageUnit) {
			case '0': // 岁
				birthDate = new Date(today.getFullYear() - state.ruleForm.age, today.getMonth(), today.getDate());
				break;
			case '1': // 月
				birthDate = new Date(today.getFullYear(), today.getMonth() - state.ruleForm.age, today.getDate());
				break;
			case '2': // 天
				birthDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - state.ruleForm.age);
				break;
			default:
				break;
		}

		state.ruleForm.birthday = birthDate;
	};

	const numberToChinese = (num: number): string => {
		const chineseNumbers = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
		const chineseUnits = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];

		if (num === 0) return '零元整';

		let str = '';
		let unitPos = 0;
		let needZero = false;

		while (num > 0) {
			const digit = num % 10;
			if (digit === 0) {
				if (unitPos === 4 || unitPos === 8) {
					str = chineseUnits[unitPos] + str;
				}
				if (needZero) {
					str = chineseNumbers[digit] + str;
					needZero = false;
				}
			} else {
				str = chineseNumbers[digit] + chineseUnits[unitPos] + str;
				needZero = true;
			}
			num = Math.floor(num / 10);
			unitPos++;
		}

		return str + '元整';
	};

	return {
		ruleFormRef,
		printDialogRef,
		state,
		register,
		handleQuery,
		calculateAgeAndUnit,
		calculateBirthDate,
		numberToChinese,
		inpatientDiagnosticSearchMethod,
		secondaryDiagnosticSearchMethod,
	};
}
