﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
namespace His.Module.Pharmacy.Service;

/// <summary>
/// 入库管理服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class StorageInRecordService(
    SqlSugarRepository<StorageInRecord> storageInRecordRep,
    ISqlSugarClient sqlSugarClient,
    SysDictTypeService sysDictTypeService,
    SqlSugarRepository<StorageInDetail> storageInDetailRep,
    SqlSugarRepository<DrugStorage> drugStorageRep,
    SqlSugarRepository<StorageAuditRecord> storageAuditRecordRep,
    InventoryService inventoryService,
    UserManager userManager)
    : IDynamicApiController, ITransient
{

    /// <summary>
    /// 分页查询入库管理 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询入库管理")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<StorageInRecordOutput>> Page(PageStorageInRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = storageInRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.DrugType.Contains(input.Keyword) || u.StorageInNo.Contains(input.Keyword) ||
                     u.StorageInType.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageInNo),
                u => u.StorageInNo.Contains(input.StorageInNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageInType),
                u => u.StorageInType.Contains(input.StorageInType.Trim()))
            .WhereIF(input.StorageId != null, u => u.StorageId == input.StorageId)
            .WhereIF(input.SupplierId != null, u => u.SupplierId == input.SupplierId)
            .WhereIF(input.StorageInTimeRange?.Length == 2,
                u => u.StorageInTime >= input.StorageInTimeRange[0] && u.StorageInTime <= input.StorageInTimeRange[1])
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<DrugStorage>((u, storage) => u.StorageId == storage.Id)
            .LeftJoin<EnterpriseDictionary>((u, storage, supplier) => u.SupplierId == supplier.Id)
            .Select((u, storage, supplier) => new StorageInRecordOutput
            {
                Id = u.Id,
                StorageId = u.StorageId,
                StorageFkDisplayName = $"{storage.StorageName}",
                StorageCode = u.StorageCode,
                DrugType = u.DrugType,
                StorageInNo = u.StorageInNo,
                SupplierId = u.SupplierId,
                SupplierFkDisplayName = $"{supplier.EnterpriseName}",
                SupplierCode = u.SupplierCode,
                SupplierName = u.SupplierName,
                StorageInType = u.StorageInType,
                StorageInTime = u.StorageInTime,
                TotalPurchasePrice = u.TotalPurchasePrice,
                TotalSalePrice = u.TotalSalePrice,
                InvoiceNo = u.InvoiceNo,
                Remark = u.Remark,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取入库管理详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取入库管理详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<StorageInRecord> Detail([FromQuery] QueryByIdStorageInRecordInput input)
    {
        var result = await storageInRecordRep.GetFirstAsync(u => u.Id == input.Id);

        return result;
    }

    /// <summary>
    /// 增加入库管理 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加入库管理")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<long> Add(AddStorageInRecordInput input)
    {
        var entity = input.Adapt<StorageInRecord>();
        entity.StorageInTime = DateTime.Now;
        entity.Status = 0;
        entity.StorageInNo = await storageInRecordRep.Context.Ado.GetStringAsync(
            "SELECT LPAD(CAST(NEXTVAL('storage_in_record_no_seq')As varchar),7,'0')");
        var storage = await drugStorageRep.GetFirstAsync(u => u.Id == entity.StorageId);
        entity.StorageCode = storage.StorageCode;
        entity.StorageName =  storage.StorageName;
        var recordId =
            await storageInRecordRep.InsertAsync(entity) ? entity.Id : 0;


        var details = input.Details.Adapt<List<StorageInDetail>>();
        await SaveDetail(details, entity);
        await storageInRecordRep.UpdateAsync(
            u
                => new StorageInRecord()
                {
                    TotalSalePrice = entity.TotalSalePrice,
                    TotalPurchasePrice = entity.TotalPurchasePrice,
                }, u => u.Id == recordId
        );
        return recordId;
    }
    
    /// <summary>
    /// 更新入库管理 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新入库管理")]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    
    public async Task Update(UpdateStorageInRecordInput input)
    {
        var result = await storageInRecordRep.GetFirstAsync(u => u.Id == input.Id);
        if (result.Status == 1)
        {
            throw Oops.Oh("当前状态禁止修改");
        }

        var entity = input.Adapt<StorageInRecord>();
        var details = input.Details.Adapt<List<StorageInDetail>>();
        await SaveDetail(details, entity);
        await storageInRecordRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除入库管理 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除入库管理")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeleteStorageInRecordInput input)
    {
        var entity = await storageInRecordRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);

        if (entity.Status != 0)
        {
            throw Oops.Oh("当前状态禁止删除");
        }

        await storageInRecordRep.FakeDeleteAsync(entity); //假删除
        var details = await storageInDetailRep.GetListAsync(u => u.StorageInId == input.Id);
        await storageInDetailRep.FakeDeleteAsync(details);
        //await _storageInRecordRep.DeleteAsync(entity);   //真删除
    }


    /// <summary>
    /// 提交入库单 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("提交入库单")]
    [ApiDescriptionSettings(Name = "Submit")][HttpPost]
    [UnitOfWork]
    public async Task<bool> Submit(SubmitStorageInRecordInput input)
    {
        var record = await storageInRecordRep.GetByIdAsync(input.Id)
                     ?? throw Oops.Oh("入库单不存在");
        if (record.Status != 0 && record.Status != 9)
            throw Oops.Oh("只有保存或审核拒绝状态的入库单可以提交");
        // 获取仓库信息并确定是否需要审核
        var storage = await drugStorageRep.GetFirstAsync(u => u.Id == record.StorageId)
                      ?? throw Oops.Oh("关联的药库不存在");
        // 如果需要审核，更改为待审核状态
        if (storage.PurchaseAudit == 1)
            return await storageInRecordRep.UpdateAsync(
                u => new StorageInRecord
                {
                    Status = 2
                },
                u => u.Id == input.Id
            );
        // 如果不需要审核，直接更新库存
        var details = await storageInDetailRep.GetListAsync(u => u.StorageInId == input.Id);
        foreach (var detail in details)
            await inventoryService.UpdateInventoryAsync(detail, record);
        // 更新入库单状态
        return await storageInRecordRep.UpdateAsync(
            u => new StorageInRecord
            {
                Status = 1, StorageInTime = DateTime.Now
            },
            u => u.Id == input.Id
        );

    }


    /// <summary>
    /// 审核通过
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Approve")]
    [HttpPost]
    [DisplayName("审核通过")]
    [UnitOfWork]
    public async Task<bool> Approve(AuditStorageInRecordInput input)
    {
        return await ProcessAudit(input.Id, input.AuditOpinion, 3);
    }


    /// <summary>
    /// 审核拒绝
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Reject")]
    [HttpPost]
    [DisplayName("审核拒绝")]
    [UnitOfWork]
    public async Task<bool> Reject(AuditStorageInRecordInput input)
    {
        return await ProcessAudit(input.Id, input.AuditOpinion, 9);
    }

    // /// <summary>
    // /// 批量删除入库管理 ❌
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("批量删除入库管理")]
    // [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    // public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteStorageInRecordInput> input)
    // {
    //     var exp = Expressionable.Create<StorageInRecord>();
    //     foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
    //     var list = await _storageInRecordRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
    //
    //     return await _storageInRecordRep.FakeDeleteAsync(list); //假删除
    //     //return await _storageInRecordRep.DeleteAsync(list);   //真删除
    // }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataStorageInRecordInput input)
    {
        var storageIdData = await storageInRecordRep.Context.Queryable<DrugStorage>()
           
            .Where(u => u.ParentId.Equals(0L))
            .Select(u => new
            {
                Value = u.Id, Label = $"{u.StorageName}", u.StorageDrugType
            }).ToListAsync();
        var supplierIdData = await storageInRecordRep.Context.Queryable<EnterpriseDictionary>()
            //.InnerJoinIF<StorageInRecord>(input.FromPage, (u, r) => u.Id == r.SupplierId)
            .Where(u => u.EnterpriseType == "supplier")
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "storageId", storageIdData },
            { "supplierId", supplierIdData },
        };
    }

    /// <summary>
    /// 导出入库管理记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出入库管理记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageStorageInRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportStorageInRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var drugTypeDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput
            {
                Code = "DrugType"
            }).Result
            .ToDictionary(x => x.Value, x => x.Label);
        var storageInTypeDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput
            {
                Code = "StorageInType"
            })
            .Result.ToDictionary(x => x.Value, x => x.Label);
        var statusDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput
            {
                Code = "StorageInOutStatus"
            })
            .Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e =>
        {
            e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType);
            e.StorageInTypeDictLabel = storageInTypeDictMap.GetValueOrDefault(e.StorageInType ?? "", e.StorageInType);
            e.StatusDictLabel = statusDictMap.GetValueOrDefault(e.Status == null ? "" : e.Status.ToString(),
                e.Status.ToString());
        });
        return ExcelHelper.ExportTemplate(list, "入库管理导出记录");
    }

    /// <summary>
    /// 下载入库管理数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载入库管理数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportStorageInRecordOutput>(), "入库管理导入模板", (_, info) =>
        {
            if (nameof(ExportStorageInRecordOutput.StorageFkDisplayName) == info.Name)
                return storageInRecordRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}").Distinct()
                    .ToList();
            if (nameof(ExportStorageInRecordOutput.SupplierFkDisplayName) == info.Name)
                return storageInRecordRep.Context.Queryable<EnterpriseDictionary>().Select(u => $"{u.EnterpriseName}")
                    .Distinct().ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入入库管理记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入入库管理记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var drugTypeDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput
                {
                    Code = "DrugType"
                }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var storageInTypeDictMap = sysDictTypeService
                .GetDataList(new GetDataDictTypeInput { Code = "StorageInType" }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var statusDictMap = sysDictTypeService
                .GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportStorageInRecordInput, StorageInRecord>(file,
                (list, markerErrorAction) =>
                {
                    sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                    {
                        // 链接 库房
                        var storageIdLabelList = pageItems.Where(x => x.StorageFkDisplayName != null)
                            .Select(x => x.StorageFkDisplayName).Distinct().ToList();
                        if (storageIdLabelList.Any())
                        {
                            var storageIdLinkMap = storageInRecordRep.Context.Queryable<DrugStorage>()
                                .Where(u => storageIdLabelList.Contains($"{u.StorageName}")).ToList()
                                .ToDictionary(u => $"{u.StorageName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.StorageId = storageIdLinkMap.GetValueOrDefault(e.StorageFkDisplayName ?? "");
                                if (e.StorageId == null) e.Error = "库房链接失败";
                            });
                        }

                        // 链接 供应商
                        var supplierIdLabelList = pageItems.Where(x => x.SupplierFkDisplayName != null)
                            .Select(x => x.SupplierFkDisplayName).Distinct().ToList();
                        if (supplierIdLabelList.Any())
                        {
                            var supplierIdLinkMap = storageInRecordRep.Context.Queryable<EnterpriseDictionary>()
                                .Where(u => supplierIdLabelList.Contains($"{u.EnterpriseName}")).ToList()
                                .ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.SupplierId = supplierIdLinkMap.GetValueOrDefault(e.SupplierFkDisplayName ?? "");
                                if (e.SupplierId == null) e.Error = "供应商链接失败";
                            });
                        }

                        // 映射字典值
                        foreach (var item in pageItems)
                        {
                            if (string.IsNullOrWhiteSpace(item.DrugTypeDictLabel)) continue;
                            item.DrugType = drugTypeDictMap.GetValueOrDefault(item.DrugTypeDictLabel);
                            if (item.DrugType == null) item.Error = "药品类型字典映射失败";
                            if (string.IsNullOrWhiteSpace(item.StorageInTypeDictLabel)) continue;
                            item.StorageInType = storageInTypeDictMap.GetValueOrDefault(item.StorageInTypeDictLabel);
                            if (item.StorageInType == null) item.Error = "入库类型字典映射失败";
                            // if (string.IsNullOrWhiteSpace(item.StatusDictLabel)) continue;
                            // item.Status = statusDictMap.GetValueOrDefault(item.StatusDictLabel);
                            if (item.Status == null) item.Error = "状态字典映射失败";
                        }

                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x => { return true; }).Adapt<List<StorageInRecord>>();

                        var storageable = storageInRecordRep.Context.Storageable(rows)
                            .SplitError(it => it.Item.StorageCode?.Length > 100, "库房编码长度不能超过100个字符")
                            .SplitError(it => it.Item.DrugType?.Length > 100, "药品类型长度不能超过100个字符")
                            .SplitError(it => it.Item.StorageInNo?.Length > 100, "入库单号长度不能超过100个字符")
                            .SplitError(it => it.Item.SupplierCode?.Length > 100, "供应商编码长度不能超过100个字符")
                            .SplitError(it => it.Item.SupplierName?.Length > 100, "供应商名称长度不能超过100个字符")
                            .SplitError(it => it.Item.StorageInType?.Length > 100, "入库类型长度不能超过100个字符")
                            .SplitError(it => it.Item.InvoiceNo?.Length > 100, "发票号长度不能超过100个字符")
                            .SplitError(it => it.Item.Remark?.Length > 100, "备注长度不能超过100个字符")
                            .SplitInsert(_ => true)
                            .ToStorage();

                        storageable.BulkCopy();
                        storageable.BulkUpdate();

                        // 标记错误信息
                        markerErrorAction.Invoke(storageable, pageItems, rows);
                    });
                });

            return stream;
        }
    }

    /// <summary>
    /// 保存入库明细信息，并更新相关数据。
    /// </summary>
    /// <param name="details">入库明细列表，包含每个药品的详细信息。</param>
    /// <param name="entity">入库记录实体，包含入库记录的基本信息。</param>
    /// <exception cref="AppFriendlyException">当有效期或生产日期无效时抛出异常。</exception>
    /// <return>无返回值。</return>
    private async Task SaveDetail(List<StorageInDetail> details, StorageInRecord entity)
    {
        await storageInDetailRep.DeleteAsync(u => u.StorageInId == entity.Id);
        foreach (var item in details)
        {
            item.StorageInNo = entity.StorageInNo;
            item.StorageInId = entity.Id;
            item.Id = 0;
            if (item.ExpirationDate == null || item.ExpirationDate <= DateTime.Now ||
                item.ExpirationDate < item.ProductionDate)
                throw Oops.Oh("有效期无效");

            if (item.ProductionDate == null || item.ProductionDate > DateTime.Now ||
                item.ExpirationDate < item.ProductionDate)
                throw Oops.Oh("生产日期无效");
        }

        var totalSalePrice = details.Sum(p => p.TotalSalePrice);
        var totalPurchasePrice = details.Sum(p => p.TotalPurchasePrice);
        entity.TotalSalePrice = totalSalePrice;
        entity.TotalPurchasePrice = totalPurchasePrice;
        await storageInDetailRep.InsertRangeAsync(details);
    }


    /// <summary>
    /// 处理审核操作
    /// </summary>
    /// <param name="id">入库单ID</param>
    /// <param name="auditOpinion">审核意见</param>
    /// <param name="auditResult">审核结果</param>
    /// <returns></returns>
    private async Task<bool> ProcessAudit(long id, string auditOpinion, int auditResult)
    {
        var inRecord = await storageInRecordRep.GetByIdAsync(id);
        if (inRecord == null)
            throw Oops.Oh("入库单记录不存在");

        if (inRecord.Status != 2)
            throw Oops.Oh("只能审核待审核状态的记录");
        switch (auditResult)
        {
            // 如果审核通过，执行库存变更操作
            case 3:
                var details = await storageInDetailRep.GetListAsync(u => u.StorageInId == id);
                foreach (var detail in details)
                    await inventoryService.UpdateInventoryAsync(detail, inRecord);
                await storageInRecordRep.UpdateAsync(
                    u => new StorageInRecord
                    {
                        Status = auditResult, StorageInTime = DateTime.Now
                    },
                    u => u.Id == id
                );
                break;
            // 如果审核拒绝，更新入库单状态为审核拒绝
            case 9:
                await storageInRecordRep.UpdateAsync(
                    u => new StorageInRecord
                    {
                        Status = auditResult
                    },
                    u => u.Id == id
                );
                break;
        }
        var auditRecord = new StorageAuditRecord
        {
            BusinessType = "StorageIn",
            BusinessId = inRecord.Id,
            BusinessNo = inRecord.StorageInNo,
            AuditStatus = auditResult,
            AuditOpinion = auditOpinion,
            AuditTime = DateTime.Now,
            AuditorId = userManager.UserId,
            AuditorName = userManager.RealName
        };
        return await storageAuditRecordRep.InsertAsync(auditRecord);
    }
}