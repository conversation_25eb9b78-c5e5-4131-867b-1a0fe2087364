using Admin.NET.Core;
using Furion.DependencyInjection;
using His.Module.Insurance.Service.Settlement.Dto;
using His.Module.Insurance.Service.Settlement.Dto.Patient;
using His.Module.Insurance.Service.Settlement.Interface;
using His.Module.InsuranceSettlementForLiaocheng.Infrastructure;
using Microsoft.Extensions.Options;
using System.Text.Json;
 
namespace His.Module.InsuranceSettlementForLiaocheng.Implement.Settlement;
using His.Module.Insurance.Service.Settlement;
public class CommonApiImpl(
    UserManager userManager,
    IOptions<InsuranceOptions> insuranceOptions,
    InsuranceSettlementBasicApi insuranceSettlementApi
    ): ICommonApi
{
    // public ICommonApiImpl()
    // {
    //     
    // }


    /// <summary>
    /// 登录获取用户key
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<string> GetUserKey()
    {
        var settlement = insuranceOptions.Value.Settlement;
        var actionName="loginByYybm"; 
        var doc=await    insuranceSettlementApi.SendSoapRequestAsync(actionName,
            userManager.Account,
            settlement.DefaultPwd,
            settlement.HosCode);

        var basicResponse = insuranceSettlementApi.ParseResponseDoc(doc, actionName, "loginResult1");
        
        if (basicResponse is { IsSuccess: true })
            return basicResponse.resulttext;
        else
            throw new Exception($"{actionName}请求失败：code={basicResponse.resultcode};text={basicResponse?.resulttext}");
    }
}