﻿namespace His.Module.Inpatient.Service.Deposit.Dto;

public class DepositInput
{

}

public class AddDepositInput
{
    /// <summary>
    /// 入院登记ID
    /// </summary>
    public long InpatientRegisterId { get; set; }

    /// <summary>
    /// 住院号
    /// </summary>

    public string InpatientNo { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    public long PatientId { get; set; }
}

public class QueryByIdDepositInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

public class PaymentDepositInput
{
    /// <summary>
    /// 押金账户ID
    /// </summary>
    [Required(ErrorMessage = "押金账户ID不能为空")]
    public long AccountId { get; set; }

    /// <summary>
    /// 缴纳金额
    /// </summary>
    [Required(ErrorMessage = "缴纳金额不能为空")]
    [Range(0.01, double.MaxValue, ErrorMessage = "缴纳金额必须大于0")]
    public decimal Amount { get; set; }

    /// <summary>
    /// 支付渠道
    /// </summary>
    public string Channel { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    [Required(ErrorMessage = "支付方式不能为空")]
    public string PayType { get; set; }

    /// <summary>
    /// 外部凭证号
    /// </summary>
    public string ReceiptNo { get; set; }

    /// <summary>
    /// 交易备注
    /// </summary>
    public string? Remark { get; set; }
}

public class RefundDepositInput
{
    /// <summary>
    /// 押金账户ID
    /// </summary>
    [Required(ErrorMessage = "押金账户ID不能为空")]
    public long AccountId { get; set; }

    /// <summary>
    /// 退款金额
    /// </summary>
    [Required(ErrorMessage = "退款金额不能为空")]
    [Range(0.01, double.MaxValue, ErrorMessage = "退款金额必须大于0")]
    public decimal TotalRefundAmount { get; set; }

    /// <summary>
    /// 退款原因或备注
    /// </summary>
    public string? Reason { get; set; }
}

public class StartSettlementDepositInput
{
    /// <summary>
    /// 押金账户ID
    /// </summary>
    [Required(ErrorMessage = "押金账户ID不能为空")]
    public long AccountId { get; set; }

}

public class CorrectPaymentErrorInput
{
    /// <summary>
    /// 需要被修正的原始缴费流水的ID。
    /// </summary>
    [Required(ErrorMessage = "原始交易ID不能为空")]
    public long OriginalTransactionId { get; set; }

    /// <summary>
    /// 正确的金额
    /// </summary>
    [Required(ErrorMessage = "正确金额不能为空")]
    [Range(0.00, double.MaxValue, ErrorMessage = "正确金额不能为负数")]
    public decimal CorrectAmount { get; set; }

    /// <summary>
    /// 修正原因
    /// </summary>
    [Required(ErrorMessage = "修正原因不能为空")]
    public string Reason { get; set; }
}