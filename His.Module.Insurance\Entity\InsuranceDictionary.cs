﻿using Admin.NET.Core;

namespace His.Module.Insurance.Entity;

/// <summary>
/// 医保数据字典
/// </summary>
[Tenant("1300000000013")]
[SugarTable("insurance_dictionary", "医保数据字典")]
public class InsuranceDictionary : EntityTenant
{
    /// <summary>
    /// 代码编号
    /// </summary>
    [SugarColumn(ColumnName = "dm_bh", ColumnDescription = "代码编号", Length = 50)]
    public virtual string DmBh { get; set; }
    
    /// <summary>
    /// 代码值
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "代码值", Length = 50)]
    public virtual string Code { get; set; }
    
    /// <summary>
    /// 字典名称
    /// </summary>
    [SugarColumn(ColumnName = "content", ColumnDescription = "字典名称", Length = 200)]
    public virtual string Content { get; set; }
    
    /// <summary>
    /// 最后同步时间
    /// </summary>
    [SugarColumn(ColumnName = "last_sync_time", ColumnDescription = "最后同步时间")]
    public virtual DateTime? LastSyncTime { get; set; }
    
    /// <summary>
    /// 排序号
    /// </summary>
    [SugarColumn(ColumnName = "sort_no", ColumnDescription = "排序号")]
    public virtual int? SortNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 500)]
    public virtual string? Remark { get; set; }
}
