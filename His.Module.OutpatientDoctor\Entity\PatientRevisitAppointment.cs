﻿using Admin.NET.Core;
namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 门诊复诊预约表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("patient_revisit_appointment", "门诊复诊预约表")]
public class PatientRevisitAppointment : EntityTenant
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 64)]
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号记录Id
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "挂号记录Id")]
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊卡号", Length = 64)]
    public virtual string? VisitNo { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [SugarColumn(ColumnName = "id_card_no", ColumnDescription = "身份证号", Length = 64)]
    public virtual string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 64)]
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 预约医生Id
    /// </summary>
    [SugarColumn(ColumnName = "appointment_doctor_id", ColumnDescription = "预约医生Id")]
    public virtual long? AppointmentDoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "appointment_doctor_name", ColumnDescription = "预约医生姓名", Length = 64)]
    public virtual string? AppointmentDoctorName { get; set; }
    
    /// <summary>
    /// 预约科室Id
    /// </summary>
    [SugarColumn(ColumnName = "appointment_dept_id", ColumnDescription = "预约科室Id")]
    public virtual long? AppointmentDeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    [SugarColumn(ColumnName = "appointment_dept_name", ColumnDescription = "预约科室名称", Length = 64)]
    public virtual string? AppointmentDeptName { get; set; }
    
    /// <summary>
    /// 复诊时间
    /// </summary>
    [SugarColumn(ColumnName = "revisit_time", ColumnDescription = "复诊时间")]
    public virtual DateTime? RevisitTime { get; set; }
    
    /// <summary>
    /// 复诊原因
    /// </summary>
    [SugarColumn(ColumnName = "revisit_reason", ColumnDescription = "复诊原因", Length = 256)]
    public virtual string? RevisitReason { get; set; }
    
    /// <summary>
    /// 复诊科室Id
    /// </summary>
    [SugarColumn(ColumnName = "revisit_dept_id", ColumnDescription = "复诊科室Id")]
    public virtual long? RevisitDeptId { get; set; }
    
    /// <summary>
    /// 复诊科室名称
    /// </summary>
    [SugarColumn(ColumnName = "revisit_dept_name", ColumnDescription = "复诊科室名称", Length = 64)]
    public virtual string? RevisitDeptName { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态，默认1（有效）")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注信息", Length = 256)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
