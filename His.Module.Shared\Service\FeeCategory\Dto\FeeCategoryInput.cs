﻿namespace His.Module.Shared.Service;

/// <summary>
/// 费用类别基础输入参数
/// </summary>
public class FeeCategoryBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 医疗类别
    /// </summary>
    [Dict(nameof(MedCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "医疗类别不能为空")]
    public virtual MedCategoryEnum? MedCategory { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public virtual MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 医保id
    /// </summary>
    public virtual Int16? MedInsId { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [Dict(nameof(MedInsTypeEnum), AllowNullValue = true)]
    public virtual MedInsTypeEnum? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public virtual StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
}

/// <summary>
/// 费用类别分页查询输入参数
/// </summary>
public class PageFeeCategoryInput : BasePageInput
{
    /// <summary>
    /// 编号
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 费用类别增加输入参数
/// </summary>
public class AddFeeCategoryInput
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 医疗类别
    /// </summary>
    [Dict(nameof(MedCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "医疗类别不能为空")]
    public MedCategoryEnum? MedCategory { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 医保id
    /// </summary>
    public Int16? MedInsId { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [Dict(nameof(MedInsTypeEnum), AllowNullValue = true)]
    public MedInsTypeEnum? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 费用类别删除输入参数
/// </summary>
public class DeleteFeeCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 费用类别更新输入参数
/// </summary>
public class UpdateFeeCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 医疗类别
    /// </summary>
    [Dict(nameof(MedCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "医疗类别不能为空")]
    public MedCategoryEnum? MedCategory { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 医保id
    /// </summary>
    public Int16? MedInsId { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [Dict(nameof(MedInsTypeEnum), AllowNullValue = true)]
    public MedInsTypeEnum? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 费用类别主键查询输入参数
/// </summary>
public class QueryByIdFeeCategoryInput : DeleteFeeCategoryInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetFeeCategoryStatusInput : BaseStatusInput
{
}

/// <summary>
/// 费用类别数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportFeeCategoryInput : BaseImportInput
{
    /// <summary>
    /// 编号
    /// </summary>
    [ImporterHeader(Name = "编号")]
    [ExporterHeader("编号", Format = "", Width = 25, IsBold = true)]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [ImporterHeader(Name = "*名称")]
    [ExporterHeader("*名称", Format = "", Width = 25, IsBold = true)]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [ImporterHeader(Name = "拼音码")]
    [ExporterHeader("拼音码", Format = "", Width = 25, IsBold = true)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [ImporterHeader(Name = "五笔码")]
    [ExporterHeader("五笔码", Format = "", Width = 25, IsBold = true)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 医疗类别
    /// </summary>
    [ImporterHeader(Name = "*医疗类别")]
    [ExporterHeader("*医疗类别", Format = "", Width = 25, IsBold = true)]
    public MedCategoryEnum? MedCategory { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [ImporterHeader(Name = "*使用范围")]
    [ExporterHeader("*使用范围", Format = "", Width = 25, IsBold = true)]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 医保id
    /// </summary>
    [ImporterHeader(Name = "医保id")]
    [ExporterHeader("医保id", Format = "", Width = 25, IsBold = true)]
    public Int16? MedInsId { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [ImporterHeader(Name = "医保类型")]
    [ExporterHeader("医保类型", Format = "", Width = 25, IsBold = true)]
    public MedInsTypeEnum? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
}