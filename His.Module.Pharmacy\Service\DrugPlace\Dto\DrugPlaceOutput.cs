﻿namespace His.Module.Pharmacy;

/// <summary>
/// 药品产地维护输出参数
/// </summary>
public class DrugPlaceOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 产地名称
    /// </summary>
    public string? PlaceName { get; set; }    
    
    /// <summary>
    /// 产地名称拼音
    /// </summary>
    public string? PlaceNamePinyin { get; set; }    
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 药品产地维护数据导入模板实体
/// </summary>
public class ExportDrugPlaceOutput : ImportDrugPlaceInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
