﻿namespace His.Module.Shared.Service;

/// <summary>
/// 收费项目单位基础输入参数
/// </summary>
public class ChargeItemUnitBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [Required(ErrorMessage = "单位名称不能为空")]
    public virtual string? UnitName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public virtual StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }
}

/// <summary>
/// 收费项目单位分页查询输入参数
/// </summary>
public class PageChargeItemUnitInput : BasePageInput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    public string? UnitCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 收费项目单位增加输入参数
/// </summary>
public class AddChargeItemUnitInput
{
    /// <summary>
    /// 单位名称
    /// </summary>
    [Required(ErrorMessage = "单位名称不能为空")]
    [MaxLength(64, ErrorMessage = "单位名称字符长度不能超过64")]
    public string? UnitName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }
}

/// <summary>
/// 收费项目单位删除输入参数
/// </summary>
public class DeleteChargeItemUnitInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 收费项目单位更新输入参数
/// </summary>
public class UpdateChargeItemUnitInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [Required(ErrorMessage = "单位名称不能为空")]
    [MaxLength(64, ErrorMessage = "单位名称字符长度不能超过64")]
    public string? UnitName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }
}

/// <summary>
/// 收费项目单位主键查询输入参数
/// </summary>
public class QueryByIdChargeItemUnitInput : DeleteChargeItemUnitInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetChargeItemUnitStatusInput : BaseStatusInput
{
}

/// <summary>
/// 收费项目单位数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportChargeItemUnitInput : BaseImportInput
{
    /// <summary>
    /// 单位编码
    /// </summary>
    [ImporterHeader(Name = "单位编码")]
    [ExporterHeader("单位编码", Format = "", Width = 25, IsBold = true)]
    public string? UnitCode { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    [ImporterHeader(Name = "*单位名称")]
    [ExporterHeader("*单位名称", Format = "", Width = 25, IsBold = true)]
    public string? UnitName { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [ImporterHeader(Name = "拼音码")]
    [ExporterHeader("拼音码", Format = "", Width = 25, IsBold = true)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [ImporterHeader(Name = "五笔码")]
    [ExporterHeader("五笔码", Format = "", Width = 25, IsBold = true)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }
}