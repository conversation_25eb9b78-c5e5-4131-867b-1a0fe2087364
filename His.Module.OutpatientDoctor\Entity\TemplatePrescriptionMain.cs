﻿using Admin.NET.Core;
namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 处方模板主表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("template_prescription_main", "处方模板主表")]
public class TemplatePrescriptionMain : EntityTenant
{
    /// <summary>
    /// `模板名称
    /// </summary>
    [SugarColumn(ColumnName = "template_name", ColumnDescription = "模板名称", Length = 64)]
    public virtual string? TemplateName { get; set; }
    
    /// <summary>
    /// 门诊处方类型
    /// </summary>
    [SugarColumn(ColumnName = "prescription_type", ColumnDescription = "门诊处方类型", Length = 64)]
    public virtual string? PrescriptionType { get; set; }
    
    /// <summary>
    /// 1 全院/ 2 科室/3 个人模板"
    /// </summary>
    [SugarColumn(ColumnName = "template_scope", ColumnDescription = "1 全院/ 2 科室/3 个人模板")]
    public virtual int? TemplateScope { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 中药付数
    /// </summary>
    [SugarColumn(ColumnName = "herbs_quantity", ColumnDescription = "中药付数")]
    public virtual int? HerbsQuantity { get; set; }
 
    
    /// <summary>
    /// 打印时间
    /// </summary>
    [SugarColumn(ColumnName = "print_time", ColumnDescription = "打印时间")]
    public virtual DateTime? PrintTime { get; set; }
    
    /// <summary>
    /// 药房
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "药房", Length = 100)]
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 药房id
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "药房id")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 创建者部门Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建者部门Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建者部门名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
