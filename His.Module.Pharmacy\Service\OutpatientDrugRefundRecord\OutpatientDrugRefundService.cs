﻿using Admin.NET.Core.Service;
using His.Module.OutpatientDoctor;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.OutpatientDoctor.Api.Prescription;
using His.Module.OutpatientDoctor.Api.Prescription.Dto;
using His.Module.Pharmacy.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy;

/// <summary>
/// 门诊退药服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class OutpatientDrugRefundService(
    SqlSugarRepository<OutpatientDrugSendRecord> outpatientDrugSendRecordRep,
    SqlSugarRepository<OutpatientDrugRefundRecord> outpatientDrugRefundRecordRep,
    IPrescriptionApi prescriptionApi,
    IChargeApi chargeApi,
    InventoryService inventoryService,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    //

    /// <summary>
    /// 增加门诊退药 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加门诊退药")]
    [ApiDescriptionSettings(Name = "Submit"), HttpPost, UnitOfWork]
    public async Task Submit(AddOutpatientDrugRefundRecordInput input)
    {
      

        var refundRecordList = new List<OutpatientDrugRefundRecord>();
        foreach (var item in input.Details)
        {
            var sendRecord = await outpatientDrugSendRecordRep.GetFirstAsync(x
                => x.PrescriptionId == input.PrescriptionId && x.PrescriptionDetailId == item.PrescriptionDetailId
                && x.DrugId==item.DrugId );
            var refundRecord = sendRecord.Adapt<OutpatientDrugRefundRecord>();
            refundRecord.Id = 0;
            refundRecord.SendRecordId = sendRecord.Id;
            refundRecord.RefundTime = DateTime.Now;
            refundRecord.Reason = input.Reason;
            refundRecord.RefundUserId = 1;
            refundRecordList.Add(refundRecord);
            //01 修改退药状态
            await outpatientDrugSendRecordRep.UpdateAsync(u =>
                    new OutpatientDrugSendRecord()
                    {
                        RefundQuantity = sendRecord.Quantity, Status = 1, 
                        UpdateTime = DateTime.Now,
                        UpdateUserId =
                            long.Parse(App.User.FindFirst(ClaimConst.UserId).Value??"0") ,
                       
                        
                    },
                x
                    => x.PrescriptionId == input.PrescriptionId && x.PrescriptionDetailId == item.PrescriptionDetailId
                                                                && x.DrugId==item.DrugId );
           
            // 修改库存数量 
            await inventoryService.UpdateInventoryAsync(refundRecord);
           
            
        }
        // 修改处方退药状态
       
        
        await prescriptionApi.PrescriptionRefundDrug(
            new PrescriptionRefundDrugDto()
            {
                PrescriptionId =input.PrescriptionId,
               // PrescriptionDetailId = first.PrescriptionDetailId,
                RefundUserId =
                    long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0"),
                ExecuteDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId)?.Value ?? "0"),
                Reason = input.Reason,
            });

        // 退费
        await chargeApi.Refund(new OutpatientChargeRefundDto(
            input.ChargeId, input.Reason));
        // 批量插入退药记录
        await outpatientDrugRefundRecordRep.InsertRangeAsync(refundRecordList);
    }
}