﻿using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 中医诊断表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("tcm_diagnosis", "中医诊断表")]
public class TcmDiagnosis : EntityTenant
{
    /// <summary>
    /// 中医诊断编码
    /// </summary>
    [SugarColumn(ColumnName = "tcm_diagnosis_code", ColumnDescription = "中医诊断编码", Length = 64)]
    public virtual string? TcmDiagnosisCode { get; set; }
    
    /// <summary>
    /// 中医诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "tcm_diagnosis_name", ColumnDescription = "中医诊断名称", Length = 64)]
    public virtual string? TcmDiagnosisName { get; set; }
    
    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 64)]
    public virtual string? PinyinCode { get; set; }
    
    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 64)]
    public virtual string? WubiCode { get; set; }
    
    /// <summary>
    /// 版本
    /// </summary>
    [SugarColumn(ColumnName = "version", ColumnDescription = "版本", Length = 32)]
    public virtual string? Version { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }
    
}
