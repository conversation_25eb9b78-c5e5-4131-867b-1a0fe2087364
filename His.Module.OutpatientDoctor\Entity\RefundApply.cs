﻿using Admin.NET.Core;

namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 退费申请表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("refund_apply", "退费申请表")]
public class RefundApply : EntityTenant
{
    /// <summary>
    /// 退费申请ID
    /// </summary>
    [SugarColumn(ColumnName = "charge_id", ColumnDescription = "退费申请ID")]
    public virtual long? ChargeId { get; set; }

    /// <summary>
    /// 退费申请类型
    /// </summary>
    [SugarColumn(ColumnName = "billing_type", ColumnDescription = "退费申请类型", Length = 100)]
    public virtual string? BillingType { get; set; }

    /// <summary>
    /// 退费申请单号
    /// </summary>
    [SugarColumn(ColumnName = "apply_no", ColumnDescription = "退费申请单号", Length = 100)]
    public virtual string? ApplyNo { get; set; }

    /// <summary>
    /// 退费申请时间
    /// </summary>
    [SugarColumn(ColumnName = "apply_time", ColumnDescription = "退费申请时间")]
    public virtual DateTime? ApplyTime { get; set; }


    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "就诊卡号", Length = 100)]
    public virtual string? CardNo { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 100)]
    public virtual string? VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 100)]
    public virtual string? OutpatientNo { get; set; }

    /// <summary>
    /// 挂号ID
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "挂号ID")]
    public virtual long? RegisterId { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }

    /// <summary>
    /// 患者名称
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者名称", Length = 100)]
    public virtual string? PatientName { get; set; }

    /// <summary>
    /// 退费原因
    /// </summary>
    [SugarColumn(ColumnName = "apply_reason", ColumnDescription = "退费原因", Length = 200)]
    public virtual string? ApplyReason { get; set; }

    /// <summary>
    /// 状态  状态 0 新增待审核 1 审核中 2 审核完成 3 完成退费
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 ")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 审核状态  0 待审核 1 审核中(审核通过) 2审核驳回  3  审核完成  4  退费完成
    /// </summary>
    [SugarColumn(ColumnName = "audit_status", ColumnDescription = "审核状态  0 待审核 1 审核中(审核通过) 2审核驳回  3  审核完成  4  退费完成")]
    public virtual int? AuditStatus { get; set; }

    /// <summary>
    /// 创建机构ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构ID")]
    public virtual long? CreateOrgId { get; set; }

    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
}