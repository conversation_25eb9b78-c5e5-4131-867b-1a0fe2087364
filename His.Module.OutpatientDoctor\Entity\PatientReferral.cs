﻿using Admin.NET.Core;
namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 患者转介表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("patient_referral", "患者转介表")]
public class PatientReferral : EntityTenant
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者名称", Length = 64)]
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 64)]
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 转介前医生Id
    /// </summary>
    [SugarColumn(ColumnName = "before_doctor_id", ColumnDescription = "转介前医生Id")]
    public virtual long? BeforeDoctorId { get; set; }
    
    /// <summary>
    /// 转介前医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "before_doctor_name", ColumnDescription = "转介前医生姓名", Length = 64)]
    public virtual string? BeforeDoctorName { get; set; }
    
    /// <summary>
    /// 转介前科室Id
    /// </summary>
    [SugarColumn(ColumnName = "before_dept_id", ColumnDescription = "转介前科室Id")]
    public virtual long? BeforeDeptId { get; set; }
    
    /// <summary>
    /// 转介前科室名称
    /// </summary>
    [SugarColumn(ColumnName = "before_dept_name", ColumnDescription = "转介前科室名称", Length = 64)]
    public virtual string? BeforeDeptName { get; set; }
    
    /// <summary>
    /// 转介后医生Id
    /// </summary>
    [SugarColumn(ColumnName = "after_doctor_id", ColumnDescription = "转介后医生Id")]
    public virtual long? AfterDoctorId { get; set; }
    
    /// <summary>
    /// 转介后医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "after_doctor_name", ColumnDescription = "转介后医生姓名", Length = 64)]
    public virtual string? AfterDoctorName { get; set; }
    
    /// <summary>
    /// 转介后科室Id
    /// </summary>
    [SugarColumn(ColumnName = "after_dept_id", ColumnDescription = "转介后科室Id")]
    public virtual long? AfterDeptId { get; set; }
    
    /// <summary>
    /// 转介后科室名称
    /// </summary>
    [SugarColumn(ColumnName = "after_dept_name", ColumnDescription = "转介后科室名称", Length = 64)]
    public virtual string? AfterDeptName { get; set; }
    
    /// <summary>
    /// 转介时间
    /// </summary>
    [SugarColumn(ColumnName = "referral_time", ColumnDescription = "转介时间")]
    public virtual DateTime? ReferralTime { get; set; }
    /// <summary>
    /// 预约挂号时间
    /// </summary>
    [SugarColumn(ColumnName = "reg_time", ColumnDescription = "预约挂号时间")]
    public virtual DateTime? RegTime { get; set; }
    /// <summary>
    /// 转介原因
    /// </summary>
    [SugarColumn(ColumnName = "referral_reason", ColumnDescription = "转介原因", Length = 256)]
    public virtual string? ReferralReason { get; set; }
    
    /// <summary>
    /// 转介前挂号记录Id
    /// </summary>
    [SugarColumn(ColumnName = "before_register_id", ColumnDescription = "转介前挂号记录Id")]
    public virtual long? BeforeRegisterId { get; set; }
    
    /// <summary>
    /// 转介后挂号记录Id
    /// </summary>
    [SugarColumn(ColumnName = "after_register_id", ColumnDescription = "转介后挂号记录Id")]
    public virtual long? AfterRegisterId { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态，默认1（有效）")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注信息", Length = 256)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
