﻿using System.ComponentModel.DataAnnotations;
namespace His.Module.MedicalTech.Api.Examination.Dto;

public class SetExaminationStatusDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 状态 字典 ApplyStatus
    /// </summary>
    [Required(ErrorMessage = "状态不能为空")]
    public int Status { get; set; }
}