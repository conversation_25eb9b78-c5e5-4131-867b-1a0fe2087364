﻿using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 医疗组成员表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("medical_team_member", "医疗组成员表")]
public class MedicalTeamMember : EntityTenant
{
    /// <summary>
    /// 医疗组ID
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "team_id", ColumnDescription = "医疗组ID")]
    public virtual long TeamId { get; set; }
    
    /// <summary>
    /// 成员ID
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "staff_id", ColumnDescription = "成员ID")]
    public virtual long StaffId { get; set; }
    
    /// <summary>
    /// 成员名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "staff_name", ColumnDescription = "成员名称", Length = 200)]
    public virtual string StaffName { get; set; }
    
    /// <summary>
    /// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "role_type", ColumnDescription = "角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)", Length = 200)]
    public virtual string RoleType { get; set; }
    
    /// <summary>
    /// 加入日期
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "join_date", ColumnDescription = "加入日期")]
    public virtual DateTime JoinDate { get; set; }
    
    /// <summary>
    /// 离开日期
    /// </summary>
    [SugarColumn(ColumnName = "leave_date", ColumnDescription = "离开日期")]
    public virtual DateTime? LeaveDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态(1:启用 2:停用,)", DefaultValue = "1")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
     
    
}
