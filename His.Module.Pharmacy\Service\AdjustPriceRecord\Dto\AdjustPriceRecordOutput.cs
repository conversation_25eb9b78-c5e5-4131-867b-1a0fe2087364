﻿namespace His.Module.Pharmacy;

/// <summary>
/// 药品调价记录输出参数
/// </summary>
public class AdjustPriceRecordOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }    
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }    
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }    
    
    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }    
    
    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }    
    
    /// <summary>
    /// 数量
    /// </summary>
    public int? Quantity { get; set; }    
    
    /// <summary>
    /// 旧零售价
    /// </summary>
    public decimal? OldSalePrice { get; set; }    
    
    /// <summary>
    /// 新零售价
    /// </summary>
    public decimal? NewSalePrice { get; set; }    
    /// <summary>
    /// 旧总零售价
    /// </summary>
    public decimal? TotalOldSalePrice { get; set; }

    /// <summary>
    /// 新总零售价
    /// </summary>
    public decimal? TotalNewSalePrice { get; set; }
    /// <summary>
    /// 调价金额差额
    /// </summary>
    public decimal? AdjustPrice { get; set; }
    
    
    /// <summary>
    /// 批号
    /// </summary>
    public string? BatchNo { get; set; }    
    
    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductionDate { get; set; }    
    
    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? ExpirationDate { get; set; }    
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public string? ApprovalNumber { get; set; }    
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }    
    
    /// <summary>
    /// 生产厂商ID
    /// </summary>
    public long? ManufacturerId { get; set; }    
    
    /// <summary>
    /// 生产厂商名称
    /// </summary>
    public string? ManufacturerName { get; set; }    
    
    /// <summary>
    /// 调价时间
    /// </summary>
    public DateTime? AdjustTime { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 药品调价记录数据导入模板实体
/// </summary>
public class ExportAdjustPriceRecordOutput : ImportAdjustPriceRecordInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
