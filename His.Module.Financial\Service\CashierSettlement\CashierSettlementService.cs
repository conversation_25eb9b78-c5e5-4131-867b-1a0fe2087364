using Furion.DatabaseAccessor;
using His.Module.Financial.Const;
using His.Module.Financial.Entity;
using His.Module.Financial.Enum;
using His.Module.Financial.OtherModuleEntity;
using System.Diagnostics;
using System.Text.Json;
namespace His.Module.Financial.Service;

/// <summary>
/// 收款员结算服务 🧩
/// </summary>
[ApiDescriptionSettings(FinancialConst.GroupName, Order = 100)]
public class CashierSettlementService(
    SqlSugarRepository<CashierSettlement> cashierSettlementRep,
    SqlSugarRepository<ChargeMain> chargeMainRep,
    SqlSugarRepository<MedicalCardRecharge> medicalCardRechargeRep,
    SqlSugarRepository<PayMethod> payMethodRep,
    SqlSugarRepository<SysUser> sysUserRep,
    CashierOperationLogService operationLogService)
    : IDynamicApiController, ITransient
{

    //需要统计的收费状态
    public static readonly List<int> ChargeStatusToInclude =
    [
        1, // 已收费
        2, // 取药
        5
    ];

    //收费状态中表示收费的状态
    public static readonly List<int> ChargeStatusForCharge =
    [
        1, // 已收费
        2
    ];

    //收费状态中表示退费的状态
    public static readonly List<int> ChargeStatusForRefund = [5];

    //需要统计的卡充值状态
    public static readonly List<CardRechargeStatusEnum> CardRechargeStatusToInclude =
    [
        CardRechargeStatusEnum.Recharge, // 充值
        CardRechargeStatusEnum.HongChong, // 红冲
        CardRechargeStatusEnum.CardRefundBalance
    ];

    //卡充值状态中表示充值的状态
    public static readonly List<CardRechargeStatusEnum> CardRechargeStatusForRecharge = [CardRechargeStatusEnum.Recharge];

    //卡充值状态中表示退费的状态
    public static readonly List<CardRechargeStatusEnum> CardRechargeStatusForCardRefund =
    [
        CardRechargeStatusEnum.CardRefundBalance,
        CardRechargeStatusEnum.HongChong
    ];


    /// <summary>
    /// 分页查询收款员结算记录 📋
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询收款员结算记录")]
    [ApiDescriptionSettings(Name = "Page")]
    [HttpPost]
    public async Task<SqlSugarPagedList<CashierSettlement>> Page(CashierSettlementQueryInput input)
    {
        return await cashierSettlementRep.AsQueryable()
            .WhereIF(input.CashierId.HasValue, u => u.CashierId == input.CashierId)
            .WhereIF(input.StartDate.HasValue, u => u.SettlementDate >= input.StartDate)
            .WhereIF(input.EndDate.HasValue, u => u.SettlementDate <= input.EndDate)
            .WhereIF(input.SettlementType.HasValue, u => u.SettlementType == input.SettlementType)
            .OrderByDescending(u => u.SettlementDate)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取收款员统计数据 📊
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取收款员统计数据")]
    [ApiDescriptionSettings(Name = "GetStatistics")]
    [HttpPost]
    public async Task<CashierStatisticsOutput> GetStatistics(CashierStatisticsInput input)
    {
        var result = new CashierStatisticsOutput
        {
            CashierId = input.CashierId ?? 0, StartTime = input.StartDate, EndTime = input.EndDate
        };

        // 获取收款员姓名
        if (input.CashierId.HasValue)
        {
            var user = await sysUserRep.GetFirstAsync(u => u.Id == input.CashierId);
            result.CashierName = user?.RealName;
        }

        //获取收费和退费记录 （status = 1 已收费 status = 2 取药 status = 5 红冲）
        var chargeMain = await chargeMainRep.AsQueryable()
            .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
            .Where(u => ChargeStatusToInclude.Contains(u.Status))
            .WhereIF(input.CashierId.HasValue, u => u.CreateUserId == input.CashierId)
            .ToListAsync();

        // 统计收费数据（status = 1 已收费 status = 2 取药）
        var chargeData = chargeMain.Where(u => ChargeStatusForCharge.Contains(u.Status)).ToList();
        result.TotalChargeAmount = chargeData.Sum(x => x.TotalAmount);
        result.ChargeCount = chargeData.Count;

        // 统计退费数据（status = 5 红冲）
        var refundData = chargeMain.Where(u => ChargeStatusForRefund.Contains(u.Status)).ToList();
        result.TotalRefundAmount = refundData.Sum(x => x.TotalAmount);
        result.RefundCount = refundData.Count;
        result.NetChargeAmount = result.TotalChargeAmount - result.TotalRefundAmount;

        //获取充值和退费记录（status = 0 充值 status = 1 红冲 status = 4 退卡余额）
        var cardRecharge = await medicalCardRechargeRep.AsQueryable()
            .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
            .Where(u => CardRechargeStatusToInclude.Contains(u.Status))
            .WhereIF(input.CashierId.HasValue, u => u.CreateUserId == input.CashierId)
            .ToListAsync();

        // 统计充值数据（status = 0 充值）
        var rechargeData = cardRecharge.Where(u => CardRechargeStatusForRecharge.Contains(u.Status)).ToList();
        result.TotalRechargeAmount = rechargeData.Sum(x => x.PayAmount ?? 0);
        result.RechargeCount = rechargeData.Count;

        // 统计退卡费用数据（status = 1 红冲 status = 4 退卡余额）
        var cardRefundData = cardRecharge
            .Where(u => CardRechargeStatusForCardRefund.Contains(u.Status))
            .ToList();
        result.TotalCardRefundAmount = cardRefundData.Sum(x => x.PayAmount ?? 0);
        result.CardRefundCount = cardRefundData.Count;
        result.NetRechargeAmount = result.TotalRechargeAmount - result.TotalCardRefundAmount;
        // 计算退费率
        result.RefundRate = result.TotalChargeAmount > 0
            ? result.TotalRefundAmount / result.TotalChargeAmount
            : 0;

        // 计算工作效率指标（仅月结使用）
        if (input.SettlementType.HasValue && input.SettlementType == 2)
        {

        }

        // 统计支付方式分布
        result.PayMethodStatistics = await GetPayMethodStatistics(chargeData, refundData, rechargeData, cardRefundData);

        // 获取各类明细
        result.ChargeDetails = await GetChargeDetails(input);
        result.RefundDetails = await GetRefundDetails(input);
        result.RechargeDetails = await GetRechargeDetails(input);
        result.CardRefundDetails = await GetCardRefundDetails(input);

        // 数据校验
        //result.ValidationResult = await ValidateData(input);

        // 检查异常数据
        //result.ExceptionData = await GetExceptionData(input);

        // 检查是否已经结算（根据结算类型）
        var existingSettlement = false;
        if (input.SettlementType.HasValue)
        {
            if (input.SettlementType == 1) // 日结
                existingSettlement = await cashierSettlementRep.IsAnyAsync(u =>
                    u.CashierId == input.CashierId &&
                    u.SettlementDate.Date == input.StartDate.Date &&
                    u.SettlementType == 1 &&
                    u.Status == 1);
            else if (input.SettlementType == 2) // 月结
            {
                var workDays = await CalculateWorkDays(input.CashierId ?? 0, input.StartDate, input.EndDate);
                result.WorkDays = workDays;
                result.DailyAvgAmount = workDays > 0 ? result.NetChargeAmount / workDays : 0;
                result.DailyAvgCount = workDays > 0 ? (decimal)result.ChargeCount / workDays : 0;
                existingSettlement = await cashierSettlementRep.IsAnyAsync(u =>
                    u.CashierId == input.CashierId &&
                    u.SettlementDate.Year == input.StartDate.Year &&
                    u.SettlementDate.Month == input.StartDate.Month &&
                    u.SettlementType == 2 &&
                    u.Status == 1);
            }
        }
        result.IsSettled = existingSettlement;
        return result;
    }

    /// <summary>
    /// 执行日结操作 📅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("执行日结操作")]
    [ApiDescriptionSettings(Name = "DailySettlement")]
    [HttpPost]
    [UnitOfWork]
    public async Task<long> DailySettlement(CashierDailySettlementInput input)
    {
        var stopwatch = Stopwatch.StartNew();
        try
        {
            var startTime = input.SettlementDate.Date;
            var endTime = startTime.AddDays(1).AddSeconds(-1);
            // 检查是否已经日结
            var existingSettlement = await cashierSettlementRep.GetFirstAsync(u =>
                u.CashierId == input.CashierId &&
                u.SettlementDate.Date == input.SettlementDate.Date &&
                u.SettlementType == 1 &&
                u.Status == 1);
            if (existingSettlement != null) throw Oops.Oh("该收款员当日已经执行过日结操作");
            // 获取统计数据
            var statistics = await GetStatistics(new CashierStatisticsInput
            {
                CashierId = input.CashierId, StartDate = startTime, EndDate = endTime
            });
            // 数据校验
            if (statistics.ValidationResult != null && !statistics.ValidationResult.IsValid) throw Oops.Oh($"数据校验失败：{string.Join(", ", statistics.ValidationResult.ValidationErrors)}");
            // 检查异常数据
            if (statistics.ExceptionData != null && statistics.ExceptionData.Count > 0)
            {
                var hasBlockingExceptions = statistics.ExceptionData.Any(e => e.ExceptionType == 2 || e.ExceptionType == 3);
                if (hasBlockingExceptions && !input.ForceSettle) throw Oops.Oh($"发现异常数据，无法执行日结。异常数量：{statistics.ExceptionData.Count}");
            }
            // 创建日结记录
            var settlement = new CashierSettlement
            {
                CashierId = input.CashierId,
                CashierName = statistics.CashierName,
                SettlementDate = input.SettlementDate.Date,
                SettlementType = 1, // 日结
                TotalChargeAmount = statistics.TotalChargeAmount,
                TotalRefundAmount = statistics.TotalRefundAmount,
                NetChargeAmount = statistics.NetChargeAmount,
                TotalRechargeAmount = statistics.TotalRechargeAmount,
                TotalCardRefundAmount = statistics.TotalCardRefundAmount,
                NetRechargeAmount = statistics.NetRechargeAmount,
                ChargeCount = statistics.ChargeCount,
                RefundCount = statistics.RefundCount,
                RechargeCount = statistics.RechargeCount,
                CardRefundCount = statistics.CardRefundCount,
                RefundRate = statistics.RefundRate,
                PayMethodStatistics = JsonSerializer.Serialize(statistics.PayMethodStatistics),
                StartTime = startTime,
                EndTime = endTime,
                Status = 1,
                Version = 1,
                HasException = statistics.ExceptionData?.Count > 0,
                ExceptionNote = statistics.ExceptionData?.Count > 0 ?
                    $"存在 {statistics.ExceptionData.Count} 个异常数据" : null,
                Remark = input.Remark
            };
            // 插入日结记录
            var settlementId = await cashierSettlementRep.InsertReturnSnowflakeIdAsync(settlement);
            // 更新收费记录的日结状态（包括退费）
            await UpdateChargeSettlementFlags(input.CashierId, input.SettlementDate.Date, settlementId);
            // 更新充值记录的日结状态（包括退卡）
            await UpdateRechargeSettlementFlags(input.CashierId, input.SettlementDate.Date, settlementId);
            stopwatch.Stop();
            // 记录操作日志
            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = settlementId,
                OperationType = 1,
                OperationDesc = "执行日结操作",
                BeforeData = input,
                AfterData = settlement,
                OperationReason = input.Remark,
                AffectedBusinessIds = null,
                OperationResult = 1,
                ErrorMessage = null,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });
            return settlementId;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = 0,
                OperationType = 1,
                OperationDesc = "日结操作失败",
                BeforeData = input,
                AfterData = null,
                OperationReason = input.Remark,
                AffectedBusinessIds = null,
                OperationResult = 2,
                ErrorMessage = ex.Message,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });
            throw;
        }
    }

    /// <summary>
    /// 执行月结操作 📅
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("执行月结操作")]
    [ApiDescriptionSettings(Name = "MonthlySettlement")]
    [HttpPost]
    [UnitOfWork]
    public async Task<long> MonthlySettlement(CashierMonthlySettlementInput input)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var startTime = new DateTime(input.SettlementMonth.Year, input.SettlementMonth.Month, 1);
            var endTime = startTime.AddMonths(1).AddSeconds(-1);

            // 检查是否已经月结
            var existingSettlement = await cashierSettlementRep.GetFirstAsync(u =>
                u.CashierId == input.CashierId &&
                u.SettlementDate.Year == input.SettlementMonth.Year &&
                u.SettlementDate.Month == input.SettlementMonth.Month &&
                u.SettlementType == 2 &&
                u.Status == 1);

            if (existingSettlement != null) throw Oops.Oh("该收款员当月已经执行过月结操作");

            // 获取统计数据
            var statistics = await GetStatistics(new CashierStatisticsInput
            {
                CashierId = input.CashierId, StartDate = startTime, EndDate = endTime
            });

            // 创建月结记录
            var settlement = new CashierSettlement
            {
                CashierId = input.CashierId,
                CashierName = statistics.CashierName,
                SettlementDate = startTime,
                SettlementType = 2, // 月结
                TotalChargeAmount = statistics.TotalChargeAmount,
                TotalRefundAmount = statistics.TotalRefundAmount,
                NetChargeAmount = statistics.NetChargeAmount,
                TotalRechargeAmount = statistics.TotalRechargeAmount,
                TotalCardRefundAmount = statistics.TotalCardRefundAmount,
                NetRechargeAmount = statistics.NetRechargeAmount,
                ChargeCount = statistics.ChargeCount,
                RefundCount = statistics.RefundCount,
                RechargeCount = statistics.RechargeCount,
                CardRefundCount = statistics.CardRefundCount,
                RefundRate = statistics.RefundRate,
                DailyAvgAmount = statistics.DailyAvgAmount,
                DailyAvgCount = statistics.DailyAvgCount,
                WorkDays = statistics.WorkDays,
                PayMethodStatistics = JsonSerializer.Serialize(statistics.PayMethodStatistics),
                StartTime = startTime,
                EndTime = endTime,
                Status = 1,
                Version = 1,
                HasException = statistics.ExceptionData?.Count > 0,
                ExceptionNote = statistics.ExceptionData?.Count > 0 ?
                    $"存在 {statistics.ExceptionData.Count} 个异常数据" : null,
                Remark = input.Remark
            };

            var settlementId = await cashierSettlementRep.InsertReturnSnowflakeIdAsync(settlement);

            stopwatch.Stop();

            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = settlementId,
                OperationType = 1,
                OperationDesc = "执行月结操作",
                BeforeData = input,
                AfterData = settlement,
                OperationReason = input.Remark,
                AffectedBusinessIds = null,
                OperationResult = 1,
                ErrorMessage = null,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });

            return settlementId;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = 0,
                OperationType = 1,
                OperationDesc = "月结操作失败",
                BeforeData = input,
                AfterData = null,
                OperationReason = input.Remark,
                AffectedBusinessIds = null,
                OperationResult = 2,
                ErrorMessage = ex.Message,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });
            throw;
        }
    }

    /// <summary>
    /// 撤销结算 ❌
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("撤销结算")]
    [ApiDescriptionSettings(Name = "CancelSettlement")]
    [HttpPost]
    [UnitOfWork]
    public async Task CancelSettlement(long id)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var settlement = await cashierSettlementRep.GetByIdAsync(id);
            if (settlement == null) throw Oops.Oh("结算记录不存在");

            if (settlement.Status == 2) throw Oops.Oh("该结算记录已经被撤销");

            var beforeData = settlement.Adapt<object>();

            // 乐观锁检查
            var currentVersion = settlement.Version;

            // 更新结算记录状态
            var updateResult = await cashierSettlementRep.AsUpdateable()
                .SetColumns(u => new CashierSettlement
                {
                    Status = 2, Version = currentVersion + 1
                })
                .Where(u => u.Id == id && u.Version == currentVersion)
                .ExecuteCommandAsync();

            if (updateResult == 0) throw Oops.Oh("结算记录已被其他用户修改，请刷新后重试");

            // 如果是日结，需要清除收费记录的日结状态
            if (settlement.SettlementType == 1)
            {
                // 清除收费记录的日结状态
                await chargeMainRep.AsUpdateable()
                    .SetColumns(u => new ChargeMain
                    {
                        IsDailySettle = YesNoEnum.N, DailySettleId = null
                    })
                    .Where(u => u.DailySettleId == id)
                    .ExecuteCommandAsync();

                // 清除充值记录的日结状态
                await medicalCardRechargeRep.AsUpdateable()
                    .SetColumns(u => new MedicalCardRecharge
                    {
                        IsDailySettle = 2, DailySettleId = null
                    })
                    .Where(u => u.DailySettleId == id)
                    .ExecuteCommandAsync();
            }
            // 获取更新后的数据
            var afterData = await cashierSettlementRep.GetByIdAsync(id);
            stopwatch.Stop();
            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = id,
                OperationType = 3,
                OperationDesc = "撤销结算",
                BeforeData = beforeData,
                AfterData = afterData,
                OperationReason = "手动撤销结算",
                AffectedBusinessIds = null,
                OperationResult = 1,
                ErrorMessage = null,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = id,
                OperationType = 3,
                OperationDesc = "撤销结算失败",
                BeforeData = null,
                AfterData = null,
                OperationReason = "撤销结算异常",
                AffectedBusinessIds = null,
                OperationResult = 2,
                ErrorMessage = ex.Message,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });
            throw;
        }
    }

    /// <summary>
    /// 获取支付方式统计
    /// </summary>
    private async Task<List<PayMethodStatistics>> GetPayMethodStatistics(
        List<ChargeMain> chargeData,
        List<ChargeMain> refundData,
        List<MedicalCardRecharge> rechargeData,
        List<MedicalCardRecharge> cardRefundData)
    {
        var payMethods = await payMethodRep.GetListAsync();
        var statistics = new List<PayMethodStatistics>();

        foreach (var payMethod in payMethods)
        {
            var stat = new PayMethodStatistics
            {
                PayMethodId = payMethod.Id, PayMethodName = payMethod.Name
            };

            // 统计收费金额
            foreach (var charge in chargeData)
            {
                if (charge.PayMethod1Id == payMethod.Id)
                {
                    stat.ChargeAmount += charge.PayAmount1 ?? 0;
                    stat.ChargeCount++;
                }
                if (charge.PayMethod2Id == payMethod.Id)
                {
                    stat.ChargeAmount += charge.PayAmount2 ?? 0;
                    stat.ChargeCount++;
                }
            }

            // 统计退费金额
            foreach (var refund in refundData)
            {
                if (refund.PayMethod1Id == payMethod.Id)
                {
                    stat.RefundAmount += refund.PayAmount1 ?? 0;
                    stat.RefundCount++;
                }
                if (refund.PayMethod2Id == payMethod.Id)
                {
                    stat.RefundAmount += refund.PayAmount2 ?? 0;
                    stat.RefundCount++;
                }
            }

            // 统计充值金额
            foreach (var recharge in rechargeData)
                if (recharge.PayMethodId == payMethod.Id)
                {
                    stat.RechargeAmount += recharge.PayAmount ?? 0;
                    stat.RechargeCount++;
                }

            // 统计退卡金额
            foreach (var cardRefund in cardRefundData)
                if (cardRefund.PayMethodId == payMethod.Id)
                {
                    stat.CardRefundAmount += cardRefund.PayAmount ?? 0;
                    stat.CardRefundCount++;
                }

            // 计算净额和总计
            stat.NetChargeAmount = stat.ChargeAmount - stat.RefundAmount;
            stat.NetRechargeAmount = stat.RechargeAmount - stat.CardRefundAmount;
            stat.TotalAmount = stat.NetChargeAmount + stat.NetRechargeAmount;
            stat.TotalCount = stat.ChargeCount + stat.RefundCount + stat.RechargeCount + stat.CardRefundCount;

            if (stat.TotalAmount != 0 || stat.TotalCount > 0) statistics.Add(stat);
        }
        return statistics.OrderByDescending(x => x.TotalAmount).ToList();
    }

    /// <summary>
    /// 获取收费明细
    /// </summary>
    private async Task<List<ChargeDetailStatistics>> GetChargeDetails(CashierStatisticsInput input)
    {
        return await chargeMainRep.AsTenant().QueryableWithAttr<ChargeMain>()
            .LeftJoin<Register>((c, r) => c.RegisterId == r.Id)
            .LeftJoin<FeeCategory>((c, r, f) => r.FeeId == f.Id)
            .LeftJoin<PayMethod>((c, r, f, p) => c.PayMethod1Id == p.Id)
            .LeftJoin<PayMethod>((c, r, f, p, p2) => c.PayMethod2Id == p2.Id)
            .Where((c, r, f, p, p2) => c.CreateTime >= input.StartDate && c.CreateTime <= input.EndDate)
            .Where((c, r, f, p, p2) => ChargeStatusForCharge.Contains(c.Status))
            .WhereIF(input.CashierId.HasValue, (c, r) => c.CreateUserId == input.CashierId)
            .Select((c, r, f, p, p2) => new ChargeDetailStatistics
            {
                InvoiceNumber = c.InvoiceNumber,
                PatientName = r.PatientName,
                VisitNo = r.VisitNo,
                TotalAmount = c.TotalAmount,
                ChargeTime = c.CreateTime,
                FeeName = f.Name,
                OutpatientNo = r.OutpatientNo,
                PayMethod = p2.Name,
                PayAmount = c.PayAmount2,
                SocialSecurityPayMethod = p.Name,
                SocialSecurityPayAmount = c.PayAmount1
            }).ToListAsync();
    }

    /// <summary>
    /// 获取充值明细
    /// </summary>
    private async Task<List<RechargeDetailStatistics>> GetRechargeDetails(CashierStatisticsInput input)
    {

        return await medicalCardRechargeRep.AsTenant().QueryableWithAttr<MedicalCardRecharge>()
            .LeftJoin<PatientInfo>((r, p) => r.PatientId == p.Id)
            .LeftJoin<PayMethod>((r, p, pm) => r.PayMethodId == pm.Id)
            .Where((r, p, pm) => r.CreateTime >= input.StartDate && r.CreateTime <= input.EndDate)
            .Where((r, p, pm) => CardRechargeStatusForRecharge.Contains(r.Status)) //充值状态
            .Select((r, p, pm) => new RechargeDetailStatistics
            {
                RechargeId = r.Id,
                InvoiceNumber = r.InvoiceNumber,
                PatientName = p.Name,
                CardNo = r.CardNo,
                PayAmount = r.PayAmount ?? 0,
                RechargeTime = r.CreateTime,
                PayMethodName = pm.Name
            })
            .ToListAsync();
    }



    /// <summary>
    /// 获取退费明细
    /// </summary>
    private async Task<List<RefundDetailStatistics>> GetRefundDetails(CashierStatisticsInput input)
    {
        return await chargeMainRep.AsTenant().QueryableWithAttr<ChargeMain>()
            .LeftJoin<Register>((c, r) => c.RegisterId == r.Id)
            .LeftJoin<FeeCategory>((c, r, f) => r.FeeId == f.Id)
            .LeftJoin<PayMethod>((c, r, f, p) => c.PayMethod1Id == p.Id)
            .LeftJoin<PayMethod>((c, r, f, p, p2) => c.PayMethod2Id == p2.Id)
            .Where((c, r, f, p, p2) => c.CreateTime >= input.StartDate && c.CreateTime <= input.EndDate)
            .Where((c, r, f, p, p2) => ChargeStatusForRefund.Contains(c.Status))
            .WhereIF(input.CashierId.HasValue, (c, r) => c.CreateUserId == input.CashierId)
            .Select((c, r, f, p, p2) => new RefundDetailStatistics
            {
                InvoiceNumber = c.InvoiceNumber,
                RefundInvoiceNumber = c.RefundInvoiceNumber,
                PatientName = r.PatientName,
                VisitNo = r.VisitNo,
                TotalAmount = c.TotalAmount,
                ChargeTime = c.CreateTime,
                FeeName = f.Name,
                OutpatientNo = r.OutpatientNo,
                PayMethod = p2.Name,
                PayAmount = c.PayAmount2,
                SocialSecurityPayMethod = p.Name,
                SocialSecurityPayAmount = c.PayAmount1,
                RefundReason = c.RefundReason
            }).ToListAsync();
    }

    /// <summary>
    /// 获取退卡和红冲明细
    /// </summary>
    private async Task<List<CardRefundDetailStatistics>> GetCardRefundDetails(CashierStatisticsInput input)
    {
        return await medicalCardRechargeRep.AsTenant().QueryableWithAttr<MedicalCardRecharge>()
            .LeftJoin<PatientInfo>((r, p) => r.PatientId == p.Id)
            .LeftJoin<PayMethod>((r, p, pm) => r.PayMethodId == pm.Id)
            .Where((r, p, pm) => r.CreateTime >= input.StartDate && r.CreateTime <= input.EndDate)
            .Where((r, p, pm) => CardRechargeStatusForCardRefund.Contains(r.Status))
            .Select((r, p, pm) => new CardRefundDetailStatistics
            {
                CardRefundId = r.Id,
                InvoiceNumber = r.InvoiceNumber,
                PatientName = p.Name,
                CardNo = r.CardNo,
                RefundAmount = r.PayAmount ?? 0,
                RefundTime = r.CreateTime,
                PayMethodName = pm.Name
            })
            .ToListAsync();
    }

    /// <summary>
    /// 计算工作天数
    /// </summary>
    private async Task<int> CalculateWorkDays(long cashierId, DateTime startDate, DateTime endDate)
    {
        // 查询该收款员在指定时间范围内有业务操作的天数
        var workDays = await chargeMainRep.AsQueryable()
            .Where(u => u.CreateUserId == cashierId)
            .Where(u => u.CreateTime >= startDate && u.CreateTime <= endDate)
            .Select(u => u.CreateTime.Date)
            .Distinct()
            .CountAsync();

        // 也要统计充值业务的工作天数
        var rechargeWorkDays = await medicalCardRechargeRep.AsQueryable()
            .Where(u => u.CreateUserId == cashierId)
            .Where(u => u.CreateTime >= startDate && u.CreateTime <= endDate)
            .Select(u => u.CreateTime.Date)
            .Distinct()
            .CountAsync();

        return Math.Max(workDays, rechargeWorkDays);
    }

    // /// <summary>
    // /// 数据校验
    // /// </summary>
    // private async Task<DataValidationResult> ValidateData(CashierStatisticsInput input)
    // {
    //     var result = new DataValidationResult();
    //
    //     try
    //     {
    //         // 1. 基础数据完整性校验
    //         await ValidateDataIntegrity(input, result);
    //
    //         // 2. 金额一致性校验
    //         await ValidateAmountConsistency(input, result);
    //
    //         // 3. 业务逻辑校验
    //         await ValidateBusinessLogic(input, result);
    //
    //         result.IsValid = result.ValidationErrors.Count == 0;
    //     }
    //     catch (Exception ex)
    //     {
    //         result.IsValid = false;
    //         result.ValidationErrors.Add($"数据校验过程中发生错误：{ex.Message}");
    //     }
    //
    //     return result;
    // }

    // /// <summary>
    // /// 获取异常数据
    // /// </summary>
    // private async Task<List<ExceptionDataInfo>> GetExceptionData(CashierStatisticsInput input)
    // {
    //     var exceptions = new List<ExceptionDataInfo>();
    //
    //     try
    //     {
    //         // 1. 检查跨日期数据
    //         await CheckCrossDateData(input, exceptions);
    //
    //         // 2. 检查状态异常数据
    //         await CheckStatusAnomalies(input, exceptions);
    //
    //         // 3. 检查金额异常数据
    //         await CheckAmountAnomalies(input, exceptions);
    //     }
    //     catch (Exception ex)
    //     {
    //         exceptions.Add(new ExceptionDataInfo
    //         {
    //             ExceptionType = 4, ExceptionDesc = "异常数据检查过程中发生错误", ExceptionDetail = ex.Message, SuggestedAction = "请联系系统管理员"
    //         });
    //     }
    //
    //     return exceptions;
    // }

    // /// <summary>
    // /// 数据校验接口 🔍
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("数据校验")]
    // [ApiDescriptionSettings(Name = "ValidateData")][HttpPost]
    // public async Task<DataValidationResult> ValidateData(DataValidationInput input)
    // {
    //     var stopwatch = Stopwatch.StartNew();
    //     var result = new DataValidationResult();
    //
    //     try
    //     {
    //         // 记录操作开始
    //         await operationLogService.LogOperation(0, 7, "开始数据校验", input, null, $"校验类型: {input.ValidationType}");
    //
    //         // 根据校验类型执行不同的校验
    //         switch (input.ValidationType)
    //         {
    //             case 1: // 基础数据校验
    //                 await ValidateDataIntegrity(new CashierStatisticsInput
    //                 {
    //                     CashierId = input.CashierId, StartDate = input.StartTime, EndDate = input.EndTime
    //                 }, result);
    //                 break;
    //             case 2: // 金额一致性校验
    //                 await ValidateAmountConsistency(new CashierStatisticsInput
    //                 {
    //                     CashierId = input.CashierId, StartDate = input.StartTime, EndDate = input.EndTime
    //                 }, result);
    //                 break;
    //             case 3: // 完整性校验
    //                 await ValidateBusinessLogic(new CashierStatisticsInput
    //                 {
    //                     CashierId = input.CashierId, StartDate = input.StartTime, EndDate = input.EndTime
    //                 }, result);
    //                 break;
    //             case 4: // 全面校验
    //             default:
    //                 var statisticsInput = new CashierStatisticsInput
    //                 {
    //                     CashierId = input.CashierId, StartDate = input.StartTime, EndDate = input.EndTime
    //                 };
    //                 await ValidateDataIntegrity(statisticsInput, result);
    //                 await ValidateAmountConsistency(statisticsInput, result);
    //                 await ValidateBusinessLogic(statisticsInput, result);
    //                 break;
    //         }
    //
    //         // 如果启用自动修复
    //         if (input.AutoFix && !result.IsValid) await AutoFixDataIssues(input, result);
    //
    //         result.IsValid = result.ValidationErrors.Count == 0;
    //
    //         // 记录操作完成
    //         stopwatch.Stop();
    //         await operationLogService.LogOperation(0, 7, "数据校验完成", input, result,
    //             $"校验结果: {(result.IsValid ? "通过" : "失败")}",
    //             null, 1, null, stopwatch.ElapsedMilliseconds);
    //
    //         return result;
    //     }
    //     catch (Exception ex)
    //     {
    //         stopwatch.Stop();
    //         result.IsValid = false;
    //         result.ValidationErrors.Add($"数据校验过程中发生错误：{ex.Message}");
    //
    //         await operationLogService.LogOperation(0, 7, "数据校验失败", input, result,
    //             "数据校验异常", null, 2, ex.Message, stopwatch.ElapsedMilliseconds);
    //
    //         throw;
    //     }
    // }

    /// <summary>
    /// 数据补录接口 📝
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("数据补录")]
    [ApiDescriptionSettings(Name = "SupplementData")][HttpPost]
    [UnitOfWork]
    public async Task<bool> SupplementData(DataSupplementInput input)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 获取结算记录并检查版本
            var settlement = await cashierSettlementRep.GetByIdAsync(input.SettlementId);
            if (settlement == null) throw Oops.Oh("结算记录不存在");

            // 记录操作前数据
            var beforeData = settlement.Adapt<object>();

            // 校验业务数据是否存在
            if (!input.ForceUpdate) await ValidateSupplementData(input);

            // 标记结算记录需要重新计算
            settlement.Status = 3; // 需重新计算
            settlement.HasException = true;
            settlement.ExceptionNote = $"数据补录: {input.SupplementReason}";
            settlement.Version += 1;

            await cashierSettlementRep.UpdateAsync(settlement);

            // 重新计算结算数据
            await RecalculateSettlementData(input.SettlementId);

            stopwatch.Stop();
            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = input.SettlementId,
                OperationType = 5,
                OperationDesc = "数据补录",
                BeforeData = beforeData,
                AfterData = settlement,
                OperationReason = input.SupplementReason,
                AffectedBusinessIds = input.BusinessIds,
                OperationResult = 1,
                ErrorMessage = null,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });

            return true;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = input.SettlementId,
                OperationType = 5,
                OperationDesc = "数据补录失败",
                BeforeData = input,
                AfterData = null,
                OperationReason = input.SupplementReason,
                AffectedBusinessIds = input.BusinessIds,
                OperationResult = 2,
                ErrorMessage = ex.Message,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });
            throw;
        }
    }

    // /// <summary>
    // /// 冲正处理接口 ↩️
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("冲正处理")]
    // [ApiDescriptionSettings(Name = "ProcessReversal")][HttpPost]
    // [UnitOfWork]
    // public async Task<bool> ProcessReversal(ReversalProcessInput input)
    // {
    //     var stopwatch = Stopwatch.StartNew();
    //
    //     try
    //     {
    //         // 验证原业务数据
    //         await ValidateOriginalBusinessData(input);
    //
    //         // 创建冲正记录
    //         await CreateReversalRecord(input);
    //
    //         // 如果影响已结算数据，更新相关结算记录
    //         if (input.AffectSettledData && input.RelatedSettlementId.HasValue) await UpdateRelatedSettlement(input);
    //
    //         stopwatch.Stop();
    //         await operationLogService.LogOperation(input.RelatedSettlementId ?? 0, 6, "冲正处理", input, null,
    //             input.ReversalReason, new List<long>
    //             {
    //                 input.OriginalBusinessId
    //             }, 1, null, stopwatch.ElapsedMilliseconds);
    //
    //         return true;
    //     }
    //     catch (Exception ex)
    //     {
    //         stopwatch.Stop();
    //         await operationLogService.LogOperation(input.RelatedSettlementId ?? 0, 6, "冲正处理失败", input, null,
    //             input.ReversalReason, new List<long>
    //             {
    //                 input.OriginalBusinessId
    //             }, 2, ex.Message, stopwatch.ElapsedMilliseconds);
    //         throw;
    //     }
    // }

    /// <summary>
    /// 重新计算结算接口 🔄
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("重新计算结算")]
    [ApiDescriptionSettings(Name = "RecalculateSettlement")][HttpPost]
    [UnitOfWork]
    public async Task<bool> RecalculateSettlement(long id)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var settlement = await cashierSettlementRep.GetByIdAsync(id);
            if (settlement == null) throw Oops.Oh("结算记录不存在");

            var beforeData = settlement.Adapt<object>();

            // 重新计算结算数据
            await RecalculateSettlementData(id);

            // 更新状态
            settlement.Status = 1; // 正常
            settlement.HasException = false;
            settlement.ExceptionNote = null;
            settlement.Version += 1;

            await cashierSettlementRep.UpdateAsync(settlement);

            stopwatch.Stop();

            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = id,
                OperationType = 4,
                OperationDesc = "重新计算结算",
                BeforeData = beforeData,
                AfterData = settlement,
                OperationReason = "手动触发重新计算",
                AffectedBusinessIds = null,
                OperationResult = 1,
                ErrorMessage = null,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });

            return true;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            await operationLogService.LogOperation(new LogOperationDetailInput
            {
                SettlementId = id,
                OperationType = 4,
                OperationDesc = "重新计算结算失败",
                BeforeData = null,
                AfterData = null,
                OperationReason = "重新计算异常",
                AffectedBusinessIds = null,
                OperationResult = 2,
                ErrorMessage = ex.Message,
                OperationDuration = stopwatch.ElapsedMilliseconds
            });
            throw;
        }
    }

    /// <summary>
    /// 获取操作日志接口 📋
    /// </summary>
    /// <param name="settlementId"></param>
    /// <returns></returns>
    [DisplayName("获取操作日志")]
    [ApiDescriptionSettings(Name = "GetOperationLogs")][HttpPost]
    public async Task<List<CashierSettlementLog>> GetOperationLogs(long settlementId)
    {
        return await operationLogService.GetOperationLogs(new GetOperationLogsInput
        {
            SettlementId = settlementId
        });
    }


    /// <summary>
    /// 重新计算结算数据
    /// </summary>
    private async Task RecalculateSettlementData(long settlementId)
    {
        var settlement = await cashierSettlementRep.GetByIdAsync(settlementId);
        if (settlement == null) return;

        // 重新获取统计数据
        var statisticsInput = new CashierStatisticsInput
        {
            CashierId = settlement.CashierId, StartDate = settlement.StartTime, EndDate = settlement.EndTime
        };

        var statistics = await GetStatistics(statisticsInput);

        // 更新结算记录
        settlement.TotalChargeAmount = statistics.TotalChargeAmount;
        settlement.TotalRefundAmount = statistics.TotalRefundAmount;
        settlement.NetChargeAmount = statistics.NetChargeAmount;
        settlement.TotalRechargeAmount = statistics.TotalRechargeAmount;
        settlement.TotalCardRefundAmount = statistics.TotalCardRefundAmount;
        settlement.NetRechargeAmount = statistics.NetRechargeAmount;
        settlement.ChargeCount = statistics.ChargeCount;
        settlement.RefundCount = statistics.RefundCount;
        settlement.RechargeCount = statistics.RechargeCount;
        settlement.CardRefundCount = statistics.CardRefundCount;
        settlement.RefundRate = statistics.RefundRate;
        settlement.DailyAvgAmount = statistics.DailyAvgAmount;
        settlement.DailyAvgCount = statistics.DailyAvgCount;
        settlement.WorkDays = statistics.WorkDays;
        settlement.PayMethodStatistics = JsonSerializer.Serialize(statistics.PayMethodStatistics);

        await cashierSettlementRep.UpdateAsync(settlement);
    }

    /// <summary>
    /// 校验补录数据
    /// </summary>
    private async Task ValidateSupplementData(DataSupplementInput input)
    {
        foreach (var businessId in input.BusinessIds)
        {
            var exists = false;

            switch (input.BusinessType)
            {
                case 1: // 收费
                case 2: // 退费
                    exists = await chargeMainRep.IsAnyAsync(u => u.Id == businessId);
                    break;
                case 3: // 充值
                case 4: // 退卡
                    exists = await medicalCardRechargeRep.IsAnyAsync(u => u.Id == businessId);
                    break;
            }

            if (!exists) throw Oops.Oh($"业务数据ID {businessId} 不存在");
        }
    }

    /// <summary>
    /// 更新充值记录的结算标记
    /// </summary>
    private async Task UpdateRechargeSettlementFlags(long cashierId, DateTime settlementDate, long settlementId)
    {
        var startTime = settlementDate.Date;
        var endTime = startTime.AddDays(1).AddSeconds(-1);

        // 更新充值记录的日结状态（包括退卡）
        await medicalCardRechargeRep.AsUpdateable()
            .SetColumns(u => new MedicalCardRecharge
            {
                IsDailySettle = 1, DailySettleId = settlementId
            })
            .Where(u => u.CreateUserId == cashierId &&
                        u.CreateTime >= startTime &&
                        u.CreateTime <= endTime &&
                        CardRechargeStatusToInclude.Contains(u.Status))
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新收费记录的结算标记
    /// </summary>
    private async Task UpdateChargeSettlementFlags(long cashierId, DateTime settlementDate, long settlementId)
    {
        var startTime = settlementDate.Date;
        var endTime = startTime.AddDays(1).AddSeconds(-1);

        // 更新收费记录的日结状态（包括退费）
        await chargeMainRep.AsUpdateable()
            .SetColumns(u => new ChargeMain
            {
                IsDailySettle = YesNoEnum.Y, DailySettleId = settlementId
            })
            .Where(u => u.CreateUserId == cashierId &&
                        u.CreateTime >= startTime &&
                        u.CreateTime <= endTime &&
                        ChargeStatusToInclude.Contains(u.Status)
            )
            .ExecuteCommandAsync();
    }
}