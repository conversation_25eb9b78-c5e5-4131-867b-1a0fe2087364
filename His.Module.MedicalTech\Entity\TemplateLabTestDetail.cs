﻿using Admin.NET.Core;
namespace His.Module.MedicalTech.Entity;

/// <summary>
/// 检验模板表
/// </summary>
[Tenant("1300000000009")]
[SugarTable("template_lab_test_detail", "检验模板表")]
public class TemplateLabTestDetail : EntityTenant
{
    /// <summary>
    /// 
    /// </summary>
 
    [SugarColumn(ColumnName = "template_id", ColumnDescription = "", Length = 64)]
    public virtual long? TemplateId { get; set; }
    
    /// <summary>
    /// 项目Id
    /// </summary>
    [SugarColumn(ColumnName = "item_id", ColumnDescription = "项目Id")]
    public virtual long? ItemId { get; set; }
    
    /// <summary>
    /// 项目编码
    /// </summary>
    [SugarColumn(ColumnName = "item_code", ColumnDescription = "项目编码", Length = 64)]
    public virtual string? ItemCode { get; set; }
    
    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnName = "item_name", ColumnDescription = "项目名称", Length = 64)]
    public virtual string? ItemName { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 64)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 单价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "单价", Length = 16, DecimalDigits=4)]
    public virtual decimal? Price { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量", Length = 16, DecimalDigits=4)]
    public virtual decimal? Quantity { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 16, DecimalDigits=4)]
    public virtual decimal? Amount { get; set; }
    
    /// <summary>
    /// 样本类型
    /// </summary>
    [SugarColumn(ColumnName = "sample_type", ColumnDescription = "样本类型", Length = 100)]
    public virtual string? SampleType { get; set; }
    
    /// <summary>
    /// 紧急程度 0:普通,1:急,2:明晨急
    /// </summary>
    [SugarColumn(ColumnName = "urgency_level", ColumnDescription = "紧急程度 0:普通,1:急,2:明晨急", Length = 32)]
    public virtual string? UrgencyLevel { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "国家医保编码", Length = 100)]
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 国标编码
    /// </summary>
    [SugarColumn(ColumnName = "nationalstandard_code", ColumnDescription = "国标编码", Length = 100)]
    public virtual string? NationalstandardCode { get; set; }
    
    /// <summary>
    /// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 是否套餐
    /// </summary>
    [SugarColumn(ColumnName = "is_package", ColumnDescription = "是否套餐")]
    public virtual int? IsPackage { get; set; }
    
    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    [SugarColumn(ColumnName = "flag", ColumnDescription = "门诊住院标识 0门诊 1住院")]
    public virtual int? Flag { get; set; }
    
    /// <summary>
    /// 执行科室Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_id", ColumnDescription = "执行科室Id")]
    public virtual long? ExecuteDeptId { get; set; }
    
    /// <summary>
    /// 执行科室名称
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_name", ColumnDescription = "执行科室名称", Length = 64)]
    public virtual string? ExecuteDeptName { get; set; }
    
    /// <summary>
    /// 执行科室地址
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_address", ColumnDescription = "执行科室地址", Length = 100)]
    public virtual string? ExecuteDeptAddress { get; set; }
    
    /// <summary>
    /// 自付比例
    /// </summary>
    [SugarColumn(ColumnName = "self_pay_ratio", ColumnDescription = "自付比例", Length = 4, DecimalDigits=4)]
    public virtual decimal? SelfPayRatio { get; set; }
    
    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    [SugarColumn(ColumnName = "is_ratio_audit", ColumnDescription = "自付比例是否审核 1审核 2不审核")]
    public virtual int? IsRatioAudit { get; set; }
    
    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_time", ColumnDescription = "自付比例审核时间")]
    public virtual DateTime? RatioAuditTime { get; set; }
    
    /// <summary>
    /// 自付比例审核人员Id
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_staff_id", ColumnDescription = "自付比例审核人员Id")]
    public virtual long? RatioAuditStaffId { get; set; }
    
    /// <summary>
    /// 自付比例审核人员名称
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_staff_name", ColumnDescription = "自付比例审核人员名称", Length = 64)]
    public virtual string? RatioAuditStaffName { get; set; }
    
    /// <summary>
    /// 收费类别Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_id", ColumnDescription = "收费类别Id")]
    public virtual long? ChargeCategoryId { get; set; }
    
    /// <summary>
    /// 创建者部门Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建者部门Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建者部门名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
