﻿import {useBaseApi} from '/@/api/base';

// 医疗组维护接口服务
export const useMedicalTeamApi = () => {
	const baseApi = useBaseApi("medicalTeam");
	return {
		// 分页查询医疗组维护
		page: baseApi.page,
		// 查看医疗组维护详细
		detail: baseApi.detail,
		// 新增医疗组维护
		add: baseApi.add,
		// 更新医疗组维护
		update: baseApi.update,
		// 删除医疗组维护
		delete: baseApi.delete,
		// 批量删除医疗组维护
		batchDelete: baseApi.batchDelete,
		// 导出医疗组维护数据
		exportData: baseApi.exportData,
		// 导入医疗组维护数据
		importData: baseApi.importData,
		// 下载医疗组维护数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 医疗组维护实体
export interface MedicalTeam {
	// 主键Id
	id: number;
	// 医疗组名称
	teamName?: string;
	// 所属科室ID
	deptId?: number;
	// 
	deptName?: string;
	// 医疗组类型 字典医疗组
	teamType?: string;
	// 组长ID
	teamLeaderId: number;
	// 组长
	teamLeaderName: string;
	// 成立日期
	establishDate: string;
	// 状态(1:启用 2:停用,)
	status: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 创建用户ID
	createUserId: number;
	// 创建用户名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 更新用户ID
	updateUserId: number;
	// 更新用户名
	updateUserName: string;
	// 是否删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}