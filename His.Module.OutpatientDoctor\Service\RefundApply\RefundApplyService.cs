﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.OutpatientDoctor.Api.Prescription.Dto;
using His.Module.OutpatientDoctor.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 门诊退费申请服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class RefundApplyService(
    PrescriptionService prescriptionService,
    RefundAuditService refundAuditService,
    SqlSugarRepository<RefundApply> refundApplyRep,
    SqlSugarRepository<ChargeMain> chargeMainRep,
    ChargeService chargeService,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _sqlSugarClient = sqlSugarClient;
    //
    //   /// <summary>
    //   /// 分页查询门诊退费申请 🔖
    //   /// </summary>
    //   /// <param name="input"></param>
    //   /// <returns></returns>
    //   [DisplayName("分页查询门诊退费申请")]
    //   [ApiDescriptionSettings(Name = "Page"), HttpPost]
    //   public async Task<SqlSugarPagedList<RefundApplyOutput>> Page(PageRefundApplyInput input)
    //   {
    //       input.Keyword = input.Keyword?.Trim();
    //       var query = refundApplyRep.AsQueryable()
    //           .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.BillingType.Contains(input.Keyword) || u.ApplyNo.Contains(input.Keyword) 
    //    || u.CardNo.Contains(input.Keyword) || u.VisitNo.Contains(input.Keyword)
    //               || u.OutpatientNo.Contains(input.Keyword) || u.PatientName.Contains(input.Keyword) || u.ApplyReason.Contains(input.Keyword))
    //           .WhereIF(!string.IsNullOrWhiteSpace(input.BillingType), u => u.BillingType.Contains(input.BillingType.Trim()))
    //           .WhereIF(!string.IsNullOrWhiteSpace(input.ApplyNo), u => u.ApplyNo.Contains(input.ApplyNo.Trim()))
    //
    //           .WhereIF(!string.IsNullOrWhiteSpace(input.CardNo), u => u.CardNo.Contains(input.CardNo.Trim()))
    //           .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo.Contains(input.VisitNo.Trim()))
    //           .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo), u => u.OutpatientNo.Contains(input.OutpatientNo.Trim()))
    //           .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))
    //           .WhereIF(!string.IsNullOrWhiteSpace(input.ApplyReason), u => u.ApplyReason.Contains(input.ApplyReason.Trim()))
    //           .WhereIF(input.ChargeId != null, u => u.ChargeId == input.ChargeId)
    //           .WhereIF(input.ApplyTimeRange?.Length == 2, u => u.ApplyTime >= input.ApplyTimeRange[0] && u.ApplyTime <= input.ApplyTimeRange[1])
    //           .WhereIF(input.ApplyDeptId != null, u => u.CreateOrgId == input.ApplyDeptId)
    //           .WhereIF(input.ApplyUserId != null, u => u.CreateUserId == input.ApplyUserId)
    //           .WhereIF(input.RegisterId != null, u => u.RegisterId == input.RegisterId)
    //           .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
    //           .WhereIF(input.Status != null, u => u.Status == input.Status)
    //           .WhereIF(input.AuditStatus != null, u => u.AuditStatus == input.AuditStatus)
    //           .Select<RefundApplyOutput>();
    // return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    //   }
 
    /// <summary>
    /// 获取门诊退费申请详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取门诊退费申请详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<RefundApply> Detail([FromQuery] QueryByIdRefundApplyInput input)
    {
        return await refundApplyRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加门诊退费申请 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加门诊退费申请")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost, UnitOfWork]
    public async Task<long> Add(AddRefundApplyInput input)
    {
        var charge = await chargeService.Get(input.ChargeId);
        RefundApply entity = new RefundApply
        {
            ApplyReason = input.ApplyReason,
            ChargeId = input.ChargeId,
            Status = 0,
            ApplyTime = DateTime.Now,
            CardNo = charge.CardNo,
            VisitNo = charge.VisitNo,
            OutpatientNo = charge.OutpatientNo,
            RegisterId = charge.RegisterId,
            PatientId = charge.PatientId,
            PatientName = input.PatientName,
            BillingType = charge.BillingType,
            AuditStatus = 0,
            ApplyNo = await refundApplyRep.Context.Ado.GetStringAsync(
                $"SELECT LPAD(CAST(NEXTVAL('refund_apply_no_seq')As varchar),8,'0')")
        };
        var applyId = await refundApplyRep.InsertAsync(entity) ? entity.Id : 0;
        await refundAuditService.InitAuditRecord(
            new InitAuditRecordDto
            {
                ApplyId = applyId,
                ChargeId = input.ChargeId
            }, charge
        );

        return applyId;
    }

    /// <summary>
    /// 更新门诊退费申请 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新门诊退费申请")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateRefundApplyInput input)
    {
        var entity = input.Adapt<RefundApply>();
        await refundApplyRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除门诊退费申请 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除门诊退费申请")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteRefundApplyInput input)
    {
        var entity = await refundApplyRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await refundApplyRep.FakeDeleteAsync(entity); //假删除
        //await _refundApplyRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除门诊退费申请 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除门诊退费申请")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteRefundApplyInput> input)
    {
        var exp = Expressionable.Create<RefundApply>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await refundApplyRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await refundApplyRep.FakeDeleteAsync(list); //假删除
        //return await _refundApplyRep.DeleteAsync(list);   //真删除
    }


    private ISugarQueryable<RefundApplyOutput> BuildQueryExpression(PageRefundApplyInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = refundApplyRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.BillingType.Contains(input.Keyword) ||
                                                                     u.ApplyNo.Contains(input.Keyword)
                                                                     || u.CardNo.Contains(input.Keyword) ||
                                                                     u.VisitNo.Contains(input.Keyword)
                                                                     || u.OutpatientNo.Contains(input.Keyword) ||
                                                                     u.PatientName.Contains(input.Keyword) ||
                                                                     u.ApplyReason.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BillingType),
                u => u.BillingType.Contains(input.BillingType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApplyNo), u => u.ApplyNo.Contains(input.ApplyNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.CardNo), u => u.CardNo.Contains(input.CardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo.Contains(input.VisitNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo),
                u => u.OutpatientNo.Contains(input.OutpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName),
                u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApplyReason),
                u => u.ApplyReason.Contains(input.ApplyReason.Trim()))
            .WhereIF(input.ChargeId != null, u => u.ChargeId == input.ChargeId)
            .WhereIF(input.ApplyTimeRange?.Length == 2,
                u => u.ApplyTime >= input.ApplyTimeRange[0] && u.ApplyTime <= input.ApplyTimeRange[1])
            .WhereIF(input.ApplyDeptId != null, u => u.CreateOrgId == input.ApplyDeptId)
            .WhereIF(input.ApplyUserId != null, u => u.CreateUserId == input.ApplyUserId)
            .WhereIF(input.RegisterId != null, u => u.RegisterId == input.RegisterId)
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .WhereIF(input.AuditStatus != null, u => u.AuditStatus == input.AuditStatus)
            .LeftJoin<ChargeMain>((r, c) => c.Id == r.ChargeId)
            .LeftJoin<RefundAudit>((r, c, a)
               => r.Id == a.ApplyId && a.AuditStatus == 0 )
     
            .Select((r, c,a) => new RefundApplyOutput()
            {
                ChargeId = c.Id,
            }, true);
        return query;
        
    }

    /// <summary>
    ///  
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询退费申请数据")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost,  SkipPermission]
    public async Task<SqlSugarPagedList<RefundApplyOutput>> Page(PageRefundApplyInput input)
    {
       
        var query =  BuildQueryExpression(input);
        var result = await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
        if(App.User.FindFirst(ClaimConst.RoleId)?.Value =="")
            return result;
        foreach (var item in result.Items)
        {
         
           if(item.AuditStatus==2)//驳回
                continue;
               // item.HasAuditPermission = (item.FlowRoleId != null &&
            //                            long.Parse(App.User.FindFirst(ClaimConst.RoleId)?.Value?? "0") ==item.FlowRoleId)
            //     || (item.FlowUserId != null &&
            //         long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0") == item.FlowUserId);
            item.HasAuditPermission = (item.FlowRoleCode != null &&
                                       (App.User.FindFirst(ClaimConst.RoleCode)?.Value?? "") ==item.FlowRoleCode)
                                      || (item.FlowUserId != null &&
                                          long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0") == item.FlowUserId);
        }

        return result;
        // var list = await query.ToListAsync();
        // if (list.IsNullOrEmpty()) return list;
        // var ids = list.Select(p => p.Id).ToList();
        // var details = await chargeDetailRep.AsQueryable()
        //     .InnerJoin<ChargeMain>((d, m) => d.ChargeId == m.Id)
        //     .WhereIF(ids.Count > 0, (d, m) => ids.Contains(m.Id)).ToListAsync();
        // // 遍历list 将details 赋值给list
        // list.ForEach(item =>
        // {
        //     item.Details = details.Where(u => u.ChargeId == item.Id).ToList().Adapt<List<ChargeDetailOutput>>();
        // });
        //
        //
        // return list;
    }
     /// <summary>
    ///  
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
 
    [ApiDescriptionSettings(IgnoreApi = true) ]
    public async Task<List<RefundApplyOutput>> GetList(PageRefundApplyInput input)
    {
      
        var query =  BuildQueryExpression(input);
       
        var result = await 
            query.Where(u => u.AuditStatus == 3)// 审核完成
                .OrderBuilder(input).ToListAsync();
      
       
        return result;
        
    }
    /// <summary>
    /// 查询审核通过未退药的处方
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns> 
 
    [DisplayName("查询审核通过未退药的处方")]
    [ApiDescriptionSettings(Name = "GetUnreturnedPrescription"), HttpPost,  SkipPermission]
    public async Task<List<UnreturnedPrescriptionOutput>> GetUnreturnedPrescription(UnreturnedPrescriptionQueryDto dto)
    {
        // 查询审核通过的数据
        
        var input=new PageRefundApplyInput
        {
            BillingType = "Prescription",
            AuditStatus = 1,
          VisitNo = dto.VisitNo,
          CardNo = dto.CardNo,
         PatientId = dto.PatientId,
        };
        var query = refundApplyRep.AsQueryable()
            
            .WhereIF(!string.IsNullOrWhiteSpace(input.BillingType),
                u => u.BillingType.Contains(input.BillingType.Trim())) 
            .WhereIF(!string.IsNullOrWhiteSpace(input.CardNo), u => u.CardNo.Contains(input.CardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo.Contains(input.VisitNo.Trim()))
           
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .WhereIF(input.AuditStatus != null, u => u.AuditStatus == 3)
            .InnerJoin<ChargeMain>((r, c) => c.Id == r.ChargeId)
          
     
            .Select((r, c ) => new UnreturnedPrescriptionOutput()
            {
                ChargeId = c.Id,
            }, true);
            var list=await query.ToListAsync();
           // var result=new List<UnreturnedPrescriptionOutput>();
            if(list.IsNullOrEmpty())
                return list;
            foreach (var item in list)
            {
             
                item.PrescriptionData= await prescriptionService.GetPrescription(item.BillingId ?? 0);
                item.AuditData = await refundAuditService.List(new RefundAuditQueryInput() { ApplyId = item.Id });
            }


        return list;
    }
}