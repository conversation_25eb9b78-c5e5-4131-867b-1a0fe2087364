﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
	  <NoWarn>1701;1702;1591;8632</NoWarn>
	  <DocumentationFile></DocumentationFile>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <GenerateDocumentationFile>True</GenerateDocumentationFile>
	  <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.NET.Core\Admin.NET.Core.csproj" />
    <ProjectReference Include="..\His.Module.Inpatient.Api\His.Module.Inpatient.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Configuration\Inpatient.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
