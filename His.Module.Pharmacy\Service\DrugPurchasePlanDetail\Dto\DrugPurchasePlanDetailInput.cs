﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 采购计划明细表基础输入参数
/// </summary>
public class DrugPurchasePlanDetailBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 采购计划ID
    /// </summary>
    public virtual long? PlanId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 药店库存数量
    /// </summary>
    public virtual int? PharmacyQuantity { get; set; }
    
    /// <summary>
    /// 药库库存数量
    /// </summary>
    public virtual int? StorageQuantity { get; set; }
    
    /// <summary>
    /// 当前销售数量
    /// </summary>
    public virtual int? CurrentSaleQuantity { get; set; }
    
    /// <summary>
    /// 上次销售数量
    /// </summary>
    public virtual int? LastSaleQuantity { get; set; }
    
    /// <summary>
    /// 平均销售数量
    /// </summary>
    public virtual int? AverageSaleQuantity { get; set; }
    
    /// <summary>
    /// 计划采购数量
    /// </summary>
    public virtual int? Quantity { get; set; }
    
    /// <summary>
    /// 采购单价
    /// </summary>
    public virtual decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 总采购价
    /// </summary>
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 生产厂家
    /// </summary>
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    public virtual long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    public virtual string? SupplierName { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 采购计划明细表分页查询输入参数
/// </summary>
public class PageDrugPurchasePlanDetailInput : BasePageInput
{
    /// <summary>
    /// 采购计划ID
    /// </summary>
    public long? PlanId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 药店库存数量
    /// </summary>
    public int? PharmacyQuantity { get; set; }
    
    /// <summary>
    /// 药库库存数量
    /// </summary>
    public int? StorageQuantity { get; set; }
    
    /// <summary>
    /// 当前销售数量
    /// </summary>
    public int? CurrentSaleQuantity { get; set; }
    
    /// <summary>
    /// 上次销售数量
    /// </summary>
    public int? LastSaleQuantity { get; set; }
    
    /// <summary>
    /// 平均销售数量
    /// </summary>
    public int? AverageSaleQuantity { get; set; }
    
    /// <summary>
    /// 计划采购数量
    /// </summary>
    public int? Quantity { get; set; }
    
    /// <summary>
    /// 采购单价
    /// </summary>
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 总采购价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 生产厂家
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    public string? SupplierName { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 采购计划明细表增加输入参数
/// </summary>
public class AddDrugPurchasePlanDetailInput
{
    /// <summary>
    /// 采购计划ID
    /// </summary>
    public long? PlanId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 药店库存数量
    /// </summary>
    public int? PharmacyQuantity { get; set; }
    
    /// <summary>
    /// 药库库存数量
    /// </summary>
    public int? StorageQuantity { get; set; }
    
    /// <summary>
    /// 当前销售数量
    /// </summary>
    public int? CurrentSaleQuantity { get; set; }
    
    /// <summary>
    /// 上次销售数量
    /// </summary>
    public int? LastSaleQuantity { get; set; }
    
    /// <summary>
    /// 平均销售数量
    /// </summary>
    public int? AverageSaleQuantity { get; set; }
    
    /// <summary>
    /// 计划采购数量
    /// </summary>
    public int? Quantity { get; set; }
    
    /// <summary>
    /// 采购单价
    /// </summary>
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 总采购价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 生产厂家
    /// </summary>
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "生产厂家名称字符长度不能超过100")]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "供应商名称字符长度不能超过100")]
    public string? SupplierName { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    public int? Status { get; set; }
    
}

/// <summary>
/// 采购计划明细表删除输入参数
/// </summary>
public class DeleteDrugPurchasePlanDetailInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 采购计划明细表更新输入参数
/// </summary>
public class UpdateDrugPurchasePlanDetailInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 采购计划ID
    /// </summary>    
    public long? PlanId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>    
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>    
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 药店库存数量
    /// </summary>    
    public int? PharmacyQuantity { get; set; }
    
    /// <summary>
    /// 药库库存数量
    /// </summary>    
    public int? StorageQuantity { get; set; }
    
    /// <summary>
    /// 当前销售数量
    /// </summary>    
    public int? CurrentSaleQuantity { get; set; }
    
    /// <summary>
    /// 上次销售数量
    /// </summary>    
    public int? LastSaleQuantity { get; set; }
    
    /// <summary>
    /// 平均销售数量
    /// </summary>    
    public int? AverageSaleQuantity { get; set; }
    
    /// <summary>
    /// 计划采购数量
    /// </summary>    
    public int? Quantity { get; set; }
    
    /// <summary>
    /// 采购单价
    /// </summary>    
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 总采购价
    /// </summary>    
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 生产厂家
    /// </summary>    
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "生产厂家名称字符长度不能超过100")]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>    
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "供应商名称字符长度不能超过100")]
    public string? SupplierName { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>    
    public int? Status { get; set; }
    
}

/// <summary>
/// 采购计划明细表主键查询输入参数
/// </summary>
public class QueryByIdDrugPurchasePlanDetailInput : DeleteDrugPurchasePlanDetailInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataDrugPurchasePlanDetailInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 采购计划明细表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugPurchasePlanDetailInput : BaseImportInput
{
    /// <summary>
    /// 采购计划ID
    /// </summary>
    [ImporterHeader(Name = "采购计划ID")]
    [ExporterHeader("采购计划ID", Format = "", Width = 25, IsBold = true)]
    public long? PlanId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [ImporterHeader(Name = "药品ID")]
    [ExporterHeader("药品ID", Format = "", Width = 25, IsBold = true)]
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [ImporterHeader(Name = "药品编码")]
    [ExporterHeader("药品编码", Format = "", Width = 25, IsBold = true)]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [ImporterHeader(Name = "药品名称")]
    [ExporterHeader("药品名称", Format = "", Width = 25, IsBold = true)]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类型 文本
    /// </summary>
    [Dict("DrugType")]
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string DrugTypeDictLabel { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [ImporterHeader(Name = "规格")]
    [ExporterHeader("规格", Format = "", Width = 25, IsBold = true)]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [ImporterHeader(Name = "单位")]
    [ExporterHeader("单位", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 药店库存数量
    /// </summary>
    [ImporterHeader(Name = "药店库存数量")]
    [ExporterHeader("药店库存数量", Format = "", Width = 25, IsBold = true)]
    public int? PharmacyQuantity { get; set; }
    
    /// <summary>
    /// 药库库存数量
    /// </summary>
    [ImporterHeader(Name = "药库库存数量")]
    [ExporterHeader("药库库存数量", Format = "", Width = 25, IsBold = true)]
    public int? StorageQuantity { get; set; }
    
    /// <summary>
    /// 当前销售数量
    /// </summary>
    [ImporterHeader(Name = "当前销售数量")]
    [ExporterHeader("当前销售数量", Format = "", Width = 25, IsBold = true)]
    public int? CurrentSaleQuantity { get; set; }
    
    /// <summary>
    /// 上次销售数量
    /// </summary>
    [ImporterHeader(Name = "上次销售数量")]
    [ExporterHeader("上次销售数量", Format = "", Width = 25, IsBold = true)]
    public int? LastSaleQuantity { get; set; }
    
    /// <summary>
    /// 平均销售数量
    /// </summary>
    [ImporterHeader(Name = "平均销售数量")]
    [ExporterHeader("平均销售数量", Format = "", Width = 25, IsBold = true)]
    public int? AverageSaleQuantity { get; set; }
    
    /// <summary>
    /// 计划采购数量
    /// </summary>
    [ImporterHeader(Name = "计划采购数量")]
    [ExporterHeader("计划采购数量", Format = "", Width = 25, IsBold = true)]
    public int? Quantity { get; set; }
    
    /// <summary>
    /// 采购单价
    /// </summary>
    [ImporterHeader(Name = "采购单价")]
    [ExporterHeader("采购单价", Format = "", Width = 25, IsBold = true)]
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 总采购价
    /// </summary>
    [ImporterHeader(Name = "总采购价")]
    [ExporterHeader("总采购价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 生产厂家 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家 文本
    /// </summary>
    [ImporterHeader(Name = "生产厂家")]
    [ExporterHeader("生产厂家", Format = "", Width = 25, IsBold = true)]
    public string ManufacturerFkDisplayName { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    [ImporterHeader(Name = "生产厂家名称")]
    [ExporterHeader("生产厂家名称", Format = "", Width = 25, IsBold = true)]
    public string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 供应商ID 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商ID 文本
    /// </summary>
    [ImporterHeader(Name = "供应商ID")]
    [ExporterHeader("供应商ID", Format = "", Width = 25, IsBold = true)]
    public string SupplierFkDisplayName { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    [ImporterHeader(Name = "供应商名称")]
    [ExporterHeader("供应商名称", Format = "", Width = 25, IsBold = true)]
    public string? SupplierName { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    [ImporterHeader(Name = "状态（0 未处理 1 处理中 2 已完成等）")]
    [ExporterHeader("状态（0 未处理 1 处理中 2 已完成等）", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
}
