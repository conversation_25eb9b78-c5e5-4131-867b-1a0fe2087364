﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品剂型表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_form", "药品剂型表")]
public class DrugForm : EntityTenant
{
    /// <summary>
    /// 剂型名称
    /// </summary>
    [SugarColumn(ColumnName = "form_name", ColumnDescription = "剂型名称", Length = 100)]
    public virtual string? FormName { get; set; }
    
    /// <summary>
    /// 剂型名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "form_name_pinyin", ColumnDescription = "剂型名称拼音", Length = 100)]
    public virtual string? FormNamePinyin { get; set; }
    
    /// <summary>
    /// 大剂型ID
    /// </summary>
    [SugarColumn(ColumnName = "big_form_id", ColumnDescription = "大剂型ID")]
    public virtual long? BigFormId { get; set; }
    
    /// <summary>
    /// 大剂型名称
    /// </summary>
    [SugarColumn(ColumnName = "big_form_name", ColumnDescription = "大剂型名称", Length = 100)]
    public virtual string? BigFormName { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "1 启用 2 停用")]
    public virtual int? Status { get; set; }
    
}
