﻿using System.Data;
using Microsoft.AspNetCore.Http;

namespace His.Module.Shared.Service;

/// <summary>
/// 收费项目单位服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class ChargeItemUnitService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ChargeItemUnit> _chargeItemUnitRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public ChargeItemUnitService(SqlSugarRepository<ChargeItemUnit> chargeItemUnitRep, ISqlSugarClient sqlSugarClient)
    {
        _chargeItemUnitRep = chargeItemUnitRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询收费项目单位 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询收费项目单位")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<ChargeItemUnitOutput>> Page(PageChargeItemUnitInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.UnitCode = input.UnitCode?.Trim();
        input.UnitName = input.UnitName?.Trim().ToLower();
        var query = _chargeItemUnitRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.UnitCode.Contains(input.Keyword)
            || u.UnitName.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.UnitCode), u => u.UnitCode.Contains(input.UnitCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.UnitName), u => u.UnitName.Contains(input.UnitName)
            || u.PinyinCode.Contains(input.UnitName)
            || u.WubiCode.Contains(input.UnitName))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select<ChargeItemUnitOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 添加收费项目单位数据
    /// </summary>
    /// <returns></returns>
    [DisplayName("添加收费项目单位数据")]
    [ApiDescriptionSettings(Name = "AddChargeItemUnit"), HttpPost]
    public async Task<bool> AddChargeItemUnit()
    {
        var dataTable = await _sqlSugarClient.Ado.GetDataTableAsync("SELECT * FROM \"shared\".\"XT_DICNODRUGUNIT\"");
        var list = new List<ChargeItemUnit>();
        foreach (DataRow item in dataTable.Rows)
        {
            var entity = new ChargeItemUnit
            {
                UnitCode = await _chargeItemUnitRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('charge_item_unit_code_seq')As varchar),4,'0')"),
                UnitName = item["VITEMNAME"].ToString(),
                Status = 1,
                OrderNo = 100
            };
            entity.PinyinCode = TextUtil.GetFirstPinyin(entity.UnitName);
            entity.WubiCode = TextUtil.GetFirstWuBi(entity.UnitName);
            list.Add(entity);
        }
        return await _chargeItemUnitRep.InsertRangeAsync(list);
    }

    /// <summary>
    /// 获取收费项目单位详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取收费项目单位详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<ChargeItemUnit> Detail([FromQuery] QueryByIdChargeItemUnitInput input)
    {
        return await _chargeItemUnitRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加收费项目单位 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加收费项目单位")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddChargeItemUnitInput input)
    {
        var entity = input.Adapt<ChargeItemUnit>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.UnitName);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.UnitName);
        return await _chargeItemUnitRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新收费项目单位 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新收费项目单位")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateChargeItemUnitInput input)
    {
        var entity = input.Adapt<ChargeItemUnit>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.UnitName);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.UnitName);
        await _chargeItemUnitRep.AsUpdateable(entity)
        .IgnoreColumns(u => new
        {
            u.UnitCode,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除收费项目单位 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除收费项目单位")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteChargeItemUnitInput input)
    {
        var entity = await _chargeItemUnitRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _chargeItemUnitRep.FakeDeleteAsync(entity);   //假删除
        //await _chargeItemUnitRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除收费项目单位 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除收费项目单位")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteChargeItemUnitInput> input)
    {
        var exp = Expressionable.Create<ChargeItemUnit>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _chargeItemUnitRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _chargeItemUnitRep.FakeDeleteAsync(list);   //假删除
        //return await _chargeItemUnitRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置收费项目单位状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置收费项目单位状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetChargeItemUnitStatus(SetChargeItemUnitStatusInput input)
    {
        await _chargeItemUnitRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 导出收费项目单位记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出收费项目单位记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageChargeItemUnitInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportChargeItemUnitOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "收费项目单位导出记录");
    }

    /// <summary>
    /// 下载收费项目单位数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载收费项目单位数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportChargeItemUnitOutput>(), "收费项目单位导入模板");
    }

    /// <summary>
    /// 导入收费项目单位记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入收费项目单位记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportChargeItemUnitInput, ChargeItemUnit>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        return true;
                    }).Adapt<List<ChargeItemUnit>>();

                    var storageable = _chargeItemUnitRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.UnitCode?.Length > 64, "单位编码长度不能超过64个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.UnitName), "单位名称不能为空")
                        .SplitError(it => it.Item.UnitName?.Length > 64, "单位名称长度不能超过64个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 64, "拼音码长度不能超过64个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 64, "五笔码长度不能超过64个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}