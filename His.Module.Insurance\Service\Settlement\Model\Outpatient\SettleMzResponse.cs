using His.Module.Insurance.Service.Settlement.Dto;

namespace His.Module.Insurance.Service.Settlement.Model.Outpatient;

/// <summary>
/// 门诊预结算接口返回结果
/// </summary>
public class SettleMzResponse:SettleMzPreResponse
{
    /// <summary>
    /// 医保系统的病人结算号（唯一标识）
    /// </summary>
    public string jshid { get; set; }

    /// <summary>
    /// 国标结算号ID（全国统一标识）
    /// </summary>
    public string gbjshid { get; set; }

    /// <summary>
    /// 医疗统筹登记号
    /// </summary>
    public string yltcdjh { get; set; }

    /// <summary>
    /// 门诊结算类型
    /// </summary>
    public string mzjslx { get; set; }

    /// <summary>
    /// 结算单（Base64编码的PDF）
    /// </summary>
    public string report { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    public string fph { get; set; }

    /// <summary>
    /// 发票人员类别
    /// </summary>
    public string fprylb { get; set; }

    /// <summary>
    /// 优抚对象减免金额
    /// </summary>
    public double jmje { get; set; }

    /// <summary>
    /// 个人账户支付金额
    /// </summary>
    public double grzhzf { get; set; }

    /// <summary>
    /// 本次起付线
    /// </summary>
    public double bcqfx { get; set; }

    /// <summary>
    /// 本次进入统筹额度
    /// </summary>
    public double bcnrtcfw { get; set; }

    /// <summary>
    /// 本年进入统筹额度
    /// </summary>
    public double bnynrtcfw { get; set; }

    /// <summary>
    /// 累计统筹支付
    /// </summary>
    public double ljtczf { get; set; }

    /// <summary>
    /// 累计大额支付
    /// </summary>
    public double ljdezf { get; set; }

    /// <summary>
    /// 累计门诊额度
    /// </summary>
    public double ljmzed { get; set; }

    /// <summary>
    /// 累积个人支付
    /// </summary>
    public double ljgrzf { get; set; }

    /// <summary>
    /// 其他统筹支付
    /// </summary>
    public double qttczf { get; set; }

    /// <summary>
    /// 财政列支
    /// </summary>
    public double czlz { get; set; }

    /// <summary>
    /// 大额商业保险
    /// </summary>
    public double desybx { get; set; }

    /// <summary>
    /// 大额支付
    /// </summary>
    public double dezf { get; set; }

    /// <summary>
    /// 公务员补助
    /// </summary>
    public double gwybz { get; set; }

    /// <summary>
    /// 大病补助金额
    /// </summary>
    public double dbbzje { get; set; }

    /// <summary>
    /// 单病种医保结算金额（淄博专用）
    /// </summary>
    public double dbzybjsje { get; set; }

    /// <summary>
    /// 单病种医院负担金额（淄博专用）
    /// </summary>
    public double dbzyyfdje { get; set; }

    /// <summary>
    /// 单病种个人负担金额（淄博专用）
    /// </summary>
    public double dbzgrfdje { get; set; }

    /// <summary>
    /// 医疗机构减免（东营/威海专用）
    /// </summary>
    public double fpryyljgjm { get; set; }

    /// <summary>
    /// 商业兜底/贫困人口补充报销（潍坊/东营/威海）
    /// </summary>
    public double pkrkbcbxje { get; set; }

    /// <summary>
    /// 卫计帮扶金额（济南专用）
    /// </summary>
    public double wjbfje { get; set; }

    /// <summary>
    /// 民政补助金额（济南专用）
    /// </summary>
    public double mzbzje { get; set; }

    /// <summary>
    /// 民政救助（威海专用）
    /// </summary>
    public double mzbzje2 { get; set; }

    /// <summary>
    /// 民政优抚（威海专用）
    /// </summary>
    public double yfbzy { get; set; }

    /// <summary>
    /// 贫困人口医疗机构减免金额（枣庄专用）
    /// </summary>
    public double pkrkyljgjmje { get; set; }

    /// <summary>
    /// 贫困人口再救助
    /// </summary>
    public double pkryzjzje { get; set; }

    /// <summary>
    /// 职工补充支付（济南专用）
    /// </summary>
    public double bczf { get; set; }

    /// <summary>
    /// 重大疾病救助额
    /// </summary>
    public double zdjbjzbxje { get; set; }

    /// <summary>
    /// 重大疾病再救助额
    /// </summary>
    public double zdjbzjzbxje { get; set; }

    /// <summary>
    /// 合计救助金额
    /// </summary>
    public double bchjjze { get; set; }

    /// <summary>
    /// 精神病救助保险报销额（东营专用）
    /// </summary>
    public double jsbjzzf { get; set; }

    /// <summary>
    /// 生育产前检查费（潍坊专用）
    /// </summary>
    public double cqjcf { get; set; }

    /// <summary>
    /// 暂缓支付
    /// </summary>
    public double zhzf { get; set; }

    /// <summary>
    /// 重特大疾病救助
    /// </summary>
    public double ztdjbzf { get; set; }

    /// <summary>
    /// 政府托底救助
    /// </summary>
    public double zftd { get; set; }

    /// <summary>
    /// 重度残疾人托底救助
    /// </summary>
    public double cjrtd { get; set; }

    /// <summary>
    /// 老党员托底救助
    /// </summary>
    public double ldytdjzbxje { get; set; }

    /// <summary>
    /// 老党员报销金额
    /// </summary>
    public double ldybxje { get; set; }

    /// <summary>
    /// 惠民保报销金额目录内
    /// </summary>
    public double hmbbxjemln { get; set; }

    /// <summary>
    /// 惠民保报销金额目录外
    /// </summary>
    public double hmbbxjemlw { get; set; }

    /// <summary>
    /// 惠民保报销总金额
    /// </summary>
    public double hmbbxje { get; set; }

    /// <summary>
    /// 跨省异地医疗补助金额
    /// </summary>
    public double ksydylbzje { get; set; }

    /// <summary>
    /// 部分自付
    /// </summary>
    public double bfzf { get; set; }

    /// <summary>
    /// 全额自付
    /// </summary>
    public double qezf { get; set; }

    /// <summary>
    /// 省异地个人账户支付
    /// </summary>
    public double sydgrzhzf { get; set; }

    /// <summary>
    /// 电子钱包支付金额
    /// </summary>
    public double dzqbzfje { get; set; }
}