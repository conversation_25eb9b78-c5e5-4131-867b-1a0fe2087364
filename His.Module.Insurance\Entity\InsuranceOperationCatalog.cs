﻿using Admin.NET.Core;

namespace His.Module.Insurance.Entity;

/// <summary>
/// 医保手术目录
/// </summary>
[Tenant("1300000000013")]
[SugarTable("insurance_operation_catalog", "医保手术目录")]
public class InsuranceOperationCatalog : EntityTenant
{
    /// <summary>
    /// 手术编码
    /// </summary>
    [SugarColumn(ColumnName = "ss_bm", ColumnDescription = "手术编码", Length = 50)]
    public virtual string SsBm { get; set; }
    
    /// <summary>
    /// 手术名称
    /// </summary>
    [SugarColumn(ColumnName = "ss_mc", ColumnDescription = "手术名称", Length = 200)]
    public virtual string SsMc { get; set; }
    
    /// <summary>
    /// 手术名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "py", ColumnDescription = "手术名称拼音", Length = 100)]
    public virtual string? Py { get; set; }
    
    /// <summary>
    /// 注销标志
    /// </summary>
    [SugarColumn(ColumnName = "zx_bz", ColumnDescription = "注销标志", Length = 10)]
    public virtual string? ZxBz { get; set; }
    
    /// <summary>
    /// 最后同步时间
    /// </summary>
    [SugarColumn(ColumnName = "last_sync_time", ColumnDescription = "最后同步时间")]
    public virtual DateTime? LastSyncTime { get; set; }
    
    /// <summary>
    /// 手术级别
    /// </summary>
    [SugarColumn(ColumnName = "ss_jb", ColumnDescription = "手术级别", Length = 10)]
    public virtual string? SsJb { get; set; }
    
    /// <summary>
    /// 手术类别
    /// </summary>
    [SugarColumn(ColumnName = "ss_lb", ColumnDescription = "手术类别", Length = 10)]
    public virtual string? SsLb { get; set; }
}
