﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

 

/// <summary>
/// 企业管理分页查询输入参数
/// </summary>
public class PageEnterpriseDictionaryInput : BasePageInput
{
    /// <summary>
    /// 企业编码
    /// </summary>
    public string? EnterpriseCode { get; set; }
    
    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EnterpriseName { get; set; }
    
    /// <summary>
    /// 企业类型
    /// </summary>
    [Dict("EnterpriseType", AllowNullValue=true)]
    public string? EnterpriseType { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 企业管理增加输入参数
/// </summary>
public class AddEnterpriseDictionaryInput
{
    /// <summary>
    /// 企业编码
    /// </summary>
    [Required(ErrorMessage = "企业编码不能为空")]
    [MaxLength(100, ErrorMessage = "企业编码字符长度不能超过100")]
    public string? EnterpriseCode { get; set; }
    
    /// <summary>
    /// 企业名称
    /// </summary>
    [Required(ErrorMessage = "企业名称不能为空")]
    [MaxLength(100, ErrorMessage = "企业名称字符长度不能超过100")]
    public string? EnterpriseName { get; set; }
    
    /// <summary>
    /// 企业名称拼音
    /// </summary>
    [MaxLength(100, ErrorMessage = "企业名称拼音字符长度不能超过100")]
    public string? EnterpriseNamePinyin { get; set; }
    
    /// <summary>
    /// 企业类型
    /// </summary>
    [Dict("EnterpriseType", AllowNullValue=true)]
    [Required(ErrorMessage = "企业类型不能为空")]
    [MaxLength(50, ErrorMessage = "企业类型字符长度不能超过50")]
    public string? EnterpriseType { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
       
    /// <summary>
    ///  联系人
    /// </summary> 
    public virtual string? ContactName { get; set; }
    /// <summary>
    ///  联系人
    /// </summary> 
    public virtual string? ContactPhone{ get; set; }
    
    /// <summary>
    ///  企业地址
    /// </summary> 
    public virtual string? EnterpriseAddress { get; set; }
}

/// <summary>
/// 企业管理删除输入参数
/// </summary>
public class DeleteEnterpriseDictionaryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 企业管理更新输入参数
/// </summary>
public class UpdateEnterpriseDictionaryInput : AddEnterpriseDictionaryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
     
    
}

/// <summary>
/// 企业管理主键查询输入参数
/// </summary>
public class QueryByIdEnterpriseDictionaryInput : DeleteEnterpriseDictionaryInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetEnterpriseDictionaryStatusInput : BaseStatusInput
{
}

/// <summary>
/// 企业管理数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportEnterpriseDictionaryInput : BaseImportInput
{
    /// <summary>
    /// 企业编码
    /// </summary>
    [ImporterHeader(Name = "*企业编码")]
    [ExporterHeader("*企业编码", Format = "", Width = 25, IsBold = true)]
    public string? EnterpriseCode { get; set; }
    
    /// <summary>
    /// 企业名称
    /// </summary>
    [ImporterHeader(Name = "*企业名称")]
    [ExporterHeader("*企业名称", Format = "", Width = 25, IsBold = true)]
    public string? EnterpriseName { get; set; }
    
    /// <summary>
    /// 企业名称拼音
    /// </summary>
    [ImporterHeader(Name = "企业名称拼音")]
    [ExporterHeader("企业名称拼音", Format = "", Width = 25, IsBold = true)]
    public string? EnterpriseNamePinyin { get; set; }
    
    /// <summary>
    /// 企业类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? EnterpriseType { get; set; }
    
    /// <summary>
    /// 企业类型 文本
    /// </summary>
    [Dict("enterprise_type")]
    [ImporterHeader(Name = "*企业类型")]
    [ExporterHeader("*企业类型", Format = "", Width = 25, IsBold = true)]
    public string EnterpriseTypeDictLabel { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
}
