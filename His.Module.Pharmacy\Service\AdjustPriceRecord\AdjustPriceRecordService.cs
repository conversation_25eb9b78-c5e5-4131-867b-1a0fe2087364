﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品调价记录服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class AdjustPriceRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<AdjustPriceRecord> _adjustPriceRecordRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    private readonly InventoryService _inventoryService;

    public AdjustPriceRecordService(SqlSugarRepository<AdjustPriceRecord> adjustPriceRecordRep,
        ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService,
        InventoryService inventoryService
        )
    {
        _adjustPriceRecordRep = adjustPriceRecordRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
        _inventoryService= inventoryService;
    }

    /// <summary>
    /// 分页查询药品调价记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品调价记录")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<AdjustPriceRecordOutput>> Page(PageAdjustPriceRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _adjustPriceRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) ||
                     u.Spec.Contains(input.Keyword) || u.Unit.Contains(input.Keyword) ||
                     u.BatchNo.Contains(input.Keyword) || u.ApprovalNumber.Contains(input.Keyword) ||
                     u.MedicineCode.Contains(input.Keyword) || u.ManufacturerName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Spec), u => u.Spec.Contains(input.Spec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BatchNo), u => u.BatchNo.Contains(input.BatchNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApprovalNumber),
                u => u.ApprovalNumber.Contains(input.ApprovalNumber.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicineCode),
                u => u.MedicineCode.Contains(input.MedicineCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ManufacturerName),
                u => u.ManufacturerName.Contains(input.ManufacturerName.Trim()))
            .WhereIF(input.Quantity != null, u => u.Quantity == input.Quantity)
            .WhereIF(input.ProductionDateRange?.Length == 2,
                u => u.ProductionDate >= input.ProductionDateRange[0] &&
                     u.ProductionDate <= input.ProductionDateRange[1])
            .WhereIF(input.ExpirationDateRange?.Length == 2,
                u => u.ExpirationDate >= input.ExpirationDateRange[0] &&
                     u.ExpirationDate <= input.ExpirationDateRange[1])
            .WhereIF(input.ManufacturerId != null, u => u.ManufacturerId == input.ManufacturerId)
            .WhereIF(input.AdjustTimeRange?.Length == 2,
                u => u.AdjustTime >= input.AdjustTimeRange[0] && u.AdjustTime <= input.AdjustTimeRange[1])
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<AdjustPriceRecordOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品调价记录详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品调价记录详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<AdjustPriceRecord> Detail([FromQuery] QueryByIdAdjustPriceRecordInput input)
    {
        return await _adjustPriceRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }
    //
    // /// <summary>
    // /// 增加药品调价记录 ➕
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("增加药品调价记录")]
    // [ApiDescriptionSettings(Name = "Add"), HttpPost]
    // public async Task<long> Add(AddAdjustPriceRecordInput input)
    // {
    //     var entity = input.Adapt<AdjustPriceRecord>();
    //     return await _adjustPriceRecordRep.InsertAsync(entity) ? entity.Id : 0;
    // }

    /// <summary>
    /// 增加药品调价记录 ➕
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
    [DisplayName("增加药品调价记录")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<Boolean> Add(List<AddAdjustPriceRecordInput> list)
    {
        var entityList = list.Adapt<List<AdjustPriceRecord>>();
        foreach (var entity in entityList)
        {
            entity.Status = 0;
            entity.TotalNewSalePrice = entity.NewSalePrice * entity.Quantity;
            entity.TotalOldSalePrice = entity.OldSalePrice * entity.Quantity;
            entity.AdjustPrice= entity.TotalNewSalePrice - entity.TotalOldSalePrice;
           
        }

        return await _adjustPriceRecordRep.InsertRangeAsync(entityList);
    }

    /// <summary>
    /// 提交 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("提交处理单")]
    [ApiDescriptionSettings(Name = "Submit"), HttpPost]
    [UnitOfWork]
    public async Task<bool> Submit(SubmitAdjustPriceRecordInput   input)
    {
        var result = await _adjustPriceRecordRep.GetFirstAsync(u => u.Id == input.Id);

        if (result.Status == 0)
        { 
             await _inventoryService.AdjustPriceAsync(  result);
            
            return await _adjustPriceRecordRep.UpdateAsync(u
                => new AdjustPriceRecord() { Status = 1, AdjustTime = DateTime.Now }, u => u.Id == input.Id);
        }
        else
            throw Oops.Oh("当前状态禁止提交");
    }
    // /// <summary>
    // /// 更新药品调价记录 ✏️
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("更新药品调价记录")]
    // [ApiDescriptionSettings(Name = "Update"), HttpPost]
    // public async Task Update(UpdateAdjustPriceRecordInput input)
    // {
    //     var entity = input.Adapt<AdjustPriceRecord>();
    //     await _adjustPriceRecordRep.AsUpdateable(entity)
    //         .IgnoreColumns(u => new
    //         {
    //             u.DrugId,
    //             u.ManufacturerId,
    //         })
    //         .ExecuteCommandAsync();
    // }

    /// <summary>
    /// 删除药品调价记录 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品调价记录")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteAdjustPriceRecordInput input)
    {
        var entity = await _adjustPriceRecordRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status != 0)
        {
            throw Oops.Oh("当前状态禁止删除");
        }

        await _adjustPriceRecordRep.FakeDeleteAsync(entity); //假删除
        //await _adjustPriceRecordRep.DeleteAsync(entity);   //真删除
    }

    // /// <summary>
    // /// 批量删除药品调价记录 ❌
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("批量删除药品调价记录")]
    // [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    // public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteAdjustPriceRecordInput> input)
    // {
    //     var exp = Expressionable.Create<AdjustPriceRecord>();
    //     foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
    //     var list = await _adjustPriceRecordRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
    //
    //     return await _adjustPriceRecordRep.FakeDeleteAsync(list); //假删除
    //     //return await _adjustPriceRecordRep.DeleteAsync(list);   //真删除
    // }

    /// <summary>
    /// 导出药品调价记录记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品调价记录记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageAdjustPriceRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportAdjustPriceRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var statusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" })
            .Result.ToDictionary(x => x.Value, x => x.Label);
        // list.ForEach(e => {
        //     e.StatusDictLabel = statusDictMap.GetValueOrDefault(e.Status ?? "", e.Status);
        // });
        return ExcelHelper.ExportTemplate(list, "药品调价记录导出记录");
    }

    /// <summary>
    /// 下载药品调价记录数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品调价记录数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportAdjustPriceRecordOutput>(), "药品调价记录导入模板");
    }

    /// <summary>
    /// 导入药品调价记录记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品调价记录记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var statusDictMap = _sysDictTypeService
                .GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportAdjustPriceRecordInput, AdjustPriceRecord>(file,
                (list, markerErrorAction) =>
                {
                    _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                    {
                        // 映射字典值
                        // foreach(var item in pageItems) {
                        //     if (string.IsNullOrWhiteSpace(item.StatusDictLabel)) continue;
                        //     item.Status = statusDictMap.GetValueOrDefault(item.StatusDictLabel);
                        //     if (item.Status == null) item.Error = "状态字典映射失败";
                        // }

                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x => { return true; }).Adapt<List<AdjustPriceRecord>>();

                        var storageable = _adjustPriceRecordRep.Context.Storageable(rows)
                            .SplitError(it => it.Item.DrugCode?.Length > 100, "药品编码长度不能超过100个字符")
                            .SplitError(it => it.Item.DrugName?.Length > 100, "药品名称长度不能超过100个字符")
                            .SplitError(it => it.Item.Spec?.Length > 100, "规格长度不能超过100个字符")
                            .SplitError(it => it.Item.Unit?.Length > 100, "单位长度不能超过100个字符")
                            .SplitError(it => it.Item.BatchNo?.Length > 100, "批号长度不能超过100个字符")
                            .SplitError(it => it.Item.ApprovalNumber?.Length > 100, "批准文号长度不能超过100个字符")
                            .SplitError(it => it.Item.MedicineCode?.Length > 100, "国家医保编码长度不能超过100个字符")
                            .SplitError(it => it.Item.ManufacturerName?.Length > 100, "生产厂商名称长度不能超过100个字符")
                            .SplitInsert(_ => true)
                            .ToStorage();

                        storageable.BulkCopy();
                        storageable.BulkUpdate();

                        // 标记错误信息
                        markerErrorAction.Invoke(storageable, pageItems, rows);
                    });
                });

            return stream;
        }
    }
}