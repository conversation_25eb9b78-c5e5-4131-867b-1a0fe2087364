﻿namespace His.Module.Insurance.Service.CatalogManagement.Dto;

/// <summary>
/// 医院项目查询输入
/// </summary>
public class HospitalItemQueryInput : BasePageInput
{
    /// <summary>
    /// 关键词（项目编码、名称、拼音）
    /// </summary>
    public new string? Keyword { get; set; }

    /// <summary>
    /// 药品标志
    /// </summary>
    public string? YpBz { get; set; }

    /// <summary>
    /// 险种标志
    /// </summary>
    public string? XzBz { get; set; }

    /// <summary>
    /// 人群类别
    /// </summary>
    public string? RqLb { get; set; }
}

/// <summary>
/// 医保项目查询输入
/// </summary>
public class InsuranceItemQueryInput : BasePageInput
{
    /// <summary>
    /// 关键词（项目编码、名称、拼音）
    /// </summary>
    public new string? Keyword { get; set; }

    /// <summary>
    /// 药品标志
    /// </summary>
    public string? YpBz { get; set; }

    /// <summary>
    /// 注销标志
    /// </summary>
    public string? ZxBz { get; set; }
}

/// <summary>
/// 疾病目录查询输入
/// </summary>
public class SickCatalogQueryInput : BasePageInput
{
    /// <summary>
    /// 关键词（疾病编码、名称、拼音）
    /// </summary>
    public new string? Keyword { get; set; }

    /// <summary>
    /// 门诊大病类别
    /// </summary>
    public string? MzdbLb { get; set; }

    /// <summary>
    /// 注销标志
    /// </summary>
    public string? ZxBz { get; set; }
}

/// <summary>
/// 手术目录查询输入
/// </summary>
public class OperationCatalogQueryInput : BasePageInput
{
    /// <summary>
    /// 关键词（手术编码、名称、拼音）
    /// </summary>
    public new string? Keyword { get; set; }

    /// <summary>
    /// 注销标志
    /// </summary>
    public string? ZxBz { get; set; }
}

/// <summary>
/// 数据字典查询输入
/// </summary>
public class DictionaryQueryInput : BasePageInput
{
    /// <summary>
    /// 代码编号
    /// </summary>
    public string? DmBh { get; set; }

    /// <summary>
    /// 关键词（代码值、字典名称）
    /// </summary>
    public new string? Keyword { get; set; }
}

/// <summary>
/// 目录同步状态DTO
/// </summary>
public class CatalogSyncStatusDto
{
    /// <summary>
    /// 医院项目数量
    /// </summary>
    public int HospitalItemCount { get; set; }

    /// <summary>
    /// 医保项目数量
    /// </summary>
    public int InsuranceItemCount { get; set; }

    /// <summary>
    /// 疾病目录数量
    /// </summary>
    public int SickCatalogCount { get; set; }

    /// <summary>
    /// 手术目录数量
    /// </summary>
    public int OperationCatalogCount { get; set; }

    /// <summary>
    /// 数据字典数量
    /// </summary>
    public int DictionaryCount { get; set; }

    /// <summary>
    /// 医院项目最后同步时间
    /// </summary>
    public DateTime? LastHospitalSync { get; set; }

    /// <summary>
    /// 医保项目最后同步时间
    /// </summary>
    public DateTime? LastInsuranceSync { get; set; }

    /// <summary>
    /// 疾病目录最后同步时间
    /// </summary>
    public DateTime? LastSickSync { get; set; }

    /// <summary>
    /// 手术目录最后同步时间
    /// </summary>
    public DateTime? LastOperationSync { get; set; }

    /// <summary>
    /// 数据字典最后同步时间
    /// </summary>
    public DateTime? LastDictionarySync { get; set; }
}