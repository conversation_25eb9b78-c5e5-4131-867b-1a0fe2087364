﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品分类维护基础输入参数
/// </summary>
public class DrugCategoryBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 分类编码
    /// </summary>
    public virtual string? CategoryCode { get; set; }
    
    /// <summary>
    /// 分类名称
    /// </summary>
    public virtual string? CategoryName { get; set; }
    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 药品分类维护分页查询输入参数
/// </summary>
public class PageDrugCategoryInput : BasePageInput
{
    /// <summary>
    /// 分类编码
    /// </summary>
    public string? CategoryCode { get; set; }
    
    /// <summary>
    /// 分类名称
    /// </summary>
    public string? CategoryName { get; set; }
    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品分类维护增加输入参数
/// </summary>
public class AddDrugCategoryInput
{
    /// <summary>
    /// 分类编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "分类编码字符长度不能超过100")]
    public string? CategoryCode { get; set; }
    
    /// <summary>
    /// 分类名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "分类名称字符长度不能超过100")]
    public string? CategoryName { get; set; }
    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>
    public int? Status { get; set; }
    
}

/// <summary>
/// 药品分类维护删除输入参数
/// </summary>
public class DeleteDrugCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品分类维护更新输入参数
/// </summary>
public class UpdateDrugCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 分类编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "分类编码字符长度不能超过100")]
    public string? CategoryCode { get; set; }
    
    /// <summary>
    /// 分类名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "分类名称字符长度不能超过100")]
    public string? CategoryName { get; set; }
    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>    
    public int? Status { get; set; }
    
}

/// <summary>
/// 药品分类维护主键查询输入参数
/// </summary>
public class QueryByIdDrugCategoryInput : DeleteDrugCategoryInput
{
}

/// <summary>
/// 药品分类维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugCategoryInput : BaseImportInput
{
    /// <summary>
    /// 分类编码
    /// </summary>
    [ImporterHeader(Name = "分类编码")]
    [ExporterHeader("分类编码", Format = "", Width = 25, IsBold = true)]
    public string? CategoryCode { get; set; }
    
    /// <summary>
    /// 分类名称
    /// </summary>
    [ImporterHeader(Name = "分类名称")]
    [ExporterHeader("分类名称", Format = "", Width = 25, IsBold = true)]
    public string? CategoryName { get; set; }
    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>
    [ImporterHeader(Name = "状态（1 启用 2 停用）")]
    [ExporterHeader("状态（1 启用 2 停用）", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
}
