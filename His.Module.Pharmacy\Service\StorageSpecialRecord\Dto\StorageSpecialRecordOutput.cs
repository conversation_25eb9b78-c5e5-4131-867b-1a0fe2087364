﻿namespace His.Module.Pharmacy;

/// <summary>
/// 特殊处理记输出参数
/// </summary>
public class StorageSpecialRecordOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }    
    
    /// <summary>
    /// 库房 描述
    /// </summary>
    public string StorageFkDisplayName { get; set; } 
    
    /// <summary>
    /// 库房编码
    /// </summary>
    public string? StorageCode { get; set; }    
    
    /// <summary>
    /// 库房名称
    /// </summary>
    public string? StorageName { get; set; }    
    
    /// <summary>
    /// 特殊处理单号
    /// </summary>
    public string? HandleNo { get; set; }    
    
    /// <summary>
    /// 药品类型
    /// </summary>
    public string? DrugType { get; set; }    
    
    /// <summary>
    /// 供应商
    /// </summary>
    public long? SupplierId { get; set; }    
    
    /// <summary>
    /// 供应商 描述
    /// </summary>
    public string SupplierFkDisplayName { get; set; } 
    
    /// <summary>
    /// 供应商编码
    /// </summary>
    public string? SupplierCode { get; set; }    
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    public string? SupplierName { get; set; }    
    
    /// <summary>
    /// 处理类型
    /// </summary>
    public string? HandleType { get; set; }    
    
    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime? HandleTime { get; set; }    
    
    /// <summary>
    /// 总进价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }    
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }    
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 特殊处理记数据导入模板实体
/// </summary>
public class ExportStorageSpecialRecordOutput : ImportStorageSpecialRecordInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
