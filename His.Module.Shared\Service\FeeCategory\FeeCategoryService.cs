﻿using Microsoft.AspNetCore.Http;

namespace His.Module.Shared.Service;

/// <summary>
/// 费用类别服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class FeeCategoryService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<FeeCategory> _feeCategoryRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public FeeCategoryService(SqlSugarRepository<FeeCategory> feeCategoryRep, ISqlSugarClient sqlSugarClient)
    {
        _feeCategoryRep = feeCategoryRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询费用类别 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询费用类别")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<FeeCategoryOutput>> Page(PageFeeCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.Code = input.Code?.Trim();
        input.Name = input.Name?.Trim().ToLower();
        var query = _feeCategoryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
            || u.Name.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select<FeeCategoryOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取费用类别详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取费用类别详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<FeeCategory> Detail([FromQuery] QueryByIdFeeCategoryInput input)
    {
        return await _feeCategoryRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取费用类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    [DisplayName("获取费用类别列表")]
    public async Task<List<FeeCategory>> List()
    {
        return await _feeCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 增加费用类别 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加费用类别")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddFeeCategoryInput input)
    {
        var entity = input.Adapt<FeeCategory>();
        entity.Code = await _feeCategoryRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('fee_category_code_seq')As varchar),3,'0')");
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        return await _feeCategoryRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新费用类别 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新费用类别")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateFeeCategoryInput input)
    {
        var entity = input.Adapt<FeeCategory>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _feeCategoryRep.AsUpdateable(entity)
        .IgnoreColumns(u => new
        {
            u.Code,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除费用类别 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除费用类别")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteFeeCategoryInput input)
    {
        var entity = await _feeCategoryRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _feeCategoryRep.FakeDeleteAsync(entity);   //假删除
        //await _feeCategoryRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除费用类别 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除费用类别")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteFeeCategoryInput> input)
    {
        var exp = Expressionable.Create<FeeCategory>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _feeCategoryRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _feeCategoryRep.FakeDeleteAsync(list);   //假删除
        //return await _feeCategoryRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置费用类别状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置费用类别状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetFeeCategoryStatus(SetFeeCategoryStatusInput input)
    {
        await _feeCategoryRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 导出费用类别记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出费用类别记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageFeeCategoryInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportFeeCategoryOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "费用类别导出记录");
    }

    /// <summary>
    /// 下载费用类别数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载费用类别数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportFeeCategoryOutput>(), "费用类别导入模板");
    }

    /// <summary>
    /// 导入费用类别记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入费用类别记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportFeeCategoryInput, FeeCategory>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.MedCategory == null)
                        {
                            x.Error = "医疗类别不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.UsageScope == null)
                        {
                            x.Error = "使用范围不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<FeeCategory>>();

                    var storageable = _feeCategoryRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Code?.Length > 32, "编号长度不能超过32个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.Name), "名称不能为空")
                        .SplitError(it => it.Item.Name?.Length > 32, "名称长度不能超过32个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 20, "拼音码长度不能超过20个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 20, "五笔码长度不能超过20个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}