// using His.Module.Financial.Entity;
// using His.Module.Financial.Enum;
// using His.Module.Financial.OtherModuleEntity;
// using System.Text.Json;
// namespace His.Module.Financial.Service;
//
// /// <summary>
// /// 收款员结算服务辅助类
// /// </summary>
// public class CashierSettlementServiceHelp(
//     SqlSugarRepository<ChargeMain> chargeMainRep,
//     SqlSugarRepository<ChargeDetail> chargeDetailRep,
//     SqlSugarRepository<MedicalCardRecharge> medicalCardRechargeRep,
//     SqlSugarRepository<CashierSettlement> cashierSettlementRep,
//     ISqlSugarClient sqlSugarClient)
// {
//     /// <summary>
//     /// 数据完整性校验
//     /// </summary>
//     private async Task ValidateDataIntegrity(CashierStatisticsInput input, DataValidationResult result)
//     {
//         // 检查是否有孤立的收费明细（没有对应的收费主记录）
//         var orphanDetails = await chargeDetailRep.AsQueryable()
//             .LeftJoin<ChargeMain>((d, m) => d.ChargeId == m.Id)
//             .Where((d, m) => m.Id == null)
//             .Where(d => d.CreateTime >= input.StartDate && d.CreateTime <= input.EndDate)
//             .CountAsync();
//
//         if (orphanDetails > 0)
//         {
//             result.ValidationErrors.Add($"发现 {orphanDetails} 条孤立的收费明细记录");
//             result.DataIntegrityCheck = false;
//         }
//
//         // 检查是否有金额为0的收费记录
//         var zeroAmountCharges = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.Status == 1 && (u.TotalAmount == null || u.TotalAmount == 0))
//             .CountAsync();
//
//         if (zeroAmountCharges > 0) result.Warnings.Add($"发现 {zeroAmountCharges} 条金额为0的收费记录");
//
//         // 检查是否有缺失患者信息的记录
//         var missingPatientCharges = await chargeMainRep.AsQueryable()
//             .LeftJoin<PatientInfo>((c, p) => c.PatientId == p.Id)
//             .Where((c, p) => c.CreateTime >= input.StartDate && c.CreateTime <= input.EndDate)
//             .Where((c, p) => p.Id == null)
//             .CountAsync();
//
//         if (missingPatientCharges > 0)
//         {
//             result.ValidationErrors.Add($"发现 {missingPatientCharges} 条缺失患者信息的收费记录");
//             result.DataIntegrityCheck = false;
//         }
//     }
//
//     /// <summary>
//     /// 金额一致性校验
//     /// </summary>
//     private async Task ValidateAmountConsistency(CashierStatisticsInput input, DataValidationResult result)
//     {
//         // 检查收费主表金额与明细金额是否一致
//         var inconsistentCharges = await sqlSugarClient.Queryable<ChargeMain>()
//             .LeftJoin<ChargeDetail>((m, d) => m.Id == d.ChargeId)
//             .Where((m, d) => m.CreateTime >= input.StartDate && m.CreateTime <= input.EndDate)
//             .Where((m, d) => m.Status == 1)
//             .GroupBy((m, d) => new
//             {
//                 m.Id, m.TotalAmount
//             })
//             .Having((m, d) => SqlFunc.AggregateSum(d.Amount) != SqlFunc.AggregateAvg(m.TotalAmount))
//             .CountAsync();
//
//         if (inconsistentCharges > 0)
//         {
//             result.ValidationErrors.Add($"发现 {inconsistentCharges} 条收费主表与明细表金额不一致的记录");
//             result.AmountConsistencyCheck = false;
//         }
//
//         // 检查支付方式金额是否与总金额一致
//         var inconsistentPayments = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.Status == 1)
//             .Where(u => (u.PayAmount1 ?? 0) + (u.PayAmount2 ?? 0) != u.TotalAmount)
//             .CountAsync();
//
//         if (inconsistentPayments > 0)
//         {
//             result.ValidationErrors.Add($"发现 {inconsistentPayments} 条支付方式金额与总金额不一致的记录");
//             result.AmountConsistencyCheck = false;
//         }
//     }
//
//     /// <summary>
//     /// 业务逻辑校验
//     /// </summary>
//     private async Task ValidateBusinessLogic(CashierStatisticsInput input, DataValidationResult result)
//     {
//         // 检查是否有退费金额大于原收费金额的情况
//         var excessiveRefunds = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.Status == 4) // 退费状态
//             .Where(u => u.TotalAmount > 0) // 退费金额应该为负数或者业务逻辑处理
//             .CountAsync();
//
//         if (excessiveRefunds > 0) result.Warnings.Add($"发现 {excessiveRefunds} 条可能的异常退费记录");
//
//         // 检查是否有同一患者在短时间内多次收费的情况
//         var rapidCharges = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.Status == 1)
//             .GroupBy(u => u.PatientId)
//             .Having(u => SqlFunc.AggregateCount(u.Id) > 10) // 一天内超过10次收费
//             .CountAsync();
//
//         if (rapidCharges > 0) result.Warnings.Add($"发现 {rapidCharges} 个患者在统计期间内收费次数异常频繁");
//     }
//
//     /// <summary>
//     /// 检查跨日期数据
//     /// </summary>
//     private async Task CheckCrossDateData(CashierStatisticsInput input, List<ExceptionDataInfo> exceptions)
//     {
//         // 检查业务时间与创建时间跨日期的情况
//         var crossDateCharges = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.CreateTime.Date != u.CreateTime.Date) // 这里需要根据实际业务时间字段调整
//             .Select(u => new
//             {
//                 u.Id, u.CreateTime, u.PatientId
//             })
//             .ToListAsync();
//
//         foreach (var charge in crossDateCharges)
//             exceptions.Add(new ExceptionDataInfo
//             {
//                 ExceptionType = 1,
//                 ExceptionDesc = "跨日期收费数据",
//                 BusinessId = charge.Id,
//                 BusinessType = 1,
//                 ExceptionDetail = $"收费时间跨越自然日期：{charge.CreateTime}",
//                 SuggestedAction = "确认业务发生时间，必要时调整结算日期"
//             });
//     }
//
//     /// <summary>
//     /// 检查状态异常数据
//     /// </summary>
//     private async Task CheckStatusAnomalies(CashierStatisticsInput input, List<ExceptionDataInfo> exceptions)
//     {
//         // 检查状态异常的收费记录
//         var abnormalStatusCharges = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.Status != 1 && u.Status != 4) // 非正常收费和退费状态
//             .Select(u => new
//             {
//                 u.Id, u.Status, u.InvoiceNumber
//             })
//             .ToListAsync();
//
//         foreach (var charge in abnormalStatusCharges)
//             exceptions.Add(new ExceptionDataInfo
//             {
//                 ExceptionType = 2,
//                 ExceptionDesc = "状态异常的收费记录",
//                 BusinessId = charge.Id,
//                 BusinessType = 1,
//                 ExceptionDetail = $"发票号：{charge.InvoiceNumber}，状态：{charge.Status}",
//                 SuggestedAction = "检查业务流程，确认记录状态是否正确"
//             });
//
//         // 检查状态异常的充值记录
//         var abnormalStatusRecharges = await medicalCardRechargeRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.Status != CardRechargeStatusEnum.Recharge && u.Status != (CardRechargeStatusEnum)4)
//             .Select(u => new
//             {
//                 u.Id, u.Status, u.InvoiceNumber
//             })
//             .ToListAsync();
//
//         foreach (var recharge in abnormalStatusRecharges)
//             exceptions.Add(new ExceptionDataInfo
//             {
//                 ExceptionType = 2,
//                 ExceptionDesc = "状态异常的充值记录",
//                 BusinessId = recharge.Id,
//                 BusinessType = 3,
//                 ExceptionDetail = $"发票号：{recharge.InvoiceNumber}，状态：{recharge.Status}",
//                 SuggestedAction = "检查充值流程，确认记录状态是否正确"
//             });
//     }
//
//     /// <summary>
//     /// 检查金额异常数据
//     /// </summary>
//     private async Task CheckAmountAnomalies(CashierStatisticsInput input, List<ExceptionDataInfo> exceptions)
//     {
//         // 检查异常大额收费（超过10000元）
//         var largeAmountCharges = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.Status == 1 && u.TotalAmount > 10000)
//             .Select(u => new
//             {
//                 u.Id, u.TotalAmount, u.InvoiceNumber
//             })
//             .ToListAsync();
//
//         foreach (var charge in largeAmountCharges)
//             exceptions.Add(new ExceptionDataInfo
//             {
//                 ExceptionType = 3,
//                 ExceptionDesc = "大额收费记录",
//                 BusinessId = charge.Id,
//                 BusinessType = 1,
//                 ExceptionDetail = $"发票号：{charge.InvoiceNumber}，金额：{charge.TotalAmount}",
//                 SuggestedAction = "确认大额收费是否正确，是否需要特殊审批"
//             });
//
//         // 检查负数金额的收费记录
//         var negativeAmountCharges = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartDate && u.CreateTime <= input.EndDate)
//             .Where(u => u.Status == 1 && u.TotalAmount < 0)
//             .Select(u => new
//             {
//                 u.Id, u.TotalAmount, u.InvoiceNumber
//             })
//             .ToListAsync();
//
//         foreach (var charge in negativeAmountCharges)
//             exceptions.Add(new ExceptionDataInfo
//             {
//                 ExceptionType = 3,
//                 ExceptionDesc = "负数金额收费记录",
//                 BusinessId = charge.Id,
//                 BusinessType = 1,
//                 ExceptionDetail = $"发票号：{charge.InvoiceNumber}，金额：{charge.TotalAmount}",
//                 SuggestedAction = "检查收费逻辑，负数金额应该使用退费流程"
//             });
//     }
//
//     /// <summary>
//     /// 记录操作日志
//     /// </summary>
//     private async Task LogOperation(long settlementId, int operationType, string operationDesc,
//         object beforeData = null, object afterData = null, string reason = null,
//         List<long> affectedBusinessIds = null, int result = 1, string errorMessage = null,
//         long duration = 0)
//     {
//         var log = new CashierSettlementLog
//         {
//             SettlementId = settlementId,
//             OperationType = operationType,
//             OperationDesc = operationDesc,
//             BeforeData = beforeData != null ? JsonSerializer.Serialize(beforeData) : null,
//             AfterData = afterData != null ? JsonSerializer.Serialize(afterData) : null,
//             OperationReason = reason,
//             AffectedBusinessIds = affectedBusinessIds != null ? JsonSerializer.Serialize(affectedBusinessIds) : null,
//             OperationResult = result,
//             ErrorMessage = errorMessage,
//             OperationDuration = duration,
//             ClientIp = "", // 这里需要从HttpContext获取
//             UserAgent = "" // 这里需要从HttpContext获取
//         };
//
//         await cashierSettlementLogRep.InsertAsync(log);
//     }
//
//     /// <summary>
//     /// 自动修复数据问题
//     /// </summary>
//     private async Task AutoFixDataIssues(DataValidationInput input, DataValidationResult result)
//     {
//         // 修复孤立的收费明细
//         var orphanDetails = await sqlSugarClient.Queryable<ChargeDetail>()
//             .LeftJoin<ChargeMain>((d, m) => d.ChargeId == m.Id)
//             .Where((d, m) => m.Id == null)
//             .Where(d => d.CreateTime >= input.StartTime && d.CreateTime <= input.EndTime)
//             .Select(d => d.Id)
//             .ToListAsync();
//
//         if (orphanDetails.Count > 0)
//         {
//             // 删除孤立的明细记录
//             await sqlSugarClient.Deleteable<ChargeDetail>()
//                 .Where(d => orphanDetails.Contains(d.Id))
//                 .ExecuteCommandAsync();
//
//             result.Warnings.Add($"已自动删除 {orphanDetails.Count} 条孤立的收费明细记录");
//         }
//
//         // 修复金额为0的收费记录
//         var zeroAmountCharges = await chargeMainRep.AsQueryable()
//             .Where(u => u.CreateTime >= input.StartTime && u.CreateTime <= input.EndTime)
//             .Where(u => u.Status == 1 && (u.TotalAmount == null || u.TotalAmount == 0))
//             .Select(u => u.Id)
//             .ToListAsync();
//
//         if (zeroAmountCharges.Count > 0)
//         {
//             // 将金额为0的记录状态改为无效
//             await chargeMainRep.AsUpdateable()
//                 .SetColumns(u => u.Status == 0) // 设为无效状态
//                 .Where(u => zeroAmountCharges.Contains(u.Id))
//                 .ExecuteCommandAsync();
//
//             result.Warnings.Add($"已自动处理 {zeroAmountCharges.Count} 条金额为0的收费记录");
//         }
//     }
//
//     /// <summary>
//     /// 校验补录数据
//     /// </summary>
//     public async Task ValidateSupplementData(DataSupplementInput input)
//     {
//         foreach (var businessId in input.BusinessIds)
//         {
//             var exists = false;
//
//             switch (input.BusinessType)
//             {
//                 case 1: // 收费
//                 case 2: // 退费
//                     exists = await chargeMainRep.IsAnyAsync(u => u.Id == businessId);
//                     break;
//                 case 3: // 充值
//                 case 4: // 退卡
//                     exists = await medicalCardRechargeRep.IsAnyAsync(u => u.Id == businessId);
//                     break;
//             }
//
//             if (!exists) throw Oops.Oh($"业务数据ID {businessId} 不存在");
//         }
//     }
//
//     
//
//     /// <summary>
//     /// 验证原业务数据
//     /// </summary>
//     private async Task ValidateOriginalBusinessData(ReversalProcessInput input)
//     {
//         var exists = false;
//
//         switch (input.BusinessType)
//         {
//             case 1: // 收费
//             case 2: // 退费
//                 exists = await chargeMainRep.IsAnyAsync(u => u.Id == input.OriginalBusinessId);
//                 break;
//             case 3: // 充值
//             case 4: // 退卡
//                 exists = await medicalCardRechargeRep.IsAnyAsync(u => u.Id == input.OriginalBusinessId);
//                 break;
//         }
//
//         if (!exists) throw Oops.Oh($"原业务数据ID {input.OriginalBusinessId} 不存在");
//     }
//
//     /// <summary>
//     /// 创建冲正记录
//     /// </summary>
//     private async Task CreateReversalRecord(ReversalProcessInput input)
//     {
//         switch (input.BusinessType)
//         {
//             case 1: // 收费冲正
//                 await CreateChargeReversalRecord(input);
//                 break;
//             case 2: // 退费冲正
//                 await CreateRefundReversalRecord(input);
//                 break;
//             case 3: // 充值冲正
//                 await CreateRechargeReversalRecord(input);
//                 break;
//             case 4: // 退卡冲正
//                 await CreateCardRefundReversalRecord(input);
//                 break;
//         }
//     }
//
//     /// <summary>
//     /// 创建收费冲正记录
//     /// </summary>
//     private async Task CreateChargeReversalRecord(ReversalProcessInput input)
//     {
//         var originalCharge = await chargeMainRep.GetByIdAsync(input.OriginalBusinessId);
//         if (originalCharge == null) return;
//
//         // 创建冲正记录（退费记录）
//         var reversalCharge = new ChargeMain
//         {
//             PatientId = originalCharge.PatientId,
//             RegisterId = originalCharge.RegisterId,
//             InvoiceNumber = $"REV-{originalCharge.InvoiceNumber}",
//             RefundInvoiceNumber = originalCharge.InvoiceNumber,
//             TotalAmount = -input.ReversalAmount, // 负数表示冲正
//             Status = 4, // 退费状态
//             Remark = input.ReversalReason,
//             PayMethod1Id = originalCharge.PayMethod1Id,
//             PayAmount1 = -originalCharge.PayAmount1,
//             PayMethod2Id = originalCharge.PayMethod2Id,
//             PayAmount2 = -originalCharge.PayAmount2
//         };
//
//         await chargeMainRep.InsertAsync(reversalCharge);
//     }
//
//     /// <summary>
//     /// 创建退费冲正记录
//     /// </summary>
//     private async Task CreateRefundReversalRecord(ReversalProcessInput input)
//     {
//         // 退费的冲正就是重新收费
//         var originalRefund = await chargeMainRep.GetByIdAsync(input.OriginalBusinessId);
//         if (originalRefund == null) return;
//
//         var reversalCharge = new ChargeMain
//         {
//             PatientId = originalRefund.PatientId,
//             RegisterId = originalRefund.RegisterId,
//             InvoiceNumber = $"REV-{originalRefund.InvoiceNumber}",
//             TotalAmount = input.ReversalAmount,
//             Status = 1, // 收费状态
//             Remark = input.ReversalReason,
//             PayMethod1Id = originalRefund.PayMethod1Id,
//             PayAmount1 = originalRefund.PayAmount1,
//             PayMethod2Id = originalRefund.PayMethod2Id,
//             PayAmount2 = originalRefund.PayAmount2
//         };
//
//         await chargeMainRep.InsertAsync(reversalCharge);
//     }
//
//     /// <summary>
//     /// 创建充值冲正记录
//     /// </summary>
//     private async Task CreateRechargeReversalRecord(ReversalProcessInput input)
//     {
//         var originalRecharge = await medicalCardRechargeRep.GetByIdAsync(input.OriginalBusinessId);
//         if (originalRecharge == null) return;
//
//         // 创建退卡记录
//         var reversalRecharge = new MedicalCardRecharge
//         {
//             PatientId = originalRecharge.PatientId,
//             CardId = originalRecharge.CardId,
//             CardNo = originalRecharge.CardNo,
//             InvoiceNumber = $"REV-{originalRecharge.InvoiceNumber}",
//             PayAmount = -input.ReversalAmount, // 负数表示冲正
//             Status = (CardRechargeStatusEnum)4, // 退卡状态
//             Remark = input.ReversalReason,
//             PayMethodId = originalRecharge.PayMethodId
//         };
//
//         await medicalCardRechargeRep.InsertAsync(reversalRecharge);
//     }
//
//     /// <summary>
//     /// 创建退卡冲正记录
//     /// </summary>
//     private async Task CreateCardRefundReversalRecord(ReversalProcessInput input)
//     {
//         // 退卡的冲正就是重新充值
//         var originalCardRefund = await medicalCardRechargeRep.GetByIdAsync(input.OriginalBusinessId);
//         if (originalCardRefund == null) return;
//
//         var reversalRecharge = new MedicalCardRecharge
//         {
//             PatientId = originalCardRefund.PatientId,
//             CardId = originalCardRefund.CardId,
//             CardNo = originalCardRefund.CardNo,
//             InvoiceNumber = $"REV-{originalCardRefund.InvoiceNumber}",
//             PayAmount = input.ReversalAmount,
//             Status = CardRechargeStatusEnum.Recharge, // 充值状态
//             Remark = input.ReversalReason,
//             PayMethodId = originalCardRefund.PayMethodId
//         };
//
//         await medicalCardRechargeRep.InsertAsync(reversalRecharge);
//     }
//
//     /// <summary>
//     /// 更新相关结算记录
//     /// </summary>
//     private async Task UpdateRelatedSettlement(ReversalProcessInput input)
//     {
//         if (!input.RelatedSettlementId.HasValue) return;
//
//         var settlement = await cashierSettlementRep.GetByIdAsync(input.RelatedSettlementId.Value);
//         if (settlement == null) return;
//
//         // 标记需要重新计算
//         settlement.Status = 3; // 需重新计算
//         settlement.HasException = true;
//         settlement.ExceptionNote = $"冲正处理影响: {input.ReversalReason}";
//         settlement.Version += 1;
//
//         await cashierSettlementRep.UpdateAsync(settlement);
//
//         // 重新计算结算数据
//         await RecalculateSettlementData(input.RelatedSettlementId.Value);
//     }
//
//     /// <summary>
//     /// 更新充值记录的结算标记
//     /// </summary>
//     private async Task UpdateRechargeSettlementFlags(long cashierId, DateTime settlementDate, long settlementId)
//     {
//         var startTime = settlementDate.Date;
//         var endTime = startTime.AddDays(1).AddSeconds(-1);
//
//         // 更新充值记录的日结状态（包括退卡）
//         await medicalCardRechargeRep.AsUpdateable()
//             .SetColumns(u => new MedicalCardRecharge
//             {
//                 DailySettle = 1, DailySettleId = settlementId
//             })
//             .Where(u => u.CreateUserId == cashierId &&
//                         u.CreateTime >= startTime &&
//                         u.CreateTime <= endTime &&
//                         (u.Status == CardRechargeStatusEnum.Recharge || u.Status == (CardRechargeStatusEnum)4)) // 充值和退卡
//             .ExecuteCommandAsync();
//     }
//
//     /// <summary>
//     /// 更新收费记录的结算标记
//     /// </summary>
//     private async Task UpdateChargeSettlementFlags(long cashierId, DateTime settlementDate, long settlementId)
//     {
//         var startTime = settlementDate.Date;
//         var endTime = startTime.AddDays(1).AddSeconds(-1);
//
//         // 更新收费记录的日结状态（包括退费）
//         await chargeMainRep.AsUpdateable()
//             .SetColumns(u => new ChargeMain
//             {
//                 DailySettle = 1, DailySettleId = settlementId
//             })
//             .Where(u => u.CreateUserId == cashierId &&
//                         u.CreateTime >= startTime &&
//                         u.CreateTime <= endTime &&
//                         (u.Status == 1 || u.Status == 4)) // 收费和退费
//             .ExecuteCommandAsync();
//     }
// }
