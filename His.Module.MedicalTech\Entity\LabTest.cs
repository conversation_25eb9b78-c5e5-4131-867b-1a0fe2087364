﻿namespace His.Module.MedicalTech.Entity;

/// <summary>
/// 检验表
/// </summary>
[Tenant("1300000000009")]
[SugarTable("lab_test", "检验表")]
public class LabTest : EntityTenantBaseData
{
    /// <summary>
    /// 申请单号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "apply_no", ColumnDescription = "申请单号", Length = 64)]
    public virtual string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "就诊Id")]
    public virtual long RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊流水号", Length = 64)]
    public virtual string VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 64)]
    public virtual string OutpatientNo { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "就诊卡号", Length = 64)]
    public virtual string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 64)]
    public virtual string PatientName { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    [SugarColumn(ColumnName = "item_id", ColumnDescription = "项目Id")]
    public virtual long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [SugarColumn(ColumnName = "item_code", ColumnDescription = "项目编码", Length = 64)]
    public virtual string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnName = "item_name", ColumnDescription = "项目名称", Length = 64)]
    public virtual string? ItemName { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 64)]
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "单价", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Amount { get; set; }

    /// <summary>
    /// 样本类型
    /// </summary>
    [SugarColumn(ColumnName = "sample_type", ColumnDescription = "样本名称", Length = 100)]
    public virtual string? SampleType { get; set; }

    /// <summary>
    /// 紧急程度 0:普通,1:急,2:明晨急
    /// </summary>
    [SugarColumn(ColumnName = "urgency_level", ColumnDescription = "紧急程度 0:普通,1:急,2:明晨急")]
    public virtual string? UrgencyLevel { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "国家医保编码", Length = 100)]
    public virtual string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>
    [SugarColumn(ColumnName = "nationalstandard_code", ColumnDescription = "国标编码", Length = 100)]
    public virtual string? NationalstandardCode { get; set; }

    /// <summary>
    /// 状态 字典 ApplyStatus
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 字典 ApplyStatus")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    [SugarColumn(ColumnName = "is_package", ColumnDescription = "是否套餐")]
    public virtual int? IsPackage { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    [SugarColumn(ColumnName = "flag", ColumnDescription = "门诊住院标识 0门诊 1住院")]
    public virtual int? Flag { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    [SugarColumn(ColumnName = "billing_time", ColumnDescription = "开单时间")]
    public virtual DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_id", ColumnDescription = "开单科室Id")]
    public virtual long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_name", ColumnDescription = "开单科室名称", Length = 64)]
    public virtual string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_id", ColumnDescription = "开单医生Id")]
    public virtual long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_name", ColumnDescription = "开单医生名称", Length = 64)]
    public virtual string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [SugarColumn(ColumnName = "execute_time", ColumnDescription = "执行时间")]
    public virtual DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_id", ColumnDescription = "执行科室Id")]
    public virtual long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_name", ColumnDescription = "执行科室名称", Length = 64)]
    public virtual string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_address", ColumnDescription = "执行科室地址", Length = 100)]
    public virtual string? ExecuteDeptAddress { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_doctor_id", ColumnDescription = "执行医生Id")]
    public virtual long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    [SugarColumn(ColumnName = "execute_doctor_name", ColumnDescription = "执行医生名称", Length = 64)]
    public virtual string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_staff_id", ColumnDescription = "收费人员Id")]
    public virtual long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费人员名称
    /// </summary>
    [SugarColumn(ColumnName = "charge_staff_name", ColumnDescription = "收费人员名称", Length = 64)]
    public virtual string? ChargeStaffName { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    [SugarColumn(ColumnName = "charge_time", ColumnDescription = "收费时间")]
    public virtual DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    [SugarColumn(ColumnName = "self_pay_ratio", ColumnDescription = "自付比例", Length = 4, DecimalDigits = 4)]
    public virtual decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    [SugarColumn(ColumnName = "is_ratio_audit", ColumnDescription = "自付比例是否审核 1审核 2不审核")]
    public virtual int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_time", ColumnDescription = "自付比例审核时间")]
    public virtual DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人员Id
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_staff_id", ColumnDescription = "自付比例审核人员Id")]
    public virtual long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人员名称
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_staff_name", ColumnDescription = "自付比例审核人员名称", Length = 64)]
    public virtual string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 医生签名
    /// </summary>
    [SugarColumn(ColumnName = "doctor_sign", ColumnDescription = "医生签名", Length = 0)]
    public virtual string? DoctorSign { get; set; }

    /// <summary>
    /// 医嘱Id
    /// </summary>
    [SugarColumn(ColumnName = "medical_advice_id", ColumnDescription = "医嘱Id")]
    public virtual long? MedicalAdviceId { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_id", ColumnDescription = "收费类别Id")]
    public virtual long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 套餐项目
    /// </summary>

    [Navigate(NavigateType.OneToMany, nameof(Entity.LabTestPackageItem.LabTestId))]
    public virtual List<LabTestPackageItem> LabTestPackageItem { get; set; }
}