﻿namespace His.Module.Inpatient;

/// <summary>
/// 住院预约管理服务 🧩
/// </summary>
[ApiDescriptionSettings(InpatientConst.GroupName, Order = 100)]
public class AppointmentRecordService : IDynamicApi<PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<AppointmentRecord> _appointmentRecordRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public AppointmentRecordService(SqlSugarRepository<AppointmentRecord> appointmentRecordRep, ISqlSugarClient sqlSugarClient)
    {
        _appointmentRecordRep = appointmentRecordRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询住院预约管理 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询住院预约管理")]
    [ApiDescriptionSettings(Name = "Page")][HttpPost]
    public async Task<SqlSugarPagedList<AppointmentRecordOutput>> Page(PageAppointmentRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _appointmentRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.PatientName.Contains(input.Keyword) || u.AgeUnit.Contains(input.Keyword) || u.IdCardNo.Contains(input.Keyword) || u.Nation.Contains(input.Keyword) || u.Phone.Contains(input.Keyword) || u.ContactName.Contains(input.Keyword) || u.ContactRelationship.Contains(input.Keyword) || u.ContactAddress.Contains(input.Keyword) || u.ContactPhone.Contains(input.Keyword) || u.Nationality.Contains(input.Keyword) || u.Occupation.Contains(input.Keyword) || u.Marriage.Contains(input.Keyword) || u.ResidenceAddress.Contains(input.Keyword) || u.WorkAddress.Contains(input.Keyword) || u.WorkPlace.Contains(input.Keyword) || u.WorkPlacePhone.Contains(input.Keyword) || u.MedicalCardNo.Contains(input.Keyword) || u.OutpatientNo.Contains(input.Keyword) || u.VisitNo.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.WardName.Contains(input.Keyword) || u.TeamName.Contains(input.Keyword) || u.DoctorName.Contains(input.Keyword) || u.ReceivingDoctorName.Contains(input.Keyword) || u.InpatientWay.Contains(input.Keyword) || u.SettlementCategory.Contains(input.Keyword) || u.AdmissionDiagnosisCode.Contains(input.Keyword) || u.PregnancyRiskLevel.Contains(input.Keyword) || u.HighRiskFactors.Contains(input.Keyword) || u.Remark.Contains(input.Keyword) || u.AdmissionDiagnosisName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AgeUnit), u => u.AgeUnit.Contains(input.AgeUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardNo), u => u.IdCardNo.Contains(input.IdCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Nation), u => u.Nation.Contains(input.Nation.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), u => u.Phone.Contains(input.Phone.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactName), u => u.ContactName.Contains(input.ContactName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactRelationship), u => u.ContactRelationship.Contains(input.ContactRelationship.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactAddress), u => u.ContactAddress.Contains(input.ContactAddress.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactPhone), u => u.ContactPhone.Contains(input.ContactPhone.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Nationality), u => u.Nationality.Contains(input.Nationality.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Occupation), u => u.Occupation.Contains(input.Occupation.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Marriage), u => u.Marriage.Contains(input.Marriage.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ResidenceAddress), u => u.ResidenceAddress.Contains(input.ResidenceAddress.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.WorkAddress), u => u.WorkAddress.Contains(input.WorkAddress.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.WorkPlace), u => u.WorkPlace.Contains(input.WorkPlace.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.WorkPlacePhone), u => u.WorkPlacePhone.Contains(input.WorkPlacePhone.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicalCardNo), u => u.MedicalCardNo.Contains(input.MedicalCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo), u => u.OutpatientNo.Contains(input.OutpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo.Contains(input.VisitNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeptName), u => u.DeptName.Contains(input.DeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.WardName), u => u.WardName.Contains(input.WardName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TeamName), u => u.TeamName.Contains(input.TeamName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DoctorName), u => u.DoctorName.Contains(input.DoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ReceivingDoctorName), u => u.ReceivingDoctorName.Contains(input.ReceivingDoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientWay), u => u.InpatientWay.Contains(input.InpatientWay.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SettlementCategory), u => u.SettlementCategory.Contains(input.SettlementCategory.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AdmissionDiagnosisCode), u => u.AdmissionDiagnosisCode.Contains(input.AdmissionDiagnosisCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PregnancyRiskLevel), u => u.PregnancyRiskLevel.Contains(input.PregnancyRiskLevel.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.HighRiskFactors), u => u.HighRiskFactors.Contains(input.HighRiskFactors.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AdmissionDiagnosisName), u => u.AdmissionDiagnosisName.Contains(input.AdmissionDiagnosisName.Trim()))
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.Sex != null, u => u.Sex == input.Sex)
            .WhereIF(input.Age != null, u => u.Age == input.Age)
            .WhereIF(input.BirthdayRange?.Length == 2, u => u.Birthday >= input.BirthdayRange[0] && u.Birthday <= input.BirthdayRange[1])
            .WhereIF(input.CardType != null, u => u.CardType == input.CardType)
            .WhereIF(input.NativePlaceProvince != null, u => u.NativePlaceProvince == input.NativePlaceProvince)
            .WhereIF(input.NativePlaceCity != null, u => u.NativePlaceCity == input.NativePlaceCity)
            .WhereIF(input.NativePlaceCounty != null, u => u.NativePlaceCounty == input.NativePlaceCounty)
            .WhereIF(input.BirthplaceProvince != null, u => u.BirthplaceProvince == input.BirthplaceProvince)
            .WhereIF(input.BirthplaceCity != null, u => u.BirthplaceCity == input.BirthplaceCity)
            .WhereIF(input.BirthplaceCounty != null, u => u.BirthplaceCounty == input.BirthplaceCounty)
            .WhereIF(input.ResidenceProvince != null, u => u.ResidenceProvince == input.ResidenceProvince)
            .WhereIF(input.ResidenceCity != null, u => u.ResidenceCity == input.ResidenceCity)
            .WhereIF(input.ResidenceCounty != null, u => u.ResidenceCounty == input.ResidenceCounty)
            .WhereIF(input.WorkProvince != null, u => u.WorkProvince == input.WorkProvince)
            .WhereIF(input.WorkCity != null, u => u.WorkCity == input.WorkCity)
            .WhereIF(input.WorkCounty != null, u => u.WorkCounty == input.WorkCounty)
            .WhereIF(input.AppointmentTimeRange?.Length == 2, u => u.AppointmentTime >= input.AppointmentTimeRange[0] && u.AppointmentTime <= input.AppointmentTimeRange[1])
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.WardId != null, u => u.WardId == input.WardId)
            .WhereIF(input.TeamId != null, u => u.TeamId == input.TeamId)
            .WhereIF(input.DoctorId != null, u => u.DoctorId == input.DoctorId)
            .WhereIF(input.ReceivingDoctorId != null, u => u.ReceivingDoctorId == input.ReceivingDoctorId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<AppointmentRecordOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取住院预约管理详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取住院预约管理详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<AppointmentRecord> Detail([FromQuery] QueryByIdAppointmentRecordInput input)
    {
        return await _appointmentRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加住院预约管理 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加住院预约管理")]
    [ApiDescriptionSettings(Name = "Add")][HttpPost]
    public async Task<long> Add(AddAppointmentRecordInput input)
    {
        var entity = input.Adapt<AppointmentRecord>();
        entity.Status = 0;
        return await _appointmentRecordRep.InsertAsync(entity) ? entity.Id : 0;
    }


    /// <summary>
    /// 预约记录确认
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [DisplayName("预约记录确认")]
    [ApiDescriptionSettings(Name = "Confirm")][HttpPost]
    public async Task Confirm(ConfirmAppointmentRecordInput input)
    {
        var entity = await _appointmentRecordRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status == 1) throw Oops.Oh("该预约记录已登记");
        entity.Status = 1;
        await _appointmentRecordRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 更新住院预约管理 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新住院预约管理")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateAppointmentRecordInput input)
    {
        var entity = input.Adapt<AppointmentRecord>();
        await _appointmentRecordRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除住院预约管理 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除住院预约管理")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteAppointmentRecordInput input)
    {
        var entity = await _appointmentRecordRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _appointmentRecordRep.FakeDeleteAsync(entity); //假删除
        //await _appointmentRecordRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除住院预约管理 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除住院预约管理")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteAppointmentRecordInput> input)
    {
        var exp = Expressionable.Create<AppointmentRecord>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _appointmentRecordRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _appointmentRecordRep.FakeDeleteAsync(list); //假删除
        //return await _appointmentRecordRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出住院预约管理记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出住院预约管理记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageAppointmentRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportAppointmentRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "住院预约管理导出记录");
    }
    
    /// <summary>
    /// 下载住院预约管理数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载住院预约管理数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportAppointmentRecordOutput>(), "住院预约管理导入模板");
    }
    
    /// <summary>
    /// 导入住院预约管理记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入住院预约管理记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportAppointmentRecordInput, AppointmentRecord>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {

                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.Sex == null)
                        {
                            x.Error = "性别不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.Age == null)
                        {
                            x.Error = "年龄不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.CardType == null)
                        {
                            x.Error = "证件类型不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.ResidenceProvince == null)
                        {
                            x.Error = "现居住地省不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.ResidenceCity == null)
                        {
                            x.Error = "现居住地市不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.ResidenceCounty == null)
                        {
                            x.Error = "现居住地县不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<AppointmentRecord>>();

                    var storageable = _appointmentRecordRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.PatientName?.Length > 100, "患者姓名长度不能超过100个字符")
                        .SplitError(it => it.Item.AgeUnit?.Length > 32, "年龄单位长度不能超过32个字符")
                        .SplitError(it => it.Item.IdCardNo?.Length > 32, "身份证号长度不能超过32个字符")
                        .SplitError(it => it.Item.Nation?.Length > 16, "民族长度不能超过16个字符")
                        .SplitError(it => it.Item.Phone?.Length > 16, "电话号码长度不能超过16个字符")
                        .SplitError(it => it.Item.ContactName?.Length > 32, "联系人姓名长度不能超过32个字符")
                        .SplitError(it => it.Item.ContactRelationship?.Length > 16, "联系人关系长度不能超过16个字符")
                        .SplitError(it => it.Item.ContactAddress?.Length > 64, "联系人地址长度不能超过64个字符")
                        .SplitError(it => it.Item.ContactPhone?.Length > 16, "联系人电话号码长度不能超过16个字符")
                        .SplitError(it => it.Item.Nationality?.Length > 16, "国籍长度不能超过16个字符")
                        .SplitError(it => it.Item.Occupation?.Length > 16, "职业长度不能超过16个字符")
                        .SplitError(it => it.Item.Marriage?.Length > 16, "婚姻长度不能超过16个字符")
                        .SplitError(it => it.Item.ResidenceAddress?.Length > 128, "详细现居住地长度不能超过128个字符")
                        .SplitError(it => it.Item.WorkAddress?.Length > 128, "详细工作地址长度不能超过128个字符")
                        .SplitError(it => it.Item.WorkPlace?.Length > 128, "工作单位长度不能超过128个字符")
                        .SplitError(it => it.Item.WorkPlacePhone?.Length > 16, "单位电话长度不能超过16个字符")
                        .SplitError(it => it.Item.MedicalCardNo?.Length > 100, "就诊卡号长度不能超过100个字符")
                        .SplitError(it => it.Item.OutpatientNo?.Length > 100, "门诊号长度不能超过100个字符")
                        .SplitError(it => it.Item.VisitNo?.Length > 100, "就诊号长度不能超过100个字符")
                        .SplitError(it => it.Item.DeptName?.Length > 100, "预约科室名称长度不能超过100个字符")
                        .SplitError(it => it.Item.WardName?.Length > 100, "预约病区名称长度不能超过100个字符")
                        .SplitError(it => it.Item.TeamName?.Length > 100, "预约诊疗组名称长度不能超过100个字符")
                        .SplitError(it => it.Item.DoctorName?.Length > 100, "预约医生姓名长度不能超过100个字符")
                        .SplitError(it => it.Item.ReceivingDoctorName?.Length > 100, "接诊医生名称长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientWay?.Length > 32, "入院途径InpatientWayType长度不能超过32个字符")
                        .SplitError(it => it.Item.SettlementCategory?.Length > 100, "结算类别长度不能超过100个字符")
                        .SplitError(it => it.Item.AdmissionDiagnosisCode?.Length > 100, "入院诊断编号长度不能超过100个字符")
                        .SplitError(it => it.Item.PregnancyRiskLevel?.Length > 32, "妊娠风险评估长度不能超过32个字符")
                        .SplitError(it => it.Item.HighRiskFactors?.Length > 256, "高危因素长度不能超过256个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitError(it => it.Item.AdmissionDiagnosisName?.Length > 100, "入院诊断名称长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
