global using System;
global using System.ComponentModel;
global using System.ComponentModel.DataAnnotations;
global using System.Threading.Tasks;
global using Admin.NET.Core;
global using Furion;
global using Furion.DatabaseAccessor;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using His.Module.Patient.Const;
global using His.Module.Patient.Entity;
global using Mapster;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.Extensions.DependencyInjection;
global using SqlSugar;