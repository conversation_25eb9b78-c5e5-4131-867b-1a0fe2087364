﻿using Admin.NET.Core;
namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 退费审核表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("refund_audit", "退费审核表")]
public class RefundAudit : EntityTenant
{
    /// <summary>
    /// 退费申请ID
    /// </summary>
    [SugarColumn(ColumnName = "apply_id", ColumnDescription = "退费申请ID")]
    public virtual long? ApplyId { get; set; }
    
    /// <summary>
    /// 审核时间
    /// </summary>
    [SugarColumn(ColumnName = "audit_time", ColumnDescription = "审核时间")]
    public virtual DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 审核部门ID
    /// </summary>
    [SugarColumn(ColumnName = "audit_dept_id", ColumnDescription = "审核部门ID")]
    public virtual long? AuditDeptId { get; set; }
    
    /// <summary>
    /// 审核部门编码
    /// </summary>
    [SugarColumn(ColumnName = "audit_dept_code", ColumnDescription = "审核部门编码", Length = 100)]
    public virtual string? AuditDeptCode { get; set; }
    
    /// <summary>
    /// 审核部门名称
    /// </summary>
    [SugarColumn(ColumnName = "audit_dept_name", ColumnDescription = "审核部门名称", Length = 100)]
    public virtual string? AuditDeptName { get; set; }
    
    /// <summary>
    /// 审核人ID
    /// </summary>
    [SugarColumn(ColumnName = "audit_user_id", ColumnDescription = "审核人ID")]
    public virtual long? AuditUserId { get; set; }
    
    /// <summary>
    /// 审核人编码
    /// </summary>
    [SugarColumn(ColumnName = "audit_user_code", ColumnDescription = "审核人编码", Length = 100)]
    public virtual string? AuditUserCode { get; set; }
    
    /// <summary>
    /// 审核人名称
    /// </summary>
    [SugarColumn(ColumnName = "audit_user_name", ColumnDescription = "审核人名称", Length = 100)]
    public virtual string? AuditUserName { get; set; }
    
    /// <summary>
    /// 审核原因
    /// </summary>
    [SugarColumn(ColumnName = "audit_reason", ColumnDescription = "审核原因", Length = 200)]
    public virtual string? AuditReason { get; set; }
    
    /// <summary>
    /// 审核状态
    /// </summary>
    [SugarColumn(ColumnName = "audit_status", ColumnDescription = "审核状态")]
    public virtual int? AuditStatus { get; set; }
    
    /// <summary>
    /// 审核流程ID
    /// </summary>
    [SugarColumn(ColumnName = "flow_id", ColumnDescription = "审核流程ID")]
    public virtual long? FlowId { get; set; }
    
    /// <summary>
    /// 审核流程排序
    /// </summary>
    [SugarColumn(ColumnName = "flow_sort", ColumnDescription = "审核流程排序")]
    public virtual int? FlowSort { get; set; }
    
    /// <summary>
    /// 审核流程角色ID
    /// </summary>
    [SugarColumn(ColumnName = "flow_role_id", ColumnDescription = "审核流程角色ID")]
    public virtual long? FlowRoleId { get; set; }
    /// <summary>
    /// 审核流程角色名称
    /// </summary>
    [SugarColumn(ColumnName = "flow_role_code", ColumnDescription = "审核流程角色名称", Length = 100)]
    public virtual string? FlowRoleCode { get; set; }
    
    /// <summary>
    /// 审核流程角色名称
    /// </summary>
    [SugarColumn(ColumnName = "flow_role_name", ColumnDescription = "审核流程角色名称", Length = 100)]
    public virtual string? FlowRoleName { get; set; }
    
    /// <summary>
    /// 审核流程审核人ID
    /// </summary>
    [SugarColumn(ColumnName = "flow_user_id", ColumnDescription = "审核流程审核人ID")]
    public virtual long? FlowUserId { get; set; }
    
    /// <summary>
    /// 审核流程审核人名称
    /// </summary>
    [SugarColumn(ColumnName = "flow_user_name", ColumnDescription = "审核流程审核人名称", Length = 100)]
    public virtual string? FlowUserName { get; set; }
    
    /// <summary>
    /// 创建机构ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构ID")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
