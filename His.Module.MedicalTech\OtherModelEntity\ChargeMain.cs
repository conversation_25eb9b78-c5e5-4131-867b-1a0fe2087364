﻿namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 收费主表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("charge_main", "收费主表")]
public class ChargeMain : EntityTenantBaseData
{


    /// <summary>
    /// 患者Id
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long PatientId { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "就诊Id")]
    public virtual long RegisterId { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    [SugarColumn(ColumnName = "invoice_number", ColumnDescription = "发票号", Length = 32)]
    public virtual string? InvoiceNumber { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    [SugarColumn(ColumnName = "total_amount", ColumnDescription = "总金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal? TotalAmount { get; set; }

    /// <summary>
    /// 支付渠道
    /// </summary>
    [SugarColumn(ColumnName = "pay_channel", ColumnDescription = "支付渠道")]
    public virtual int? PayChannel { get; set; }

    /// <summary>
    /// 支付方式1Id
    /// </summary>
    [SugarColumn(ColumnName = "pay_method1_id", ColumnDescription = "支付方式1Id")]
    public virtual long? PayMethod1Id { get; set; }

    /// <summary>
    /// 支付方式1金额
    /// </summary>
    [SugarColumn(ColumnName = "pay_amount1", ColumnDescription = "支付方式1金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal? PayAmount1 { get; set; }

    /// <summary>
    /// 支付方式2Id
    /// </summary>
    [SugarColumn(ColumnName = "pay_method2_id", ColumnDescription = "支付方式2Id")]
    public virtual long? PayMethod2Id { get; set; }

    /// <summary>
    /// 支付方式2金额
    /// </summary>
    [SugarColumn(ColumnName = "pay_amount2", ColumnDescription = "支付方式2金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal? PayAmount2 { get; set; }

    /// <summary>
    /// 打印次数
    /// </summary>
    [SugarColumn(ColumnName = "print_number", ColumnDescription = "打印次数")]
    public virtual short? PrintNumber { get; set; }

    /// <summary>
    /// 打印时间
    /// </summary>
    [SugarColumn(ColumnName = "print_time", ColumnDescription = "打印时间")]
    public virtual DateTime? PrintTime { get; set; }

    /// <summary>
    /// 打印者Id
    /// </summary>
    [SugarColumn(ColumnName = "print_user_id", ColumnDescription = "打印者Id")]
    public virtual long? PrintUserId { get; set; }

    /// <summary>
    /// 状态 0未收费 1已收费 2取药 3退药 4退费 5红冲
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 0未收费 1已收费 2取药 3退药 4退费 5红冲")]
    public virtual int Status { get; set; }

    /// <summary>
    /// 是否日结
    /// </summary>
    [SugarColumn(ColumnName = "daily_settle", ColumnDescription = "是否日结")]
    public virtual YesNoEnum? DailySettle { get; set; }

    /// <summary>
    /// 日结Id
    /// </summary>
    [SugarColumn(ColumnName = "daily_settle_id", ColumnDescription = "日结Id")]
    public virtual long? DailySettleId { get; set; }

    /// <summary>
    /// 收费类型 1收费 0挂号
    /// </summary>
    [SugarColumn(ColumnName = "type", ColumnDescription = "收费类型 1收费 0挂号")]
    public virtual short? Type { get; set; }

    /// <summary>
    /// 退费发票号
    /// </summary>
    [SugarColumn(ColumnName = "refund_invoice_number", ColumnDescription = "退费发票号", Length = 32)]
    public virtual string? RefundInvoiceNumber { get; set; }

    // 2025年4月12日 新增 


    /// <summary>
    /// 执行科室Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_id", ColumnDescription = "执行科室")]
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_doctor_id", ColumnDescription = "执行医生")]
    public long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行状态
    /// </summary>
    [SugarColumn(ColumnName = "execute_status", ColumnDescription = "执行状态")]
    public YesNoEnum? ExecuteStatus { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [SugarColumn(ColumnName = "execute_time", ColumnDescription = "执行时间")]
    public DateTime? ExecuteTime { get; set; }

    // 新增属性

    /// <summary>
    /// 卡号
    /// </summary>
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "卡号", Length = 32)]
    [Required(ErrorMessage = "卡号不能为空")]
    public string? CardNo { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 32)]
    [Required(ErrorMessage = "就诊号不能为空")]
    public string? VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 32)]
    [Required(ErrorMessage = "门诊号不能为空")]
    public string? OutpatientNo { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_dept_id", ColumnDescription = "开单科室Id")]
    [Required(ErrorMessage = "开单科室Id不能为空")]
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    [SugarColumn(ColumnName = "billing_doctor_id", ColumnDescription = "开单医生Id")]
    [Required(ErrorMessage = "开单医生Id不能为空")]
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单类型  处方，处置，检验，检查
    /// </summary>
    [SugarColumn(ColumnName = "billing_type", ColumnDescription = "开单类型", Length = 32)]
    [Required(ErrorMessage = "开单类型不能为空")]
    public string? BillingType { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    [SugarColumn(ColumnName = "billing_time", ColumnDescription = "开单时间")]
    [Required(ErrorMessage = "开单时间不能为空")]
    public DateTime? BillingTime { get; set; }

    /// <summary>
    /// 单据Id(处方id)
    /// </summary>
    [SugarColumn(ColumnName = "billing_id", ColumnDescription = "单据Id(处方id)")]
    public virtual long? BillingId { get; set; }

    /// <summary>
    /// 单据号
    /// </summary>
    [SugarColumn(ColumnName = "billing_no", ColumnDescription = "单据号", Length = 64)]
    public virtual string? BillingNo { get; set; }

    /// <summary>
    /// 原始收费记录id
    /// </summary>
    [SugarColumn(ColumnName = "original_record_id", ColumnDescription = "原始收费记录id")]
    public virtual long? OriginalRecordId { get; set; }


    /// <summary>
    /// 退费原因
    /// </summary>
    [SugarColumn(ColumnName = "refund_reason", ColumnDescription = "退费原因")]
    public virtual string? RefundReason { get; set; }


    /// <summary>
    /// 实际收费时间
    /// </summary>
    [SugarColumn(ColumnName = "charge_time", ColumnDescription = "实际收费时间")]

    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 实际收费用户id
    /// </summary>
    [SugarColumn(ColumnName = "charge_user_id", ColumnDescription = "实际收费用户id")]
    public virtual long? ChargeUserId { get; set; }
    /// <summary>
    /// 实际收费用户
    /// </summary>
    [SugarColumn(ColumnName = "charge_user_name", ColumnDescription = "实际收费用户")]
    public virtual string? ChargeUserName { get; set; }
    /// <summary>
    /// 实际收费科室id
    /// </summary>
    [SugarColumn(ColumnName = "charge_dept_id", ColumnDescription = "实际收费科室id")]
    public virtual long? ChargeDeptId { get; set; }
    /// <summary>
    /// 实际收费科室
    /// </summary>
    [SugarColumn(ColumnName = "charge_dept_name", ColumnDescription = "实际收费科室")]
    public virtual string? ChargeDeptName { get; set; }
}