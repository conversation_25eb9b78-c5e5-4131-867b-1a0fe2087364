﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 患者转介表输出参数
/// </summary>
public class PatientReferralDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 患者Id
    /// </summary>
    public long PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 转介前医生Id
    /// </summary>
    public long? BeforeDoctorId { get; set; }
    
    /// <summary>
    /// 转介前医生姓名
    /// </summary>
    public string? BeforeDoctorName { get; set; }
    
    /// <summary>
    /// 转介前科室Id
    /// </summary>
    public long? BeforeDeptId { get; set; }
    
    /// <summary>
    /// 转介前科室名称
    /// </summary>
    public string? BeforeDeptName { get; set; }
    
    /// <summary>
    /// 转介后医生Id
    /// </summary>
    public long? AfterDoctorId { get; set; }
    
    /// <summary>
    /// 转介后医生姓名
    /// </summary>
    public string? AfterDoctorName { get; set; }
    
    /// <summary>
    /// 转介后科室Id
    /// </summary>
    public long? AfterDeptId { get; set; }
    
    /// <summary>
    /// 转介后科室名称
    /// </summary>
    public string? AfterDeptName { get; set; }
    
    /// <summary>
    /// 转介时间
    /// </summary>
    public DateTime? ReferralTime { get; set; }
    
    /// <summary>
    /// 转介原因
    /// </summary>
    public string? ReferralReason { get; set; }
    
    /// <summary>
    /// 转介前挂号记录Id
    /// </summary>
    public long? BeforeRegisterId { get; set; }
    
    /// <summary>
    /// 转介后挂号记录Id
    /// </summary>
    public long? AfterRegisterId { get; set; }
    /// <summary>
    /// 预约挂号时间
    /// </summary>
    public DateTime? RegTime { get; set; }
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    public long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    public string? CreateOrgName { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
