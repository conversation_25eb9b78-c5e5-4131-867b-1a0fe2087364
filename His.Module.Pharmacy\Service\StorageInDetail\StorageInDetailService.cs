﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品入库明细服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class StorageInDetailService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<StorageInDetail> _storageInDetailRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public StorageInDetailService(SqlSugarRepository<StorageInDetail> storageInDetailRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _storageInDetailRep = storageInDetailRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询药品入库明细 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品入库明细")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<StorageInDetailOutput>> Page(PageStorageInDetailInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        if (input.StorageInId == null || input.StorageInId == 0)
        {
            return new SqlSugarPagedList<StorageInDetailOutput>();
        }

        var query = _storageInDetailRep.AsQueryable()
            .Where(u=>u.StorageInId==input.StorageInId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.DrugType.Contains(input.Keyword) || u.StorageInNo.Contains(input.Keyword) || u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageInNo), u => u.StorageInNo.Contains(input.StorageInNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .Select<StorageInDetailOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品入库明细详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品入库明细详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<StorageInDetail> Detail([FromQuery] QueryByIdStorageInDetailInput input)
    {
        return await _storageInDetailRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品入库明细 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品入库明细")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddStorageInDetailInput input)
    {
        var entity = input.Adapt<StorageInDetail>();
        return await _storageInDetailRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品入库明细 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品入库明细")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateStorageInDetailInput input)
    {
        var entity = input.Adapt<StorageInDetail>();
        await _storageInDetailRep.AsUpdateable(entity)
        .IgnoreColumns(u => new {
            u.Status,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品入库明细 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品入库明细")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteStorageInDetailInput input)
    {
        var entity = await _storageInDetailRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _storageInDetailRep.FakeDeleteAsync(entity);   //假删除
        //await _storageInDetailRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品入库明细 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品入库明细")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteStorageInDetailInput> input)
    {
        var exp = Expressionable.Create<StorageInDetail>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _storageInDetailRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _storageInDetailRep.FakeDeleteAsync(list);   //假删除
        //return await _storageInDetailRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出药品入库明细记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品入库明细记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageStorageInDetailInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportStorageInDetailOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Value, x => x.Label);
        var qualityStatusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInQualityStatus" }).Result.ToDictionary(x => x.Value, x => x.Label);
        var acceptanceStatusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInAcceptanceStatus" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType);
            e.QualityStatusDictLabel = qualityStatusDictMap.GetValueOrDefault(e.QualityStatus ?? "", e.QualityStatus);
            e.AcceptanceStatusDictLabel = acceptanceStatusDictMap.GetValueOrDefault(e.AcceptanceStatus ?? "", e.AcceptanceStatus);
        });
        return ExcelHelper.ExportTemplate(list, "药品入库明细导出记录");
    }
    
    /// <summary>
    /// 下载药品入库明细数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品入库明细数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportStorageInDetailOutput>(), "药品入库明细导入模板");
    }
    
    /// <summary>
    /// 导入药品入库明细记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品入库明细记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var qualityStatusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInQualityStatus" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var acceptanceStatusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInAcceptanceStatus" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportStorageInDetailInput, StorageInDetail>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 映射字典值
                    foreach(var item in pageItems) {
                        if (string.IsNullOrWhiteSpace(item.DrugTypeDictLabel)) continue;
                        item.DrugType = drugTypeDictMap.GetValueOrDefault(item.DrugTypeDictLabel);
                        if (item.DrugType == null) item.Error = "药品类型字典映射失败";
                        if (string.IsNullOrWhiteSpace(item.QualityStatusDictLabel)) continue;
                        item.QualityStatus = qualityStatusDictMap.GetValueOrDefault(item.QualityStatusDictLabel);
                        if (item.QualityStatus == null) item.Error = "质量状况字典映射失败";
                        if (string.IsNullOrWhiteSpace(item.AcceptanceStatusDictLabel)) continue;
                        item.AcceptanceStatus = acceptanceStatusDictMap.GetValueOrDefault(item.AcceptanceStatusDictLabel);
                        if (item.AcceptanceStatus == null) item.Error = "验收状态字典映射失败";
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<StorageInDetail>>();
                    
                    var storageable = _storageInDetailRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.DrugType?.Length > 100, "药品类型长度不能超过100个字符")
                        .SplitError(it => it.Item.StorageInNo?.Length > 100, "入库单号长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugCode?.Length > 100, "药品编码长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugName?.Length > 100, "药品名称长度不能超过100个字符")
                        .SplitError(it => it.Item.Spec?.Length > 100, "规格长度不能超过100个字符")
                        .SplitError(it => it.Item.Unit?.Length > 100, "单位长度不能超过100个字符")
                        .SplitError(it => it.Item.BatchNo?.Length > 100, "批号长度不能超过100个字符")
                        .SplitError(it => it.Item.ApprovalNumber?.Length > 100, "批准文号长度不能超过100个字符")
                        .SplitError(it => it.Item.MedicineCode?.Length > 100, "国家医保编码长度不能超过100个字符")
                        .SplitError(it => it.Item.QualityStatus?.Length > 100, "质量状况长度不能超过100个字符")
                        .SplitError(it => it.Item.AcceptanceStatus?.Length > 100, "验收状态长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
