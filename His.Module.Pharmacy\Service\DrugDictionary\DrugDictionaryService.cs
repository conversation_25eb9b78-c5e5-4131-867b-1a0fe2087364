﻿using Admin.NET.Core.Service;
using His.Module.Shared.Entity;
using NewLife.Common;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品字典维护服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugDictionaryService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugDictionary> _drugDictionaryRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;
    private readonly SqlSugarRepository<Frequency> _frequencyRep;

    public DrugDictionaryService(SqlSugarRepository<DrugDictionary> drugDictionaryRep, ISqlSugarClient sqlSugarClient,
        SysDictTypeService sysDictTypeService,
        SqlSugarRepository<Frequency> frequencyRep
    )
    {
        _drugDictionaryRep = drugDictionaryRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
        _frequencyRep = frequencyRep;
    }

    /// <summary>
    /// 分页查询药品字典维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品字典维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugDictionaryOutput>> Page(PageDrugDictionaryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToUpper();
        var query = _drugDictionaryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) ||
                     u.DrugNamePinyin.Contains(input.Keyword) || u.GenericName.Contains(input.Keyword) ||
                     u.GenericNamePinyin.Contains(input.Keyword) || u.ProductName.Contains(input.Keyword) ||
                     u.ProductNamePinyin.Contains(input.Keyword) || u.DrugType.Contains(input.Keyword) ||
                     u.AntibacterialLevel.Contains(input.Keyword) || u.ManufacturerName.Contains(input.Keyword) ||
                     u.StorageUnit.Contains(input.Keyword) || u.PackageSpec.Contains(input.Keyword) ||
                     u.MinPackageUnit.Contains(input.Keyword) || u.DosageUnit.Contains(input.Keyword) ||
                     u.ContentUnit.Contains(input.Keyword) || u.OutpatientSpec.Contains(input.Keyword) ||
                     u.OutpatientUnit.Contains(input.Keyword) || u.InpatientSpec.Contains(input.Keyword) ||
                     u.InpatientUnit.Contains(input.Keyword) || u.PurchaseType.Contains(input.Keyword) ||
                     u.RegulationCode.Contains(input.Keyword) || u.ApprovalNumber.Contains(input.Keyword) ||
                     u.PriorityUse.Contains(input.Keyword) || u.PharmacyLocation.Contains(input.Keyword) ||
                     u.StorehouseLocation.Contains(input.Keyword) || u.Ypid.Contains(input.Keyword) ||
                     u.MedicineCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            // .WhereIF(!string.IsNullOrWhiteSpace(input.DrugNamePinyin), u => u.DrugNamePinyin.Contains(input.DrugNamePinyin.Trim()))
            // .WhereIF(!string.IsNullOrWhiteSpace(input.GenericName), u => u.GenericName.Contains(input.GenericName.Trim()))
            // .WhereIF(!string.IsNullOrWhiteSpace(input.GenericNamePinyin), u => u.GenericNamePinyin.Contains(input.GenericNamePinyin.Trim()))
            // .WhereIF(!string.IsNullOrWhiteSpace(input.ProductName), u => u.ProductName.Contains(input.ProductName.Trim()))
            // .WhereIF(!string.IsNullOrWhiteSpace(input.ProductNamePinyin), u => u.ProductNamePinyin.Contains(input.ProductNamePinyin.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType == input.DrugType.Trim())
            .WhereIF(!string.IsNullOrWhiteSpace(input.AntibacterialLevel),
                u => u.AntibacterialLevel.Contains(input.AntibacterialLevel.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ManufacturerName),
                u => u.ManufacturerName.Contains(input.ManufacturerName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageUnit),
                u => u.StorageUnit.Contains(input.StorageUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PackageSpec),
                u => u.PackageSpec.Contains(input.PackageSpec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MinPackageUnit),
                u => u.MinPackageUnit.Contains(input.MinPackageUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DosageUnit), u => u.DosageUnit.Contains(input.DosageUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContentUnit),
                u => u.ContentUnit.Contains(input.ContentUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientSpec),
                u => u.OutpatientSpec.Contains(input.OutpatientSpec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientUnit),
                u => u.OutpatientUnit.Contains(input.OutpatientUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientSpec),
                u => u.InpatientSpec.Contains(input.InpatientSpec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientUnit),
                u => u.InpatientUnit.Contains(input.InpatientUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PurchaseType),
                u => u.PurchaseType.Contains(input.PurchaseType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RegulationCode),
                u => u.RegulationCode.Contains(input.RegulationCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApprovalNumber),
                u => u.ApprovalNumber.Contains(input.ApprovalNumber.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PriorityUse),
                u => u.PriorityUse.Contains(input.PriorityUse.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PharmacyLocation),
                u => u.PharmacyLocation.Contains(input.PharmacyLocation.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorehouseLocation),
                u => u.StorehouseLocation.Contains(input.StorehouseLocation.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Ypid), u => u.Ypid.Contains(input.Ypid.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicineCode),
                u => u.MedicineCode.Contains(input.MedicineCode.Trim()))
            // .WhereIF(input.DrugCategory != null, u => u.DrugCategory == input.DrugCategory)
            // .WhereIF(input.PharmacologicalClass != null, u => u.PharmacologicalClass == input.PharmacologicalClass)
            // .WhereIF(input.DrugForm != null, u => u.DrugForm == input.DrugForm)
            // .WhereIF(input.DrugRoute != null, u => u.DrugRoute == input.DrugRoute)
            // .WhereIF(input.DrugFreq != null, u => u.DrugFreq == input.DrugFreq)
            .WhereIF(input.ManufacturerId != null, u => u.ManufacturerId == input.ManufacturerId)
            .WhereIF(input.PlaceOfOrigin != null, u => u.PlaceOfOrigin == input.PlaceOfOrigin)
            .WhereIF(input.PackageQuantity != null, u => u.PackageQuantity == input.PackageQuantity)
            .WhereIF(input.OutpatientPackageQuantity != null,
                u => u.OutpatientPackageQuantity == input.OutpatientPackageQuantity)
            .WhereIF(input.InpatientPackageQuantity != null,
                u => u.InpatientPackageQuantity == input.InpatientPackageQuantity)
            .WhereIF(input.Holder != null, u => u.Holder == input.Holder)
            .WhereIF(input.IsSplit != null, u => u.IsSplit == (int)input.IsSplit)
            .WhereIF(input.IsMedicare != null, u => u.IsMedicare == (int)input.IsMedicare)
            .WhereIF(input.IsSelf != null, u => u.IsSelf == (int)input.IsSelf)
            .WhereIF(input.IsBasic != null, u => u.IsBasic == (int)input.IsBasic)
            .WhereIF(input.IsSkinTest != null, u => u.IsSkinTest == (int)input.IsSkinTest)
            .WhereIF(input.IsCountry != null, u => u.IsCountry == (int)input.IsCountry)
            .WhereIF(input.IsAssist != null, u => u.IsAssist == (int)input.IsAssist)
            .WhereIF(input.IsTemporary != null, u => u.IsTemporary == (int)input.IsTemporary)
            .WhereIF(input.IsSolvent != null, u => u.IsSolvent == (int)input.IsSolvent)
            .WhereIF(input.IsCovid != null, u => u.IsCovid == (int)input.IsCovid)
            .WhereIF(input.Status != null, u => u.Status == (int)input.Status)
            .Select<DrugDictionaryOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
        // var query = _drugDictionaryRep.AsQueryable()
        //     .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.DrugName.Contains(input.Keyword))
        //
        //     .WhereIF(input.Status.HasValue, u => u.Status == (int)input.Status)
        //     .Select<DrugDictionaryOutput>();
        // return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品字典维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品字典维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugDictionary> Detail([FromQuery] QueryByIdDrugDictionaryInput input)
    {
        return await _drugDictionaryRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品字典维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品字典维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugDictionaryInput input)
    {
        var entity = input.Adapt<DrugDictionary>();
        entity.DrugCode =  await _drugDictionaryRep.Context.Ado.GetStringAsync(
            "SELECT 'Y'||LPAD(CAST(NEXTVAL('drug_dictionary_code_seq')As varchar),7,'0')");
        entity.DrugNamePinyin = PinYin.GetFirst(entity.DrugName);
        entity.GenericNamePinyin = PinYin.GetFirst(entity.GenericName ?? "");
        entity.ProductNamePinyin = PinYin.GetFirst(entity.ProductName ?? "");
        return await _drugDictionaryRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品字典维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品字典维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugDictionaryInput input)
    {
        var entity = input.Adapt<DrugDictionary>();
        
        entity.DrugNamePinyin = PinYin.GetFirst(entity.DrugName);
        entity.GenericNamePinyin = PinYin.GetFirst(entity.GenericName ?? "");
        entity.ProductNamePinyin = PinYin.GetFirst(entity.ProductName ?? "");
        await _drugDictionaryRep.AsUpdateable(entity)
            .IgnoreColumns(u => new
            {
                u.DrugNamePinyin, u.GenericNamePinyin, u.ProductNamePinyin, u.ManufacturerName,
            })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品字典维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品字典维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugDictionaryInput input)
    {
        var entity = await _drugDictionaryRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugDictionaryRep.FakeDeleteAsync(entity); //假删除
        //await _drugDictionaryRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品字典维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品字典维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteDrugDictionaryInput> input)
    {
        var exp = Expressionable.Create<DrugDictionary>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugDictionaryRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _drugDictionaryRep.FakeDeleteAsync(list); //假删除
        //return await _drugDictionaryRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置药品字典维护状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置药品字典维护状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetDrugDictionaryStatus(SetDrugDictionaryStatusInput input)
    {
        await _drugDictionaryRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataDrugDictionaryInput input)
    {
        var drugCategoryData = await _drugDictionaryRep.Context.Queryable<DrugCategory>()
            .InnerJoinIF<DrugDictionary>(input.FromPage, (u, r) => u.Id == r.DrugCategory)
            .Select(u => new { Value = u.Id, Label = $"{u.CategoryName}" }).ToListAsync();
        var pharmacologicalClassData = await _drugDictionaryRep.Context.Queryable<PharmacologicalClass>()
            .InnerJoinIF<DrugDictionary>(input.FromPage, (u, r) => u.Id == r.PharmacologicalClass)
            .Select(u => new { Value = u.Id, Label = $"{u.ClassName}" }).ToListAsync();
        var drugFormData = await _drugDictionaryRep.Context.Queryable<DrugForm>()
            .InnerJoinIF<DrugDictionary>(input.FromPage, (u, r) => u.Id == r.DrugForm)
            .Select(u => new { Value = u.Id, Label = $"{u.FormName}" }).ToListAsync();

        var manufacturerIdData = await _drugDictionaryRep.Context.Queryable<EnterpriseDictionary>()
            .InnerJoinIF<DrugDictionary>(input.FromPage, (u, r) => u.Id == r.ManufacturerId)
            .Select(u => new { Value = u.Id, Label = $"{u.EnterpriseName}" }).ToListAsync();
        var placeOfOriginData = await _drugDictionaryRep.Context.Queryable<DrugPlace>()
            .Select(u => new { Value = u.Id, Label = $"{u.PlaceName}" }).ToListAsync();
        // var frequencyData = await _drugDictionaryRep.Context.Queryable<Frequency>()
        //     .InnerJoinIF<DrugDictionary>(input.FromPage, (u, r) => u.Id == r.DrugFreq)
        //     .Select(u => new
        //     {
        //         Value = u.Id,
        //         Label = $"{u.Name}"
        //     }).ToListAsync();
        var frequencyData = await _frequencyRep.Context.Queryable<Frequency>()
            .Select(u => new { Value = u.Id, Label = $"{u.Name}" }).ToListAsync();

        var storageUnitData = await _drugDictionaryRep.Context.Queryable<DrugUnit>()
            .Select(u => new { Value = $"{u.Unit}", Label = $"{u.Unit}" }).ToListAsync();
        var dosageUnitData = await _drugDictionaryRep.Context.Queryable<DrugDosageUnit>()
            .Select(u => new { Value = $"{u.UnitName}", Label = $"{u.UnitName}" }).ToListAsync();

        var holderData = await _drugDictionaryRep.Context.Queryable<DrugHolder>()
            .InnerJoinIF<DrugDictionary>(input.FromPage, (u, r) => u.Id == r.Holder)
            .Select(u => new { Value = u.Id, Label = $"{u.HolderName}" }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "drugCategory", drugCategoryData },
            { "pharmacologicalClass", pharmacologicalClassData },
            { "drugForm", drugFormData },
            { "drugFreq", frequencyData },
            { "manufacturerId", manufacturerIdData },
            { "placeOfOrigin", placeOfOriginData },
            { "frequency", frequencyData },
            { "storageUnit", storageUnitData },
            { "dosageUnit", dosageUnitData },
            { "contentValue", dosageUnitData },
            { "outpatientUnit", storageUnitData },
            { "inpatientUnit", storageUnitData },
            { "holder", holderData },
        };
    }

    /// <summary>
    /// 导出药品字典维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    // [DisplayName("导出药品字典维护记录")]
    // [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    // public async Task<IActionResult> Export(PageDrugDictionaryInput input)
    // {
    //     var list = (await Page(input)).Items?.Adapt<List<ExportDrugDictionaryOutput>>() ?? new();
    //     if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
    //     var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result
    //         .ToDictionary(x => x.Value, x => x.Label);
    //     var antibacterialLevelDictMap = _sysDictTypeService
    //         .GetDataList(new GetDataDictTypeInput { Code = "AntibacterialLevel" }).Result
    //         .ToDictionary(x => x.Value, x => x.Label);
    //     var purchaseTypeDictMap = _sysDictTypeService
    //         .GetDataList(new GetDataDictTypeInput { Code = "DrugPurchaseType" }).Result
    //         .ToDictionary(x => x.Value, x => x.Label);
    //     var priorityUseDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugPriorityUse" })
    //         .Result.ToDictionary(x => x.Value, x => x.Label);
    //     list.ForEach(e =>
    //     {
    //         e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType);
    //         e.AntibacterialLevelDictLabel =
    //             antibacterialLevelDictMap.GetValueOrDefault(e.AntibacterialLevel ?? "", e.AntibacterialLevel);
    //         e.PurchaseTypeDictLabel = purchaseTypeDictMap.GetValueOrDefault(e.PurchaseType ?? "", e.PurchaseType);
    //         e.PriorityUseDictLabel = priorityUseDictMap.GetValueOrDefault(e.PriorityUse ?? "", e.PriorityUse);
    //     });
    //     return ExcelHelper.ExportTemplate(list, "药品字典维护导出记录");
    // }

    /// <summary>
    /// 下载药品字典维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品字典维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugDictionaryOutput>(), "药品字典维护导入模板", (_, info) =>
        {
            if (nameof(ExportDrugDictionaryOutput.DrugCategoryFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugCategory>().Select(u => $"{u.CategoryName}").Distinct()
                    .ToList();
            if (nameof(ExportDrugDictionaryOutput.PharmacologicalClassFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<PharmacologicalClass>().Select(u => $"{u.ClassName}")
                    .Distinct().ToList();
            if (nameof(ExportDrugDictionaryOutput.DrugFormFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugForm>().Select(u => $"{u.FormName}").Distinct()
                    .ToList();
            if (nameof(ExportDrugDictionaryOutput.DrugFreqFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<Frequency>().Select(u => $"{u.Name}").Distinct().ToList();
            if (nameof(ExportDrugDictionaryOutput.ManufacturerFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<EnterpriseDictionary>().Select(u => $"{u.EnterpriseName}")
                    .Distinct().ToList();
            if (nameof(ExportDrugDictionaryOutput.PlaceOfOriginFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugPlace>().Select(u => $"{u.PlaceName}").Distinct()
                    .ToList();
            if (nameof(ExportDrugDictionaryOutput.StorageUnitFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugUnit>().Select(u => $"{u.Unit}").Distinct().ToList();
            if (nameof(ExportDrugDictionaryOutput.DosageUnitFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugDosageUnit>().Select(u => $"{u.UnitName}").Distinct()
                    .ToList();
            if (nameof(ExportDrugDictionaryOutput.ContentValueFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugDosageUnit>().Select(u => $"{u.UnitName}").Distinct()
                    .ToList();
            if (nameof(ExportDrugDictionaryOutput.OutpatientUnitFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugUnit>().Select(u => $"{u.Unit}").Distinct().ToList();
            if (nameof(ExportDrugDictionaryOutput.InpatientUnitFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugUnit>().Select(u => $"{u.Unit}").Distinct().ToList();
            if (nameof(ExportDrugDictionaryOutput.HolderFkDisplayName) == info.Name)
                return _drugDictionaryRep.Context.Queryable<DrugHolder>().Select(u => $"{u.HolderName}").Distinct()
                    .ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入药品字典维护记录 💾
    /// </summary>
    /// <returns></returns>
    // [DisplayName("导入药品字典维护记录")]
    // [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    // public IActionResult ImportData([Required] IFormFile file)
    // {
    //     lock (this)
    //     {
    //         var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result
    //             .ToDictionary(x => x.Label!, x => x.Value);
    //         var antibacterialLevelDictMap = _sysDictTypeService
    //             .GetDataList(new GetDataDictTypeInput { Code = "AntibacterialLevel" }).Result
    //             .ToDictionary(x => x.Label!, x => x.Value);
    //         var purchaseTypeDictMap = _sysDictTypeService
    //             .GetDataList(new GetDataDictTypeInput { Code = "DrugPurchaseType" }).Result
    //             .ToDictionary(x => x.Label!, x => x.Value);
    //         var priorityUseDictMap = _sysDictTypeService
    //             .GetDataList(new GetDataDictTypeInput { Code = "DrugPriorityUse" }).Result
    //             .ToDictionary(x => x.Label!, x => x.Value);
    //         var stream = ExcelHelper.ImportData<ImportDrugDictionaryInput, DrugDictionary>(file,
    //             (list, markerErrorAction) =>
    //             {
    //                 _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
    //                 {
    //                     // 链接 药品分类
    //                     var drugCategoryLabelList = pageItems.Where(x => x.DrugCategoryFkDisplayName != null)
    //                         .Select(x => x.DrugCategoryFkDisplayName).Distinct().ToList();
    //                     if (drugCategoryLabelList.Any())
    //                     {
    //                         var drugCategoryLinkMap = _drugDictionaryRep.Context.Queryable<DrugCategory>()
    //                             .Where(u => drugCategoryLabelList.Contains($"{u.CategoryName}")).ToList()
    //                             .ToDictionary(u => $"{u.CategoryName}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.DrugCategory =
    //                                 drugCategoryLinkMap.GetValueOrDefault(e.DrugCategoryFkDisplayName ?? "");
    //                             if (e.DrugCategory == null) e.Error = "药品分类链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 药理分类
    //                     var pharmacologicalClassLabelList = pageItems
    //                         .Where(x => x.PharmacologicalClassFkDisplayName != null)
    //                         .Select(x => x.PharmacologicalClassFkDisplayName).Distinct().ToList();
    //                     if (pharmacologicalClassLabelList.Any())
    //                     {
    //                         var pharmacologicalClassLinkMap = _drugDictionaryRep.Context
    //                             .Queryable<PharmacologicalClass>()
    //                             .Where(u => pharmacologicalClassLabelList.Contains($"{u.ClassName}")).ToList()
    //                             .ToDictionary(u => $"{u.ClassName}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.PharmacologicalClass =
    //                                 pharmacologicalClassLinkMap.GetValueOrDefault(e.PharmacologicalClassFkDisplayName ??
    //                                     "");
    //                             if (e.PharmacologicalClass == null) e.Error = "药理分类链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 剂型
    //                     var drugFormLabelList = pageItems.Where(x => x.DrugFormFkDisplayName != null)
    //                         .Select(x => x.DrugFormFkDisplayName).Distinct().ToList();
    //                     if (drugFormLabelList.Any())
    //                     {
    //                         var drugFormLinkMap = _drugDictionaryRep.Context.Queryable<DrugForm>()
    //                             .Where(u => drugFormLabelList.Contains($"{u.FormName}")).ToList()
    //                             .ToDictionary(u => $"{u.FormName}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.DrugForm = drugFormLinkMap.GetValueOrDefault(e.DrugFormFkDisplayName ?? "");
    //                             if (e.DrugForm == null) e.Error = "剂型链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 用药频次
    //                     var drugFreqLabelList = pageItems.Where(x => x.DrugFreqFkDisplayName != null)
    //                         .Select(x => x.DrugFreqFkDisplayName).Distinct().ToList();
    //                     if (drugFreqLabelList.Any())
    //                     {
    //                         var drugFreqLinkMap = _drugDictionaryRep.Context.Queryable<Frequency>()
    //                             .Where(u => drugFreqLabelList.Contains($"{u.Name}")).ToList()
    //                             .ToDictionary(u => $"{u.Name}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.DrugFreq = drugFreqLinkMap.GetValueOrDefault(e.DrugFreqFkDisplayName ?? "");
    //                             if (e.DrugFreq == null) e.Error = "用药频次链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 生产企业
    //                     var manufacturerIdLabelList = pageItems.Where(x => x.ManufacturerFkDisplayName != null)
    //                         .Select(x => x.ManufacturerFkDisplayName).Distinct().ToList();
    //                     if (manufacturerIdLabelList.Any())
    //                     {
    //                         var manufacturerIdLinkMap = _drugDictionaryRep.Context.Queryable<EnterpriseDictionary>()
    //                             .Where(u => manufacturerIdLabelList.Contains($"{u.EnterpriseName}")).ToList()
    //                             .ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.ManufacturerId =
    //                                 manufacturerIdLinkMap.GetValueOrDefault(e.ManufacturerFkDisplayName ?? "");
    //                             if (e.ManufacturerId == null) e.Error = "生产企业链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 产地
    //                     var placeOfOriginLabelList = pageItems.Where(x => x.PlaceOfOriginFkDisplayName != null)
    //                         .Select(x => x.PlaceOfOriginFkDisplayName).Distinct().ToList();
    //                     if (placeOfOriginLabelList.Any())
    //                     {
    //                         var placeOfOriginLinkMap = _drugDictionaryRep.Context.Queryable<DrugPlace>()
    //                             .Where(u => placeOfOriginLabelList.Contains($"{u.PlaceName}")).ToList()
    //                             .ToDictionary(u => $"{u.PlaceName}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.PlaceOfOrigin =
    //                                 placeOfOriginLinkMap.GetValueOrDefault(e.PlaceOfOriginFkDisplayName ?? "");
    //                             if (e.PlaceOfOrigin == null) e.Error = "产地链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 入库单位
    //                     var storageUnitLabelList = pageItems.Where(x => x.StorageUnitFkDisplayName != null)
    //                         .Select(x => x.StorageUnitFkDisplayName).Distinct().ToList();
    //                     if (storageUnitLabelList.Any())
    //                     {
    //                         var storageUnitLinkMap = _drugDictionaryRep.Context.Queryable<DrugUnit>()
    //                             .Where(u => storageUnitLabelList.Contains($"{u.Unit}")).ToList()
    //                             .ToDictionary(u => $"{u.Unit}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.StorageUnit = storageUnitLinkMap.GetValueOrDefault(e.StorageUnitFkDisplayName ?? "");
    //                             if (e.StorageUnit == null) e.Error = "入库单位链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 剂量单位
    //                     var dosageUnitLabelList = pageItems.Where(x => x.DosageUnitFkDisplayName != null)
    //                         .Select(x => x.DosageUnitFkDisplayName).Distinct().ToList();
    //                     if (dosageUnitLabelList.Any())
    //                     {
    //                         var dosageUnitLinkMap = _drugDictionaryRep.Context.Queryable<DrugDosageUnit>()
    //                             .Where(u => dosageUnitLabelList.Contains($"{u.UnitName}")).ToList()
    //                             .ToDictionary(u => $"{u.UnitName}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.DosageUnit = dosageUnitLinkMap.GetValueOrDefault(e.DosageUnitFkDisplayName ?? "");
    //                             if (e.DosageUnit == null) e.Error = "剂量单位链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 含量
    //                     var contentValueLabelList = pageItems.Where(x => x.ContentValueFkDisplayName != null)
    //                         .Select(x => x.ContentValueFkDisplayName).Distinct().ToList();
    //                     if (contentValueLabelList.Any())
    //                     {
    //                         var contentValueLinkMap = _drugDictionaryRep.Context.Queryable<DrugDosageUnit>()
    //                             .Where(u => contentValueLabelList.Contains($"{u.UnitName}")).ToList()
    //                             .ToDictionary(u => $"{u.UnitName}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.ContentValue =
    //                                 contentValueLinkMap.GetValueOrDefault(e.ContentValueFkDisplayName ?? "");
    //                             if (e.ContentValue == null) e.Error = "含量链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 门诊单位
    //                     var outpatientUnitLabelList = pageItems.Where(x => x.OutpatientUnitFkDisplayName != null)
    //                         .Select(x => x.OutpatientUnitFkDisplayName).Distinct().ToList();
    //                     if (outpatientUnitLabelList.Any())
    //                     {
    //                         var outpatientUnitLinkMap = _drugDictionaryRep.Context.Queryable<DrugUnit>()
    //                             .Where(u => outpatientUnitLabelList.Contains($"{u.Unit}")).ToList()
    //                             .ToDictionary(u => $"{u.Unit}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.OutpatientUnit =
    //                                 outpatientUnitLinkMap.GetValueOrDefault(e.OutpatientUnitFkDisplayName ?? "");
    //                             if (e.OutpatientUnit == null) e.Error = "门诊单位链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 住院单位
    //                     var inpatientUnitLabelList = pageItems.Where(x => x.InpatientUnitFkDisplayName != null)
    //                         .Select(x => x.InpatientUnitFkDisplayName).Distinct().ToList();
    //                     if (inpatientUnitLabelList.Any())
    //                     {
    //                         var inpatientUnitLinkMap = _drugDictionaryRep.Context.Queryable<DrugUnit>()
    //                             .Where(u => inpatientUnitLabelList.Contains($"{u.Unit}")).ToList()
    //                             .ToDictionary(u => $"{u.Unit}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.InpatientUnit =
    //                                 inpatientUnitLinkMap.GetValueOrDefault(e.InpatientUnitFkDisplayName ?? "");
    //                             if (e.InpatientUnit == null) e.Error = "住院单位链接失败";
    //                         });
    //                     }
    //
    //                     // 链接 上市许可持有人
    //                     var holderLabelList = pageItems.Where(x => x.HolderFkDisplayName != null)
    //                         .Select(x => x.HolderFkDisplayName).Distinct().ToList();
    //                     if (holderLabelList.Any())
    //                     {
    //                         var holderLinkMap = _drugDictionaryRep.Context.Queryable<DrugHolder>()
    //                             .Where(u => holderLabelList.Contains($"{u.HolderName}")).ToList()
    //                             .ToDictionary(u => $"{u.HolderName}", u => u.Id);
    //                         pageItems.ForEach(e =>
    //                         {
    //                             e.Holder = holderLinkMap.GetValueOrDefault(e.HolderFkDisplayName ?? "");
    //                             if (e.Holder == null) e.Error = "上市许可持有人链接失败";
    //                         });
    //                     }
    //
    //                     // 映射字典值
    //                     foreach (var item in pageItems)
    //                     {
    //                         if (string.IsNullOrWhiteSpace(item.DrugTypeDictLabel)) continue;
    //                         item.DrugType = drugTypeDictMap.GetValueOrDefault(item.DrugTypeDictLabel);
    //                         if (item.DrugType == null) item.Error = "药品类型字典映射失败";
    //                         if (string.IsNullOrWhiteSpace(item.AntibacterialLevelDictLabel)) continue;
    //                         item.AntibacterialLevel =
    //                             antibacterialLevelDictMap.GetValueOrDefault(item.AntibacterialLevelDictLabel);
    //                         if (item.AntibacterialLevel == null) item.Error = "抗生素级别字典映射失败";
    //                         if (string.IsNullOrWhiteSpace(item.PurchaseTypeDictLabel)) continue;
    //                         item.PurchaseType = purchaseTypeDictMap.GetValueOrDefault(item.PurchaseTypeDictLabel);
    //                         if (item.PurchaseType == null) item.Error = "采购类型字典映射失败";
    //                         if (string.IsNullOrWhiteSpace(item.PriorityUseDictLabel)) continue;
    //                         item.PriorityUse = priorityUseDictMap.GetValueOrDefault(item.PriorityUseDictLabel);
    //                         if (item.PriorityUse == null) item.Error = "优先使用字典映射失败";
    //                     }
    //
    //                     // 校验并过滤必填基本类型为null的字段
    //                     var rows = pageItems.Where(x => { return true; }).Adapt<List<DrugDictionary>>();
    //
    //                     var storageable = _drugDictionaryRep.Context.Storageable(rows)
    //                         .SplitError(it => string.IsNullOrWhiteSpace(it.Item.DrugCode), "药品编码不能为空")
    //                         .SplitError(it => it.Item.DrugCode?.Length > 50, "药品编码长度不能超过50个字符")
    //                         .SplitError(it => string.IsNullOrWhiteSpace(it.Item.DrugName), "药品名称不能为空")
    //                         .SplitError(it => it.Item.DrugName?.Length > 200, "药品名称长度不能超过200个字符")
    //                         .SplitError(it => it.Item.DrugNamePinyin?.Length > 200, "药品名称拼音长度不能超过200个字符")
    //                         .SplitError(it => it.Item.GenericName?.Length > 200, "通用名称长度不能超过200个字符")
    //                         .SplitError(it => it.Item.GenericNamePinyin?.Length > 200, "通用名称拼音长度不能超过200个字符")
    //                         .SplitError(it => it.Item.ProductName?.Length > 200, "产品名称长度不能超过200个字符")
    //                         .SplitError(it => it.Item.ProductNamePinyin?.Length > 200, "产品名称拼音长度不能超过200个字符")
    //                         .SplitError(it => it.Item.DrugType?.Length > 100, "药品类型长度不能超过100个字符")
    //                         .SplitError(it => it.Item.DrugCategory?.Length > 100, "药品分类长度不能超过100个字符")
    //                         .SplitError(it => it.Item.PharmacologicalClass?.Length > 100, "药理分类长度不能超过100个字符")
    //                         .SplitError(it => it.Item.AntibacterialLevel?.Length > 100, "抗生素级别长度不能超过100个字符")
    //                         .SplitError(it => it.Item.DrugForm?.Length > 100, "剂型长度不能超过100个字符")
    //                         .SplitError(it => it.Item.DrugRoute?.Length > 100, "用药途径长度不能超过100个字符")
    //                         .SplitError(it => it.Item.DrugFreq?.Length > 100, "用药频次长度不能超过100个字符")
    //                         .SplitError(it => it.Item.PlaceOfOrigin?.Length > 200, "产地长度不能超过200个字符")
    //                         .SplitError(it => it.Item.StorageUnit?.Length > 100, "入库单位长度不能超过100个字符")
    //                         .SplitError(it => it.Item.PackageSpec?.Length > 100, "包装规格长度不能超过100个字符")
    //                         .SplitError(it => it.Item.MinPackageUnit?.Length > 100, "最小包装单位长度不能超过100个字符")
    //                         .SplitError(it => it.Item.DosageUnit?.Length > 100, "剂量单位长度不能超过100个字符")
    //                         .SplitError(it => it.Item.ContentUnit?.Length > 100, "含量单位长度不能超过100个字符")
    //                         .SplitError(it => it.Item.OutpatientSpec?.Length > 100, "门诊规格长度不能超过100个字符")
    //                         .SplitError(it => it.Item.OutpatientUnit?.Length > 100, "门诊单位长度不能超过100个字符")
    //                         .SplitError(it => it.Item.InpatientSpec?.Length > 100, "住院规格长度不能超过100个字符")
    //                         .SplitError(it => it.Item.InpatientUnit?.Length > 100, "住院单位长度不能超过100个字符")
    //                         .SplitError(it => it.Item.PurchaseType?.Length > 100, "采购类型长度不能超过100个字符")
    //                         .SplitError(it => it.Item.Holder?.Length > 100, "上市许可持有人长度不能超过100个字符")
    //                         .SplitError(it => it.Item.RegulationCode?.Length > 100, "电子监管码长度不能超过100个字符")
    //                         .SplitError(it => it.Item.ApprovalNumber?.Length > 100, "批准文号长度不能超过100个字符")
    //                         .SplitError(it => it.Item.PriorityUse?.Length > 100, "优先使用长度不能超过100个字符")
    //                         .SplitError(it => it.Item.PharmacyLocation?.Length > 100, "药房货位长度不能超过100个字符")
    //                         .SplitError(it => it.Item.StorehouseLocation?.Length > 100, "药库货位长度不能超过100个字符")
    //                         .SplitError(it => it.Item.Ypid?.Length > 100, "YPID长度不能超过100个字符")
    //                         .SplitError(it => it.Item.MedicineCode?.Length > 100, "国家医保编码长度不能超过100个字符")
    //                         .SplitInsert(_ => true)
    //                         .ToStorage();
    //
    //                     storageable.BulkCopy();
    //                     storageable.BulkUpdate();
    //
    //                     // 标记错误信息
    //                     markerErrorAction.Invoke(storageable, pageItems, rows);
    //                 });
    //             });
    //
    //         return stream;
    //     }
    // }
}