﻿namespace His.Module.Shared.Service;

/// <summary>
/// 频次基础输入参数
/// </summary>
public class FrequencyBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 时间间隔
    /// </summary>
    [Required(ErrorMessage = "时间间隔不能为空")]
    public virtual int? TimeInterval { get; set; }

    /// <summary>
    /// 时间单位
    /// </summary>
    [Required(ErrorMessage = "时间单位不能为空")]
    public virtual Int16? TimeUnit { get; set; }

    /// <summary>
    /// 执行频率
    /// </summary>
    [Required(ErrorMessage = "执行频率不能为空")]
    public virtual int? ExecutionFrequency { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [Required(ErrorMessage = "执行时间不能为空")]
    public virtual string? ExecutionTime { get; set; }

    /// <summary>
    /// 持续标识
    /// </summary>
    [Required(ErrorMessage = "持续标识不能为空")]
    public virtual Int16? Sustain { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public virtual MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public virtual StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
}

/// <summary>
/// 频次分页查询输入参数
/// </summary>
public class PageFrequencyInput : BasePageInput
{
    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 频次增加输入参数
/// </summary>
public class AddFrequencyInput
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 时间间隔
    /// </summary>
    [Required(ErrorMessage = "时间间隔不能为空")]
    public int? TimeInterval { get; set; }

    /// <summary>
    /// 时间单位
    /// </summary>
    [Required(ErrorMessage = "时间单位不能为空")]
    public Int16? TimeUnit { get; set; }

    /// <summary>
    /// 执行频率
    /// </summary>
    [Required(ErrorMessage = "执行频率不能为空")]
    public int? ExecutionFrequency { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [Required(ErrorMessage = "执行时间不能为空")]
    [MaxLength(256, ErrorMessage = "执行时间字符长度不能超过256")]
    public string? ExecutionTime { get; set; }

    /// <summary>
    /// 持续标识
    /// </summary>
    [Required(ErrorMessage = "持续标识不能为空")]
    public Int16? Sustain { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 频次删除输入参数
/// </summary>
public class DeleteFrequencyInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 频次更新输入参数
/// </summary>
public class UpdateFrequencyInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 时间间隔
    /// </summary>
    [Required(ErrorMessage = "时间间隔不能为空")]
    public int? TimeInterval { get; set; }

    /// <summary>
    /// 时间单位
    /// </summary>
    [Required(ErrorMessage = "时间单位不能为空")]
    public Int16? TimeUnit { get; set; }

    /// <summary>
    /// 执行频率
    /// </summary>
    [Required(ErrorMessage = "执行频率不能为空")]
    public int? ExecutionFrequency { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [Required(ErrorMessage = "执行时间不能为空")]
    [MaxLength(256, ErrorMessage = "执行时间字符长度不能超过256")]
    public string? ExecutionTime { get; set; }

    /// <summary>
    /// 持续标识
    /// </summary>
    [Required(ErrorMessage = "持续标识不能为空")]
    public Int16? Sustain { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 频次主键查询输入参数
/// </summary>
public class QueryByIdFrequencyInput : DeleteFrequencyInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetFrequencyStatusInput : BaseStatusInput
{
}

/// <summary>
/// 频次数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportFrequencyInput : BaseImportInput
{
    /// <summary>
    /// 编码
    /// </summary>
    [ImporterHeader(Name = "编码")]
    [ExporterHeader("编码", Format = "", Width = 25, IsBold = true)]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [ImporterHeader(Name = "*名称")]
    [ExporterHeader("*名称", Format = "", Width = 25, IsBold = true)]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [ImporterHeader(Name = "拼音码")]
    [ExporterHeader("拼音码", Format = "", Width = 25, IsBold = true)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [ImporterHeader(Name = "五笔码")]
    [ExporterHeader("五笔码", Format = "", Width = 25, IsBold = true)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 时间间隔
    /// </summary>
    [ImporterHeader(Name = "*时间间隔")]
    [ExporterHeader("*时间间隔", Format = "", Width = 25, IsBold = true)]
    public int? TimeInterval { get; set; }

    /// <summary>
    /// 时间单位
    /// </summary>
    [ImporterHeader(Name = "*时间单位")]
    [ExporterHeader("*时间单位", Format = "", Width = 25, IsBold = true)]
    public Int16? TimeUnit { get; set; }

    /// <summary>
    /// 执行频率
    /// </summary>
    [ImporterHeader(Name = "*执行频率")]
    [ExporterHeader("*执行频率", Format = "", Width = 25, IsBold = true)]
    public int? ExecutionFrequency { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    [ImporterHeader(Name = "*执行时间")]
    [ExporterHeader("*执行时间", Format = "", Width = 25, IsBold = true)]
    public string? ExecutionTime { get; set; }

    /// <summary>
    /// 持续标识
    /// </summary>
    [ImporterHeader(Name = "*持续标识")]
    [ExporterHeader("*持续标识", Format = "", Width = 25, IsBold = true)]
    public Int16? Sustain { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [ImporterHeader(Name = "*使用范围")]
    [ExporterHeader("*使用范围", Format = "", Width = 25, IsBold = true)]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
}