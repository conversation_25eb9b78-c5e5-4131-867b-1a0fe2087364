namespace His.Module.Financial.Entity;

/// <summary>
/// 收款员结算操作日志表
/// </summary>
[Tenant("1300000000010")]
[SugarTable("cashier_settlement_log", "收款员结算操作日志表")]
public class CashierSettlementLog : EntityTenantBaseData
{
    /// <summary>
    /// 结算记录ID
    /// </summary>
    [SugarColumn(ColumnName = "settlement_id", ColumnDescription = "结算记录ID")]
    public virtual long SettlementId { get; set; }

    /// <summary>
    /// 操作类型 1=创建 2=修改 3=撤销 4=重新计算 5=数据补录 6=冲正处理
    /// </summary>
    [SugarColumn(ColumnName = "operation_type", ColumnDescription = "操作类型")]
    public virtual int OperationType { get; set; }

    /// <summary>
    /// 操作描述
    /// </summary>
    [SugarColumn(ColumnName = "operation_desc", ColumnDescription = "操作描述", Length = 200)]
    public virtual string? OperationDesc { get; set; }

    /// <summary>
    /// 操作前数据JSON
    /// </summary>
    [SugarColumn(ColumnName = "before_data", ColumnDescription = "操作前数据JSON", IsJson = true)]
    public virtual string? BeforeData { get; set; }

    /// <summary>
    /// 操作后数据JSON
    /// </summary>
    [SugarColumn(ColumnName = "after_data", ColumnDescription = "操作后数据JSON", IsJson = true)]
    public virtual string? AfterData { get; set; }

    /// <summary>
    /// 操作原因
    /// </summary>
    [SugarColumn(ColumnName = "operation_reason", ColumnDescription = "操作原因", Length = 500)]
    public virtual string? OperationReason { get; set; }

    /// <summary>
    /// 影响的业务数据ID列表（JSON格式）
    /// </summary>
    [SugarColumn(ColumnName = "affected_business_ids", ColumnDescription = "影响的业务数据ID列表", IsJson = true)]
    public virtual string? AffectedBusinessIds { get; set; }

    /// <summary>
    /// 操作结果 1=成功 2=失败
    /// </summary>
    [SugarColumn(ColumnName = "operation_result", ColumnDescription = "操作结果")]
    public virtual int OperationResult { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(ColumnName = "error_message", ColumnDescription = "错误信息", Length = 1000)]
    public virtual string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作耗时（毫秒）
    /// </summary>
    [SugarColumn(ColumnName = "operation_duration", ColumnDescription = "操作耗时")]
    public virtual long? OperationDuration { get; set; }


}