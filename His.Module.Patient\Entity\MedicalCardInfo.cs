﻿using His.Module.Patient.Api.Enum;

namespace His.Module.Patient.Entity;

/// <summary>
/// 就诊卡信息表
/// </summary>
[SugarTable(null, "就诊卡信息表")]
[Tenant("1300000000003")]
public class MedicalCardInfo : EntityTenant
{
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnDescription = "就诊卡号")]
    public string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "患者Id")]
    public long PatientId { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    [SugarColumn(ColumnDescription = "业务类型", Length = 32)]
    public BusinessTypeEnum BusinessType { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    [SugarColumn(ColumnDescription = "密码", Length = 128)]
    public string? Password { get; set; }

    /// <summary>
    /// 余额
    /// </summary>
    [SugarColumn(ColumnDescription = "余额", Length = 16, DecimalDigits = 2)]
    public decimal? Balance { get; set; }

    /// <summary>
    /// 卡状态 0挂失;1正常;2 退卡
    /// </summary>
    [SugarColumn(ColumnDescription = "卡状态 0挂失;1正常;2 退卡")]
    public CardStatusEnum Status { get; set; } = CardStatusEnum.Normal;

    /// <summary>
    /// 是否储值
    /// </summary>
    [SugarColumn(ColumnDescription = "是否储值")]
    public bool? IsStored { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 256)]
    public string? Remark { get; set; }

    /// <summary>
    /// 指定使用科室，多个以“,”分割
    /// </summary>
    [SugarColumn(ColumnDescription = "指定使用科室，多个以“,”分割", Length = 256)]
    public string? UseDepts { get; set; }

    /// <summary>
    /// 指定充值方式，多个以“,”分割
    /// </summary>
    [SugarColumn(ColumnDescription = "指定充值方式，多个以“,”分割", Length = 256)]
    public string? ChargeModes { get; set; }
}