﻿namespace His.Module.Shared.Service;

/// <summary>
/// 频次输出参数
/// </summary>
public class FrequencyOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    public string? WubiCode { get; set; }

    /// <summary>
    /// 时间间隔
    /// </summary>
    public int? TimeInterval { get; set; }

    /// <summary>
    /// 时间单位
    /// </summary>
    public Int16? TimeUnit { get; set; }

    /// <summary>
    /// 执行频率
    /// </summary>
    public int? ExecutionFrequency { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public string? ExecutionTime { get; set; }

    /// <summary>
    /// 持续标识
    /// </summary>
    public Int16? Sustain { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    public MedServiceCategoryEnum UsageScope { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 频次数据导入模板实体
/// </summary>
public class ExportFrequencyOutput : ImportFrequencyInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}