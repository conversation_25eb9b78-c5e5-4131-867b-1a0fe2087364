<template>
	<el-dialog v-model="state.isShowDialog" title="押金退款" width="700px" :close-on-click-modal="false" :close-on-press-escape="false" draggable>
		<!-- 账户信息 -->
		<el-card shadow="never" style="margin-bottom: 20px">
			<template #header>
				<span>账户信息</span>
			</template>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="住院号">{{ state.accountInfo.inpatientNo }}</el-descriptions-item>
				<el-descriptions-item label="当前余额">
					<span :class="(state.accountInfo.currentBalance || 0) > 0 ? 'text-success' : 'text-danger'"> ¥{{ (state.accountInfo.currentBalance || 0).toFixed(2) }} </span>
				</el-descriptions-item>
				<el-descriptions-item label="总缴费金额">
					<span class="text-success">¥{{ (state.accountInfo.totalPaidAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="总退款金额">
					<span class="text-warning">¥{{ (state.accountInfo.totalRefundedAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
			</el-descriptions>
		</el-card>

		<!-- 交易信息（针对特定交易退款时显示） -->
		<el-card shadow="never" style="margin-bottom: 20px" v-if="state.targetTransaction">
			<template #header>
				<span>退款交易信息</span>
			</template>
			<el-descriptions :column="2" border>
				<el-descriptions-item label="交易ID">{{ state.targetTransaction.id }}</el-descriptions-item>
				<el-descriptions-item label="交易金额">
					<span class="text-success">¥{{ (state.targetTransaction.amount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="可退金额">
					<span class="text-warning">¥{{ (state.targetTransaction.refundableAmount || 0).toFixed(2) }}</span>
				</el-descriptions-item>
				<el-descriptions-item label="交易时间">{{ state.targetTransaction.createTime }}</el-descriptions-item>
			</el-descriptions>
		</el-card>

		<!-- 退款表单 -->
		<el-form :model="state.ruleForm" :rules="state.rules" ref="ruleFormRef" label-width="120px" label-position="right">
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="退款金额" prop="totalRefundAmount">
						<el-input-number v-model="state.ruleForm.totalRefundAmount" placeholder="请输入退款金额" :min="0.01" :max="getMaxRefundAmount()" :precision="2" style="width: 100%" />
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item>
						<el-button type="primary" size="small" @click="setFullRefund" :disabled="getMaxRefundAmount() <= 0">
							{{ state.targetTransaction ? '最大退款' : '全额退款' }}
						</el-button>
					</el-form-item>
				</el-col>
				<el-col :span="24">
					<el-form-item label="退款原因" prop="reason">
						<el-input v-model="state.ruleForm.reason" type="textarea" :rows="4" placeholder="请输入退款原因或备注" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<!-- 退款提示 -->
		<el-alert title="退款说明" type="info" :closable="false" style="margin-bottom: 20px">
			<template #default>
				<ul style="margin: 0; padding-left: 20px">
					<li v-if="state.targetTransaction">针对特定交易的退款，只能退还该交易的可退金额</li>
					<li v-else>退款将按照缴费记录的时间倒序进行，优先退还最新的缴费</li>
					<li>退款金额不能超过{{ state.targetTransaction ? '该交易的可退金额' : '当前账户余额' }}</li>
					<li>退款操作不可撤销，请谨慎操作</li>
					<li>退款完成后将生成退款凭证</li>
				</ul>
			</template>
		</el-alert>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="closeDialog">取消</el-button>
				<el-button type="danger" @click="handleSubmit" :loading="state.loading"> 确认退款 </el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="RefundDialog">
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useDepositApi, type RefundDepositInput, type DepositAccount, type DepositTransaction } from '/@/api/inpatient/deposit';

// 定义 emits
const emit = defineEmits(['reload-data']);

// API实例
const depositApi = useDepositApi();

// 响应式数据
const state = reactive({
	isShowDialog: false,
	loading: false,
	accountInfo: {} as DepositAccount,
	targetTransaction: null as DepositTransaction | null,
	ruleForm: {
		accountId: 0,
		totalRefundAmount: undefined,
		reason: '',
	} as RefundDepositInput,
	rules: {
		totalRefundAmount: [
			{ required: true, message: '请输入退款金额', trigger: 'blur' },
			{ type: 'number', min: 0.01, message: '退款金额必须大于0', trigger: 'blur' },
		],
		reason: [
			{ required: true, message: '请输入退款原因', trigger: 'blur' },
			{ min: 1, max: 500, message: '退款原因长度在1到500个字符', trigger: 'blur' },
		],
	},
});

// 引用
const ruleFormRef = ref();

// 打开对话框
const openDialog = (accountInfo: DepositAccount, transaction?: DepositTransaction) => {
	state.accountInfo = { ...accountInfo };
	state.targetTransaction = transaction || null;
	state.isShowDialog = true;
	resetForm();
	state.ruleForm.accountId = accountInfo.id || 0;

	// 如果是针对特定交易的退款，设置最大退款金额
	if (transaction && transaction.refundableAmount) {
		state.ruleForm.totalRefundAmount = transaction.refundableAmount;
	}
};

// 关闭对话框
const closeDialog = () => {
	state.isShowDialog = false;
	resetForm();
};

// 重置表单
const resetForm = () => {
	state.ruleForm = {
		accountId: 0,
		totalRefundAmount: undefined,
		reason: '',
	};
	ruleFormRef.value?.clearValidate();
};

// 获取最大退款金额
const getMaxRefundAmount = () => {
	if (state.targetTransaction) {
		// 针对特定交易的退款，最大金额为该交易的可退金额
		return state.targetTransaction.refundableAmount || 0;
	} else {
		// 账户退款，最大金额为账户余额
		return state.accountInfo.currentBalance || 0;
	}
};

// 设置全额退款
const setFullRefund = () => {
	state.ruleForm.totalRefundAmount = getMaxRefundAmount();
};

// 提交表单
const handleSubmit = async () => {
	try {
		const valid = await ruleFormRef.value?.validate();
		if (!valid) return;

		// 验证退款金额
		const maxAmount = getMaxRefundAmount();
		if (state.ruleForm.totalRefundAmount! > maxAmount) {
			const errorMsg = state.targetTransaction ? '退款金额不能超过该交易的可退金额' : '退款金额不能超过当前账户余额';
			ElMessage.error(errorMsg);
			return;
		}

		// 确认对话框
		await ElMessageBox.confirm(`确认退款金额：¥${state.ruleForm.totalRefundAmount?.toFixed(2)}？\n退款原因：${state.ruleForm.reason}`, '确认退款', {
			confirmButtonText: '确认',
			cancelButtonText: '取消',
			type: 'warning',
		});

		state.loading = true;

		await depositApi.refund(state.ruleForm);

		ElMessage.success('押金退款成功');
		closeDialog();
		emit('reload-data');
	} catch (error: any) {
		if (error !== 'cancel') {
			console.error('押金退款失败:', error);
			ElMessage.error(error.message || '押金退款失败');
		}
	} finally {
		state.loading = false;
	}
};

// 暴露方法
defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.dialog-footer {
	text-align: right;
}

.text-success {
	color: #67c23a;
	font-weight: bold;
}

.text-warning {
	color: #e6a23c;
	font-weight: bold;
}

.text-danger {
	color: #f56c6c;
	font-weight: bold;
}

:deep(.el-input),
:deep(.el-input-number) {
	width: 100%;
}
</style>
