﻿using Magicodes.ExporterAndImporter.Core;

namespace His.Module.Registration;

/// <summary>
/// 分诊队列输出参数
/// </summary>
public class TriageQueueOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 排班计划ID
    /// </summary>
    public long? SchedulingPlanId { get; set; }    
    
    /// <summary>
    /// 诊室ID
    /// </summary>
    public long? RoomId { get; set; }    
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public string? RoomName { get; set; }    
    
    /// <summary>
    /// 分诊台ID
    /// </summary>
    public long? ConsoleId { get; set; }    
    
    /// <summary>
    /// 分诊台名称
    /// </summary>
    public string? ConsoleName { get; set; }    
    
    /// <summary>
    /// 排队号
    /// </summary>
    public int? QueueNumber { get; set; }    
    
    /// <summary>
    /// 状态（如排队中、已就诊等）
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 时间段ID
    /// </summary>
    public long? TimePeriodId { get; set; }    
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    public string? TimePeriodCode { get; set; }    
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    public string? TimePeriodName { get; set; }    
    
    /// <summary>
    /// 时间段开始时间
    /// </summary>
    public DateTime? TimePeriodStartTime { get; set; }    
    
    /// <summary>
    /// 时间段结束时间
    /// </summary>
    public DateTime? TimePeriodEndTime { get; set; }    
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 分诊队列数据导入模板实体
/// </summary>
public class ExportTriageQueueOutput : ImportTriageQueueInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
