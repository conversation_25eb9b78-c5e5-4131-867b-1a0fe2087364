﻿using Admin.NET.Core;
namespace His.Module.Registration.Entity;

/// <summary>
/// 分诊队列表，用于记录患者排队信息
/// </summary>
[Tenant("1300000000004")]
[SugarTable("triage_queue_detail", "分诊队列表，用于记录患者排队信息")]
public class TriageQueueDetail : EntityTenant
{
    /// <summary>
    /// 分诊队列表ID
    /// </summary>
    [SugarColumn(ColumnName = "triage_queue_detail_id", ColumnDescription = "分诊队列表ID")]
    public virtual long? TriageQueueDetailId { get; set; }
    
    /// <summary>
    /// 挂号ID
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "挂号ID")]
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 255)]
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 诊室ID
    /// </summary>
    [SugarColumn(ColumnName = "room_id", ColumnDescription = "诊室ID")]
    public virtual long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [SugarColumn(ColumnName = "room_name", ColumnDescription = "诊室名称", Length = 255)]
    public virtual string? RoomName { get; set; }
    
    /// <summary>
    /// 分诊台ID
    /// </summary>
    [SugarColumn(ColumnName = "console_id", ColumnDescription = "分诊台ID")]
    public virtual long? ConsoleId { get; set; }
    
    /// <summary>
    /// 分诊台名称
    /// </summary>
    [SugarColumn(ColumnName = "console_name", ColumnDescription = "分诊台名称", Length = 255)]
    public virtual string? ConsoleName { get; set; }
    
    /// <summary>
    /// 排队号
    /// </summary>
    [SugarColumn(ColumnName = "queue_number", ColumnDescription = "排队号")]
    public virtual int? QueueNumber { get; set; }
    
    /// <summary>
    /// 状态（0 未分诊 1 已分诊 2 已就诊）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态（0 未分诊 1 已分诊 2 已就诊）")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 优先级(0:普通,1:复诊,2:老年人,3:军人等)
    /// </summary>
    [SugarColumn(ColumnName = "priority_level", ColumnDescription = "优先级(0:普通,1:复诊,2:老年人,3:军人等)")]
    public virtual int? PriorityLevel { get; set; }
    
    /// <summary>
    /// 时间段ID
    /// </summary>
    [SugarColumn(ColumnName = "time_period_id", ColumnDescription = "时间段ID")]
    public virtual long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    [SugarColumn(ColumnName = "time_period_code", ColumnDescription = "时间段编码", Length = 64)]
    public virtual string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    [SugarColumn(ColumnName = "time_period_name", ColumnDescription = "时间段名称", Length = 64)]
    public virtual string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 分诊时间
    /// </summary>
    [SugarColumn(ColumnName = "triage_time", ColumnDescription = "分诊时间")]
    public virtual DateTime? TriageTime { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(ColumnName = "start_time", ColumnDescription = "开始时间")]
    public virtual DateTime? StartTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(ColumnName = "end_time", ColumnDescription = "结束时间")]
    public virtual DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
