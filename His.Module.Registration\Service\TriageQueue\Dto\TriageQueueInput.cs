﻿using Admin.NET.Core;
using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Registration;

/// <summary>
/// 分诊队列基础输入参数
/// </summary>
public class TriageQueueBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 排班计划ID
    /// </summary>
    public virtual long? SchedulingPlanId { get; set; }
    
    /// <summary>
    /// 诊室ID
    /// </summary>
    public virtual long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public virtual string? RoomName { get; set; }
    
    /// <summary>
    /// 分诊台ID
    /// </summary>
    public virtual long? ConsoleId { get; set; }
    
    /// <summary>
    /// 分诊台名称
    /// </summary>
    public virtual string? ConsoleName { get; set; }
    
    /// <summary>
    /// 排队号
    /// </summary>
    public virtual int? QueueNumber { get; set; }
    
    /// <summary>
    /// 状态（如排队中、已就诊等）
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 时间段ID
    /// </summary>
    public virtual long? TimePeriodId { get; set; }
    

    /// <summary>
    /// 时间段编码
    /// </summary>
    public virtual string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    public virtual string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 时间段开始时间
    /// </summary>
    public virtual DateTime? TimePeriodStartTime { get; set; }
    
    /// <summary>
    /// 时间段结束时间
    /// </summary>
    public virtual DateTime? TimePeriodEndTime { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 分诊队列分页查询输入参数
/// </summary>
public class PageTriageQueueInput : BasePageInput
{
    public virtual long?     DeptId { get; set; }
    /// <summary>
    /// 排班计划ID
    /// </summary>
    public long? SchedulingPlanId { get; set; }
    
    /// <summary>
    /// 诊室ID
    /// </summary>
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 分诊台ID
    /// </summary>
    public long? ConsoleId { get; set; }
    
    /// <summary>
    /// 分诊台名称
    /// </summary>
    public string? ConsoleName { get; set; }
    
    /// <summary>
    /// 排队号
    /// </summary>
    public int? QueueNumber { get; set; }
    
    /// <summary>
    /// 状态（如排队中、已就诊等）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 时间段ID
    /// </summary>
    public long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    public string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    public string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 时间段开始时间范围
    /// </summary>
     public DateTime?[] TimePeriodStartTimeRange { get; set; }
    
    /// <summary>
    /// 时间段结束时间范围
    /// </summary>
     public DateTime?[] TimePeriodEndTimeRange { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 分诊队列增加输入参数
/// </summary>
public class AddTriageQueueInput
{
    /// <summary>
    /// 排班计划ID
    /// </summary>
    public long? SchedulingPlanId { get; set; }
    /// <summary>
    /// 诊室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "诊室名称字符长度不能超过255")]
    public string? DeptName { get; set; }
    /// <summary>
    /// 诊室ID
    /// </summary>
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "诊室名称字符长度不能超过255")]
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 分诊台ID
    /// </summary>
    public long? ConsoleId { get; set; }
    
    /// <summary>
    /// 分诊台名称
    /// </summary>
    [MaxLength(255, ErrorMessage = "分诊台名称字符长度不能超过255")]
    public string? ConsoleName { get; set; }
    
    /// <summary>
    /// 排队号
    /// </summary>
    public int? QueueNumber { get; set; }
    
    /// <summary>
    /// 状态（如排队中、已就诊等）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 时间段ID
    /// </summary>
    public long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    [MaxLength(64, ErrorMessage = "时间段编码字符长度不能超过64")]
    public string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "时间段名称字符长度不能超过64")]
    public string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 时间段开始时间
    /// </summary>
    public DateTime? TimePeriodStartTime { get; set; }
    
    /// <summary>
    /// 时间段结束时间
    /// </summary>
    public DateTime? TimePeriodEndTime { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 分诊队列删除输入参数
/// </summary>
public class DeleteTriageQueueInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 分诊队列更新输入参数
/// </summary>
public class UpdateTriageQueueInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 排班计划ID
    /// </summary>    
    public long? SchedulingPlanId { get; set; }
    
    /// <summary>
    /// 诊室ID
    /// </summary>    
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>    
    [MaxLength(255, ErrorMessage = "诊室名称字符长度不能超过255")]
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 分诊台ID
    /// </summary>    
    public long? ConsoleId { get; set; }
    
    /// <summary>
    /// 分诊台名称
    /// </summary>    
    [MaxLength(255, ErrorMessage = "分诊台名称字符长度不能超过255")]
    public string? ConsoleName { get; set; }
    
    /// <summary>
    /// 排队号
    /// </summary>    
    public int? QueueNumber { get; set; }
    
    /// <summary>
    /// 状态（如排队中、已就诊等）
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 时间段ID
    /// </summary>    
    public long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>    
    [MaxLength(64, ErrorMessage = "时间段编码字符长度不能超过64")]
    public string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>    
    [MaxLength(64, ErrorMessage = "时间段名称字符长度不能超过64")]
    public string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 时间段开始时间
    /// </summary>    
    public DateTime? TimePeriodStartTime { get; set; }
    
    /// <summary>
    /// 时间段结束时间
    /// </summary>    
    public DateTime? TimePeriodEndTime { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
}

/// <summary>
/// 分诊队列主键查询输入参数
/// </summary>
public class QueryByIdTriageQueueInput : DeleteTriageQueueInput
{
}

/// <summary>
/// 分诊队列数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportTriageQueueInput : BaseImportInput
{
    /// <summary>
    /// 排班计划ID
    /// </summary>
    [ImporterHeader(Name = "排班计划ID")]
    [ExporterHeader("排班计划ID", Format = "", Width = 25, IsBold = true)]
    public long? SchedulingPlanId { get; set; }
    
    /// <summary>
    /// 诊室ID
    /// </summary>
    [ImporterHeader(Name = "诊室ID")]
    [ExporterHeader("诊室ID", Format = "", Width = 25, IsBold = true)]
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [ImporterHeader(Name = "诊室名称")]
    [ExporterHeader("诊室名称", Format = "", Width = 25, IsBold = true)]
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 分诊台ID
    /// </summary>
    [ImporterHeader(Name = "分诊台ID")]
    [ExporterHeader("分诊台ID", Format = "", Width = 25, IsBold = true)]
    public long? ConsoleId { get; set; }
    
    /// <summary>
    /// 分诊台名称
    /// </summary>
    [ImporterHeader(Name = "分诊台名称")]
    [ExporterHeader("分诊台名称", Format = "", Width = 25, IsBold = true)]
    public string? ConsoleName { get; set; }
    
    /// <summary>
    /// 排队号
    /// </summary>
    [ImporterHeader(Name = "排队号")]
    [ExporterHeader("排队号", Format = "", Width = 25, IsBold = true)]
    public int? QueueNumber { get; set; }
    
    /// <summary>
    /// 状态（如排队中、已就诊等）
    /// </summary>
    [ImporterHeader(Name = "状态（如排队中、已就诊等）")]
    [ExporterHeader("状态（如排队中、已就诊等）", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 时间段ID
    /// </summary>
    [ImporterHeader(Name = "时间段ID")]
    [ExporterHeader("时间段ID", Format = "", Width = 25, IsBold = true)]
    public long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    [ImporterHeader(Name = "时间段编码")]
    [ExporterHeader("时间段编码", Format = "", Width = 25, IsBold = true)]
    public string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    [ImporterHeader(Name = "时间段名称")]
    [ExporterHeader("时间段名称", Format = "", Width = 25, IsBold = true)]
    public string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 时间段开始时间
    /// </summary>
    [ImporterHeader(Name = "时间段开始时间")]
    [ExporterHeader("时间段开始时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? TimePeriodStartTime { get; set; }
    
    /// <summary>
    /// 时间段结束时间
    /// </summary>
    [ImporterHeader(Name = "时间段结束时间")]
    [ExporterHeader("时间段结束时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? TimePeriodEndTime { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
