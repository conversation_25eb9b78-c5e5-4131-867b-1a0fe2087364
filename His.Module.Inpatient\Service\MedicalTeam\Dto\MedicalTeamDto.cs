﻿namespace His.Module.Inpatient;

/// <summary>
/// 医疗组维护输出参数
/// </summary>
public class MedicalTeamDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 医疗组名称
    /// </summary>
    public string TeamName { get; set; }
    
    /// <summary>
    /// 所属科室ID
    /// </summary>
    public long DeptId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string DeptName { get; set; }
    
    /// <summary>
    /// 医疗组类型 字典医疗组
    /// </summary>
    public string TeamType { get; set; }
    
    /// <summary>
    /// 组长ID
    /// </summary>
    public long? TeamLeaderId { get; set; }
    
    /// <summary>
    /// 组长
    /// </summary>
    public string? TeamLeaderName { get; set; }
    
    /// <summary>
    /// 成立日期
    /// </summary>
    public DateTime? EstablishDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建用户ID
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建用户名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 更新用户ID
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 更新用户名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 是否删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
