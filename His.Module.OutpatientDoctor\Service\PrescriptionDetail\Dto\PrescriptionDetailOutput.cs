﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 处方明细表输出参数
/// </summary>
public class PrescriptionDetailOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 处方主表Id
    /// </summary>
    public long? PrescriptionId { get; set; }

    /// <summary>
    /// 药品Id
    /// </summary>
    public long? DrugId { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    /// <summary>
    /// 药品名称
    /// </summary>
    public string?  DrugType{ get; set; }
    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 单次量
    /// </summary>
    public decimal? SingleDose { get; set; }

    /// <summary>
    /// 单次量单位
    /// </summary>
    public string? SingleDoseUnit { get; set; }

    /// <summary>
    /// 给药途径Id
    /// </summary>
    public long? MedicationRoutesId { get; set; }

    /// <summary>
    /// 给药途径名称
    /// </summary>
    public string? MedicationRoutesName { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    public string? FrequencyName { get; set; }

    /// <summary>
    /// 用药天数
    /// </summary>
    public Int16? MedicationDays { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 药房Id
    /// </summary>
    public long? StorageId { get; set; }

    /// <summary>
    /// 药房名称
    /// </summary>
    public string? StorageName { get; set; }
    /// <summary>
    /// 库存id
    /// </summary>
    public long? InventoryId { get; set; }
    /// <summary>
    /// 组标志
    /// </summary>
    public string? GroupFlag { get; set; }

    /// <summary>
    /// 组号
    /// </summary>
    public string? GroupNo { get; set; }

    /// <summary>
    /// 药品限制标志
    /// </summary>
    public Int16? DrugLimitFlag { get; set; }

    /// <summary>
    /// 药品待发标志
    /// </summary>
    public Int16? DrugPendingFlag { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 剂量单位
    /// </summary>
    public string? DosageUnit { get; set; }

    /// <summary>
    /// 剂量值
    /// </summary>
    public decimal? DosageValue { get; set; }

    /// <summary>
    /// 含量
    /// </summary>
    public decimal? ContentValue { get; set; }

    /// <summary>
    /// 含量单位
    /// </summary>
    public string? ContentUnit { get; set; }

    /// <summary>
    /// 门诊包装数量
    /// </summary>
    public int? OutpatientPackageQuantity { get; set; }

    /// <summary>
    /// 最小包装单位
    /// </summary>
    public string? MinPackageUnit { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 退费人员Id
    /// </summary>
    public long? RefundStaffId { get; set; }

    /// <summary>
    /// 退费时间
    /// </summary>
    public DateTime? RefundTime { get; set; }

    /// <summary>
    /// 库存零售价
    /// </summary>
    public decimal? InventorySalePrice { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    public decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    public int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    public DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人Id
    /// </summary>
    public long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人名称
    /// </summary>
    public string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 用药方式 1治疗用药 2预防用药
    /// </summary>
    public Int16? MedicationMethod { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 用法Id
    /// </summary>
    public long? UsageId { get; set; }

    /// <summary>
    /// 用法编码
    /// </summary>
    public string? UsageCode { get; set; }

    /// <summary>
    /// 用法名称
    /// </summary>
    public string? UsageName { get; set; }

    /// <summary>
    /// 是否皮试
    /// </summary>
    public int? IsSkinTest { get; set; }

    /// <summary>
    /// 皮试结果
    /// </summary>
    public int? SkinTestResults { get; set; }

    /// <summary>
    /// 创建者部门Id
    /// </summary>
    public long? CreateOrgId { get; set; }

    /// <summary>
    /// 创建者部门名称
    /// </summary>
    public string? CreateOrgName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
     

 

}