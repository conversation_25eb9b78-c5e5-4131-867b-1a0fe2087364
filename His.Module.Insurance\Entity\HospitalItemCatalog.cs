﻿namespace His.Module.Insurance.Entity;

/// <summary>
/// 医院项目目录
/// </summary>
[Tenant("1300000000013")]
[SugarTable("hospital_item_catalog", "医院项目目录")]
public class HospitalItemCatalog : EntityTenant
{
    /// <summary>
    /// 医院项目编码
    /// </summary>
    [SugarColumn(ColumnName = "yyxm_bm", ColumnDescription = "医院项目编码", Length = 50)]
    public virtual string Yyxmbm { get; set; }
    
    /// <summary>
    /// 医院项目名称
    /// </summary>
    [SugarColumn(ColumnName = "yyxm_mc", ColumnDescription = "医院项目名称", Length = 200)]
    public virtual string Yyxmmc { get; set; }

    /// <summary>
    /// 门诊结算项目编号
    /// </summary>
    [SugarColumn(ColumnName = "mzjs_xmbh", ColumnDescription = "门诊结算项目编号", Length = 50)]
    public virtual string? Mzjsxmbh { get; set; }

    /// <summary>
    /// 住院结算项目编号
    /// </summary>
    [SugarColumn(ColumnName = "zyjs_xmbh", ColumnDescription = "住院结算项目编号", Length = 50)]
    public virtual string? Zyjsxmbh { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    [SugarColumn(ColumnName = "zf_bl", ColumnDescription = "自付比例")]
    public virtual decimal? Zfbl { get; set; }

    /// <summary>
    /// 自付比例说明
    /// </summary>
    [SugarColumn(ColumnName = "sm", ColumnDescription = "自付比例说明", Length = 200)]
    public virtual string? Sm { get; set; }

    /// <summary>
    /// 医疗项目编码
    /// </summary>
    [SugarColumn(ColumnName = "ylxm_bm", ColumnDescription = "医疗项目编码", Length = 50)]
    public virtual string? Ylxmbm { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "gg", ColumnDescription = "规格", Length = 100)]
    public virtual string? Gg { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "dw", ColumnDescription = "单位", Length = 20)]
    public virtual string? Dw { get; set; }

    /// <summary>
    /// 参考价
    /// </summary>
    [SugarColumn(ColumnName = "ckj", ColumnDescription = "参考价")]
    public virtual decimal? Ckj { get; set; }

    /// <summary>
    /// 剂型
    /// </summary>
    [SugarColumn(ColumnName = "jxm", ColumnDescription = "剂型", Length = 50)]
    public virtual string? Jxm { get; set; }

    /// <summary>
    /// 生产企业
    /// </summary>
    [SugarColumn(ColumnName = "scqy", ColumnDescription = "生产企业", Length = 200)]
    public virtual string? Scqy { get; set; }

    /// <summary>
    /// 处方药标志
    /// </summary>
    [SugarColumn(ColumnName = "cfy_bz", ColumnDescription = "处方药标志", Length = 10)]
    public virtual string? Cfybz { get; set; }

    /// <summary>
    /// GMP标志
    /// </summary>
    [SugarColumn(ColumnName = "gmp_bz", ColumnDescription = "GMP标志", Length = 10)]
    public virtual string? Gmpbz { get; set; }

    /// <summary>
    /// 最小规格
    /// </summary>
    [SugarColumn(ColumnName = "zx_gg", ColumnDescription = "最小规格", Length = 100)]
    public virtual string? Zxgg { get; set; }

    /// <summary>
    /// 包含数量
    /// </summary>
    [SugarColumn(ColumnName = "bzsl", ColumnDescription = "包含数量")]
    public virtual decimal? Bzsl { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(ColumnName = "gxsj", ColumnDescription = "更新时间", Length = 50)]
    public virtual string? Gxsj { get; set; }

    /// <summary>
    /// 起始日期
    /// </summary>
    [SugarColumn(ColumnName = "qsrq", ColumnDescription = "起始日期")]
    public virtual DateTime? Qsrq { get; set; }

    /// <summary>
    /// 终止日期
    /// </summary>
    [SugarColumn(ColumnName = "zzrq", ColumnDescription = "终止日期")]
    public virtual DateTime? Zzrq { get; set; }

    /// <summary>
    /// 审批标志
    /// </summary>
    [SugarColumn(ColumnName = "sp_bz", ColumnDescription = "审批标志", Length = 10)]
    public virtual string? Spbz { get; set; }
    
    /// <summary>
    /// 险种标志
    /// </summary>
    [SugarColumn(ColumnName = "xz_bz", ColumnDescription = "险种标志", Length = 10)]
    public virtual string? Xzbz { get; set; }
    
    /// <summary>
    /// 药品标志
    /// </summary>
    [SugarColumn(ColumnName = "yp_bz", ColumnDescription = "药品标志", Length = 10)]
    public virtual string? Ypbz { get; set; }
    
    /// <summary>
    /// 人群类别
    /// </summary>
    [SugarColumn(ColumnName = "rq_lb", ColumnDescription = "人群类别", Length = 10)]
    public virtual string? Rqlb { get; set; }

    /// <summary>
    /// 包装规格
    /// </summary>
    [SugarColumn(ColumnName = "bzgg", ColumnDescription = "包装规格", Length = 100)]
    public virtual string? Bzgg { get; set; }
    
    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    [SugarColumn(ColumnName = "yltc_lb", ColumnDescription = "医疗统筹类别", Length = 10)]
    public virtual string? Yltclb { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [SugarColumn(ColumnName = "dj", ColumnDescription = "单价")]
    public virtual decimal Dj { get; set; }


    /// <summary>
    /// 同步序号
    /// </summary>
    [SugarColumn(ColumnName = "sxh", ColumnDescription = "同步序号")]
    public virtual long? Sxh { get; set; }

    /// <summary>
    /// 最后同步时间
    /// </summary>
    [SugarColumn(ColumnName = "last_sync_time", ColumnDescription = "最后同步时间")]
    public virtual DateTime? LastSyncTime { get; set; }
    
}
