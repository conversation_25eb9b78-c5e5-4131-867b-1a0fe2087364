﻿namespace His.Module.Pharmacy;

/// <summary>
/// 药品盘点输出参数
/// </summary>
public class StorageTakingRecordDto
{
    /// <summary>
    /// 库房
    /// </summary>
    public string StorageIdFkColumn { get; set; }
    
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 盘点单号
    /// </summary>
    public string? TakingNo { get; set; }
    
    /// <summary>
    /// 盘点时间
    /// </summary>
    public DateTime? TakingTime { get; set; }
    
    /// <summary>
    /// 盘点结果
    /// </summary>
    public int? TakingResult { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    public decimal? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    public decimal? TakingQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    public decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    public decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
