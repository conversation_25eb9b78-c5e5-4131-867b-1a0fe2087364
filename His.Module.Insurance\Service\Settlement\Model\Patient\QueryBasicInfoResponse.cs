using His.Module.Insurance.Service.Settlement.Dto;
namespace His.Module.Insurance.Service.Settlement.Model.Patient;
 

/// <summary>
/// 查询个人基本信息的响应类
/// </summary>
public class QueryBasicInfoResponse:BaseSettlementResponse
{


    /// <summary>
    /// 姓名
    /// </summary>
    public string xm { get; set; }

    /// <summary>
    /// 性别（1:男，2:女，9:不确定，可调用数据字典接口获取，代码编号：XB）
    /// </summary>
    public string xb { get; set; }

    /// <summary>
    /// 社会保障号码（社会保障号码或者身份证号）
    /// </summary>
    public string grbh { get; set; }

    /// <summary>
    /// 支付标志（灰名单标志）（0 :灰名单，1:白名单）
    /// </summary>
    public string zfbz { get; set; }

    /// <summary>
    /// 支付说明（灰名单原因）（如果是白名单该值为空）
    /// </summary>
    public string zfsm { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string dwmc { get; set; }

    /// <summary>
    /// 医疗人员类别（内容为汉字）
    /// </summary>
    public string ylrylb { get; set; }

    /// <summary>
    /// 异地标志（1:异地，0:不是异地）
    /// </summary>
    public string ydbz { get; set; }

    /// <summary>
    /// 疾病编码（结果格式是: 疾病病种的名称1 +’#m’+疾病病种编码1 + ‘/’ + 疾病病种的名称2 +’#m’+疾病病种编码2 + ‘/’ + ……）
    /// </summary>
    public string mzdbjbs { get; set; }

    /// <summary>
    /// 门诊大病备注
    /// </summary>
    public string mzdbbz { get; set; }

    /// <summary>
    /// 社保局编码
    /// </summary>
    public string sbjgbh { get; set; }

    /// <summary>
    /// 有无15(医保参数控制)天内的住院记录（1:有，0 :无）
    /// </summary>
    public string zhzybz { get; set; }

    /// <summary>
    /// 15(医保参数控制)天内的住院记录说明
    /// </summary>
    public string zhzysm { get; set; }

    /// <summary>
    /// 转出医院名称（如果zcyymc不为“”和“*”,则表示本次住院是市内转院来的）
    /// </summary>
    public string zcyymc { get; set; }

    /// <summary>
    /// 转出医院出院日期
    /// </summary>
    public string zccyrq { get; set; }

    /// <summary>
    /// 人群类别（A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB）
    /// </summary>
    public string rqlb { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public string csrq { get; set; }

    /// <summary>
    /// 优抚对象标志（济南专用。’1’为优抚对象）
    /// </summary>
    public string yfdxbz { get; set; }

    /// <summary>
    /// 优抚对象类别（济南专用。(汉字说明)）
    /// </summary>
    public string yfdxlb { get; set; }

    /// <summary>
    /// 参保地市编号（省异地用）
    /// </summary>
    public string cbdsbh { get; set; }

    /// <summary>
    /// 参保机构名称（省异地用）
    /// </summary>
    public string cbjgmc { get; set; }

    /// <summary>
    /// 多社保局标志（0：标识病人只有一个参保身份，1：标识病人有多个参保身份 （使用mhs.dll动态库时，无需处理改参数））
    /// </summary>
    public string multisbj { get; set; }

    /// <summary>
    /// 多社保局信息（当multisbj为1时，会返回此数据集，数据集中为病人的多个参保身份，his可根据病人需求选择其中一个身份进行业务办理（使用mhs.dll动态库时，无需处理改参数，否则需单独做页面展示多个社保局供操作员选择））
    /// </summary>
    public List<MultiSbjInfo> multisbjds { get; set; }

    /// <summary>
    /// 门诊定点标志（值为1时，当前医院是该参保人的门诊统筹定点医院。（异地不返回））
    /// </summary>
    public string mzddbz { get; set; }

    /// <summary>
    /// 门诊定点说明（参保人的门诊统筹定点医院说明（异地不返回））
    /// </summary>
    public string mzddsm { get; set; }

    /// <summary>
    /// 普通门诊慢性病备案疾病（威海专用：医疗统筹类别为6时，返回普通门诊慢性病备案疾病）
    /// </summary>
    public string ptmzjbs { get; set; }

    /// <summary>
    /// 余额
    /// </summary>
    public double ye { get; set; }

    /// <summary>
    /// 救助人员类别（具体值调用数据字典接口获取，代码编号：JZRYLB）
    /// </summary>
    public string jzrylb { get; set; }

    /// <summary>
    /// 贫困人口标志（1：贫困人口，0：非贫困人口）
    /// </summary>
    public string pkrkbz { get; set; }

    /// <summary>
    /// 单位性质名称
    /// </summary>
    public string dwxzmc { get; set; }

    /// <summary>
    /// 居民两病备案疾病编码
    /// </summary>
    public string lbjbbm { get; set; }

    /// <summary>
    /// 工伤检查信息（济南使用）
    /// </summary>
    public string gsjcxx { get; set; }

    /// <summary>
    /// 门慢的二级代码（聊城使用）
    /// </summary>
    public string mzmxm_ejjbbm { get; set; }

    /// <summary>
    /// 门慢的二级名称（聊城使用）
    /// </summary>
    public string mzmxm_ejjbmc { get; set; }

    /// <summary>
    /// 人员id
    /// </summary>
    public string ryid { get; set; }
}

/// <summary>
/// 多社保局信息类
/// </summary>
public class MultiSbjInfo
{
    /// <summary>
    /// 单位名称
    /// </summary>
    public string dwmc { get; set; }

    /// <summary>
    /// 参保状态
    /// </summary>
    public string cbzt { get; set; }

    /// <summary>
    /// 险种标志
    /// </summary>
    public string xzbz { get; set; }

    /// <summary>
    /// 社保机构编号
    /// </summary>
    public string sbjgbh { get; set; }

    /// <summary>
    /// 参保人员类别
    /// </summary>
    public string cbrylb { get; set; }

    /// <summary>
    /// 保机构名称
    /// </summary>
    public string sbjgmc { get; set; }
    
    /// <summary>
    /// 社保保障号码
    /// </summary>
    public string shbzhm { get; set; }
    
}
   