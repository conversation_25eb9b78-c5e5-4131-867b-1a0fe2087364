namespace His.Module.Insurance.Service.Settlement.Model.Outpatient;

/// <summary>
/// 门诊预结算接口请求参数（查询类）
/// </summary>
public class SettleMzRequest:SettleMzPreRequest
{
    /// <summary>
    /// 个人账户支付金额（大于0时忽略是否使用个账参数）
    /// </summary>
    public decimal? grzhzf { get; set; }

    /// <summary>
    /// 是否使用个人账户（虚账户地区专用）
    /// </summary>
    public string sfsygrzh { get; set; }

    /// <summary>
    /// 是否需要返回结算单（1是 0否，默认1）
    /// </summary>
    public string needjsd { get; set; }

    /// <summary>
    /// 病人ID（诊间审核用）
    /// </summary>
    public string hzid { get; set; }

    /// <summary>
    /// 就诊标识（诊间审核用）
    /// </summary>
    public string jzbs { get; set; }

    /// <summary>
    /// 是否本院职工（诊间审核用）
    /// </summary>
    public string sfbyzg { get; set; }

    /// <summary>
    /// 病区标识（诊间审核用）
    /// </summary>
    public string bqbs { get; set; }

    /// <summary>
    /// 主诊断编码（诊间审核用）
    /// </summary>
    public string zzdbm { get; set; }

    /// <summary>
    /// 主诊断名称（诊间审核用）
    /// </summary>
    public string zzdmc { get; set; }

    /// <summary>
    /// 单病种结算标识（诊间审核用）
    /// </summary>
    public string dbzjsbs { get; set; }

    /// <summary>
    /// 病房号（诊间审核用）
    /// </summary>
    public string bf { get; set; }

    /// <summary>
    /// 病床号（诊间审核用）
    /// </summary>
    public string cw { get; set; }

    /// <summary>
    /// 病种类型（诊间审核用）
    /// </summary>
    public string bzlx { get; set; }

    /// <summary>
    /// 病种代码（诊间审核用）
    /// </summary>
    public string bzdm { get; set; }

    /// <summary>
    /// 病种名称（诊间审核用）
    /// </summary>
    public string bzmc { get; set; }

    /// <summary>
    /// 医疗类别（诊间审核用）
    /// </summary>
    public string yllb { get; set; }

    /// <summary>
    /// 生育状态（诊间审核用）
    /// </summary>
    public string syzt { get; set; }

    /// <summary>
    /// 多诊断信息（诊间审核用）
    /// </summary>
    public List<SettleMzDzd> Dzd { get; set; }
}
/// <summary>
/// 多诊断信息结构体（诊间审核用）
/// </summary>
public class SettleMzDzd
{
    /// <summary>
    /// 诊断编码（ICD-10）
    /// </summary>
    public string zdbm { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    public string zdmc { get; set; }

    /// <summary>
    /// 诊断类别：1=入院诊断，2=出院诊断
    /// </summary>
    public string zdlb { get; set; }

    /// <summary>
    /// 诊断序号：0=主要诊断，1=第一辅诊，2=第二辅诊...
    /// </summary>
    public string zdlb2 { get; set; }

    /// <summary>
    /// 诊断内容（完整诊断描述）
    /// </summary>
    public string zdnr { get; set; }

    /// <summary>
    /// 入院病情：1.危 2.急 3.一般
    /// </summary>
    public string rybq { get; set; }

    /// <summary>
    /// 诊断时间，格式：yyyyMMddHHmmss
    /// </summary>
    public string zdsj { get; set; }
}