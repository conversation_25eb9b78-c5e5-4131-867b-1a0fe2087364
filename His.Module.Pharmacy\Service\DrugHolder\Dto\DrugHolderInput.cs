﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品持有人表基础输入参数
/// </summary>
public class DrugHolderBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 持有人名称
    /// </summary>
    public virtual string? HolderName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品持有人表分页查询输入参数
/// </summary>
public class PageDrugHolderInput : BasePageInput
{
    /// <summary>
    /// 持有人名称
    /// </summary>
    public string? HolderName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品持有人表增加输入参数
/// </summary>
public class AddDrugHolderInput
{
    /// <summary>
    /// 持有人名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "持有人名称字符长度不能超过100")]
    public string? HolderName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品持有人表删除输入参数
/// </summary>
public class DeleteDrugHolderInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品持有人表更新输入参数
/// </summary>
public class UpdateDrugHolderInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 持有人名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "持有人名称字符长度不能超过100")]
    public string? HolderName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品持有人表主键查询输入参数
/// </summary>
public class QueryByIdDrugHolderInput : DeleteDrugHolderInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetDrugHolderStatusInput : BaseStatusInput
{
}

/// <summary>
/// 药品持有人表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugHolderInput : BaseImportInput
{
    /// <summary>
    /// 持有人名称
    /// </summary>
    [ImporterHeader(Name = "持有人名称")]
    [ExporterHeader("持有人名称", Format = "", Width = 25, IsBold = true)]
    public string? HolderName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
}
