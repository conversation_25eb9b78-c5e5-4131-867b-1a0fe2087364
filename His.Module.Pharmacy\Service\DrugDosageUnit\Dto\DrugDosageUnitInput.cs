﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品剂量单位表基础输入参数
/// </summary>
public class DrugDosageUnitBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 剂量单位名称
    /// </summary>
    public virtual string? UnitName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品剂量单位表分页查询输入参数
/// </summary>
public class PageDrugDosageUnitInput : BasePageInput
{
    /// <summary>
    /// 剂量单位名称
    /// </summary>
    public string? UnitName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品剂量单位表增加输入参数
/// </summary>
public class AddDrugDosageUnitInput
{
    /// <summary>
    /// 剂量单位名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "剂量单位名称字符长度不能超过100")]
    public string? UnitName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品剂量单位表删除输入参数
/// </summary>
public class DeleteDrugDosageUnitInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品剂量单位表更新输入参数
/// </summary>
public class UpdateDrugDosageUnitInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 剂量单位名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "剂量单位名称字符长度不能超过100")]
    public string? UnitName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品剂量单位表主键查询输入参数
/// </summary>
public class QueryByIdDrugDosageUnitInput : DeleteDrugDosageUnitInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetDrugDosageUnitStatusInput : BaseStatusInput
{
}

/// <summary>
/// 药品剂量单位表数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugDosageUnitInput : BaseImportInput
{
    /// <summary>
    /// 剂量单位名称
    /// </summary>
    [ImporterHeader(Name = "剂量单位名称")]
    [ExporterHeader("剂量单位名称", Format = "", Width = 25, IsBold = true)]
    public string? UnitName { get; set; }
    
    /// <summary>
    /// 剂量单位名称拼音
    /// </summary>
    [ImporterHeader(Name = "剂量单位名称拼音")]
    [ExporterHeader("剂量单位名称拼音", Format = "", Width = 25, IsBold = true)]
    public string? UnitPinyin { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
}
