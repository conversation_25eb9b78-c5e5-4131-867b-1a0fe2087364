﻿namespace His.Module.Shared.Entity;

/// <summary>
/// 挂号类别表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("reg_category", "挂号类别表")]
public class RegCategory : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码", Length = 32)]
    public virtual string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 32)]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 32)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 32)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 挂号费
    /// </summary>
    [SugarColumn(ColumnName = "registration_fee", ColumnDescription = "挂号费", Length = 16, DecimalDigits = 4)]
    public virtual decimal? RegistrationFee { get; set; }

    /// <summary>
    /// 诊疗费
    /// </summary>
    [SugarColumn(ColumnName = "consultation_fee", ColumnDescription = "诊疗费", Length = 16, DecimalDigits = 4)]
    public virtual decimal? ConsultationFee { get; set; }

    /// <summary>
    /// 收费项目id
    /// </summary>
    [SugarColumn(ColumnName = "charge_item_id", ColumnDescription = "收费项目id")]
    public virtual long? ChargeItemId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }
}