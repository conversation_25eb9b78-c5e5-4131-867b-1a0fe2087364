﻿namespace His.Module.MedicalTech.Service;

/// <summary>
/// 会诊表输出参数
/// </summary>
public class ConsultationOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>
    public string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    public long RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    public string VisitNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string PatientName { get; set; }

    /// <summary>
    /// 就诊时间
    /// </summary>
    public DateTime VisitTime { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    public string? AgeUnit { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    public int? Flag { get; set; }

    /// <summary>
    /// 期望会诊时间
    /// </summary>
    public DateTime? ExpectedTime { get; set; }

    /// <summary>
    /// 会诊类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 会诊状态
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// 病情摘要
    /// </summary>
    public string? ClinicalSummary { get; set; }

    /// <summary>
    /// 会诊目的
    /// </summary>
    public string? Purpose { get; set; }

    /// <summary>
    /// 会诊意见
    /// </summary>
    public string? ConsultationOpinion { get; set; }

    /// <summary>
    /// 意见填写时间
    /// </summary>
    public DateTime? OpinionTime { get; set; }

    /// <summary>
    /// 意见填写人Id
    /// </summary>
    public long? OpinionStaffId { get; set; }

    /// <summary>
    /// 意见填写人名称
    /// </summary>
    public string? OpinionStaffName { get; set; }

    /// <summary>
    /// 意见填写人签名
    /// </summary>
    public string? OpinionStaffSign { get; set; }

    /// <summary>
    /// 申请时间
    /// </summary>
    public DateTime? ApplyTime { get; set; }

    /// <summary>
    /// 申请科室Id
    /// </summary>
    public long? ApplyDeptId { get; set; }

    /// <summary>
    /// 申请科室名称
    /// </summary>
    public string? ApplyDeptName { get; set; }

    /// <summary>
    /// 申请医生Id
    /// </summary>
    public long? ApplyDoctorId { get; set; }

    /// <summary>
    /// 申请医生名称
    /// </summary>
    public string? ApplyDoctorName { get; set; }

    /// <summary>
    /// 申请医生签名
    /// </summary>
    public string? ApplyDoctorSign { get; set; }

    /// <summary>
    /// 会诊科室Id
    /// </summary>
    public long? ConsultationDeptId { get; set; }

    /// <summary>
    /// 会诊科室名称
    /// </summary>
    public string? ConsultationDeptName { get; set; }

    /// <summary>
    /// 会诊医生Id
    /// </summary>
    public long? ConsultationDoctorId { get; set; }

    /// <summary>
    /// 会诊医生名称
    /// </summary>
    public string? ConsultationDoctorName { get; set; }

    /// <summary>
    /// 会诊接受时间
    /// </summary>
    public DateTime? ConsultationAcceptTime { get; set; }

    /// <summary>
    /// 会诊结束时间
    /// </summary>
    public DateTime? ConsultationEndTime { get; set; }

    /// <summary>
    /// 院外会诊机构名称
    /// </summary>
    public string? OutsideHospitalName { get; set; }

    /// <summary>
    /// 收费项目Id
    /// </summary>
    public long? ItemId { get; set; }

    /// <summary>
    /// 创建者部门Id
    /// </summary>
    public long? CreateOrgId { get; set; }

    /// <summary>
    /// 创建者部门名称
    /// </summary>
    public string? CreateOrgName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
}