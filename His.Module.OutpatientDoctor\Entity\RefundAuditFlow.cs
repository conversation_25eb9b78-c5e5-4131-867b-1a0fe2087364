﻿using Admin.NET.Core;
namespace His.Module.OutpatientDoctor.Entity;

/// <summary>
/// 退费审核流程表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("refund_audit_flow", "退费审核流程表")]
public class RefundAuditFlow : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码", Length = 100)]
    public virtual string? Code { get; set; }
    
    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 200)]
    public virtual string? Name { get; set; }
    
    /// <summary>
    /// 流程状态
    /// </summary>
    [SugarColumn(ColumnName = "flow_status", ColumnDescription = "流程状态")]
    public virtual int? FlowStatus { get; set; }
    
    /// <summary>
    /// 流程排序
    /// </summary>
    [SugarColumn(ColumnName = "flow_sort", ColumnDescription = "流程排序")]
    public virtual int? FlowSort { get; set; }
    
    /// <summary>
    /// 角色ID
    /// </summary>
    [SugarColumn(ColumnName = "role_id", ColumnDescription = "角色ID")]
    public virtual long? RoleId { get; set; }
    /// <summary>
    /// 角色名称
    /// </summary>
    [SugarColumn(ColumnName = "role_code", ColumnDescription = "角色名称", Length = 100)]
    public virtual string? RoleCode { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    [SugarColumn(ColumnName = "role_name", ColumnDescription = "角色名称", Length = 100)]
    public virtual string? RoleName { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    [SugarColumn(ColumnName = "user_id", ColumnDescription = "用户ID")]
    public virtual long? UserId { get; set; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    [SugarColumn(ColumnName = "user_name", ColumnDescription = "用户名", Length = 100)]
    public virtual string? UserName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 创建机构ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构ID")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
