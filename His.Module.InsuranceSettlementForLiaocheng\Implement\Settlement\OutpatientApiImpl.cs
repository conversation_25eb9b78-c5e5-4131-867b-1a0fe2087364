using Admin.NET.Core;
using Furion.DependencyInjection;
using His.Module.Insurance.Service.Settlement.Dto.Outpatient;
using His.Module.Insurance.Service.Settlement.Dto.Patient;
using His.Module.Insurance.Service.Settlement.Interface;
using His.Module.Insurance.Service.Settlement.Model.Outpatient;
using His.Module.InsuranceSettlementForLiaocheng.Infrastructure;
using Microsoft.Extensions.Options;

namespace His.Module.InsuranceSettlementForLiaocheng.Implement.Settlement;
using His.Module.Insurance.Service.Settlement;
public class OutpatientApiImpl(
    UserManager userManager,
    IOptions<InsuranceOptions> insuranceOptions,
    InsuranceSettlementBasicApi insuranceSettlementApi
    ): IOutpatientApi,ITransient
{
    public async Task<SettleMzPreResponse> SettlePre(SettleMzPreRequest request)
    {
        var result=await insuranceSettlementApi.SendSoapInvokeRequestAsync( "settle_mz_pre",
            request);
        return (SettleMzPreResponse)result;
    }
}