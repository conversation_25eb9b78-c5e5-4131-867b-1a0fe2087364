﻿ 
namespace Admin.NET.Core;

/// <summary>
/// 医保
/// </summary>
public sealed class InsuranceOptions : IConfigurableOptions
{
     public InsuranceSettlementSettings Settlement { get; set; }
     public  InsuranceAuditSettings Audit { get; set; }
}
/// <summary>
/// 医保结算
/// </summary>
public sealed class InsuranceSettlementSettings
{
    /// <summary>
    /// 主机
    /// </summary>
    public string Host { get; set; }

    /// <summary>
    /// 端口
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    /// 结算地址
    /// </summary>
    public string Url { get; set; }
    /// <summary>
    /// 医保医院编码
    /// </summary>
    public string HosCode { get; set; }
    /// <summary>
    ///  医保医院名称
    /// </summary>
    public string HosName { get; set; }
    /// <summary>
    ///  医保医院密码
    /// </summary>
    public string DefaultPwd { get; set; }
    
    
    /// <summary>
    /// 默认社保机构代码
    /// </summary>
    public string DefaultSbjgbh { get; set; }
    
 
}

/// <summary>
/// 医保结算
/// </summary>
public sealed class InsuranceAuditSettings
{
    /// <summary>
    /// 主机
    /// </summary>
    public string Host { get; set; }

    /// <summary>
    /// 端口
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    /// 结算地址
    /// </summary>
    public string Url { get; set; }
    /// <summary>
    /// 医保医院编码
    /// </summary>
    public string HosCode { get; set; }
    /// <summary>
    ///  医保医院名称
    /// </summary>
    public string HosName { get; set; }
 
 
}