﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Patient;

/// <summary>
/// 患者备忘录主表服务 🧩
/// </summary>
[ApiDescriptionSettings(PatientConst.GroupName, Order = 100)]
public class PatientMemorandumService(
    UserManager userManager,
    SqlSugarRepository<PatientMemorandum> patientMemorandumRep,
    SqlSugarRepository<PatientMemorandumHistory> patientMemorandumHistoryRep,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 分页查询患者备忘录主表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询患者备忘录主表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<PatientMemorandumOutput>> Page(PagePatientMemorandumInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = patientMemorandumRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.PatientName.Contains(input.Keyword) || u.IdCardNo.Contains(input.Keyword) ||
                     u.OutpatientNo.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName),
                u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardNo), u => u.IdCardNo.Contains(input.IdCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.OutpatientNo),
                u => u.OutpatientNo.Contains(input.OutpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .Select<PatientMemorandumOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取患者备忘录主表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取患者备忘录主表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<PatientMemorandum> Detail([FromQuery] QueryByIdPatientMemorandumInput input)
    {
        return await patientMemorandumRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加患者备忘录主表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加患者备忘录主表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddPatientMemorandumInput input)
    {
        var entity = input.Adapt<PatientMemorandum>();
        return await patientMemorandumRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新患者备忘录主表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新患者备忘录主表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost, UnitOfWork]
    public async Task Update(UpdatePatientMemorandumInput input)
    {
        var exist = await patientMemorandumRep.GetFirstAsync(u => u.Id == input.Id) ??
                    throw Oops.Oh(ErrorCodeEnum.D1002);

        var history = exist.Adapt<PatientMemorandumHistory>();
        history.OriginalId = exist.Id;
        history.Id = 0;
        await patientMemorandumHistoryRep.InsertAsync(history);

        var entity = input.Adapt<PatientMemorandum>();
        await patientMemorandumRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除患者备忘录主表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除患者备忘录主表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost,UnitOfWork]
    public async Task Delete(DeletePatientMemorandumInput input)
    {
        var entity = await patientMemorandumRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.CreateUserId != userManager.UserId)
            throw Oops.Oh("只能删除自己的备忘录");

        await patientMemorandumRep.FakeDeleteAsync(entity); //假删除
        // 删除历史记录

        var history = await patientMemorandumHistoryRep.AsQueryable()
            .Where(u => u.OriginalId == entity.Id).ToListAsync();
        foreach (var item in history)
            await patientMemorandumHistoryRep.FakeDeleteAsync(entity); //假删除
        //await _patientMemorandumRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除患者备忘录主表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除患者备忘录主表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost, UnitOfWork]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeletePatientMemorandumInput> input)
    {
        var exp = Expressionable.Create<PatientMemorandum>();
        foreach (var row in input)
        {
            await Delete(new DeletePatientMemorandumInput() { Id = row.Id });
        }

        return input.Count;
    }


    /// <summary>
    /// 导出患者备忘录主表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出患者备忘录主表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PagePatientMemorandumInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportPatientMemorandumOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "患者备忘录主表导出记录");
    }

    /// <summary>
    /// 下载患者备忘录主表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载患者备忘录主表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportPatientMemorandumOutput>(), "患者备忘录主表导入模板");
    }

    /// <summary>
    /// 导入患者备忘录主表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入患者备忘录主表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportPatientMemorandumInput, PatientMemorandum>(file,
                (list, markerErrorAction) =>
                {
                    sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                    {
                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x =>
                        {
                            if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                            if (x.PatientId == null)
                            {
                                x.Error = "患者Id不能为空";
                                return false;
                            }

                            return true;
                        }).Adapt<List<PatientMemorandum>>();

                        var storageable = patientMemorandumRep.Context.Storageable(rows)
                            .SplitError(it => it.Item.PatientName?.Length > 64, "患者姓名长度不能超过64个字符")
                            .SplitError(it => it.Item.IdCardNo?.Length > 64, "身份证号长度不能超过64个字符")
                            .SplitError(it => it.Item.OutpatientNo?.Length > 64, "门诊号长度不能超过64个字符")
                            .SplitError(it => it.Item.Remark?.Length > 2000, "备忘内容长度不能超过2000个字符")
                            .SplitInsert(_ => true)
                            .ToStorage();

                        storageable.BulkCopy();
                        storageable.BulkUpdate();

                        // 标记错误信息
                        markerErrorAction.Invoke(storageable, pageItems, rows);
                    });
                });

            return stream;
        }
    }
}