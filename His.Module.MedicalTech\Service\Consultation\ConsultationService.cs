﻿using Furion.DatabaseAccessor;
using His.Module.Shared.Api.Api.ChargeItem;
using Yitter.IdGenerator;

namespace His.Module.MedicalTech.Service;

/// <summary>
/// 会诊表服务 🧩
/// </summary>
[ApiDescriptionSettings(MedicalTechConst.GroupName, Order = 100)]
public class ConsultationService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Consultation> _consultationRep;
    private readonly SqlSugarRepository<ConsultationItem> _consultationItemRep;
    private readonly IChargeItemApi _chargeItemApi;

    public ConsultationService(SqlSugarRepository<Consultation> consultationRep,
        SqlSugarRepository<ConsultationItem> consultationItemRep,
        IChargeItemApi chargeItemApi)
    {
        _consultationRep = consultationRep;
        _chargeItemApi = chargeItemApi;
        _consultationItemRep = consultationItemRep;
    }

    /// <summary>
    /// 分页查询会诊表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询会诊表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<ConsultationOutput>> Page(PageConsultationInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _consultationRep.AsQueryable()
            .Where(u => u.RegisterId == input.RegisterId)
            .Where(u => u.Flag == input.Flag)
            .WhereIF(input.StartTime != null, u => u.ApplyTime >= input.StartTime)
            .WhereIF(input.EndTime != null, u => u.ApplyTime <= input.EndTime)
            .Select<ConsultationOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取会诊表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取会诊表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<ConsultationOutput> Detail([FromQuery] QueryByIdConsultationInput input)
    {
        return await _consultationRep.AsQueryable()
            .LeftJoin<ConsultationItem>((u, a) => u.Id == a.ConsultationId)
            .Where(u => u.Id == input.Id)
            .Select((u, a) => new ConsultationOutput()
            {
                ItemId = a.ItemId,
            }, true)
            .FirstAsync();
    }

    /// <summary>
    /// 增加会诊表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加会诊表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost, UnitOfWork]
    public async Task<long> Add(AddConsultationInput input)
    {
        var entity = input.Adapt<Consultation>();
        entity.Id = YitIdHelper.NextId();
        entity.ApplyNo = await _consultationRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('apply_no_seq') As varchar), 8, '0')");
        entity.ApplyTime = DateTime.Now;
        entity.ApplyDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId)?.Value ?? "0");
        entity.ApplyDeptName = App.User.FindFirst(ClaimConst.OrgName)?.Value;
        entity.ApplyDoctorId = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");
        entity.ApplyDoctorName = App.User.FindFirst(ClaimConst.RealName)?.Value;
        entity.Status = "1";
        var chargeItem = (await _chargeItemApi.GetDetails([input.ItemId])).FirstOrDefault();
        if (chargeItem == null)
        {
            return 0;
        }

        var consultationItem = new ConsultationItem
        {
            ConsultationId = entity.Id,
            ApplyNo = entity.ApplyNo,
            ItemId = chargeItem.Id,
            ItemCode = chargeItem.Code,
            ItemName = chargeItem.Name,
            Unit = chargeItem.Unit,
            Price = chargeItem.Price,
            Quantity = 1,
            Amount = chargeItem.Price,
            ChargeCategoryId = chargeItem.ChargeCategoryId,
        };
        await _consultationItemRep.InsertAsync(consultationItem);
        var consultationInsertResult = await _consultationRep.InsertAsync(entity);

        return consultationInsertResult ? entity.Id : 0;
    }

    /// <summary>
    /// 更新会诊表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新会诊表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateConsultationInput input)
    {
        var entity = input.Adapt<Consultation>();
        await _consultationRep.AsUpdateable(entity)
        .IgnoreColumns(u => new
        {
            u.ApplyNo,
            u.RegisterId,
            u.PatientId,
            u.VisitTime,
            u.Flag,
            u.Status,
            u.ConsultationOpinion,
            u.OpinionTime,
            u.OpinionStaffId,
            u.OpinionStaffName,
            u.OpinionStaffSign,
            u.ApplyTime,
            u.ConsultationAcceptTime,
            u.ConsultationEndTime,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除会诊表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除会诊表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteConsultationInput input)
    {
        var entity = await _consultationRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _consultationRep.FakeDeleteAsync(entity);   //假删除
        //await _consultationRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除会诊表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除会诊表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteConsultationInput> input)
    {
        var exp = Expressionable.Create<Consultation>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _consultationRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _consultationRep.FakeDeleteAsync(list);   //假删除
        //return await _consultationRep.DeleteAsync(list);   //真删除
    }
}