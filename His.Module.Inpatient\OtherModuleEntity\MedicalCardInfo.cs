﻿namespace His.Module.Inpatient.OtherModuleEntity;
 
/// <summary>
/// 就诊卡信息表
/// </summary>
[SugarTable(null, "就诊卡信息表")]
[Tenant("1300000000003")]
public class MedicalCardInfo : EntityTenant
{
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnDescription = "就诊卡号")]
    public string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "患者Id")]
    public long PatientId { get; set; }
 
}