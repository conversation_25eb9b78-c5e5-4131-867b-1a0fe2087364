﻿using His.Module.Shared.Api.Enum;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品库房维护服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugStorageService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugStorage> _drugStorageRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DrugStorageService(SqlSugarRepository<DrugStorage> drugStorageRep, ISqlSugarClient sqlSugarClient)
    {
        _drugStorageRep = drugStorageRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药品库房维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品库房维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugStorageOutput>> Page(PageDrugStorageInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugStorageRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.StorageName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageName),
                u => u.StorageName.Contains(input.StorageName.Trim()))
            .LeftJoin<DrugStorage>((u, parent) => u.ParentId == parent.Id)
            .Select((u, parent) => new DrugStorageOutput
            {
                Id = u.Id,
                StorageCode = u.StorageCode,
                StorageName = u.StorageName,
                StorageDrugType = u.StorageDrugType,
                StorageAmountLimit = u.StorageAmountLimit,
                ParentId = u.ParentId,
                ParentFkDisplayName = $"{parent.StorageName}-{parent.StorageCode}",
                ParentCode = u.ParentCode,
                ServiceObject = u.ServiceObject,
                PurchaseAudit = (YesNoEnum)u.PurchaseAudit,
                PurchaseReturnAudit = (YesNoEnum)u.PurchaseReturnAudit,
                ApplyAudit = (YesNoEnum)u.ApplyAudit,
                ApplyReturnAudit = (YesNoEnum)u.ApplyReturnAudit,
                OutAudit = (YesNoEnum)u.OutAudit,
                SpecialAudit = (YesNoEnum)u.SpecialAudit,
                BatchCheck = (YesNoEnum)u.BatchCheck,
                Status = (StatusEnum)u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
        var result = await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
        if (input.ServiceObject != null)
        {
            result.Items =
                result.Items.Where(u =>
                    u.ServiceObject!=null &&   u.ServiceObject.Contains(input.ServiceObject));
            result.Total = result.Items.Count();
        }

        return result;
    }

    /// <summary>
    /// 获取药品库房维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品库房维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugStorage> Detail([FromQuery] QueryByIdDrugStorageInput input)
    {
        return await _drugStorageRep.GetFirstAsync(u => u.Id == input.Id);
    }

    // /// <summary>
    // /// 增加药品库房维护 ➕
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("增加药品库房维护")]
    // [ApiDescriptionSettings(Name = "Add"), HttpPost]
    // public async Task<long> Add(AddDrugStorageInput input)
    // {
    //     var entity = input.Adapt<DrugStorage>();
    //     return await _drugStorageRep.InsertAsync(entity) ? entity.Id : 0;
    // }

    /// <summary>
    /// 更新药品库房维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品库房维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugStorageInput input)
    {
        var entity = input.Adapt<DrugStorage>();
        await _drugStorageRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }
    //
    // /// <summary>
    // /// 删除药品库房维护 ❌
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("删除药品库房维护")]
    // [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    // public async Task Delete(DeleteDrugStorageInput input)
    // {
    //     var entity = await _drugStorageRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
    //     await _drugStorageRep.FakeDeleteAsync(entity); //假删除
    //     //await _drugStorageRep.DeleteAsync(entity);   //真删除
    // }
    //
    // /// <summary>
    // /// 批量删除药品库房维护 ❌
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("批量删除药品库房维护")]
    // [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    // public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteDrugStorageInput> input)
    // {
    //     var exp = Expressionable.Create<DrugStorage>();
    //     foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
    //     var list = await _drugStorageRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
    //
    //     return await _drugStorageRep.FakeDeleteAsync(list); //假删除
    //     //return await _drugStorageRep.DeleteAsync(list);   //真删除
    // }

    /// <summary>
    /// 设置药品库房维护状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置药品库房维护状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetDrugStorageStatus(SetDrugStorageStatusInput input)
    {
        await _drugStorageRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataDrugStorageInput input)
    {
        var parentIdData = await _drugStorageRep.Context.Queryable<DrugStorage>()
            .InnerJoinIF<DrugStorage>(input.FromPage, (u, r) => u.Id == r.ParentId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.StorageName}-{u.StorageCode}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "parentId", parentIdData },
        };
    }

    /// <summary>
    /// 导出药品库房维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品库房维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugStorageInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugStorageOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药品库房维护导出记录");
    }

    /// <summary>
    /// 下载药品库房维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品库房维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugStorageOutput>(), "药品库房维护导入模板", (_, info) =>
        {
            if (nameof(ExportDrugStorageOutput.ParentFkDisplayName) == info.Name)
                return _drugStorageRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}-{u.StorageCode}")
                    .Distinct().ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入药品库房维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品库房维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportDrugStorageInput, DrugStorage>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 父级库房ID
                    var parentIdLabelList = pageItems.Where(x => x.ParentFkDisplayName != null)
                        .Select(x => x.ParentFkDisplayName).Distinct().ToList();
                    if (parentIdLabelList.Any())
                    {
                        var parentIdLinkMap = _drugStorageRep.Context.Queryable<DrugStorage>()
                            .Where(u => parentIdLabelList.Contains($"{u.StorageName}-{u.StorageCode}")).ToList()
                            .ToDictionary(u => $"{u.StorageName}-{u.StorageCode}", u => u.Id);
                        pageItems.ForEach(e =>
                        {
                            e.ParentId = parentIdLinkMap.GetValueOrDefault(e.ParentFkDisplayName ?? "");
                            if (e.ParentId == null) e.Error = "父级库房ID链接失败";
                        });
                    }

                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => { return true; }).Adapt<List<DrugStorage>>();

                    var storageable = _drugStorageRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.StorageCode?.Length > 100, "库房编码长度不能超过100个字符")
                        .SplitError(it => it.Item.StorageName?.Length > 100, "库房名称长度不能超过100个字符")
                        .SplitError(it => it.Item.ParentCode?.Length > 100, "父级库房编码长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}