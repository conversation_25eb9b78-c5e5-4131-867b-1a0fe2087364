using His.Module.Pharmacy.Api.DrugInventory.Dto;

namespace His.Module.Pharmacy.Api.DrugInventory;

public interface IDrugInventoryApi
{

    /// <summary>
    /// 开方锁定库存
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
      Task<Boolean> LockInventory(List<LockDrugInventoryInput> list);

    Task<Boolean> LockInventory(LockDrugInventoryInput item);
    /// <summary>
    /// 移除锁定库存 删除处方时
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    Task<Boolean> RemoveLockInventory(LockDrugInventoryInput item);
}