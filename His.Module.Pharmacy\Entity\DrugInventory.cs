﻿namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品库存表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_inventory", "药品库存表")]
public class DrugInventory : EntityTenant
{
    /// <summary>
    /// 药品ID
    /// </summary>
    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品ID")]
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 100)]
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类型", Length = 100)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "药房ID")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 药房编码
    /// </summary>
    [SugarColumn(ColumnName = "storage_code", ColumnDescription = "药房编码", Length = 100)]
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "药房名称", Length = 100)]
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "规格", Length = 100)]
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 生产厂家ID
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_id", ColumnDescription = "生产厂家ID")]
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_name", ColumnDescription = "生产厂家名称", Length = 100)]
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量")]
    public virtual decimal? Quantity { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 100)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 待发药数量
    /// </summary>
    [SugarColumn(ColumnName = "pending_quantity", ColumnDescription = "待发药数量",DefaultValue = "0")]
    public virtual decimal? PendingQuantity { get; set; }
    
    /// <summary>
    /// 可用库存数量
    /// </summary>
    [SugarColumn(ColumnName = "available_quantity", ColumnDescription = "可用库存(存储生成列)", DefaultValue = "0", IsOnlyIgnoreInsert = true, IsOnlyIgnoreUpdate = true)]
    public virtual decimal? AvailableQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [SugarColumn(ColumnName = "sale_price", ColumnDescription = "零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 零售金额
    /// </summary>
    [SugarColumn(ColumnName = "total_sale_price", ColumnDescription = "零售金额", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    [SugarColumn(ColumnName = "purchase_price", ColumnDescription = "进价", Length = 20, DecimalDigits=4)]
    public virtual decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 采购金额
    /// </summary>
    [SugarColumn(ColumnName = "total_purchase_price", ColumnDescription = "采购金额", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [SugarColumn(ColumnName = "batch_no", ColumnDescription = "批号", Length = 100)]
    public virtual string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    [SugarColumn(ColumnName = "production_date", ColumnDescription = "生产日期")]
    public virtual DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 有效期
    /// </summary>
    [SugarColumn(ColumnName = "expiration_date", ColumnDescription = "有效期")]
    public virtual DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [SugarColumn(ColumnName = "approval_number", ColumnDescription = "批准文号", Length = 100)]
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "国家医保编码", Length = 100)]
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 最后一次供应商ID
    /// </summary>
    [SugarColumn(ColumnName = "last_supplier_id", ColumnDescription = "最后一次供应商ID")]
    public virtual long? LastSupplierId { get; set; }
    
    /// <summary>
    /// 最后一次供应商名称
    /// </summary>
    [SugarColumn(ColumnName = "last_supplier_name", ColumnDescription = "最后一次供应商名称", Length = 100)]
    public virtual string? LastSupplierName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
}
