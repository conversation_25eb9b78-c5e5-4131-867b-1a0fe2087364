﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Inpatient;

/// <summary>
/// 医疗组成员表服务 🧩
/// </summary>
[ApiDescriptionSettings(InpatientConst.GroupName, Order = 100)]
public class MedicalTeamMemberService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MedicalTeamMember> _medicalTeamMemberRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public MedicalTeamMemberService(SqlSugarRepository<MedicalTeamMember> medicalTeamMemberRep, ISqlSugarClient sqlSugarClient)
    {
        _medicalTeamMemberRep = medicalTeamMemberRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询医疗组成员表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询医疗组成员表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<MedicalTeamMemberOutput>> Page(PageMedicalTeamMemberInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _medicalTeamMemberRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.StaffName.Contains(input.Keyword) || u.RoleType.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StaffName), u => u.StaffName.Contains(input.StaffName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RoleType), u => u.RoleType.Contains(input.RoleType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.TeamId != null, u => u.TeamId == input.TeamId)
            .WhereIF(input.StaffId != null, u => u.StaffId == input.StaffId)
            .WhereIF(input.JoinDateRange?.Length == 2, u => u.JoinDate >= input.JoinDateRange[0] && u.JoinDate <= input.JoinDateRange[1])
            .WhereIF(input.LeaveDateRange?.Length == 2, u => u.LeaveDate >= input.LeaveDateRange[0] && u.LeaveDate <= input.LeaveDateRange[1])
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<MedicalTeamMemberOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取医疗组成员表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取医疗组成员表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<MedicalTeamMember> Detail([FromQuery] QueryByIdMedicalTeamMemberInput input)
    {
        return await _medicalTeamMemberRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加医疗组成员表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加医疗组成员表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddMedicalTeamMemberInput input)
    {
        var entity = input.Adapt<MedicalTeamMember>();
        entity.Status = 1;
        return await _medicalTeamMemberRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新医疗组成员表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新医疗组成员表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateMedicalTeamMemberInput input)
    {
        var entity = input.Adapt<MedicalTeamMember>();
        await _medicalTeamMemberRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除医疗组成员表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除医疗组成员表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteMedicalTeamMemberInput input)
    {
        var entity = await _medicalTeamMemberRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _medicalTeamMemberRep.FakeDeleteAsync(entity);   //假删除
        //await _medicalTeamMemberRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除医疗组成员表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除医疗组成员表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteMedicalTeamMemberInput> input)
    {
        var exp = Expressionable.Create<MedicalTeamMember>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _medicalTeamMemberRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _medicalTeamMemberRep.FakeDeleteAsync(list);   //假删除
        //return await _medicalTeamMemberRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出医疗组成员表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出医疗组成员表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageMedicalTeamMemberInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportMedicalTeamMemberOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "医疗组成员表导出记录");
    }
    
    /// <summary>
    /// 下载医疗组成员表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载医疗组成员表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportMedicalTeamMemberOutput>(), "医疗组成员表导入模板");
    }
    
    /// <summary>
    /// 导入医疗组成员表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入医疗组成员表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportMedicalTeamMemberInput, MedicalTeamMember>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.TeamId == null){
                            x.Error = "医疗组ID不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.StaffId == null){
                            x.Error = "成员ID不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<MedicalTeamMember>>();
                    
                    var storageable = _medicalTeamMemberRep.Context.Storageable(rows)
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.StaffName), "成员名称不能为空")
                        .SplitError(it => it.Item.StaffName?.Length > 200, "成员名称长度不能超过200个字符")
                        .SplitError(it => it.Item.RoleType?.Length > 200, "角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)长度不能超过200个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
