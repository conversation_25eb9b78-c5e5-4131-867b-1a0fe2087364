﻿namespace His.Module.Pharmacy;

/// <summary>
/// 药品退货输出参数
/// </summary>
public class StorageRefundRecordOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 退货单号
    /// </summary>
    public string? RefundNo { get; set; }    
    
    /// <summary>
    /// 退货时间
    /// </summary>
    public DateTime? RefundTime { get; set; }    
    
    /// <summary>
    /// 退货类型
    /// </summary>
    public string? RefundType { get; set; }    
    
    /// <summary>
    /// 退货原因
    /// </summary>
    public string? RefundReason { get; set; }    
    
    /// <summary>
    /// 申请科室
    /// </summary>
    public long? ApplyDeptId { get; set; }    
    
    /// <summary>
    /// 申请科室 描述
    /// </summary>
    public string ApplyDeptFkDisplayName { get; set; } 
    
    /// <summary>
    /// 申请部门编码
    /// </summary>
    public string? ApplyDeptCode { get; set; }    
    
    /// <summary>
    /// 申请部门名称
    /// </summary>
    public string? ApplyDeptName { get; set; }    
    
    /// <summary>
    /// 目标库房
    /// </summary>
    public long? TargetStorageId { get; set; }    
    
    /// <summary>
    /// 目标库房 描述
    /// </summary>
    public string TargetStorageFkDisplayName { get; set; } 
    
    /// <summary>
    /// 目标库房编码
    /// </summary>
    public string? TargetStorageCode { get; set; }    
    
    /// <summary>
    /// 目标库房名称
    /// </summary>
    public string? TargetStorageName { get; set; }    
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 药品退货数据导入模板实体
/// </summary>
public class ExportStorageRefundRecordOutput : ImportStorageRefundRecordInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
