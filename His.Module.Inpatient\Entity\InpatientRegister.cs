﻿namespace His.Module.Inpatient.Entity;

/// <summary>
/// 住院登记表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("inpatient_register", "住院登记表")]
public class InpatientRegister : EntityTenantBaseData
{
    /// <summary>
    /// 住院流水号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_serial_no", ColumnDescription = "住院流水号", Length = 100)]
    public virtual string? InpatientSerialNo { get; set; }

    /// <summary>
    /// 住院号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_no", ColumnDescription = "住院号", Length = 100)]
    public virtual string InpatientNo { get; set; }

    /// <summary>
    /// 住院次数
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_count", ColumnDescription = "住院次数")]
    public virtual int InpatientCount { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnName = "medical_card_no", ColumnDescription = "就诊卡号", Length = 100)]
    public virtual string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 100)]
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    [SugarColumn(ColumnName = "sex", ColumnDescription = "性别")]
    public virtual int? Sex { get; set; }
    
    /// <summary>
    /// 年龄
    /// </summary>
    [SugarColumn(ColumnName = "age", ColumnDescription = "年龄")]
    public virtual int? Age { get; set; }
    
    /// <summary>
    /// 年龄单位
    /// </summary>
    [SugarColumn(ColumnName = "age_unit", ColumnDescription = "年龄单位", Length = 32)]
    public virtual string? AgeUnit { get; set; }
    
    /// <summary>
    /// 出生日期
    /// </summary>
    [SugarColumn(ColumnName = "birthday", ColumnDescription = "出生日期")]
    public virtual DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [SugarColumn(ColumnName = "card_type", ColumnDescription = "证件类型")]
    public virtual int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [SugarColumn(ColumnName = "id_card_no", ColumnDescription = "身份证号", Length = 32)]
    public virtual string? IdCardNo { get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    [SugarColumn(ColumnName = "phone", ColumnDescription = "电话号码", Length = 16)]
    public virtual string? Phone { get; set; }
    
    /// <summary>
    /// 联系人姓名
    /// </summary>
    [SugarColumn(ColumnName = "contact_name", ColumnDescription = "联系人姓名", Length = 32)]
    public virtual string? ContactName { get; set; }
    
    /// <summary>
    /// 联系人关系
    /// </summary>
    [SugarColumn(ColumnName = "contact_relationship", ColumnDescription = "联系人关系", Length = 16)]
    public virtual string? ContactRelationship { get; set; }
    
    /// <summary>
    /// 联系人地址
    /// </summary>
    [SugarColumn(ColumnName = "contact_address", ColumnDescription = "联系人地址", Length = 64)]
    public virtual string? ContactAddress { get; set; }
    
    /// <summary>
    /// 联系人电话号码
    /// </summary>
    [SugarColumn(ColumnName = "contact_phone", ColumnDescription = "联系人电话号码", Length = 16)]
    public virtual string? ContactPhone { get; set; }
    
    /// <summary>
    /// 现居住地省
    /// </summary>
    [SugarColumn(ColumnName = "residence_province", ColumnDescription = "现居住地省")]
    public virtual int? ResidenceProvince { get; set; }
    
    /// <summary>
    /// 现居住地市
    /// </summary>
    [SugarColumn(ColumnName = "residence_city", ColumnDescription = "现居住地市")]
    public virtual int? ResidenceCity { get; set; }
    
    /// <summary>
    /// 现居住地县
    /// </summary>
    [SugarColumn(ColumnName = "residence_county", ColumnDescription = "现居住地县")]
    public virtual int? ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>
    [SugarColumn(ColumnName = "residence_address", ColumnDescription = "详细现居住地", Length = 128)]
    public virtual string? ResidenceAddress { get; set; }

    /// <summary>
    /// 入院时间
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_time", ColumnDescription = "入院时间")]
    public virtual DateTime? InpatientTime { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室ID")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "科室名称", Length = 100)]
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 病区ID
    /// </summary>
    [SugarColumn(ColumnName = "ward_id", ColumnDescription = "病区ID")]
    public virtual long? WardId { get; set; }
    
    /// <summary>
    /// 病区名称
    /// </summary>
    [SugarColumn(ColumnName = "ward_name", ColumnDescription = "病区名称", Length = 100)]
    public virtual string? WardName { get; set; }
    
    /// <summary>
    /// 诊疗组ID
    /// </summary>
    [SugarColumn(ColumnName = "team_id", ColumnDescription = "诊疗组ID")]
    public virtual long? TeamId { get; set; }
    
    /// <summary>
    /// 诊疗组名称
    /// </summary>
    [SugarColumn(ColumnName = "team_name", ColumnDescription = "诊疗组名称", Length = 100)]
    public virtual string? TeamName { get; set; }
    
    /// <summary>
    /// 医生ID
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "医生ID")]
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "doctor_name", ColumnDescription = "医生姓名", Length = 100)]
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>
    [SugarColumn(ColumnName = "receiving_doctor_id", ColumnDescription = "接诊医生id")]
    public virtual long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>
    [SugarColumn(ColumnName = "receiving_doctor_name", ColumnDescription = "接诊医生名称", Length = 100)]
    public virtual string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_way", ColumnDescription = "入院途径", Length = 32)]
    public virtual string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>
    [SugarColumn(ColumnName = "settlement_category", ColumnDescription = "结算类别", Length = 100)]
    public virtual string? SettlementCategory { get; set; }
    
    /// <summary>
    /// 入院诊断编号
    /// </summary>
    [SugarColumn(ColumnName = "admission_diagnosis_code", ColumnDescription = "入院诊断编号", Length = 100)]
    public virtual string? AdmissionDiagnosisCode { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "admission_diagnosis_name", ColumnDescription = "入院诊断名称", Length = 100)]
    public virtual string? AdmissionDiagnosisName { get; set; }
    
    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    [SugarColumn(ColumnName = "pregnancy_risk_level", ColumnDescription = "妊娠风险评估", Length = 32)]
    public virtual string? PregnancyRiskLevel { get; set; }
    
    /// <summary>
    /// 高危因素
    /// </summary>
    [SugarColumn(ColumnName = "high_risk_factors", ColumnDescription = "高危因素", Length = 256)]
    public virtual string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
    
}
