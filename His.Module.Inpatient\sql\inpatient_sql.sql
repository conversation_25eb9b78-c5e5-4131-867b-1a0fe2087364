drop table inpatient.inpatient_register;
create table inpatient.inpatient_register
(
    id                        bigserial primary key,
    patient_id                bigint,
    appointment_record_id     bigint,
    patient_no                varchar(100),
    patient_name              varchar(100),
    -- 证件类型
    id_card_type              int,
    -- 证件号
    id_card_no                varchar(100),
    insurance_no              varchar(100),
    medical_card_id           bigint,
    medical_card_no           varchar(100),
    outpatient_no             varchar(100),
    -- 住院号
    inpatient_no              varchar(100),
    -- 住院流水号
    inpatient_serial_no       varchar(100),
    -- 住院次数
    inpatient_times          int,
    -- 病案号
    medical_record_no         varchar(100),
    fee_id                    bigint,-- '费别id'; 
    fee_name                  varchar(100),-- '费别名称';
    -- 医生id
    doctor_id                 bigint,
    doctor_name               varchar(100),
    -- 科室
    dept_id                   bigint,
    dept_name                 varchar(100),
    -- 主治医生
    main_doctor_id            bigint,
    main_doctor_name          varchar(100),
    -- 入院诊断
    inpatient_diagnostic_code varchar(100),
    inpatient_diagnostic_name varchar(100),
    -- 次要诊断
    secondary_diagnostic_code varchar(100),
    secondary_diagnostic_name varchar(100),
    -- 入院途径
    inpatient_way             varchar(100),
    -- 入院时间
    inpatient_time            timestamp,
    -- 允许欠费
    allow_arrears             bool,
    -- 欠费上限
    arrears_limit             decimal(20, 4),
    -- 担保人
    guarantee_person          varchar(100),
    contact_name varchar(100),
    contact_phone varchar(100),
    contact_relationship varchar(100),
    contact_address jsonb,
    contact_address_street varchar(100),
    
    -- 新生儿出生体重g
    newborn_birth_weight1     int,
    -- 新生儿出生体重单位g
    newborn_birth_weight2     int,
    newborn_birth_weight3     int,
    -- 新生儿入院体重g
    newborn_inpatient_weight  int,
    -- 是否有医保卡
    has_medical_insurance    bool,
    status int default 0,
    create_org_id bigint,
    create_org_name varchar(100),
    create_time timestamp,
    create_user_id bigint,
    create_user_name varchar(100),
    update_time timestamp,
    update_user_id bigint,
    update_user_name varchar(100),
    is_delete bool default false,
    tenant_id bigint
);
-- create sequence inpatient.register_inpatient_no_seq;
-- comment on sequence inpatient.register_inpatient_no_seq IS '住院号';
-- create sequence inpatient.register_inpatient_serial_no_seq;
-- comment on sequence inpatient.register_inpatient_serial_no_seq IS '住院流水号';
-- create sequence inpatient.register_medical_record_no_seq;
-- comment on sequence inpatient.register_medical_record_no_seq IS '病案号';



COMMENT ON TABLE inpatient.inpatient_register IS '住院登记表';
COMMENT ON COLUMN inpatient.inpatient_register.id IS '主键ID';
COMMENT ON COLUMN inpatient.inpatient_register.patient_id IS '患者ID';
COMMENT ON COLUMN inpatient.inpatient_register.appointment_record_id IS '预约记录ID';
COMMENT ON COLUMN inpatient.inpatient_register.patient_no IS '患者编号';
COMMENT ON COLUMN inpatient.inpatient_register.patient_name IS '患者姓名';
COMMENT ON COLUMN inpatient.inpatient_register.id_card_type IS '证件类型';
COMMENT ON COLUMN inpatient.inpatient_register.id_card_no IS '证件号码';
COMMENT ON COLUMN inpatient.inpatient_register.insurance_no IS '保险号';
COMMENT ON COLUMN inpatient.inpatient_register.medical_card_id IS '就诊卡ID';
COMMENT ON COLUMN inpatient.inpatient_register.medical_card_no IS '就诊卡号';
COMMENT ON COLUMN inpatient.inpatient_register.outpatient_no IS '门诊号';
COMMENT ON COLUMN inpatient.inpatient_register.inpatient_no IS '住院号';
COMMENT ON COLUMN inpatient.inpatient_register.inpatient_serial_no IS '住院流水号';
COMMENT ON COLUMN inpatient.inpatient_register.inpatient_times IS '住院次数';
COMMENT ON COLUMN inpatient.inpatient_register.medical_record_no IS '病案号';
COMMENT ON COLUMN inpatient.inpatient_register.fee_id IS '费别ID fee_category表id';
COMMENT ON COLUMN inpatient.inpatient_register.fee_name IS '费别名称';
COMMENT ON COLUMN inpatient.inpatient_register.doctor_id IS '医生ID';
COMMENT ON COLUMN inpatient.inpatient_register.doctor_name IS '医生姓名';
COMMENT ON COLUMN inpatient.inpatient_register.dept_id IS '科室ID';
COMMENT ON COLUMN inpatient.inpatient_register.dept_name IS '科室名称';
COMMENT ON COLUMN inpatient.inpatient_register.main_doctor_id IS '主治医生ID';
COMMENT ON COLUMN inpatient.inpatient_register.main_doctor_name IS '主治医生姓名';
COMMENT ON COLUMN inpatient.inpatient_register.inpatient_diagnostic_code IS '入院诊断代码';
COMMENT ON COLUMN inpatient.inpatient_register.inpatient_diagnostic_name IS '入院诊断名称';
COMMENT ON COLUMN inpatient.inpatient_register.secondary_diagnostic_code IS '次要诊断代码';
COMMENT ON COLUMN inpatient.inpatient_register.secondary_diagnostic_name IS '次要诊断名称';
COMMENT ON COLUMN inpatient.inpatient_register.inpatient_way IS '入院途径';
COMMENT ON COLUMN inpatient.inpatient_register.inpatient_time IS '入院时间';
COMMENT ON COLUMN inpatient.inpatient_register.allow_arrears IS '是否允许欠费';
COMMENT ON COLUMN inpatient.inpatient_register.arrears_limit IS '欠费上限';
COMMENT ON COLUMN inpatient.inpatient_register.guarantee_person IS '担保人';
COMMENT ON COLUMN inpatient.inpatient_register.contact_name IS '联系人姓名';
COMMENT ON COLUMN inpatient.inpatient_register.contact_phone IS '联系人电话';
COMMENT ON COLUMN inpatient.inpatient_register.contact_relationship IS '联系人关系';
COMMENT ON COLUMN inpatient.inpatient_register.contact_address IS '联系人地址';
COMMENT ON COLUMN inpatient.inpatient_register.newborn_birth_weight1 IS '新生儿出生体重g';
COMMENT ON COLUMN inpatient.inpatient_register.newborn_birth_weight2 IS '新生儿出生体重单位g';
COMMENT ON COLUMN inpatient.inpatient_register.newborn_birth_weight3 IS '新生儿出生体重单位g';
COMMENT ON COLUMN inpatient.inpatient_register.newborn_inpatient_weight IS '新生儿入院体重g';
COMMENT ON COLUMN inpatient.inpatient_register.has_medical_insurance IS '是否有医保卡';
COMMENT ON COLUMN inpatient.inpatient_register.status IS '状态';
COMMENT ON COLUMN inpatient.inpatient_register.create_org_id IS '创建组织ID';
COMMENT ON COLUMN inpatient.inpatient_register.create_org_name IS '创建组织名称';
COMMENT ON COLUMN inpatient.inpatient_register.create_time IS '创建时间';
COMMENT ON COLUMN inpatient.inpatient_register.create_user_id IS '创建用户ID';
COMMENT ON COLUMN inpatient.inpatient_register.create_user_name IS '创建用户名';
COMMENT ON COLUMN inpatient.inpatient_register.update_time IS '更新时间';
COMMENT ON COLUMN inpatient.inpatient_register.update_user_id IS '更新用户ID';
COMMENT ON COLUMN inpatient.inpatient_register.update_user_name IS '更新用户名';
COMMENT ON COLUMN inpatient.inpatient_register.is_delete IS '是否删除';
COMMENT ON COLUMN inpatient.inpatient_register.tenant_id IS '租户ID';

 

-- 医保信息
create table inpatient.medical_insurance_info(
                                                 id                        bigserial primary key,
    -- 住院登记号
                                                 patient_id                bigint,
                                                 register_id               bigint,
    -- 住院号
                                                 inpatient_no              varchar(100),
    -- 住院流水号
                                                 inpatient_serial_no       varchar(100),
    -- 病案号
                                                 medical_record_no         varchar(100),
    -- 保险号码
                                                 insurance_no              varchar(100),
    -- 工作单位
                                                 work_unit                 varchar(100),
    -- 保险类型
                                                 insurance_type            varchar(100),
    -- 住院类别
                                                 inpatient_category        varchar(100),
    -- 险种标志
                                                 insurance_flag            varchar(100),
    -- 就医类别
                                                 medical_use_category     varchar(100),
    -- 医保卡号
                                                 medical_insurance_no     varchar(100),
    --参保地
                                                 insurance_place          varchar(100),
    -- 单位电话
                                                 work_unit_phone               varchar(100),
    -- 单位邮编
                                                 work_unit_post_code           varchar(100),

    -- 工作地址
                                                 work_unit_address             varchar(100),
    -- 工作地址
                                                 work_unit_address_street             varchar(100),
    -- 医保卡余额
                                                 insurance_balance    decimal(20,4),
    --转出医疗统筹登记号
                                                 out_register_no            varchar(100),

    --无第三方
                                                 no_third_party            int default 0,
    -- 外伤标志
                                                 external_injury_flag      int default 0,
    --第三方标志
                                                 third_party_flag          int default 0,

    -- 医疗住院方式
                                                 medical_inpatient_type    int default 0,
    -- 住院类型
                                                 inpatient_type            int default 0,
                                                 status int default 0,
                                                 create_org_id bigint,
                                                 create_org_name varchar(100),
                                                 create_time timestamp,
                                                 create_user_id bigint,
                                                 create_user_name varchar(100),
                                                 update_time timestamp,
                                                 update_user_id bigint,
                                                 update_user_name varchar(100),
                                                 is_delete bool default false,
                                                 tenant_id bigint
);
COMMENT ON TABLE inpatient.medical_insurance_info IS '医保信息表';
COMMENT ON COLUMN inpatient.medical_insurance_info.id IS '主键ID';
COMMENT ON COLUMN inpatient.medical_insurance_info.patient_id IS '患者ID';
COMMENT ON COLUMN inpatient.medical_insurance_info.register_id IS '住院登记号';
COMMENT ON COLUMN inpatient.medical_insurance_info.inpatient_no IS '住院号';
COMMENT ON COLUMN inpatient.medical_insurance_info.inpatient_serial_no IS '住院流水号';
COMMENT ON COLUMN inpatient.medical_insurance_info.medical_record_no IS '病案号';
COMMENT ON COLUMN inpatient.medical_insurance_info.insurance_no IS '保险号码';
COMMENT ON COLUMN inpatient.medical_insurance_info.work_unit IS '工作单位';
COMMENT ON COLUMN inpatient.medical_insurance_info.insurance_type IS '保险类型';
COMMENT ON COLUMN inpatient.medical_insurance_info.inpatient_category IS '住院类别';
COMMENT ON COLUMN inpatient.medical_insurance_info.insurance_flag IS '险种标志';
COMMENT ON COLUMN inpatient.medical_insurance_info.medical_use_category IS '就医类别';
COMMENT ON COLUMN inpatient.medical_insurance_info.medical_insurance_no IS '医保卡号';
COMMENT ON COLUMN inpatient.medical_insurance_info.insurance_place IS '参保地';
COMMENT ON COLUMN inpatient.medical_insurance_info.work_unit_phone IS '单位电话';
COMMENT ON COLUMN inpatient.medical_insurance_info.work_unit_post_code IS '单位邮编';
COMMENT ON COLUMN inpatient.medical_insurance_info.work_unit_address IS '工作地址';
COMMENT ON COLUMN inpatient.medical_insurance_info.work_unit_address_street IS '工作地址（街道）';
COMMENT ON COLUMN inpatient.medical_insurance_info.insurance_balance IS '医保卡余额';
COMMENT ON COLUMN inpatient.medical_insurance_info.out_register_no IS '转出医疗统筹登记号';
COMMENT ON COLUMN inpatient.medical_insurance_info.no_third_party IS '无第三方标志';
COMMENT ON COLUMN inpatient.medical_insurance_info.external_injury_flag IS '外伤标志';
COMMENT ON COLUMN inpatient.medical_insurance_info.third_party_flag IS '第三方标志';
COMMENT ON COLUMN inpatient.medical_insurance_info.medical_inpatient_type IS '医疗住院方式';
COMMENT ON COLUMN inpatient.medical_insurance_info.inpatient_type IS '住院类型';
COMMENT ON COLUMN inpatient.medical_insurance_info.status IS '状态';
COMMENT ON COLUMN inpatient.medical_insurance_info.create_org_id IS '创建组织ID';
COMMENT ON COLUMN inpatient.medical_insurance_info.create_org_name IS '创建组织名称';
COMMENT ON COLUMN inpatient.medical_insurance_info.create_time IS '创建时间';
COMMENT ON COLUMN inpatient.medical_insurance_info.create_user_id IS '创建用户ID';
COMMENT ON COLUMN inpatient.medical_insurance_info.create_user_name IS '创建用户名';
COMMENT ON COLUMN inpatient.medical_insurance_info.update_time IS '更新时间';
COMMENT ON COLUMN inpatient.medical_insurance_info.update_user_id IS '更新用户ID';
COMMENT ON COLUMN inpatient.medical_insurance_info.update_user_name IS '更新用户名';
COMMENT ON COLUMN inpatient.medical_insurance_info.is_delete IS '是否删除';
COMMENT ON COLUMN inpatient.medical_insurance_info.tenant_id IS '租户ID';

-- 预交金表
create table inpatient.advance_payment(
                                          id                        bigserial primary key,
    -- 收据号
                                          voucher_no                varchar(100),
                                          patient_id                bigint,
                                          register_id               bigint,
    -- 住院号
                                          inpatient_no              varchar(100),
    -- 住院流水号
                                          inpatient_serial_no       varchar(100),
    -- 病案号
                                          medical_record_no         varchar(100),
    -- 保险号码
                                          insurance_no              varchar(100),
    -- 预缴金额
                                          advance_amount            decimal(20,4),
    -- 大写金额
                                          advance_amount_chinese    varchar(100),
    -- 预缴时间
                                          advance_time              timestamp,
    -- 付款方式
                                          payment_method            varchar(100),
    -- 付款记录id
                                          payment_record_id         bigint,
                                          status int default 0,
                                          create_org_id bigint,
                                          create_org_name varchar(100),
                                          create_time timestamp,
                                          create_user_id bigint,
                                          create_user_name varchar(100),
                                          update_time timestamp,
                                          update_user_id bigint,
                                          update_user_name varchar(100),
                                          is_delete bool default false,
                                          tenant_id bigint
);

create sequence inpatient.advance_payment_voucher_no_seq;
COMMENT ON SEQUENCE inpatient.advance_payment_voucher_no_seq IS '预交金表-收据号';
COMMENT ON TABLE inpatient.advance_payment IS '预交金表';

COMMENT ON COLUMN inpatient.advance_payment.id IS '主键ID';
COMMENT ON COLUMN inpatient.advance_payment.voucher_no IS '收据号';
COMMENT ON COLUMN inpatient.advance_payment.patient_id IS '患者ID';
COMMENT ON COLUMN inpatient.advance_payment.register_id IS '住院登记号';
COMMENT ON COLUMN inpatient.advance_payment.inpatient_no IS '住院号';
COMMENT ON COLUMN inpatient.advance_payment.inpatient_serial_no IS '住院流水号';
COMMENT ON COLUMN inpatient.advance_payment.medical_record_no IS '病案号';
COMMENT ON COLUMN inpatient.advance_payment.insurance_no IS '保险号码';
COMMENT ON COLUMN inpatient.advance_payment.advance_amount IS '预缴金额';
COMMENT ON COLUMN inpatient.advance_payment.advance_amount_chinese IS '大写金额';
COMMENT ON COLUMN inpatient.advance_payment.advance_time IS '预缴时间';
COMMENT ON COLUMN inpatient.advance_payment.payment_method IS '付款方式';
COMMENT ON COLUMN inpatient.advance_payment.payment_record_id IS '付款记录ID';
COMMENT ON COLUMN inpatient.advance_payment.status IS '状态';
COMMENT ON COLUMN inpatient.advance_payment.create_org_id IS '创建组织ID';
COMMENT ON COLUMN inpatient.advance_payment.create_org_name IS '创建组织名称';
COMMENT ON COLUMN inpatient.advance_payment.create_time IS '创建时间';
COMMENT ON COLUMN inpatient.advance_payment.create_user_id IS '创建用户ID';
COMMENT ON COLUMN inpatient.advance_payment.create_user_name IS '创建用户名';
COMMENT ON COLUMN inpatient.advance_payment.update_time IS '更新时间';
COMMENT ON COLUMN inpatient.advance_payment.update_user_id IS '更新用户ID';
COMMENT ON COLUMN inpatient.advance_payment.update_user_name IS '更新用户名';
COMMENT ON COLUMN inpatient.advance_payment.is_delete IS '是否删除';
COMMENT ON COLUMN inpatient.advance_payment.tenant_id IS '租户ID';

--
alter table inpatient.inpatient_register
    add column contact_name varchar(100),
    add column contact_phone varchar(100),
    add column contact_relationship varchar(100);
comment on column inpatient.inpatient_register.contact_name is '联系人姓名';
comment on column inpatient.inpatient_register.contact_phone is '联系人电话';
comment on column inpatient.inpatient_register.contact_relationship is '联系人关系';

alter table inpatient.appointment_record
add column fee_category varchar(16);
comment on column inpatient.appointment_record.fee_category is '费用类别';

alter table inpatient.appointment_record
add column inpatient_way varchar(16);
comment on column inpatient.appointment_record.inpatient_way is '入院途径';