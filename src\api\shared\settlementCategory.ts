﻿import {useBaseApi} from '/@/api/base';

// 结算类别接口服务
export const useSettlementCategoryApi = () => {
	const baseApi = useBaseApi("settlementCategory");
	return {
		// 分页查询结算类别
		page: baseApi.page,
		// 查看结算类别详细
		detail: baseApi.detail,
		// 新增结算类别
		add: baseApi.add,
		// 更新结算类别
		update: baseApi.update,
		// 设置结算类别状态
		setStatus: baseApi.setStatus,
		// 删除结算类别
		delete: baseApi.delete,
		// 批量删除结算类别
		batchDelete: baseApi.batchDelete,
		// 导出结算类别数据
		exportData: baseApi.exportData,
		// 导入结算类别数据
		importData: baseApi.importData,
		// 下载结算类别数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 结算类别实体
export interface SettlementCategory {
	// 主键Id
	id: number;
	// 编号
	code: string;
	// 名称
	name: string;
	// 拼音码
	pinyinCode: string;
	// 医疗类别
	medCategory: string;
	// 使用范围
	usageScope: string;
	// 医保类型
	medInsType: string;
	// 医疗统筹类别
	medicalPoolingCategory: string;
	// 医疗统筹类别名称
	medicalPoolingCategoryName: string;
	// 险种标志
	medicalInsuranceFlag: string;
	// 险种标志名称
	medicalInsuranceFlagName: string;
	// 住院是否允许欠费 1是2否
	isInpatientArrearsAllowed: number;
	// 住院允许欠费金额
	inpatientAllowedArrearsAmount: number;
	// 住院允许欠费比例
	inpatientAllowedArrearsRatio: number;
	// 状态
	status: number;
	// 排序
	orderNo: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
	// 租户Id
	tenantId: number;
}