﻿using Admin.NET.Core;
namespace His.Module.MedicalTech.Entity;

/// <summary>
/// 检查模板表
/// </summary>
[Tenant("1300000000009")]
[SugarTable("template_examination", "检查模板表")]
public class TemplateExamination : EntityTenant
{
    /// <summary>
    /// 检查名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "template_name", ColumnDescription = "检查名称", Length = 64)]
    public virtual string TemplateName { get; set; }
    
    /// <summary>
    /// 1 全院/ 2 科室模板/3 个人模板
    /// </summary>
    [SugarColumn(ColumnName = "template_scope", ColumnDescription = "1 全院/ 2 科室模板/3 个人模板")]
    public virtual int? TemplateScope { get; set; }
    
    /// <summary>
    /// 检查类别Id
    /// </summary>
    [SugarColumn(ColumnName = "check_category_id", ColumnDescription = "检查类别Id")]
    public virtual long? CheckCategoryId { get; set; }
    
    /// <summary>
    /// 检查类别名称
    /// </summary>
    [SugarColumn(ColumnName = "check_category_name", ColumnDescription = "检查类别名称", Length = 64)]
    public virtual string? CheckCategoryName { get; set; }
    
    /// <summary>
    /// 检查部位Id
    /// </summary>
    [SugarColumn(ColumnName = "check_point_id", ColumnDescription = "检查部位Id")]
    public virtual long? CheckPointId { get; set; }
    
    /// <summary>
    /// 检查部位名称
    /// </summary>
    [SugarColumn(ColumnName = "check_point_name", ColumnDescription = "检查部位名称", Length = 64)]
    public virtual string? CheckPointName { get; set; }
    
    /// <summary>
    /// 检查目的
    /// </summary>
    [SugarColumn(ColumnName = "check_objective", ColumnDescription = "检查目的", Length = 200)]
    public virtual string? CheckObjective { get; set; }
    
    /// <summary>
    /// 状态 
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 ")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    [SugarColumn(ColumnName = "flag", ColumnDescription = "门诊住院标识 0门诊 1住院")]
    public virtual int? Flag { get; set; }
    
    /// <summary>
    /// 执行科室Id
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_id", ColumnDescription = "执行科室Id")]
    public virtual long? ExecuteDeptId { get; set; }
    
    /// <summary>
    /// 执行科室名称
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_name", ColumnDescription = "执行科室名称", Length = 64)]
    public virtual string? ExecuteDeptName { get; set; }
    
    /// <summary>
    /// 执行科室地址
    /// </summary>
    [SugarColumn(ColumnName = "execute_dept_address", ColumnDescription = "执行科室地址", Length = 100)]
    public virtual string? ExecuteDeptAddress { get; set; }
    
    /// <summary>
    /// 创建者部门Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建者部门Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建者部门名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
