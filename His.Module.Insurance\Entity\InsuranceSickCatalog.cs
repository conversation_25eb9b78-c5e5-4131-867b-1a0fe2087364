﻿using Admin.NET.Core;

namespace His.Module.Insurance.Entity;

/// <summary>
/// 医保疾病目录
/// </summary>
[Tenant("1300000000013")]
[SugarTable("insurance_sick_catalog", "医保疾病目录")]
public class InsuranceSickCatalog : EntityTenant
{
    /// <summary>
    /// 疾病编码
    /// </summary>
    [SugarColumn(ColumnName = "jb_bm", ColumnDescription = "疾病编码", Length = 50)]
    public virtual string JbBm { get; set; }
    
    /// <summary>
    /// 疾病名称
    /// </summary>
    [SugarColumn(ColumnName = "jb_mc", ColumnDescription = "疾病名称", Length = 200)]
    public virtual string JbMc { get; set; }
    
    /// <summary>
    /// 疾病名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "py", ColumnDescription = "疾病名称拼音", Length = 100)]
    public virtual string? Py { get; set; }
    
    /// <summary>
    /// 门诊大病类别
    /// </summary>
    [SugarColumn(ColumnName = "mzdb_lb", ColumnDescription = "门诊大病类别", Length = 10)]
    public virtual string? MzdbLb { get; set; }
    
    /// <summary>
    /// 社保机构编号
    /// </summary>
    [SugarColumn(ColumnName = "sbjg_bh", ColumnDescription = "社保机构编号", Length = 20)]
    public virtual string? SbjgBh { get; set; }
    
    /// <summary>
    /// 注销标志
    /// </summary>
    [SugarColumn(ColumnName = "zx_bz", ColumnDescription = "注销标志", Length = 10)]
    public virtual string? ZxBz { get; set; }
    
    /// <summary>
    /// 同步序号
    /// </summary>
    [SugarColumn(ColumnName = "sxh", ColumnDescription = "同步序号")]
    public virtual long? Sxh { get; set; }
    
    /// <summary>
    /// 最后同步时间
    /// </summary>
    [SugarColumn(ColumnName = "last_sync_time", ColumnDescription = "最后同步时间")]
    public virtual DateTime? LastSyncTime { get; set; }
    
    /// <summary>
    /// ICD编码
    /// </summary>
    [SugarColumn(ColumnName = "icd_bm", ColumnDescription = "ICD编码", Length = 50)]
    public virtual string? IcdBm { get; set; }
    
    /// <summary>
    /// 疾病类别
    /// </summary>
    [SugarColumn(ColumnName = "jb_lb", ColumnDescription = "疾病类别", Length = 10)]
    public virtual string? JbLb { get; set; }
}
