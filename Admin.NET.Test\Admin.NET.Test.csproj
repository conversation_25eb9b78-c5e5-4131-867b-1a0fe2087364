﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
        <NoWarn>1701;1702;1591;8632</NoWarn>
        <DocumentationFile></DocumentationFile>
        <ImplicitUsings>enable</ImplicitUsings>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <Nullable>disable</Nullable>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
        <Copyright>Admin.NET</Copyright>
        <Description>Admin.NET 通用权限开发平台</Description>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Furion.Xunit" Version="*******" />
      <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
      <PackageReference Include="Selenium.Support" Version="4.28.0" />
      <PackageReference Include="Selenium.WebDriver" Version="4.28.0" />
      <PackageReference Include="Selenium.WebDriver.MSEdgeDriver" Version="131.0.2903.48" />
      <PackageReference Include="xunit.assert" Version="2.9.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Admin.NET.Core\Admin.NET.Core.csproj" />
    </ItemGroup>
</Project>
