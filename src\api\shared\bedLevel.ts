﻿import {useBaseApi} from '/@/api/base';

// 床位等级接口服务
export const useBedLevelApi = () => {
	const baseApi = useBaseApi("bedLevel");
	return {
		// 分页查询床位等级
		page: baseApi.page,
		// 查看床位等级详细
		detail: baseApi.detail,
		// 新增床位等级
		add: baseApi.add,
		// 更新床位等级
		update: baseApi.update,
			// 设置床位信息状态
		setStatus: baseApi.setStatus,
		// 删除床位等级
		delete: baseApi.delete,
		deleteChargeItem: function (data: any, cancel: boolean = false) {
            return baseApi.request({
                url: baseApi.baseUrl + 'deleteChargeItem',
                method: 'post',
                data
            }, cancel);
        },
		// 批量删除床位等级
		batchDelete: baseApi.batchDelete,
		// 导出床位等级数据
		exportData: baseApi.exportData,
		// 导入床位等级数据
		importData: baseApi.importData,
		// 下载床位等级数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 床位等级实体
export interface BedLevel {
	// 主键Id
	id: number;
	// 床位等级名称
	levelName?: string;
	// 金额
	amount?: number;
	// 状态
	status: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 更新时间
	updateTime: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete: boolean;
	// 租户Id
	tenantId: number;
}