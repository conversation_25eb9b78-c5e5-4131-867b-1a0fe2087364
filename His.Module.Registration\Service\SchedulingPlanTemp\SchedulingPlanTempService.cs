﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.Registration.Const;
using His.Module.Registration.Entity;
using Microsoft.AspNetCore.Http;

namespace His.Module.Registration;

/// <summary>
/// 医生排班模板服务 🧩
/// </summary>
[ApiDescriptionSettings(RegistrationConst.GroupName, Order = 100)]
public class SchedulingPlanTempService(
    SqlSugarRepository<SchedulingPlanTemp> schedulingPlanTempRep, 
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 分页查询医生排班模板 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询医生排班模板")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<SchedulingPlanTempOutput>> Page(PageSchedulingPlanTempInput input)
    {
         
        input.Keyword = input.Keyword?.Trim();
        var query = schedulingPlanTempRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.DoctorName.Contains(input.Keyword) || u.TimePeriodCode.Contains(input.Keyword) || u.TimePeriodName.Contains(input.Keyword) || u.RegCategoryName.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.WeekDay.Contains(input.Keyword) || u.RoomName.Contains(input.Keyword) || u.IpAddress.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
    
            .WhereIF(input.WeekDay!=null  , u => u.WeekDay == input.WeekDay) 
            .WhereIF(input.DeptId >0, u => u.DeptId == input.DeptId)
            
            .Select<SchedulingPlanTempOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

   
    /// <summary>
    /// 获取医生排班模板详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取医生排班模板详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<SchedulingPlanTemp> Detail([FromQuery] QueryByIdSchedulingPlanTempInput input)
    {
        return await schedulingPlanTempRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加医生排班模板 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加医生排班模板")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddSchedulingPlanTempInput input)
    {
        var entity = input.Adapt<SchedulingPlanTemp>();
        return await schedulingPlanTempRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新医生排班模板 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新医生排班模板")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateSchedulingPlanTempInput input)
    {
        var entity = input.Adapt<SchedulingPlanTemp>();
        await schedulingPlanTempRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除医生排班模板 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除医生排班模板")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteSchedulingPlanTempInput input)
    {
        var entity = await schedulingPlanTempRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await schedulingPlanTempRep.FakeDeleteAsync(entity);   //假删除
        //await _schedulingPlanRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除医生排班模板 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除医生排班模板")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteSchedulingPlanTempInput> input)
    {
        var exp = Expressionable.Create<SchedulingPlanTemp>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await schedulingPlanTempRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await schedulingPlanTempRep.FakeDeleteAsync(list);   //假删除
        //return await _schedulingPlanRep.DeleteAsync(list);   //真删除
    }
    
     
    
 
 
}
