using His.Module.Insurance.Service.Settlement.Model.Patient;

namespace His.Module.Insurance.Service.Settlement.Dto.Patient;

public class SavePersonalInfoInput 
{
    /// <summary>
    /// 挂号id
    /// </summary>
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊唯一号
    /// </summary>
    public virtual string? VisitNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 患者id
    /// </summary>
    public virtual long? PatientId { get; set; }
    public QueryBasicInfoResponse QueryBasicInfoResponse { get; set; }
    public ReadCardResponse ReadCardResponse { get; set; }
}