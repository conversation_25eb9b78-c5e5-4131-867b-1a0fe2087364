﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 门诊退药基础输入参数
/// </summary>
public class OutpatientDrugRefundRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 退药单号
    /// </summary>
    public virtual string? RefundNo { get; set; }
    
    /// <summary>
    /// 关联的发药记录ID
    /// </summary>
    public virtual long? SendRecordId { get; set; }
    
    /// <summary>
    /// 退药人ID
    /// </summary>
    public virtual long? RefundUserId { get; set; }
    
    /// <summary>
    /// 退药人名称
    /// </summary>
    public virtual string? RefundUserName { get; set; }
    
    /// <summary>
    /// 退药时间
    /// </summary>
    public virtual DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退药申请id
    /// </summary>
    public virtual long? RefundApplyId { get; set; }
    
    /// <summary>
    /// 审核完成时间
    /// </summary>
    public virtual DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 退药原因
    /// </summary>
    public virtual string? Reason { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public virtual string? VisitNo { get; set; }
    
    /// <summary>
    /// 就诊id
    /// </summary>
    public virtual long? VisitId { get; set; }
    
    /// <summary>
    /// 卡号
    /// </summary>
    public virtual string? CardNo { get; set; }
    
    /// <summary>
    /// 卡id
    /// </summary>
    public virtual long? CardId { get; set; }
    
    /// <summary>
    /// 处方ID
    /// </summary>
    public virtual long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    public virtual long? PrescriptionDetailId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 退药数量
    /// </summary>
    public virtual int? RefundQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public virtual decimal? Price { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>
    public virtual decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public virtual string? BatchNo { get; set; }
    
}

/// <summary>
/// 门诊退药分页查询输入参数
/// </summary>
public class PageOutpatientDrugRefundRecordInput : BasePageInput
{
    /// <summary>
    /// 退药单号
    /// </summary>
    public string? RefundNo { get; set; }
    
    /// <summary>
    /// 关联的发药记录ID
    /// </summary>
    public long? SendRecordId { get; set; }
    
    /// <summary>
    /// 退药人ID
    /// </summary>
    public long? RefundUserId { get; set; }
    
    /// <summary>
    /// 退药人名称
    /// </summary>
    public string? RefundUserName { get; set; }
    
    /// <summary>
    /// 退药时间范围
    /// </summary>
     public DateTime?[] RefundTimeRange { get; set; }
    
    /// <summary>
    /// 退药申请id
    /// </summary>
    public long? RefundApplyId { get; set; }
    
    /// <summary>
    /// 审核完成时间范围
    /// </summary>
     public DateTime?[] AuditTimeRange { get; set; }
    
    /// <summary>
    /// 退药原因
    /// </summary>
    public string? Reason { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 就诊id
    /// </summary>
    public long? VisitId { get; set; }
    
    /// <summary>
    /// 卡号
    /// </summary>
    public string? CardNo { get; set; }
    
    /// <summary>
    /// 卡id
    /// </summary>
    public long? CardId { get; set; }
    
    /// <summary>
    /// 处方ID
    /// </summary>
    public long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    public long? PrescriptionDetailId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    public string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 退药数量
    /// </summary>
    public int? RefundQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? Price { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>
    public decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 门诊退药增加输入参数
/// </summary>
public class AddOutpatientDrugRefundRecordInput
{
 
 
     
    
    /// <summary>
    ///  计费id
    /// </summary>
    public long ChargeId { get; set; }
     
    /// <summary>
    /// 退药原因
    /// </summary>
    public string? Reason { get; set; }
    
    
     
    
    /// <summary>
    /// 处方ID
    /// </summary>
    public long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    public List<AddOutpatientDrugRefundDetailInput> Details { get; set; }
     
    
}

/// <summary>
/// 门诊退药增加输入参数
/// </summary>
public class AddOutpatientDrugRefundDetailInput
{
 
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    public long? PrescriptionDetailId { get; set; }
    public    long? DrugId { get; set; }
    
}
//
// /// <summary>
// /// 门诊退药增加输入参数
// /// </summary>
// public class AddOutpatientDrugRefundRecordInput
// {
//  
//     
//     /// <summary>
//     /// 关联的发药记录ID
//     /// </summary>
//     public long? SendRecordId { get; set; }
//     
//     /// <summary>
//     /// 退药人ID
//     /// </summary>
//     public long? RefundUserId { get; set; }
//     
//     /// <summary>
//     /// 退药人名称
//     /// </summary>
//     public string? RefundUserName { get; set; }
//     
//     /// <summary>
//     /// 退药时间
//     /// </summary>
//     public DateTime? RefundTime { get; set; }
//     
//     /// <summary>
//     /// 退药申请id
//     /// </summary>
//     public long? RefundApplyId { get; set; }
//     
//     /// <summary>
//     /// 审核完成时间
//     /// </summary>
//     public DateTime? AuditTime { get; set; }
//     
//     /// <summary>
//     /// 退药原因
//     /// </summary>
//     public string? Reason { get; set; }
//     
//     /// <summary>
//     /// 药房ID
//     /// </summary>
//     public long? StorageId { get; set; }
//     
//     /// <summary>
//     /// 药房名称
//     /// </summary>
//     public string? StorageName { get; set; }
//     
//     /// <summary>
//     /// 患者ID
//     /// </summary>
//     public long? PatientId { get; set; }
//     
//     /// <summary>
//     /// 患者名称
//     /// </summary>
//     public string? PatientName { get; set; }
//     
//     /// <summary>
//     /// 就诊号
//     /// </summary>
//     public string? VisitNo { get; set; }
//     
//  
//     
//     /// <summary>
//     /// 卡号
//     /// </summary>
//     public string? CardNo { get; set; }
//     
//     /// <summary>
//     /// 卡id
//     /// </summary>
//     public long? CardId { get; set; }
//     
//     /// <summary>
//     /// 处方ID
//     /// </summary>
//     public long? PrescriptionId { get; set; }
//     
//     /// <summary>
//     /// 处方明细ID
//     /// </summary>
//     public long? PrescriptionDetailId { get; set; }
//      
//     
// }

/// <summary>
/// 门诊退药删除输入参数
/// </summary>
public class DeleteOutpatientDrugRefundRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 门诊退药更新输入参数
/// </summary>
public class UpdateOutpatientDrugRefundRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 退药单号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "退药单号字符长度不能超过100")]
    public string? RefundNo { get; set; }
    
    /// <summary>
    /// 关联的发药记录ID
    /// </summary>    
    public long? SendRecordId { get; set; }
    
    /// <summary>
    /// 退药人ID
    /// </summary>    
    public long? RefundUserId { get; set; }
    
    /// <summary>
    /// 退药人名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "退药人名称字符长度不能超过100")]
    public string? RefundUserName { get; set; }
    
    /// <summary>
    /// 退药时间
    /// </summary>    
    public DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退药申请id
    /// </summary>    
    public long? RefundApplyId { get; set; }
    
    /// <summary>
    /// 审核完成时间
    /// </summary>    
    public DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 退药原因
    /// </summary>    
    public string? Reason { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>    
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药房名称字符长度不能超过100")]
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>    
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "患者名称字符长度不能超过100")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "就诊号字符长度不能超过100")]
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 就诊id
    /// </summary>    
    public long? VisitId { get; set; }
    
    /// <summary>
    /// 卡号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "卡号字符长度不能超过100")]
    public string? CardNo { get; set; }
    
    /// <summary>
    /// 卡id
    /// </summary>    
    public long? CardId { get; set; }
    
    /// <summary>
    /// 处方ID
    /// </summary>    
    public long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>    
    public long? PrescriptionDetailId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>    
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 退药数量
    /// </summary>    
    public decimal? RefundQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>    
    public decimal? Price { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>    
    public decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }
    
}

/// <summary>
/// 门诊退药主键查询输入参数
/// </summary>
public class QueryByIdOutpatientDrugRefundRecordInput : DeleteOutpatientDrugRefundRecordInput
{
}

/// <summary>
/// 门诊退药数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportOutpatientDrugRefundRecordInput : BaseImportInput
{
    /// <summary>
    /// 退药单号
    /// </summary>
    [ImporterHeader(Name = "退药单号")]
    [ExporterHeader("退药单号", Format = "", Width = 25, IsBold = true)]
    public string? RefundNo { get; set; }
    
    /// <summary>
    /// 关联的发药记录ID
    /// </summary>
    [ImporterHeader(Name = "关联的发药记录ID")]
    [ExporterHeader("关联的发药记录ID", Format = "", Width = 25, IsBold = true)]
    public long? SendRecordId { get; set; }
    
    /// <summary>
    /// 退药人ID
    /// </summary>
    [ImporterHeader(Name = "退药人ID")]
    [ExporterHeader("退药人ID", Format = "", Width = 25, IsBold = true)]
    public long? RefundUserId { get; set; }
    
    /// <summary>
    /// 退药人名称
    /// </summary>
    [ImporterHeader(Name = "退药人名称")]
    [ExporterHeader("退药人名称", Format = "", Width = 25, IsBold = true)]
    public string? RefundUserName { get; set; }
    
    /// <summary>
    /// 退药时间
    /// </summary>
    [ImporterHeader(Name = "退药时间")]
    [ExporterHeader("退药时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退药申请id
    /// </summary>
    [ImporterHeader(Name = "退药申请id")]
    [ExporterHeader("退药申请id", Format = "", Width = 25, IsBold = true)]
    public long? RefundApplyId { get; set; }
    
    /// <summary>
    /// 审核完成时间
    /// </summary>
    [ImporterHeader(Name = "审核完成时间")]
    [ExporterHeader("审核完成时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 退药原因
    /// </summary>
    [ImporterHeader(Name = "退药原因")]
    [ExporterHeader("退药原因", Format = "", Width = 25, IsBold = true)]
    public string? Reason { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    [ImporterHeader(Name = "药房ID")]
    [ExporterHeader("药房ID", Format = "", Width = 25, IsBold = true)]
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    [ImporterHeader(Name = "药房名称")]
    [ExporterHeader("药房名称", Format = "", Width = 25, IsBold = true)]
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [ImporterHeader(Name = "患者ID")]
    [ExporterHeader("患者ID", Format = "", Width = 25, IsBold = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    [ImporterHeader(Name = "患者名称")]
    [ExporterHeader("患者名称", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    [ImporterHeader(Name = "就诊号")]
    [ExporterHeader("就诊号", Format = "", Width = 25, IsBold = true)]
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 就诊id
    /// </summary>
    [ImporterHeader(Name = "就诊id")]
    [ExporterHeader("就诊id", Format = "", Width = 25, IsBold = true)]
    public long? VisitId { get; set; }
    
    /// <summary>
    /// 卡号
    /// </summary>
    [ImporterHeader(Name = "卡号")]
    [ExporterHeader("卡号", Format = "", Width = 25, IsBold = true)]
    public string? CardNo { get; set; }
    
    /// <summary>
    /// 卡id
    /// </summary>
    [ImporterHeader(Name = "卡id")]
    [ExporterHeader("卡id", Format = "", Width = 25, IsBold = true)]
    public long? CardId { get; set; }
    
    /// <summary>
    /// 处方ID
    /// </summary>
    [ImporterHeader(Name = "处方ID")]
    [ExporterHeader("处方ID", Format = "", Width = 25, IsBold = true)]
    public long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    [ImporterHeader(Name = "处方明细ID")]
    [ExporterHeader("处方明细ID", Format = "", Width = 25, IsBold = true)]
    public long? PrescriptionDetailId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [ImporterHeader(Name = "药品ID")]
    [ExporterHeader("药品ID", Format = "", Width = 25, IsBold = true)]
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [ImporterHeader(Name = "药品编码")]
    [ExporterHeader("药品编码", Format = "", Width = 25, IsBold = true)]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [ImporterHeader(Name = "药品名称")]
    [ExporterHeader("药品名称", Format = "", Width = 25, IsBold = true)]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    [ImporterHeader(Name = "药品规格")]
    [ExporterHeader("药品规格", Format = "", Width = 25, IsBold = true)]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    [ImporterHeader(Name = "药品单位")]
    [ExporterHeader("药品单位", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 退药数量
    /// </summary>
    [ImporterHeader(Name = "退药数量")]
    [ExporterHeader("退药数量", Format = "", Width = 25, IsBold = true)]
    public decimal? RefundQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [ImporterHeader(Name = "零售价")]
    [ExporterHeader("零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? Price { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>
    [ImporterHeader(Name = "总退药金额")]
    [ExporterHeader("总退药金额", Format = "", Width = 25, IsBold = true)]
    public decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [ImporterHeader(Name = "批号")]
    [ExporterHeader("批号", Format = "", Width = 25, IsBold = true)]
    public string? BatchNo { get; set; }
    
}
