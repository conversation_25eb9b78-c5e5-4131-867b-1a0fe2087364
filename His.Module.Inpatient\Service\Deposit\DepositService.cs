using His.Module.Inpatient.Service.Deposit.Dto;
namespace His.Module.Inpatient.Service.Deposit;

/// <summary>
/// 押金管理服务 🧩
/// </summary>
[ApiDescriptionSettings(InpatientConst.GroupName, Order = 100)]
public class DepositService(
    SqlSugarRepository<InpatientDepositAccount> inpatientDepositAccountRep,
    SqlSugarRepository<DepositTransaction> depositTransactionRep,
    SqlSugarRepository<RefundSourceLink> refundSourceLinkRep) : IDynamicApiController, ITransient
{

    /// <summary>
    /// 创建押金账户 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("创建押金账户")]
    [ApiDescriptionSettings(Name = "Add")]
    [HttpPost]
    public async Task Add(AddDepositInput input)
    {
        // 先根据住院流水号查询账户是否存在
        var exist = await inpatientDepositAccountRep.IsAnyAsync(u => u.InpatientNo == input.InpatientNo && u.Status == 0);
        if (exist) return;
        await inpatientDepositAccountRep.InsertAsync(new InpatientDepositAccount
        {
            InpatientRegisterId = input.InpatientRegisterId,
            InpatientNo = input.InpatientNo,
            PatientId = input.PatientId,
            Status = 0,
            TotalPaidAmount = 0,
            TotalRefundedAmount = 0,
            TotalUsedAmount = 0,
            CurrentBalance = 0,
            ClosedTime = null
        });
    }


    /// <summary>
    /// 获取押金账户详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取押金账户详情")]
    [ApiDescriptionSettings(Name = "Detail")]
    [HttpGet]
    public async Task<InpatientDepositAccount> Detail([FromQuery] QueryByIdDepositInput input)
    {
        return await inpatientDepositAccountRep.AsQueryable()
            .Includes(u => u.Transactions)
            .FirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 押金缴费
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [DisplayName("押金缴费")]
    [ApiDescriptionSettings(Name = "Payment")]
    [HttpPost]
    [UnitOfWork]
    public async Task Payment(PaymentDepositInput input)
    {
        // 获取账户信息并验证账户状态
        var account = await inpatientDepositAccountRep
            .GetFirstAsync(u => u.Id == input.AccountId);

        // 检查账户是否存在
        if (account == null)
            throw Oops.Oh("没有查询到押金账户信息");

        // 检查账户状态是否允许缴费（状态为3时不允许缴费）
        if (account.Status == 3)
            throw Oops.Oh("当前押金账户状态不允许缴费");

        // 创建缴费交易记录
        var paymentTx = new DepositTransaction
        {
            AccountId = input.AccountId, // 账户ID
            TransactionType = "0", // 交易类型：0表示缴费
            Amount = input.Amount, // 缴费金额
            RefundableAmount = input.Amount, // 初始可退金额等于缴费金额
            Channel = input.Channel, // 支付渠道
            PaymentMethod = input.PayType, // 支付方式
            ReceiptNo = input.ReceiptNo, // 外部凭证号
            Status = "1", // 交易状态：1表示成功
            InvoiceNo = await GenerateInvoiceNo(),
            Remark = input.Remark // 备注信息
        };
        await depositTransactionRep.InsertAsync(paymentTx);
        // 保存账户信息更新
        await inpatientDepositAccountRep.AsUpdateable()
            .SetColumns(u => new InpatientDepositAccount
            {
                CurrentBalance = u.CurrentBalance + paymentTx.Amount, TotalPaidAmount = u.TotalPaidAmount + paymentTx.Amount
            })
            .Where(u => u.Id == account.Id)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 押金退款
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [DisplayName("押金退款")]
    [ApiDescriptionSettings(Name = "Refund")]
    [HttpPost]
    [UnitOfWork]
    public async Task Refund(RefundDepositInput input)
    {
        // 获取账户信息并验证余额是否充足
        var account = await inpatientDepositAccountRep
            .GetFirstAsync(u => u.Id == input.AccountId);

        // 检查账户当前余额是否足够进行退款操作
        if (account.CurrentBalance < input.TotalRefundAmount)
            throw Oops.Oh("退款金额超过账户余额");

        // 查询该账户下所有可退款的缴费记录，按交易ID倒序排列（最新的记录在前）
        var paymentSources = await depositTransactionRep.AsQueryable()
            .Where(u => u.AccountId == input.AccountId && u.TransactionType == "0" && u.RefundableAmount > 0)
            .OrderBy(t => t.Id, OrderByType.Desc)
            .ToListAsync();

        // 制定退款计划，采用后进先出原则，优先从最新的缴费记录中退款
        var refundPlan = new List<(long paymentId, decimal amount, string? channel, string? receipt)>();
        var amountLeftToRefund = input.TotalRefundAmount;

        // 遍历可退款的缴费记录，计算每条记录需要退款的金额
        foreach (var source in paymentSources)
        {
            // 计算从当前缴费记录中需要退款的金额（不能超过该记录的可退金额）
            var refundFromThisSource = Math.Min(amountLeftToRefund, source.RefundableAmount.Value);

            // 将退款信息添加到退款计划中
            refundPlan.Add((source.Id, refundFromThisSource, source.Channel, source.ReceiptNo));

            // 更新剩余待退款金额
            amountLeftToRefund -= refundFromThisSource;

            // 如果已满足退款金额需求，则停止遍历
            if (amountLeftToRefund <= 0)
                break;
        }

        // 创建主退款交易记录
        var mainRefundTx = new DepositTransaction
        {
            AccountId = input.AccountId,
            TransactionType = "1", // 交易类型：1表示退款
            Amount = input.TotalRefundAmount, // 退款总金额
            Status = "1", // 交易状态：1表示成功
            InvoiceNo = await GenerateInvoiceNo(),
            Remark = input.Reason // 退款原因
        };
        await depositTransactionRep.InsertAsync(mainRefundTx);

        // 执行退款计划，更新相关记录
        foreach (var (paymentId, amount, _, _) in refundPlan)
        {
            // 更新原缴费记录的可退金额（减去已退金额）
            await depositTransactionRep.AsUpdateable()
                .SetColumns(it => it.RefundableAmount == it.RefundableAmount - amount)
                .Where(it => it.Id == paymentId)
                .ExecuteCommandAsync();

            // 创建退款来源关联记录，用于追踪资金流向
            var link = new RefundSourceLink
            {
                RefundTransactionId = mainRefundTx.Id, // 退款交易ID
                PaymentTransactionId = paymentId, // 原缴费交易ID
                Amount = amount // 本次退款金额
            };
            await refundSourceLinkRep.InsertAsync(link);

            // todo 调用退款接口
        }

        // 更新账户的总退款金额和当前余额
        await inpatientDepositAccountRep.AsUpdateable()
            .SetColumns(u => new InpatientDepositAccount
            {
                CurrentBalance = u.CurrentBalance - input.TotalRefundAmount, TotalRefundedAmount = u.TotalRefundedAmount + input.TotalRefundAmount
            })
            .Where(u => u.Id == account.Id)
            .ExecuteCommandAsync();
        ;
    }

    /// <summary>
    /// 红冲
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [DisplayName("红冲")]
    [ApiDescriptionSettings(Name = "CorrectPaymentError")]
    [HttpPost]
    [UnitOfWork]
    public async Task CorrectPaymentError(CorrectPaymentErrorInput input)
    {

        // 查找原始交易
        var originalTx = await depositTransactionRep
            .GetFirstAsync(t => t.Id == input.OriginalTransactionId);

        if (originalTx == null) throw Oops.Oh($"找不到原始交易ID为 {input.OriginalTransactionId} 的交易记录");

        // 核心业务规则校验
        // 校验1: 确保只有'缴纳'类型的流水才能被修正。
        if (originalTx.TransactionType != "0")
            throw Oops.Oh("仅允许对“缴纳”类型的交易进行冲正");

        // 校验2: 【关键风控】确保这笔缴费是“干净”的，即从未被部分退款过。
        // 如果 amount 和 refundable_amount 不相等，说明已经有退款操作依赖于此交易，直接冲正会破坏数据链。
        if (originalTx.Amount != originalTx.RefundableAmount)
            throw Oops.Oh("交易已部分退款，无法直接冲正。");
        // 校验3: 修正后的金额不能大于原始金额。
        if (input.CorrectAmount > originalTx.Amount) throw Oops.Oh("正确的金额不能大于原始记录金额");
        // 执行账务操作：冲正
        // 创建一笔与原始交易金额完全相同、类型为'reversal'的流水，用于在账面上抵消原始交易。
        var reversalTx = new DepositTransaction
        {
            AccountId = originalTx.AccountId,
            TransactionType = "3",
            Amount = originalTx.Amount,
            Status = "1", // 冲正操作是即时完成的
            Remark = $"原始交易ID={originalTx.Id}，修正原因: {input.Reason}",
            InvoiceNo = await GenerateInvoiceNo()
        };
        await depositTransactionRep.InsertAsync(reversalTx);

        //  执行账务操作：重录
        // 创建一笔新的'缴纳'流水，记录正确的金额。
        var correctTx = new DepositTransaction
        {
            AccountId = originalTx.AccountId,
            TransactionType = "0",
            Amount = input.CorrectAmount,
            RefundableAmount = input.CorrectAmount, // 新的正确流水的可退金额是其自身的全额
            Channel = originalTx.Channel, // 沿用原始的支付渠道
            ReceiptNo = originalTx.ReceiptNo, // 【重要】沿用原始的外部支付凭证号，保证资金来源的可追溯性
            Status = "1",
            Remark = $"原始交易ID={originalTx.Id}，修正原因: {input.Reason}",
            InvoiceNo = await GenerateInvoiceNo()
        };
        await depositTransactionRep.InsertAsync(correctTx);


        //更新原始交易状态
        // 将原始缴费单的`refundable_amount`清零，标志其在财务上已被完全消耗，不能再被用于退款。
        originalTx.RefundableAmount = 0;
        originalTx.Remark = (originalTx.Remark ?? "") + "已被冲正"; // 在备注中添加标记，便于人工查看
        await depositTransactionRep.UpdateAsync(originalTx);

        // 更新账户总览
        var account = await inpatientDepositAccountRep
            .GetFirstAsync(a => a.Id == originalTx.AccountId);

        // 核心计算：总缴纳额 = 原总额 - 错误的金额 + 正确的金额
        await inpatientDepositAccountRep.AsUpdateable()
            .SetColumns(u => new InpatientDepositAccount
            {
                TotalPaidAmount = u.TotalPaidAmount - originalTx.Amount + input.CorrectAmount, CurrentBalance = u.CurrentBalance - originalTx.Amount + input.CorrectAmount
            })
            .Where(u => u.Id == account.Id)
            .ExecuteCommandAsync();

        // 自动触发差额退款
        var refundAmount = originalTx.Amount - input.CorrectAmount;
        if (refundAmount > 0)
        {
            // 如果原始金额大于正确金额（多收了钱），则系统需要自动为患者发起一笔退款。
            var refundRequest = new RefundDepositInput
            {
                AccountId = account.Id, TotalRefundAmount = refundAmount, Reason = $"原始交易ID={originalTx.Id}，冲正后自动退款"
            };
            await Refund(refundRequest);
        }
    }


    /// <summary>
    /// 生成发票号
    /// </summary>
    /// <returns></returns>
    private async Task<string> GenerateInvoiceNo()
    {
        var invoiceNo = await depositTransactionRep.Context.Ado.GetStringAsync(
            "SELECT LPAD(CAST(NEXTVAL('deposit_transaction_invoice_no_seq')As varchar),9,'0')");
        return "F" + invoiceNo;
    }
}