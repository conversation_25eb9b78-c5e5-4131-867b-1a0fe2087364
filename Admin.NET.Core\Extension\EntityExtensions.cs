namespace Admin.NET.Core;

 
    
public static class EntityExtensions
{
    /// <summary>
    /// 设置审计属性默认值
    /// </summary>
    /// <param name="entity"></param>
    public static void ResetNull(this EntityTenantBaseData entity)
    {
        entity.Id = 0;
        entity.CreateUserId = null;
        entity.CreateTime = DateTime.Now;
        entity.CreateUserName = null;
        entity.UpdateUserId = null;
        entity.UpdateTime = null;
        entity.UpdateUserName = null;
        entity.CreateOrgId = null;
        entity.CreateOrgName = null;
    }
}
