namespace His.Module.Pharmacy.Api.DrugInventory.Dto;


public class LockDrugInventoryInput  
{
    /// <summary>
    /// 库存id
    /// </summary>
    public virtual long? InventoryId { get; set; }
    
    public virtual long? PrescriptionId { get; set; }
    public virtual long? PrescriptionDetailId { get; set; }
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public virtual string? DrugCode { get; set; }
    /// <summary>
    /// 药品名称
    /// </summary>
    public virtual string? DrugName { get; set; }

    
 
    /// <summary>
    /// 药房ID
    /// </summary>
    public virtual long? StorageId { get; set; }
     
    
    /// <summary>
    /// 数量
    /// </summary>
    public virtual int? Quantity { get; set; }
    
    public virtual string? Unit { get; set; }
    
     
}