using Aop.Api.Domain;
using Furion.DatabaseAccessor;
using His.Module.Insurance.Const;
using His.Module.Insurance.Entity;
using His.Module.Insurance.Service.Settlement.Dto.Outpatient;
using His.Module.Insurance.Service.Settlement.Dto.Patient;
using His.Module.Insurance.Service.Settlement.Interface;
using His.Module.Insurance.Service.Settlement.Model.Outpatient;
using His.Module.Insurance.Service.Settlement.Model.Patient;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.OutpatientDoctor.Api.Register;
using His.Module.OutpatientDoctor.Api.Register.Dto;
using Newtonsoft.Json;

namespace His.Module.Insurance.Service.Settlement;

/// <summary>
/// 医保患者
/// </summary>
[ApiDescriptionSettings(InsuranceConst.GroupName, Order = 100)]
public class InsuranceOutpatientService(
    IOutpatientApi outpatientApi,
    IOutpatientRegisterApi outpatientRegisterApi,
    IChargeApi outpatientChargeApi,
    ICommonApi commonApi,
    SqlSugarRepository<PersonalInfo> personalInfoRepository
) : IDynamicApiController, ITransient
{
    /// <summary>
    /// 门诊预结算
    /// </summary>
    /// <returns></returns>
    [DisplayName("门诊预结算")]
    [ApiDescriptionSettings(Name = "SettlePre"), HttpPost]
    [SkipPermission]
    public async Task<SettleMzPreResponse> SettlePre(OutpatientChargeSettlementInput input)
    {
        // 处理诊断 


        if (input.ChargeId is { Length: > 0 })
        {
            // 查询费用

            input.SettleMzPreRequest.p_fypd_ds =
                await this.GetMatchedItems(input);
        }
        else
        {
            if (input.SettleMzPreRequest.p_fypd_ds == null || input.SettleMzPreRequest.p_fypd_ds.Count == 0)
            {
                throw Oops.Oh("费用明细不能为空");
            }
        }


        // 个账消费，判断金额
        // 判断医师是对照

        // var r= await outpatientApi.SettlePre(input.SettleMzPreRequest);
        // return r;
        var totalAmount = input.SettleMzPreRequest.p_fypd_ds.Sum(x => x.zje);
        return SettleMzPreResponseGenerator.GenerateMockData(totalAmount);
    }

    /// <summary>
    /// 门诊预结算
    /// </summary>
    /// <returns></returns>
    [DisplayName("门诊结算")]
    [ApiDescriptionSettings(Name = "Settle"), HttpPost, UnitOfWork]
    [SkipPermission]
    public async Task<SettleMzResponse> Settle(OutpatientChargeSettlementInput input)
    {
        // 处理诊断 
        // input.SettleMzPreRequest.p_fypd_ds =
        //     await this.GetMatchedItems(input);


        //  var r= await outpatientApi.SettlePre(input.SettleMzPreRequest);
        //   return r;
    
       
        // 保存数据

        return new SettleMzResponse();
    }
    /// <summary>
    /// 门诊预结算
    /// </summary>
    /// <returns></returns>
    [DisplayName("预撤销门诊结算")]
    [ApiDescriptionSettings(Name = "DestroyPre"), HttpPost, UnitOfWork]
    [SkipPermission]
    public async Task<DestroyMzPreResponse> DestroyPre(DestroyMzRequest input)
    {
        // 处理诊断 
        // input.SettleMzPreRequest.p_fypd_ds =
        //     await this.GetMatchedItems(input);


        //  var r= await outpatientApi.SettlePre(input.SettleMzPreRequest);
        //   return r;
    
       
        // 保存数据

        return new DestroyMzPreResponse();
    }
    /// <summary>
    /// 门诊预结算
    /// </summary>
    /// <returns></returns>
    [DisplayName("撤销门诊结算")]
    [ApiDescriptionSettings(Name = "Destroy"), HttpPost, UnitOfWork]
    [SkipPermission]
    public async Task<DestroyMzResponse> Destroy(DestroyMzRequest input)
    {
        // 处理诊断 
        // input.SettleMzPreRequest.p_fypd_ds =
        //     await this.GetMatchedItems(input);


        //  var r= await outpatientApi.SettlePre(input.SettleMzPreRequest);
        //   return r;
    
       
        // 保存数据

        return new DestroyMzResponse();
    }
    /// <summary>
    /// 获取门诊医保对应后的项目
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取门诊医保对应后的项目")]
    [ApiDescriptionSettings(Name = "GetMatchedItems"), HttpPost]
    [SkipPermission]
    public async Task<List<SettleMzPreFypd>> GetMatchedItems(OutpatientChargeSettlementInput input)
    {
        // 获取收费项目
        List<OutpatientChargeDetailDto> chargeItems = await outpatientChargeApi.GetChargeList(input.ChargeId);
        //对照医保目录

        var result = new List<SettleMzPreFypd>();
        foreach (var item in chargeItems)
        {
            var fypd = new SettleMzPreFypd()
            {
                yyxmbm = item.ItemCode,
                yyxmmc = item.ItemName,
                dj = item.Price.HasValue ? (double)item.Price.Value : 0,
                sl = item.Quantity.HasValue ? (double)item.Quantity.Value : 0,
                zje = item.Amount.HasValue ? (double)item.Amount.Value : 0,
                zxksbm = item.ExecuteDeptId.ToString(),
                kdksbm = item.BillingDeptId.ToString(),
                zxysbm = item.ExecuteDoctorId.ToString(),
                kdysbm = item.BillingDoctorId.ToString(),
                //自付比例 sxzfbl = 
                fyfssj = item.BillingTime.HasValue ? item.BillingTime.Value.ToString("yyyyMMddHHmmss") : "",
                gg = item.Spec,
                bzsl = 1, // 包装数量
                yyts = item.MedicationDays,
                yysm = "", // 用药
                yzlsh = item.BillingDetailId.ToString(),
                gytj = item.MedicationRoutesId.ToString(),
                dcyl = item.SingleDose.HasValue ? (double)item.SingleDose.Value : 0,
                yypc = item.FrequencyId.ToString(),

                // sfryxm = item.CreateOrgName 收费人员姓名
                // zsm
                // clbz 
            };
            result.Add(fypd);
        }

        return result;
    }
}