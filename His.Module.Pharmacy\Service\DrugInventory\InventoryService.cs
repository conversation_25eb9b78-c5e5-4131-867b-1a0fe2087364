using Admin.NET.Core.Service;
using His.Module.Pharmacy.Api.DrugInventory.Dto;

namespace His.Module.Pharmacy.Service;

public class InventoryService : ITransient
{
    private readonly SqlSugarRepository<DrugInventory> _drugInventoryRep;
    private readonly SqlSugarRepository<DrugStorage> _drugStorageRep;
    private readonly SysCacheService _sysCacheService;

    public InventoryService(
        SqlSugarRepository<DrugStorage> drugStorageRep,
        SqlSugarRepository<DrugInventory> drugInventoryRep, SysCacheService sysCacheService)
    {
        _drugStorageRep = drugStorageRep;
        _drugInventoryRep = drugInventoryRep;
        _sysCacheService = sysCacheService;
    }

    private const string LOCK_KEY = "sync_inventory";

    /// <summary>
    /// 入库单更新库存
    /// </summary>
    /// <param name="detail"></param>
    /// <param name="record"></param>
    /// <exception cref="Exception"></exception>
    public async Task UpdateInventoryAsync(StorageInDetail detail, StorageInRecord record)
    {
        using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{detail.DrugId}"))
        {
            if (lockDisposable == null)
            {
                throw Oops.Oh("获取锁失败");
            }

            // 在这里执行需要同步的代码
            var inventory = await _drugInventoryRep.GetFirstAsync(u =>
                u.StorageId == record.StorageId &&
                u.Unit == detail.Unit &&
                u.DrugType == detail.DrugType &&
                u.BatchNo == detail.BatchNo &&
                u.ProductionDate == detail.ProductionDate &&
                u.ExpirationDate == detail.ExpirationDate);


            if (inventory != null)
            {
                inventory.Quantity += detail.Quantity;
                await _drugInventoryRep.UpdateAsync(u => new DrugInventory()
                {
                    Quantity = inventory.Quantity,
                    UpdateTime = DateTime.Now,
                    TotalSalePrice = inventory.Quantity * detail.SalePrice,
                    TotalPurchasePrice = inventory.Quantity * detail.PurchasePrice,
                    LastSupplierId = record.SupplierId,
                    LastSupplierName = record.SupplierName,
                }, u => u.Id == inventory.Id);
            }
            else
            {
                var newInventory = detail.Adapt<DrugInventory>();
                newInventory.StorageId = record.StorageId;
                newInventory.StorageCode = record.StorageCode;

                newInventory.LastSupplierId = record.SupplierId;
                newInventory.LastSupplierName = record.SupplierName;
                newInventory.TotalSalePrice = detail.Quantity * detail.SalePrice;
                newInventory.TotalPurchasePrice = detail.Quantity * detail.PurchasePrice;
                newInventory.Id = 0;
                newInventory.PendingQuantity = 0;
                await _drugInventoryRep.InsertAsync(newInventory);
            }
        }
    }

    // /// <summary>
    // /// 出库单更新库存
    // /// </summary>
    // /// <param name="detail"></param>
    // /// <param name="record"></param>
    // /// <exception cref="Exception"></exception>
    public async Task UpdateInventoryAsync(StorageOutDetail detail, StorageOutRecord record, DrugStorage target)
    {
        using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{detail.InventoryId}"))
        {
            if (lockDisposable == null)
            {
                throw Oops.Oh("获取锁失败");
            }

            // 在这里执行需要同步的代码
            var inventory = await _drugInventoryRep.GetFirstAsync(u =>
                u.Id == detail.InventoryId);

            if (inventory != null)
            {
                if (inventory.Quantity < detail.Quantity)
                {
                    throw Oops.Oh("库存不足");
                }


                inventory.Quantity -= detail.Quantity;
                await _drugInventoryRep.UpdateAsync(u => new DrugInventory()
                {
                    Quantity = inventory.Quantity,
                    UpdateTime = DateTime.Now,
                    TotalSalePrice = inventory.Quantity * detail.SalePrice,
                    TotalPurchasePrice = inventory.Quantity * detail.PurchasePrice,
                    LastSupplierId = record.SupplierId,
                    LastSupplierName = record.SupplierName,
                }, u => u.Id == inventory.Id);
                // 写入到药房库存

                if (target != null) //库房 
                {
                    // 增加库存
                    var targetInventory = inventory.Adapt<DrugInventory>();
                    targetInventory.StorageId = record.TargetDeptId;
                    targetInventory.StorageCode = target.StorageCode;
                    targetInventory.StorageName = target.StorageName;
                    targetInventory.TotalSalePrice = detail.Quantity * detail.SalePrice;
                    targetInventory.TotalPurchasePrice = detail.Quantity * detail.PurchasePrice;
                    targetInventory.Quantity = detail.Quantity;

                    targetInventory.Id = 0;
                    // 单位换算
                    if (detail.DrugType == "3" && target.HerbUnitConv == 1 && detail.Unit=="kg")
                    {
                        // 草药出库
                        targetInventory.Quantity = detail.Quantity * 1000;
                        targetInventory.Unit = "g";
                        // 价格？
                    }

                    targetInventory.PendingQuantity = 0;
                    await _drugInventoryRep.InsertAsync(targetInventory);
                }
            }
            else
            {
                throw Oops.Oh("库存不存在");
            }
        }
    }

    public async Task AdjustPriceAsync(AdjustPriceRecord record)
    {
        var list = await _drugInventoryRep.GetListAsync(u =>
            u.DrugCode == record.DrugCode && u.BatchNo == record.BatchNo
                                          && u.SalePrice == record.OldSalePrice
        );
        foreach (var item in list)
        {
            using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{item.DrugCode}"))
            {
                if (lockDisposable == null)
                {
                    throw Oops.Oh("获取锁失败");
                }

                item.SalePrice = record.NewSalePrice;
                item.TotalSalePrice = record.NewSalePrice * item.Quantity;
                await _drugInventoryRep.UpdateAsync(u => new DrugInventory()
                {
                    SalePrice = record.NewSalePrice,
                    TotalSalePrice = record.NewSalePrice * item.Quantity
                }, u => u.Id == item.Id);
            }
        }

        if (list.Count == 0)
        {
            throw Oops.Oh("未查询到该价格、批号的库存！");
        }
    }

    public async Task UpdateInventoryAsync(StorageSpecialDetail detail, StorageSpecialRecord record)
    {
        using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{detail.InventoryId}"))
        {
            if (lockDisposable == null)
            {
                throw Oops.Oh("获取锁失败");
            }

            var inventory = await _drugInventoryRep.GetFirstAsync(u =>
                u.Id == detail.InventoryId);
            if (inventory == null)
            {
                throw Oops.Oh("库存不存在");
            }
            else
            {
                //减少库存
                inventory.Quantity -= detail.Quantity;
                await _drugInventoryRep.UpdateAsync(u => new DrugInventory()
                {
                    Quantity = inventory.Quantity,
                    TotalSalePrice = inventory.Quantity * detail.SalePrice,
                    TotalPurchasePrice = inventory.Quantity * detail.PurchasePrice,
                }, u => u.Id == inventory.Id);
            }
        }
    }

    /// <summary>
    /// 退库 ,供应商退药
    /// </summary>
    /// <param name="detail"></param>
    /// <param name="record"></param>
    /// <exception cref="AppFriendlyException"></exception>
    public async Task UpdateInventoryAsync(StorageRefundDetail detail, StorageRefundRecord record)
    {
        using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{detail.InventoryId}"))
        {
            if (lockDisposable == null)
            {
                throw Oops.Oh("获取锁失败");
            }

            // 在这里执行需要同步的代码
            var inventory = await _drugInventoryRep.GetFirstAsync(u =>
                u.Id == detail.InventoryId);

            if (inventory != null)
            {
                if (inventory.Quantity < detail.Quantity)
                {
                    throw Oops.Oh("库存不足");
                }

                inventory.Quantity -= detail.Quantity;
                //申请科室减少库存
                await _drugInventoryRep.UpdateAsync(u => new DrugInventory()
                {
                    Quantity = inventory.Quantity,
                    UpdateTime = DateTime.Now,
                    TotalSalePrice = inventory.Quantity * detail.SalePrice,
                }, u => u.Id == inventory.Id);
                // 供应商退药不需要增加库存
                if (record.RefundType == "09")
                {
                    return;
                }

                // 目标科室增加库存
                var target = await _drugStorageRep.GetFirstAsync(u => u.Id == record.TargetStorageId);
                if (target != null) //库房 
                {
                    // 查询是否存在库存
                    var exist = await _drugInventoryRep.GetFirstAsync(u =>
                        u.DrugId == detail.DrugId && u.StorageId == record.TargetStorageId &&
                        u.DrugCode == detail.DrugCode && u.BatchNo == detail.BatchNo
                        && u.SalePrice == detail.SalePrice);
                    if (exist != null)
                    {
                        //修改
                        var quantity = exist.Quantity + detail.Quantity;

                        await _drugInventoryRep.UpdateAsync(u => new DrugInventory()
                        {
                            Quantity = quantity,
                            TotalSalePrice = quantity * detail.SalePrice,
                            TotalPurchasePrice = quantity * exist.PurchasePrice,
                            StorageCode = target.StorageCode,
                            StorageName = target.StorageName,
                        }, u => u.Id == exist.Id);
                    }
                    else
                    {
                        throw Oops.Oh("库存不存在");
                    }
                }
            }
            else
            {
                throw Oops.Oh("库存不存在");
            }
        }
    }

    /// <summary>
    /// 盘点
    /// </summary>
    /// <param name="detail"></param>
    /// <param name="record"></param>
    /// <exception cref="AppFriendlyException"></exception>
    public async Task UpdateInventoryAsync(StorageTakingDetail detail, StorageTakingRecord record)
    {
        using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{detail.InventoryId}"))
        {
            if (lockDisposable == null)
            {
                throw Oops.Oh("获取锁失败");
            }

            var inventory = await _drugInventoryRep.GetFirstAsync(u =>
                u.Id == detail.InventoryId);
            if (inventory == null)
            {
                throw Oops.Oh("库存不存在");
            }
            else
            {
                //减少库存

                var quantity = inventory.Quantity + (detail.TakingQuantity - detail.CurrentQuantity);
                if (quantity < 0)
                {
                    throw Oops.Oh("药品" + inventory.DrugName + "[" + inventory.DrugCode + "]库存不足无法完成盘点");
                }

                await _drugInventoryRep.UpdateAsync(u => new DrugInventory()
                {
                    Quantity = quantity,
                    TotalSalePrice = quantity * inventory.SalePrice,
                    TotalPurchasePrice = quantity * inventory.PurchasePrice,
                }, u => u.Id == inventory.Id);
            }
        }
    }

    /// <summary>
    /// 根据库存id锁定库存
    /// </summary>
    /// <param name="item"></param> 
    /// <exception cref="AppFriendlyException"></exception>
    public async Task<bool> UpdateInventoryAsync(LockDrugInventoryInput item)
    {
        using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{item.InventoryId}"))
        {
            if (lockDisposable == null)
            {
                throw Oops.Oh("获取锁失败");
            }

            // var result = await _drugInventoryRep.UpdateAsync(u =>
            //         new DrugInventory()
            //         {
            //             PendingQuantity = u.PendingQuantity + item.Quantity,
            //         },
            //     u => u.StorageId == item.StorageId &&
            //          u.Id == item.InventoryId &&
            //          u.Quantity >= u.PendingQuantity + item.Quantity
            // );
            // 使用原生sql语句执行   当删除处方时恢复锁定的库存
            // pending_quantity+{item.Quantity} 小于零时因为解除锁定库存时产生的数据  确保pending_quantity 不能为负数
            var result =    await _drugInventoryRep.Context.Ado.ExecuteCommandAsync(
                $"""
                 update pharmacy.drug_inventory 
                       set pending_quantity=case when pending_quantity+{item.Quantity} < 0 then 0 else pending_quantity+{item.Quantity} end 
                            where Id={item.InventoryId} and Quantity>=pending_quantity+{item.Quantity}
                 """
            );

            if (result==0)
            {
                throw Oops.Oh("药品【" + item.DrugName + "】库存不足！");
            }

            return true;
        }
    }

    /// <summary>
    /// 根据库存id发药
    /// </summary>
    /// <param name="item"></param> 
    /// <exception cref="AppFriendlyException"></exception>
    public async Task UpdateInventoryAsync(OutpatientDrugSendRecord item)
    {
        using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{item.InventoryId}"))
        {
            if (lockDisposable == null)
            {
                throw Oops.Oh("获取锁失败");
            }

            var result = await _drugInventoryRep.UpdateAsync(u =>
                    new DrugInventory()
                    {
                        PendingQuantity = u.PendingQuantity - item.Quantity,
                        Quantity = u.Quantity - item.Quantity,
                    }, u => u.Id == item.InventoryId &&
                            u.Quantity >= item.Quantity && u.PendingQuantity >= item.Quantity
            );
            if (!result)
            {
                throw Oops.Oh("药品【" + item.DrugName + "】库存扣减失败，请确认库存数量或当前处方是否已发药！");
            }
        }
    }
    
    /// <summary>
    /// 根据库存id退药
    /// </summary>
    /// <param name="item"></param> 
    /// <exception cref="AppFriendlyException"></exception>
    public async Task UpdateInventoryAsync(OutpatientDrugRefundRecord item)
    {
        using (var lockDisposable = _sysCacheService.BeginCacheLock($"{LOCK_KEY}_{item.InventoryId}"))
        {
            if (lockDisposable == null)
            {
                throw Oops.Oh("获取锁失败");
            }

            var result = await _drugInventoryRep.UpdateAsync(u =>
                    new DrugInventory()
                    {
                    
                        Quantity = u.Quantity + item.RefundQuantity,
                    }, u => u.Id == item.InventoryId
            );
            if (!result)
            {
                throw Oops.Oh("药品【" + item.DrugName + "】库存退药失败！");
            }
        }
    }
}