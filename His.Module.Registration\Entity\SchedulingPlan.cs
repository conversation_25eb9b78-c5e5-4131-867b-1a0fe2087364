﻿using Admin.NET.Core;
namespace His.Module.Registration.Entity;

/// <summary>
/// 排班计划表
/// </summary>
[Tenant("1300000000004")]
[SugarTable("scheduling_plan", "排班计划表")]
public class SchedulingPlan : EntityTenant
{
    /// <summary>
    /// 医生id
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "医生id")]
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 医生姓名
    /// </summary>
    [SugarColumn(ColumnName = "doctor_name", ColumnDescription = "医生姓名", Length = 64)]
    public virtual string? DoctorName { get; set; }
    
    /// <summary>
    /// 时间段id
    /// </summary>
    [SugarColumn(ColumnName = "time_period_id", ColumnDescription = "时间段id")]
    public virtual long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    [SugarColumn(ColumnName = "time_period_code", ColumnDescription = "时间段编码", Length = 64)]
    public virtual string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    [SugarColumn(ColumnName = "time_period_name", ColumnDescription = "时间段名称", Length = 64)]
    public virtual string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 号别id
    /// </summary>
    [SugarColumn(ColumnName = "reg_category_id", ColumnDescription = "号别id")]
    public virtual long? RegCategoryId { get; set; }
    
    /// <summary>
    /// 挂号类别名称
    /// </summary>
    [SugarColumn(ColumnName = "reg_category_name", ColumnDescription = "挂号类别名称", Length = 200)]
    public virtual string? RegCategoryName { get; set; }
    
    /// <summary>
    /// 限号数
    /// </summary>
    [SugarColumn(ColumnName = "reg_limit", ColumnDescription = "限号数")]
    public virtual int? RegLimit { get; set; }
    
    /// <summary>
    /// 限预约号数
    /// </summary>
    [SugarColumn(ColumnName = "app_limit", ColumnDescription = "限预约号数")]
    public virtual int? AppLimit { get; set; }
    
    /// <summary>
    /// 已挂号数
    /// </summary>
    [SugarColumn(ColumnName = "reg_number", ColumnDescription = "已挂号数")]
    public virtual int? RegNumber { get; set; }
    
    /// <summary>
    /// 已预约号数
    /// </summary>
    [SugarColumn(ColumnName = "app_number", ColumnDescription = "已预约号数")]
    public virtual int? AppNumber { get; set; }
    
    /// <summary>
    /// 门诊日期
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_date", ColumnDescription = "门诊日期")]
    public virtual DateTime? OutpatientDate { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(ColumnName = "start_time", ColumnDescription = "开始时间")]
    public virtual TimeSpan? StartTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(ColumnName = "end_time", ColumnDescription = "结束时间")]
    public virtual TimeSpan? EndTime { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室id")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "科室名称", Length = 64)]
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 星期几
    /// </summary>
    [SugarColumn(ColumnName = "week_day", ColumnDescription = "星期几", Length = 32)]
    public virtual string? WeekDay { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    [SugarColumn(ColumnName = "room_id", ColumnDescription = "诊室id")]
    public virtual long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [SugarColumn(ColumnName = "room_name", ColumnDescription = "诊室名称", Length = 64)]
    public virtual string? RoomName { get; set; }
    
    /// <summary>
    /// ip地址
    /// </summary>
    [SugarColumn(ColumnName = "ip_address", ColumnDescription = "ip地址", Length = 64)]
    public virtual string? IpAddress { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }
    
}
