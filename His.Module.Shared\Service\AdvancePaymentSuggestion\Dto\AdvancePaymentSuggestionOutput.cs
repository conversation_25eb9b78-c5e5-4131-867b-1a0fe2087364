﻿namespace His.Module.Shared;

/// <summary>
/// 预交金建议输出参数
/// </summary>
public class AdvancePaymentSuggestionOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 诊断编码
    /// </summary>
    public string? DiagnosisCode { get; set; }    
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosisName { get; set; }    
    
    /// <summary>
    /// 病种名称
    /// </summary>
    public string? DiseaseTypeName { get; set; }    
    
    /// <summary>
    /// 职工
    /// </summary>
    public decimal? Employee { get; set; }    
    
    /// <summary>
    /// 居民
    /// </summary>
    public decimal? Resident { get; set; }    
    
    /// <summary>
    /// 自费
    /// </summary>
    public decimal? SelfFunded { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }    
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }    
    
}

/// <summary>
/// 预交金建议数据导入模板实体
/// </summary>
public class ExportAdvancePaymentSuggestionOutput : ImportAdvancePaymentSuggestionInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
