﻿namespace His.Module.Shared.Service;

/// <summary>
/// 收费类别基础输入参数
/// </summary>
public class ChargeCategoryBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 提成
    /// </summary>
    public virtual decimal? Commission { get; set; }

    /// <summary>
    /// 记账属性
    /// </summary>
    [Required(ErrorMessage = "记账属性不能为空")]
    public virtual Int16? AccountAttribute { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    [Required(ErrorMessage = "类型不能为空")]
    public virtual Int16? Type { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [Dict("MedInsChargeType", AllowNullValue = true)]
    [Required(ErrorMessage = "医保类型不能为空")]
    public virtual string? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public virtual StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
}

/// <summary>
/// 收费类别分页查询输入参数
/// </summary>
public class PageChargeCategoryInput : BasePageInput
{
    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 收费类别增加输入参数
/// </summary>
public class AddChargeCategoryInput
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 提成
    /// </summary>
    public decimal? Commission { get; set; }

    /// <summary>
    /// 记账属性
    /// </summary>
    [Required(ErrorMessage = "记账属性不能为空")]
    public Int16? AccountAttribute { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    [Required(ErrorMessage = "类型不能为空")]
    public Int16? Type { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [Dict("MedInsChargeType", AllowNullValue = true)]
    [Required(ErrorMessage = "医保类型不能为空")]
    [MaxLength(16, ErrorMessage = "医保类型字符长度不能超过16")]
    public string? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 收费类别删除输入参数
/// </summary>
public class DeleteChargeCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 收费类别更新输入参数
/// </summary>
public class UpdateChargeCategoryInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 提成
    /// </summary>
    public decimal? Commission { get; set; }

    /// <summary>
    /// 记账属性
    /// </summary>
    [Required(ErrorMessage = "记账属性不能为空")]
    public Int16? AccountAttribute { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    [Required(ErrorMessage = "类型不能为空")]
    public Int16? Type { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [Dict("MedInsChargeType", AllowNullValue = true)]
    [Required(ErrorMessage = "医保类型不能为空")]
    [MaxLength(16, ErrorMessage = "医保类型字符长度不能超过16")]
    public string? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 收费类别主键查询输入参数
/// </summary>
public class QueryByIdChargeCategoryInput : DeleteChargeCategoryInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetChargeCategoryStatusInput : BaseStatusInput
{
}

/// <summary>
/// 收费类别数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportChargeCategoryInput : BaseImportInput
{
    /// <summary>
    /// 编码
    /// </summary>
    [ImporterHeader(Name = "编码")]
    [ExporterHeader("编码", Format = "", Width = 25, IsBold = true)]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [ImporterHeader(Name = "*名称")]
    [ExporterHeader("*名称", Format = "", Width = 25, IsBold = true)]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [ImporterHeader(Name = "拼音码")]
    [ExporterHeader("拼音码", Format = "", Width = 25, IsBold = true)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [ImporterHeader(Name = "五笔码")]
    [ExporterHeader("五笔码", Format = "", Width = 25, IsBold = true)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 提成
    /// </summary>
    [ImporterHeader(Name = "提成")]
    [ExporterHeader("提成", Format = "", Width = 25, IsBold = true)]
    public decimal? Commission { get; set; }

    /// <summary>
    /// 记账属性
    /// </summary>
    [ImporterHeader(Name = "*记账属性")]
    [ExporterHeader("*记账属性", Format = "", Width = 25, IsBold = true)]
    public Int16? AccountAttribute { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    [ImporterHeader(Name = "*类型")]
    [ExporterHeader("*类型", Format = "", Width = 25, IsBold = true)]
    public Int16? Type { get; set; }

    /// <summary>
    /// 医保类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? MedInsType { get; set; }

    /// <summary>
    /// 医保类型 文本
    /// </summary>
    [Dict("MedInsChargeType")]
    [ImporterHeader(Name = "*医保类型")]
    [ExporterHeader("*医保类型", Format = "", Width = 25, IsBold = true)]
    public string MedInsTypeDictLabel { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
}