﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 处方明细表服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class PrescriptionDetailService : IDynamic<PERSON><PERSON><PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<PrescriptionDetail> _prescriptionDetailRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public PrescriptionDetailService(SqlSugarRepository<PrescriptionDetail> prescriptionDetailRep, ISqlSugarClient sqlSugarClient)
    {
        _prescriptionDetailRep = prescriptionDetailRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询处方明细表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询处方明细表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<PrescriptionDetailOutput>> Page(PagePrescriptionDetailInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _prescriptionDetailRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) 
                || u.Spec.Contains(input.Keyword) || u.Unit.Contains(input.Keyword) || u.SingleDoseUnit.Contains(input.Keyword)
                || u.MedicationRoutesName.Contains(input.Keyword) || u.FrequencyName.Contains(input.Keyword) || u.Manufacturer.Contains(input.Keyword)
                || u.StorageName.Contains(input.Keyword) || u.GroupFlag.Contains(input.Keyword) || u.GroupNo.Contains(input.Keyword) || u.DosageUnit.Contains(input.Keyword) || u.ContentUnit.Contains(input.Keyword) || u.MinPackageUnit.Contains(input.Keyword) || u.RatioAuditStaffName.Contains(input.Keyword) || u.MedicineCode.Contains(input.Keyword) || u.UsageCode.Contains(input.Keyword) || u.UsageName.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Spec), u => u.Spec.Contains(input.Spec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SingleDoseUnit), u => u.SingleDoseUnit.Contains(input.SingleDoseUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicationRoutesName), u => u.MedicationRoutesName.Contains(input.MedicationRoutesName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.FrequencyName), u => u.FrequencyName.Contains(input.FrequencyName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Manufacturer), u => u.Manufacturer.Contains(input.Manufacturer.Trim()))
             
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupFlag), u => u.GroupFlag.Contains(input.GroupFlag.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.GroupNo), u => u.GroupNo.Contains(input.GroupNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DosageUnit), u => u.DosageUnit.Contains(input.DosageUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContentUnit), u => u.ContentUnit.Contains(input.ContentUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MinPackageUnit), u => u.MinPackageUnit.Contains(input.MinPackageUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RatioAuditStaffName), u => u.RatioAuditStaffName.Contains(input.RatioAuditStaffName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicineCode), u => u.MedicineCode.Contains(input.MedicineCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.UsageCode), u => u.UsageCode.Contains(input.UsageCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.UsageName), u => u.UsageName.Contains(input.UsageName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.PrescriptionId != null, u => u.PrescriptionId == input.PrescriptionId)
            .WhereIF(input.DrugId != null, u => u.DrugId == input.DrugId)
            .WhereIF(input.MedicationRoutesId != null, u => u.MedicationRoutesId == input.MedicationRoutesId)
            .WhereIF(input.FrequencyId != null, u => u.FrequencyId == input.FrequencyId)
            .WhereIF(input.StorageId != null, u => u.StorageId== input.StorageId)
            .WhereIF(input.ChargeCategoryId != null, u => u.ChargeCategoryId == input.ChargeCategoryId)
            .WhereIF(input.OutpatientPackageQuantity != null, u => u.OutpatientPackageQuantity == input.OutpatientPackageQuantity)
            .WhereIF(input.ChargeStaffId != null, u => u.ChargeStaffId == input.ChargeStaffId)
            .WhereIF(input.ChargeTimeRange?.Length == 2, u => u.ChargeTime >= input.ChargeTimeRange[0] && u.ChargeTime <= input.ChargeTimeRange[1])
            .WhereIF(input.RefundStaffId != null, u => u.RefundStaffId == input.RefundStaffId)
            .WhereIF(input.RefundTimeRange?.Length == 2, u => u.RefundTime >= input.RefundTimeRange[0] && u.RefundTime <= input.RefundTimeRange[1])
            .WhereIF(input.IsRatioAudit != null, u => u.IsRatioAudit == input.IsRatioAudit)
            .WhereIF(input.RatioAuditTimeRange?.Length == 2, u => u.RatioAuditTime >= input.RatioAuditTimeRange[0] && u.RatioAuditTime <= input.RatioAuditTimeRange[1])
            .WhereIF(input.RatioAuditStaffId != null, u => u.RatioAuditStaffId == input.RatioAuditStaffId)
            .WhereIF(input.UsageId != null, u => u.UsageId == input.UsageId)
            .WhereIF(input.IsSkinTest != null, u => u.IsSkinTest == input.IsSkinTest)
            .Select<PrescriptionDetailOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取处方明细表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取处方明细表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<PrescriptionDetail> Detail([FromQuery] QueryByIdPrescriptionDetailInput input)
    {
        return await _prescriptionDetailRep.GetFirstAsync(u => u.Id == input.Id);
    }
 
}