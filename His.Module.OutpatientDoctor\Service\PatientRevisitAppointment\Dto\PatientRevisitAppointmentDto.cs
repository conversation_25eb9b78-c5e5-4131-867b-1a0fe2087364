﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 复诊预约记录输出参数
/// </summary>
public class PatientRevisitAppointmentDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 患者Id
    /// </summary>
    public long PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 挂号记录Id
    /// </summary>
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 预约医生Id
    /// </summary>
    public long? AppointmentDoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public string? AppointmentDoctorName { get; set; }
    
    /// <summary>
    /// 预约科室Id
    /// </summary>
    public long? AppointmentDeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public string? AppointmentDeptName { get; set; }
    
    /// <summary>
    /// 复诊时间
    /// </summary>
    public DateTime? RevisitTime { get; set; }
    
    /// <summary>
    /// 复诊原因
    /// </summary>
    public string? RevisitReason { get; set; }
    
    /// <summary>
    /// 复诊科室Id
    /// </summary>
    public long? RevisitDeptId { get; set; }
    
    /// <summary>
    /// 复诊科室名称
    /// </summary>
    public string? RevisitDeptName { get; set; }
    
    /// <summary>
    /// 状态，默认1（有效）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注信息
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 创建机构Id
    /// </summary>
    public long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    public string? CreateOrgName { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
