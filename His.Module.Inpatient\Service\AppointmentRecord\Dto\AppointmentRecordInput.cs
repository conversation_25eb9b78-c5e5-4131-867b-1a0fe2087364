﻿namespace His.Module.Inpatient;

/// <summary>
/// 住院预约管理基础输入参数
/// </summary>
public class AppointmentRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    [Required(ErrorMessage = "性别不能为空")]
    public virtual int? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    [Required(ErrorMessage = "年龄不能为空")]
    public virtual int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    public virtual string? AgeUnit { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public virtual DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [Required(ErrorMessage = "证件类型不能为空")]
    public virtual int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public virtual string? IdCardNo { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    public virtual string? Nation { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    public virtual string? Phone { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public virtual string? ContactName { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    public virtual string? ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>
    public virtual string? ContactAddress { get; set; }

    /// <summary>
    /// 联系人电话号码
    /// </summary>
    public virtual string? ContactPhone { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>
    public virtual string? Nationality { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public virtual string? Occupation { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>
    public virtual string? Marriage { get; set; }

    /// <summary>
    /// 籍贯省
    /// </summary>
    public virtual int? NativePlaceProvince { get; set; }

    /// <summary>
    /// 籍贯市
    /// </summary>
    public virtual int? NativePlaceCity { get; set; }

    /// <summary>
    /// 籍贯县
    /// </summary>
    public virtual int? NativePlaceCounty { get; set; }

    /// <summary>
    /// 出生地省
    /// </summary>
    public virtual int? BirthplaceProvince { get; set; }

    /// <summary>
    /// 出生地市
    /// </summary>
    public virtual int? BirthplaceCity { get; set; }

    /// <summary>
    /// 出生地县
    /// </summary>
    public virtual int? BirthplaceCounty { get; set; }

    /// <summary>
    /// 现居住地省
    /// </summary>
    [Required(ErrorMessage = "现居住地省不能为空")]
    public virtual int? ResidenceProvince { get; set; }

    /// <summary>
    /// 现居住地市
    /// </summary>
    [Required(ErrorMessage = "现居住地市不能为空")]
    public virtual int? ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>
    [Required(ErrorMessage = "现居住地县不能为空")]
    public virtual int? ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>
    public virtual string? ResidenceAddress { get; set; }

    /// <summary>
    /// 工作地址省
    /// </summary>
    public virtual int? WorkProvince { get; set; }

    /// <summary>
    /// 工作地址市
    /// </summary>
    public virtual int? WorkCity { get; set; }

    /// <summary>
    /// 工作地址县
    /// </summary>
    public virtual int? WorkCounty { get; set; }
    
    /// <summary>
    /// 详细工作地址
    /// </summary>
    public virtual string? WorkAddress { get; set; }

    /// <summary>
    /// 工作单位
    /// </summary>
    public virtual string? WorkPlace { get; set; }

    /// <summary>
    /// 单位电话
    /// </summary>
    public virtual string? WorkPlacePhone { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string? MedicalCardNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public virtual string? VisitNo { get; set; }

    /// <summary>
    /// 预约时间
    /// </summary>
    public virtual DateTime? AppointmentTime { get; set; }
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public virtual string? DeptName { get; set; }

    /// <summary>
    /// 预约病区ID
    /// </summary>
    public virtual long? WardId { get; set; }

    /// <summary>
    /// 预约病区名称
    /// </summary>
    public virtual string? WardName { get; set; }

    /// <summary>
    /// 预约诊疗组ID
    /// </summary>
    public virtual long? TeamId { get; set; }
    
    /// <summary>
    /// 预约诊疗组名称
    /// </summary>
    public virtual string? TeamName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    public virtual long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public virtual string? DoctorName { get; set; }

    /// <summary>
    /// 接诊医生id
    /// </summary>
    public virtual long? ReceivingDoctorId { get; set; }

    /// <summary>
    /// 接诊医生名称
    /// </summary>
    public virtual string? ReceivingDoctorName { get; set; }

    /// <summary>
    /// 入院途径InpatientWayType
    /// </summary>
    public virtual string? InpatientWay { get; set; }

    /// <summary>
    /// 结算类别
    /// </summary>
    public virtual string? SettlementCategory { get; set; }

    /// <summary>
    /// 入院诊断编号
    /// </summary>
    public virtual string? AdmissionDiagnosisCode { get; set; }

    /// <summary>
    /// 预交金
    /// </summary>
    public virtual decimal? AdvancePayment { get; set; }
    
    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    public virtual string? PregnancyRiskLevel { get; set; }
    
    /// <summary>
    /// 高危因素
    /// </summary>
    public virtual string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public virtual string? AdmissionDiagnosisName { get; set; }
    
}

/// <summary>
/// 住院预约管理分页查询输入参数
/// </summary>
public class PageAppointmentRecordInput : BasePageInput
{
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    public int? Sex { get; set; }
    
    /// <summary>
    /// 年龄
    /// </summary>
    public int? Age { get; set; }
    
    /// <summary>
    /// 年龄单位
    /// </summary>
    public string? AgeUnit { get; set; }
    
    /// <summary>
    /// 出生日期范围
    /// </summary>
    public DateTime?[] BirthdayRange { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 民族
    /// </summary>
    public string? Nation { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    public string? ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>
    public string? ContactAddress { get; set; }

    /// <summary>
    /// 联系人电话号码
    /// </summary>
    public string? ContactPhone { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>
    public string? Nationality { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    public string? Occupation { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>
    public string? Marriage { get; set; }

    /// <summary>
    /// 籍贯省
    /// </summary>
    public int? NativePlaceProvince { get; set; }

    /// <summary>
    /// 籍贯市
    /// </summary>
    public int? NativePlaceCity { get; set; }

    /// <summary>
    /// 籍贯县
    /// </summary>
    public int? NativePlaceCounty { get; set; }
    
    /// <summary>
    /// 出生地省
    /// </summary>
    public int? BirthplaceProvince { get; set; }
    
    /// <summary>
    /// 出生地市
    /// </summary>
    public int? BirthplaceCity { get; set; }

    /// <summary>
    /// 出生地县
    /// </summary>
    public int? BirthplaceCounty { get; set; }

    /// <summary>
    /// 现居住地省
    /// </summary>
    public int? ResidenceProvince { get; set; }

    /// <summary>
    /// 现居住地市
    /// </summary>
    public int? ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>
    public int? ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>
    public string? ResidenceAddress { get; set; }

    /// <summary>
    /// 工作地址省
    /// </summary>
    public int? WorkProvince { get; set; }

    /// <summary>
    /// 工作地址市
    /// </summary>
    public int? WorkCity { get; set; }

    /// <summary>
    /// 工作地址县
    /// </summary>
    public int? WorkCounty { get; set; }

    /// <summary>
    /// 详细工作地址
    /// </summary>
    public string? WorkAddress { get; set; }

    /// <summary>
    /// 工作单位
    /// </summary>
    public string? WorkPlace { get; set; }

    /// <summary>
    /// 单位电话
    /// </summary>
    public string? WorkPlacePhone { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? MedicalCardNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }

    /// <summary>
    /// 预约时间范围
    /// </summary>
    public DateTime?[] AppointmentTimeRange { get; set; }

    /// <summary>
    /// 预约科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public string? DeptName { get; set; }

    /// <summary>
    /// 预约病区ID
    /// </summary>
    public long? WardId { get; set; }

    /// <summary>
    /// 预约病区名称
    /// </summary>
    public string? WardName { get; set; }

    /// <summary>
    /// 预约诊疗组ID
    /// </summary>
    public long? TeamId { get; set; }

    /// <summary>
    /// 预约诊疗组名称
    /// </summary>
    public string? TeamName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public string? DoctorName { get; set; }

    /// <summary>
    /// 接诊医生id
    /// </summary>
    public long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>
    public string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径InpatientWayType
    /// </summary>
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>
    public string? SettlementCategory { get; set; }
    
    /// <summary>
    /// 入院诊断编号
    /// </summary>
    public string? AdmissionDiagnosisCode { get; set; }

    /// <summary>
    /// 预交金
    /// </summary>
    public decimal? AdvancePayment { get; set; }

    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    public string? PregnancyRiskLevel { get; set; }

    /// <summary>
    /// 高危因素
    /// </summary>
    public string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 入院诊断名称
    /// </summary>
    public string? AdmissionDiagnosisName { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 住院预约管理增加输入参数
/// </summary>
public class AddAppointmentRecordInput
{
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "患者姓名字符长度不能超过100")]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    [Required(ErrorMessage = "性别不能为空")]
    public int? Sex { get; set; }
    
    /// <summary>
    /// 年龄
    /// </summary>
    [Required(ErrorMessage = "年龄不能为空")]
    public int? Age { get; set; }
    
    /// <summary>
    /// 年龄单位
    /// </summary>
    [MaxLength(32, ErrorMessage = "年龄单位字符长度不能超过32")]
    public string? AgeUnit { get; set; }
    
    /// <summary>
    /// 出生日期
    /// </summary>
    public DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [Required(ErrorMessage = "证件类型不能为空")]
    public int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [MaxLength(32, ErrorMessage = "身份证号字符长度不能超过32")]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 民族
    /// </summary>
    [MaxLength(16, ErrorMessage = "民族字符长度不能超过16")]
    public string? Nation { get; set; }
    
    /// <summary>
    /// 电话号码
    /// </summary>
    [MaxLength(16, ErrorMessage = "电话号码字符长度不能超过16")]
    public string? Phone { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [MaxLength(32, ErrorMessage = "联系人姓名字符长度不能超过32")]
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    [MaxLength(16, ErrorMessage = "联系人关系字符长度不能超过16")]
    public string? ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>
    [MaxLength(64, ErrorMessage = "联系人地址字符长度不能超过64")]
    public string? ContactAddress { get; set; }
    
    /// <summary>
    /// 联系人电话号码
    /// </summary>
    [MaxLength(16, ErrorMessage = "联系人电话号码字符长度不能超过16")]
    public string? ContactPhone { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>
    [MaxLength(16, ErrorMessage = "国籍字符长度不能超过16")]
    public string? Nationality { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    [MaxLength(16, ErrorMessage = "职业字符长度不能超过16")]
    public string? Occupation { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>
    [MaxLength(16, ErrorMessage = "婚姻字符长度不能超过16")]
    public string? Marriage { get; set; }

    /// <summary>
    /// 籍贯省
    /// </summary>
    public int? NativePlaceProvince { get; set; }

    /// <summary>
    /// 籍贯市
    /// </summary>
    public int? NativePlaceCity { get; set; }

    /// <summary>
    /// 籍贯县
    /// </summary>
    public int? NativePlaceCounty { get; set; }

    /// <summary>
    /// 出生地省
    /// </summary>
    public int? BirthplaceProvince { get; set; }

    /// <summary>
    /// 出生地市
    /// </summary>
    public int? BirthplaceCity { get; set; }

    /// <summary>
    /// 出生地县
    /// </summary>
    public int? BirthplaceCounty { get; set; }

    /// <summary>
    /// 现居住地省
    /// </summary>
    [Required(ErrorMessage = "现居住地省不能为空")]
    public int? ResidenceProvince { get; set; }

    /// <summary>
    /// 现居住地市
    /// </summary>
    [Required(ErrorMessage = "现居住地市不能为空")]
    public int? ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>
    [Required(ErrorMessage = "现居住地县不能为空")]
    public int? ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>
    [MaxLength(128, ErrorMessage = "详细现居住地字符长度不能超过128")]
    public string? ResidenceAddress { get; set; }

    /// <summary>
    /// 工作地址省
    /// </summary>
    public int? WorkProvince { get; set; }

    /// <summary>
    /// 工作地址市
    /// </summary>
    public int? WorkCity { get; set; }

    /// <summary>
    /// 工作地址县
    /// </summary>
    public int? WorkCounty { get; set; }

    /// <summary>
    /// 详细工作地址
    /// </summary>
    [MaxLength(128, ErrorMessage = "详细工作地址字符长度不能超过128")]
    public string? WorkAddress { get; set; }

    /// <summary>
    /// 工作单位
    /// </summary>
    [MaxLength(128, ErrorMessage = "工作单位字符长度不能超过128")]
    public string? WorkPlace { get; set; }

    /// <summary>
    /// 单位电话
    /// </summary>
    [MaxLength(16, ErrorMessage = "单位电话字符长度不能超过16")]
    public string? WorkPlacePhone { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [MaxLength(100, ErrorMessage = "就诊卡号字符长度不能超过100")]
    public string? MedicalCardNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [MaxLength(100, ErrorMessage = "门诊号字符长度不能超过100")]
    public string? OutpatientNo { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [MaxLength(100, ErrorMessage = "就诊号字符长度不能超过100")]
    public string? VisitNo { get; set; }

    /// <summary>
    /// 预约时间
    /// </summary>
    public DateTime? AppointmentTime { get; set; }

    /// <summary>
    /// 预约科室ID
    /// </summary>
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约科室名称字符长度不能超过100")]
    public string? DeptName { get; set; }

    /// <summary>
    /// 预约病区ID
    /// </summary>
    public long? WardId { get; set; }

    /// <summary>
    /// 预约病区名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约病区名称字符长度不能超过100")]
    public string? WardName { get; set; }

    /// <summary>
    /// 预约诊疗组ID
    /// </summary>
    public long? TeamId { get; set; }

    /// <summary>
    /// 预约诊疗组名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约诊疗组名称字符长度不能超过100")]
    public string? TeamName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约医生姓名字符长度不能超过100")]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>
    public long? ReceivingDoctorId { get; set; }
    
    /// <summary>
    /// 接诊医生名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "接诊医生名称字符长度不能超过100")]
    public string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径InpatientWayType
    /// </summary>
    [MaxLength(32, ErrorMessage = "入院途径InpatientWayType字符长度不能超过32")]
    public string? InpatientWay { get; set; }

    /// <summary>
    /// 结算类别
    /// </summary>
    [MaxLength(100, ErrorMessage = "结算类别字符长度不能超过100")]
    public string? SettlementCategory { get; set; }

    /// <summary>
    /// 入院诊断编号
    /// </summary>
    [MaxLength(100, ErrorMessage = "入院诊断编号字符长度不能超过100")]
    public string? AdmissionDiagnosisCode { get; set; }

    /// <summary>
    /// 预交金
    /// </summary>
    public decimal? AdvancePayment { get; set; }

    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    [MaxLength(32, ErrorMessage = "妊娠风险评估字符长度不能超过32")]
    public string? PregnancyRiskLevel { get; set; }

    /// <summary>
    /// 高危因素
    /// </summary>
    [MaxLength(256, ErrorMessage = "高危因素字符长度不能超过256")]
    public string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }

    /// <summary>
    /// 入院诊断名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "入院诊断名称字符长度不能超过100")]
    public string? AdmissionDiagnosisName { get; set; }
    
}

/// <summary>
/// 住院预约管理删除输入参数
/// </summary>
public class DeleteAppointmentRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 住院预约管理更新输入参数
/// </summary>
public class UpdateAppointmentRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "患者姓名字符长度不能超过100")]
    public string? PatientName { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [Required(ErrorMessage = "性别不能为空")]
    public int? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    [Required(ErrorMessage = "年龄不能为空")]
    public int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [MaxLength(32, ErrorMessage = "年龄单位字符长度不能超过32")]
    public string? AgeUnit { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    public DateTime? Birthday { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    [Required(ErrorMessage = "证件类型不能为空")]
    public int? CardType { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    [MaxLength(32, ErrorMessage = "身份证号字符长度不能超过32")]
    public string? IdCardNo { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    [MaxLength(16, ErrorMessage = "民族字符长度不能超过16")]
    public string? Nation { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    [MaxLength(16, ErrorMessage = "电话号码字符长度不能超过16")]
    public string? Phone { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [MaxLength(32, ErrorMessage = "联系人姓名字符长度不能超过32")]
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    [MaxLength(16, ErrorMessage = "联系人关系字符长度不能超过16")]
    public string? ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>
    [MaxLength(64, ErrorMessage = "联系人地址字符长度不能超过64")]
    public string? ContactAddress { get; set; }

    /// <summary>
    /// 联系人电话号码
    /// </summary>
    [MaxLength(16, ErrorMessage = "联系人电话号码字符长度不能超过16")]
    public string? ContactPhone { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>
    [MaxLength(16, ErrorMessage = "国籍字符长度不能超过16")]
    public string? Nationality { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    [MaxLength(16, ErrorMessage = "职业字符长度不能超过16")]
    public string? Occupation { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>
    [MaxLength(16, ErrorMessage = "婚姻字符长度不能超过16")]
    public string? Marriage { get; set; }

    /// <summary>
    /// 籍贯省
    /// </summary>
    public int? NativePlaceProvince { get; set; }

    /// <summary>
    /// 籍贯市
    /// </summary>
    public int? NativePlaceCity { get; set; }

    /// <summary>
    /// 籍贯县
    /// </summary>
    public int? NativePlaceCounty { get; set; }

    /// <summary>
    /// 出生地省
    /// </summary>
    public int? BirthplaceProvince { get; set; }

    /// <summary>
    /// 出生地市
    /// </summary>
    public int? BirthplaceCity { get; set; }

    /// <summary>
    /// 出生地县
    /// </summary>
    public int? BirthplaceCounty { get; set; }

    /// <summary>
    /// 现居住地省
    /// </summary>
    [Required(ErrorMessage = "现居住地省不能为空")]
    public int? ResidenceProvince { get; set; }

    /// <summary>
    /// 现居住地市
    /// </summary>
    [Required(ErrorMessage = "现居住地市不能为空")]
    public int? ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>
    [Required(ErrorMessage = "现居住地县不能为空")]
    public int? ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>
    [MaxLength(128, ErrorMessage = "详细现居住地字符长度不能超过128")]
    public string? ResidenceAddress { get; set; }

    /// <summary>
    /// 工作地址省
    /// </summary>
    public int? WorkProvince { get; set; }

    /// <summary>
    /// 工作地址市
    /// </summary>
    public int? WorkCity { get; set; }

    /// <summary>
    /// 工作地址县
    /// </summary>
    public int? WorkCounty { get; set; }

    /// <summary>
    /// 详细工作地址
    /// </summary>
    [MaxLength(128, ErrorMessage = "详细工作地址字符长度不能超过128")]
    public string? WorkAddress { get; set; }

    /// <summary>
    /// 工作单位
    /// </summary>
    [MaxLength(128, ErrorMessage = "工作单位字符长度不能超过128")]
    public string? WorkPlace { get; set; }

    /// <summary>
    /// 单位电话
    /// </summary>
    [MaxLength(16, ErrorMessage = "单位电话字符长度不能超过16")]
    public string? WorkPlacePhone { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [MaxLength(100, ErrorMessage = "就诊卡号字符长度不能超过100")]
    public string? MedicalCardNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [MaxLength(100, ErrorMessage = "门诊号字符长度不能超过100")]
    public string? OutpatientNo { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [MaxLength(100, ErrorMessage = "就诊号字符长度不能超过100")]
    public string? VisitNo { get; set; }

    /// <summary>
    /// 预约时间
    /// </summary>
    public DateTime? AppointmentTime { get; set; }

    /// <summary>
    /// 预约科室ID
    /// </summary>
    public long? DeptId { get; set; }

    /// <summary>
    /// 预约科室名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约科室名称字符长度不能超过100")]
    public string? DeptName { get; set; }

    /// <summary>
    /// 预约病区ID
    /// </summary>
    public long? WardId { get; set; }

    /// <summary>
    /// 预约病区名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约病区名称字符长度不能超过100")]
    public string? WardName { get; set; }

    /// <summary>
    /// 预约诊疗组ID
    /// </summary>
    public long? TeamId { get; set; }

    /// <summary>
    /// 预约诊疗组名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约诊疗组名称字符长度不能超过100")]
    public string? TeamName { get; set; }

    /// <summary>
    /// 预约医生ID
    /// </summary>
    public long? DoctorId { get; set; }

    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [MaxLength(100, ErrorMessage = "预约医生姓名字符长度不能超过100")]
    public string? DoctorName { get; set; }

    /// <summary>
    /// 接诊医生id
    /// </summary>
    public long? ReceivingDoctorId { get; set; }

    /// <summary>
    /// 接诊医生名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "接诊医生名称字符长度不能超过100")]
    public string? ReceivingDoctorName { get; set; }

    /// <summary>
    /// 入院途径InpatientWayType
    /// </summary>
    [MaxLength(32, ErrorMessage = "入院途径InpatientWayType字符长度不能超过32")]
    public string? InpatientWay { get; set; }

    /// <summary>
    /// 结算类别
    /// </summary>
    [MaxLength(100, ErrorMessage = "结算类别字符长度不能超过100")]
    public string? SettlementCategory { get; set; }

    /// <summary>
    /// 入院诊断编号
    /// </summary>
    [MaxLength(100, ErrorMessage = "入院诊断编号字符长度不能超过100")]
    public string? AdmissionDiagnosisCode { get; set; }

    /// <summary>
    /// 预交金
    /// </summary>
    public decimal? AdvancePayment { get; set; }

    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    [MaxLength(32, ErrorMessage = "妊娠风险评估字符长度不能超过32")]
    public string? PregnancyRiskLevel { get; set; }

    /// <summary>
    /// 高危因素
    /// </summary>
    [MaxLength(256, ErrorMessage = "高危因素字符长度不能超过256")]
    public string? HighRiskFactors { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }

    /// <summary>
    /// 入院诊断名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "入院诊断名称字符长度不能超过100")]
    public string? AdmissionDiagnosisName { get; set; }

}

/// <summary>
/// 住院预约管理主键查询输入参数
/// </summary>
public class QueryByIdAppointmentRecordInput : DeleteAppointmentRecordInput
{
}

public class ConfirmAppointmentRecordInput : DeleteAppointmentRecordInput
{

}


/// <summary>
/// 住院预约管理数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportAppointmentRecordInput : BaseImportInput
{
    /// <summary>
    /// 患者ID
    /// </summary>
    [ImporterHeader(Name = "患者ID")]
    [ExporterHeader("患者ID", Format = "", Width = 25, IsBold = true)]
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者姓名
    /// </summary>
    [ImporterHeader(Name = "患者姓名")]
    [ExporterHeader("患者姓名", Format = "", Width = 25, IsBold = true)]
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 性别
    /// </summary>
    [ImporterHeader(Name = "*性别")]
    [ExporterHeader("*性别", Format = "", Width = 25, IsBold = true)]
    public int? Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    [ImporterHeader(Name = "*年龄")]
    [ExporterHeader("*年龄", Format = "", Width = 25, IsBold = true)]
    public int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [ImporterHeader(Name = "年龄单位")]
    [ExporterHeader("年龄单位", Format = "", Width = 25, IsBold = true)]
    public string? AgeUnit { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    [ImporterHeader(Name = "出生日期")]
    [ExporterHeader("出生日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? Birthday { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    [ImporterHeader(Name = "*证件类型")]
    [ExporterHeader("*证件类型", Format = "", Width = 25, IsBold = true)]
    public int? CardType { get; set; }
    
    /// <summary>
    /// 身份证号
    /// </summary>
    [ImporterHeader(Name = "身份证号")]
    [ExporterHeader("身份证号", Format = "", Width = 25, IsBold = true)]
    public string? IdCardNo { get; set; }
    
    /// <summary>
    /// 民族
    /// </summary>
    [ImporterHeader(Name = "民族")]
    [ExporterHeader("民族", Format = "", Width = 25, IsBold = true)]
    public string? Nation { get; set; }

    /// <summary>
    /// 电话号码
    /// </summary>
    [ImporterHeader(Name = "电话号码")]
    [ExporterHeader("电话号码", Format = "", Width = 25, IsBold = true)]
    public string? Phone { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [ImporterHeader(Name = "联系人姓名")]
    [ExporterHeader("联系人姓名", Format = "", Width = 25, IsBold = true)]
    public string? ContactName { get; set; }

    /// <summary>
    /// 联系人关系
    /// </summary>
    [ImporterHeader(Name = "联系人关系")]
    [ExporterHeader("联系人关系", Format = "", Width = 25, IsBold = true)]
    public string? ContactRelationship { get; set; }

    /// <summary>
    /// 联系人地址
    /// </summary>
    [ImporterHeader(Name = "联系人地址")]
    [ExporterHeader("联系人地址", Format = "", Width = 25, IsBold = true)]
    public string? ContactAddress { get; set; }

    /// <summary>
    /// 联系人电话号码
    /// </summary>
    [ImporterHeader(Name = "联系人电话号码")]
    [ExporterHeader("联系人电话号码", Format = "", Width = 25, IsBold = true)]
    public string? ContactPhone { get; set; }

    /// <summary>
    /// 国籍
    /// </summary>
    [ImporterHeader(Name = "国籍")]
    [ExporterHeader("国籍", Format = "", Width = 25, IsBold = true)]
    public string? Nationality { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    [ImporterHeader(Name = "职业")]
    [ExporterHeader("职业", Format = "", Width = 25, IsBold = true)]
    public string? Occupation { get; set; }

    /// <summary>
    /// 婚姻
    /// </summary>
    [ImporterHeader(Name = "婚姻")]
    [ExporterHeader("婚姻", Format = "", Width = 25, IsBold = true)]
    public string? Marriage { get; set; }

    /// <summary>
    /// 籍贯省
    /// </summary>
    [ImporterHeader(Name = "籍贯省")]
    [ExporterHeader("籍贯省", Format = "", Width = 25, IsBold = true)]
    public int? NativePlaceProvince { get; set; }

    /// <summary>
    /// 籍贯市
    /// </summary>
    [ImporterHeader(Name = "籍贯市")]
    [ExporterHeader("籍贯市", Format = "", Width = 25, IsBold = true)]
    public int? NativePlaceCity { get; set; }

    /// <summary>
    /// 籍贯县
    /// </summary>
    [ImporterHeader(Name = "籍贯县")]
    [ExporterHeader("籍贯县", Format = "", Width = 25, IsBold = true)]
    public int? NativePlaceCounty { get; set; }

    /// <summary>
    /// 出生地省
    /// </summary>
    [ImporterHeader(Name = "出生地省")]
    [ExporterHeader("出生地省", Format = "", Width = 25, IsBold = true)]
    public int? BirthplaceProvince { get; set; }

    /// <summary>
    /// 出生地市
    /// </summary>
    [ImporterHeader(Name = "出生地市")]
    [ExporterHeader("出生地市", Format = "", Width = 25, IsBold = true)]
    public int? BirthplaceCity { get; set; }

    /// <summary>
    /// 出生地县
    /// </summary>
    [ImporterHeader(Name = "出生地县")]
    [ExporterHeader("出生地县", Format = "", Width = 25, IsBold = true)]
    public int? BirthplaceCounty { get; set; }

    /// <summary>
    /// 现居住地省
    /// </summary>
    [ImporterHeader(Name = "*现居住地省")]
    [ExporterHeader("*现居住地省", Format = "", Width = 25, IsBold = true)]
    public int? ResidenceProvince { get; set; }

    /// <summary>
    /// 现居住地市
    /// </summary>
    [ImporterHeader(Name = "*现居住地市")]
    [ExporterHeader("*现居住地市", Format = "", Width = 25, IsBold = true)]
    public int? ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>
    [ImporterHeader(Name = "*现居住地县")]
    [ExporterHeader("*现居住地县", Format = "", Width = 25, IsBold = true)]
    public int? ResidenceCounty { get; set; }

    /// <summary>
    /// 详细现居住地
    /// </summary>
    [ImporterHeader(Name = "详细现居住地")]
    [ExporterHeader("详细现居住地", Format = "", Width = 25, IsBold = true)]
    public string? ResidenceAddress { get; set; }

    /// <summary>
    /// 工作地址省
    /// </summary>
    [ImporterHeader(Name = "工作地址省")]
    [ExporterHeader("工作地址省", Format = "", Width = 25, IsBold = true)]
    public int? WorkProvince { get; set; }

    /// <summary>
    /// 工作地址市
    /// </summary>
    [ImporterHeader(Name = "工作地址市")]
    [ExporterHeader("工作地址市", Format = "", Width = 25, IsBold = true)]
    public int? WorkCity { get; set; }

    /// <summary>
    /// 工作地址县
    /// </summary>
    [ImporterHeader(Name = "工作地址县")]
    [ExporterHeader("工作地址县", Format = "", Width = 25, IsBold = true)]
    public int? WorkCounty { get; set; }

    /// <summary>
    /// 详细工作地址
    /// </summary>
    [ImporterHeader(Name = "详细工作地址")]
    [ExporterHeader("详细工作地址", Format = "", Width = 25, IsBold = true)]
    public string? WorkAddress { get; set; }

    /// <summary>
    /// 工作单位
    /// </summary>
    [ImporterHeader(Name = "工作单位")]
    [ExporterHeader("工作单位", Format = "", Width = 25, IsBold = true)]
    public string? WorkPlace { get; set; }

    /// <summary>
    /// 单位电话
    /// </summary>
    [ImporterHeader(Name = "单位电话")]
    [ExporterHeader("单位电话", Format = "", Width = 25, IsBold = true)]
    public string? WorkPlacePhone { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [ImporterHeader(Name = "就诊卡号")]
    [ExporterHeader("就诊卡号", Format = "", Width = 25, IsBold = true)]
    public string? MedicalCardNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [ImporterHeader(Name = "门诊号")]
    [ExporterHeader("门诊号", Format = "", Width = 25, IsBold = true)]
    public string? OutpatientNo { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [ImporterHeader(Name = "就诊号")]
    [ExporterHeader("就诊号", Format = "", Width = 25, IsBold = true)]
    public string? VisitNo { get; set; }

    /// <summary>
    /// 预约时间
    /// </summary>
    [ImporterHeader(Name = "预约时间")]
    [ExporterHeader("预约时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? AppointmentTime { get; set; }
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    [ImporterHeader(Name = "预约科室ID")]
    [ExporterHeader("预约科室ID", Format = "", Width = 25, IsBold = true)]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    [ImporterHeader(Name = "预约科室名称")]
    [ExporterHeader("预约科室名称", Format = "", Width = 25, IsBold = true)]
    public string? DeptName { get; set; }

    /// <summary>
    /// 预约病区ID
    /// </summary>
    [ImporterHeader(Name = "预约病区ID")]
    [ExporterHeader("预约病区ID", Format = "", Width = 25, IsBold = true)]
    public long? WardId { get; set; }

    /// <summary>
    /// 预约病区名称
    /// </summary>
    [ImporterHeader(Name = "预约病区名称")]
    [ExporterHeader("预约病区名称", Format = "", Width = 25, IsBold = true)]
    public string? WardName { get; set; }

    /// <summary>
    /// 预约诊疗组ID
    /// </summary>
    [ImporterHeader(Name = "预约诊疗组ID")]
    [ExporterHeader("预约诊疗组ID", Format = "", Width = 25, IsBold = true)]
    public long? TeamId { get; set; }

    /// <summary>
    /// 预约诊疗组名称
    /// </summary>
    [ImporterHeader(Name = "预约诊疗组名称")]
    [ExporterHeader("预约诊疗组名称", Format = "", Width = 25, IsBold = true)]
    public string? TeamName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    [ImporterHeader(Name = "预约医生ID")]
    [ExporterHeader("预约医生ID", Format = "", Width = 25, IsBold = true)]
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    [ImporterHeader(Name = "预约医生姓名")]
    [ExporterHeader("预约医生姓名", Format = "", Width = 25, IsBold = true)]
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 接诊医生id
    /// </summary>
    [ImporterHeader(Name = "接诊医生id")]
    [ExporterHeader("接诊医生id", Format = "", Width = 25, IsBold = true)]
    public long? ReceivingDoctorId { get; set; }

    /// <summary>
    /// 接诊医生名称
    /// </summary>
    [ImporterHeader(Name = "接诊医生名称")]
    [ExporterHeader("接诊医生名称", Format = "", Width = 25, IsBold = true)]
    public string? ReceivingDoctorName { get; set; }
    
    /// <summary>
    /// 入院途径InpatientWayType
    /// </summary>
    [ImporterHeader(Name = "入院途径InpatientWayType")]
    [ExporterHeader("入院途径InpatientWayType", Format = "", Width = 25, IsBold = true)]
    public string? InpatientWay { get; set; }
    
    /// <summary>
    /// 结算类别
    /// </summary>
    [ImporterHeader(Name = "结算类别")]
    [ExporterHeader("结算类别", Format = "", Width = 25, IsBold = true)]
    public string? SettlementCategory { get; set; }

    /// <summary>
    /// 入院诊断编号
    /// </summary>
    [ImporterHeader(Name = "入院诊断编号")]
    [ExporterHeader("入院诊断编号", Format = "", Width = 25, IsBold = true)]
    public string? AdmissionDiagnosisCode { get; set; }

    /// <summary>
    /// 预交金
    /// </summary>
    [ImporterHeader(Name = "预交金")]
    [ExporterHeader("预交金", Format = "", Width = 25, IsBold = true)]
    public decimal? AdvancePayment { get; set; }

    /// <summary>
    /// 妊娠风险评估
    /// </summary>
    [ImporterHeader(Name = "妊娠风险评估")]
    [ExporterHeader("妊娠风险评估", Format = "", Width = 25, IsBold = true)]
    public string? PregnancyRiskLevel { get; set; }

    /// <summary>
    /// 高危因素
    /// </summary>
    [ImporterHeader(Name = "高危因素")]
    [ExporterHeader("高危因素", Format = "", Width = 25, IsBold = true)]
    public string? HighRiskFactors { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }

    /// <summary>
    /// 入院诊断名称
    /// </summary>
    [ImporterHeader(Name = "入院诊断名称")]
    [ExporterHeader("入院诊断名称", Format = "", Width = 25, IsBold = true)]
    public string? AdmissionDiagnosisName { get; set; }
    
}
