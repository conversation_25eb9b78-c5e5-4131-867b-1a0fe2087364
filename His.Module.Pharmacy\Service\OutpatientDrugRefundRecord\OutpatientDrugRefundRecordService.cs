﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy;

/// <summary>
/// 门诊退药服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class OutpatientDrugRefundRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<OutpatientDrugRefundRecord> _outpatientDrugRefundRecordRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public OutpatientDrugRefundRecordService(SqlSugarRepository<OutpatientDrugRefundRecord> outpatientDrugRefundRecordRep, ISqlSugarClient sqlSugarClient)
    {
        _outpatientDrugRefundRecordRep = outpatientDrugRefundRecordRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询门诊退药 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询门诊退药")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<OutpatientDrugRefundRecordOutput>> Page(PageOutpatientDrugRefundRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _outpatientDrugRefundRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.RefundNo.Contains(input.Keyword) || u.RefundUserName.Contains(input.Keyword) || u.Reason.Contains(input.Keyword) || u.StorageName.Contains(input.Keyword) || u.PatientName.Contains(input.Keyword) || u.VisitNo.Contains(input.Keyword) || u.CardNo.Contains(input.Keyword) || u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) || u.Spec.Contains(input.Keyword) || u.Unit.Contains(input.Keyword) || u.BatchNo.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RefundNo), u => u.RefundNo.Contains(input.RefundNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RefundUserName), u => u.RefundUserName.Contains(input.RefundUserName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Reason), u => u.Reason.Contains(input.Reason.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageName), u => u.StorageName.Contains(input.StorageName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo.Contains(input.VisitNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.CardNo), u => u.CardNo.Contains(input.CardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Spec), u => u.Spec.Contains(input.Spec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BatchNo), u => u.BatchNo.Contains(input.BatchNo.Trim()))
            .WhereIF(input.SendRecordId != null, u => u.SendRecordId == input.SendRecordId)
            .WhereIF(input.RefundUserId != null, u => u.RefundUserId == input.RefundUserId)
            .WhereIF(input.RefundTimeRange?.Length == 2, u => u.RefundTime >= input.RefundTimeRange[0] && u.RefundTime <= input.RefundTimeRange[1])
            .WhereIF(input.RefundApplyId != null, u => u.RefundApplyId == input.RefundApplyId)
            .WhereIF(input.AuditTimeRange?.Length == 2, u => u.AuditTime >= input.AuditTimeRange[0] && u.AuditTime <= input.AuditTimeRange[1])
            .WhereIF(input.StorageId != null, u => u.StorageId == input.StorageId)
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.VisitId != null, u => u.VisitId == input.VisitId)
            .WhereIF(input.CardId != null, u => u.CardId == input.CardId)
            .WhereIF(input.PrescriptionId != null, u => u.PrescriptionId == input.PrescriptionId)
            .WhereIF(input.PrescriptionDetailId != null, u => u.PrescriptionDetailId == input.PrescriptionDetailId)
            .WhereIF(input.DrugId != null, u => u.DrugId == input.DrugId)
            .WhereIF(input.RefundQuantity != null, u => u.RefundQuantity == input.RefundQuantity)
            .Select<OutpatientDrugRefundRecordOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取门诊退药详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取门诊退药详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<OutpatientDrugRefundRecord> Detail([FromQuery] QueryByIdOutpatientDrugRefundRecordInput input)
    {
        return await _outpatientDrugRefundRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }

    
    /// <summary>
    /// 导出门诊退药记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出门诊退药记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageOutpatientDrugRefundRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportOutpatientDrugRefundRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "门诊退药导出记录");
    }
    
    /// <summary>
    /// 下载门诊退药数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载门诊退药数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportOutpatientDrugRefundRecordOutput>(), "门诊退药导入模板");
    }
    
    /// <summary>
    /// 导入门诊退药记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入门诊退药记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportOutpatientDrugRefundRecordInput, OutpatientDrugRefundRecord>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<OutpatientDrugRefundRecord>>();
                    
                    var storageable = _outpatientDrugRefundRecordRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.RefundNo?.Length > 100, "退药单号长度不能超过100个字符")
                        .SplitError(it => it.Item.RefundUserName?.Length > 100, "退药人名称长度不能超过100个字符")
                        .SplitError(it => it.Item.StorageName?.Length > 100, "药房名称长度不能超过100个字符")
                        .SplitError(it => it.Item.PatientName?.Length > 100, "患者名称长度不能超过100个字符")
                        .SplitError(it => it.Item.VisitNo?.Length > 100, "就诊号长度不能超过100个字符")
                        .SplitError(it => it.Item.CardNo?.Length > 100, "卡号长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugCode?.Length > 100, "药品编码长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugName?.Length > 100, "药品名称长度不能超过100个字符")
                        .SplitError(it => it.Item.Spec?.Length > 100, "药品规格长度不能超过100个字符")
                        .SplitError(it => it.Item.Unit?.Length > 100, "药品单位长度不能超过100个字符")
                        .SplitError(it => it.Item.BatchNo?.Length > 100, "批号长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
