﻿namespace His.Module.Shared.Service;

/// <summary>
/// 核算类别服务
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class CalculateCategoryService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<CalculateCategory> _calculateCategoryRep;

    public CalculateCategoryService(SqlSugarRepository<CalculateCategory> calculateCategoryRep)
    {
        _calculateCategoryRep = calculateCategoryRep;
    }

    /// <summary>
    /// 分页查询核算类别
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    [DisplayName("分页查询核算类别")]
    public async Task<SqlSugarPagedList<CalculateCategory>> Page(PageCalculateCategoryInput input)
    {
        input.Name = input.Name?.ToLower();
        return await _calculateCategoryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    public async Task SetStatus(SetStatusCalculateCategoryInput input)
    {
        var entity = await _calculateCategoryRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (!System.Enum.IsDefined(typeof(StatusEnum), input.Status))
            throw Oops.Oh(ErrorCodeEnum.D3005);
        entity.Status = input.Status;
        await _calculateCategoryRep.AsUpdateable(entity).UpdateColumns(u => new { u.Status }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 增加核算类别
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    [DisplayName("增加核算类别")]
    public async Task Add(AddCalculateCategoryInput input)
    {
        var entity = input.Adapt<CalculateCategory>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        entity.Code = await _calculateCategoryRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('calculate_category_code_seq')As varchar),3,'0')");
        await _calculateCategoryRep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除核算类别
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    [DisplayName("删除核算类别")]
    public async Task Delete(DeleteCalculateCategoryInput input)
    {
        var entity = await _calculateCategoryRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _calculateCategoryRep.FakeDeleteAsync(entity);//假删除
    }

    /// <summary>
    /// 更新核算类别
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    [DisplayName("更新核算类别")]
    public async Task Update(UpdateCalculateCategoryInput input)
    {
        var entity = input.Adapt<CalculateCategory>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _calculateCategoryRep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取核算类别
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    [DisplayName("获取核算类别")]
    public async Task<CalculateCategory> Detail([FromQuery] QueryByIdCalculateCategoryInput input)
    {
        return await _calculateCategoryRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取核算类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    [DisplayName("获取核算类别列表")]
    public async Task<List<CalculateCategory>> List()
    {
        return await _calculateCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select<CalculateCategory>()
            .ToListAsync();
    }
}