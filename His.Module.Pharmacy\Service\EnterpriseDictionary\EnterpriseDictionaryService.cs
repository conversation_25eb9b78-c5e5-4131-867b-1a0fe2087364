﻿using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;

using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 企业管理服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class EnterpriseDictionaryService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EnterpriseDictionary> _enterpriseDictionaryRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public EnterpriseDictionaryService(SqlSugarRepository<EnterpriseDictionary> enterpriseDictionaryRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _enterpriseDictionaryRep = enterpriseDictionaryRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询企业管理 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询企业管理")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<EnterpriseDictionaryOutput>> Page(PageEnterpriseDictionaryInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _enterpriseDictionaryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.EnterpriseCode.Contains(input.Keyword) || u.EnterpriseName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.EnterpriseCode), u => u.EnterpriseCode.Contains(input.EnterpriseCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.EnterpriseName), u => u.EnterpriseName.Contains(input.EnterpriseName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.EnterpriseType), u => u.EnterpriseType == input.EnterpriseType)
            .Select<EnterpriseDictionaryOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取企业管理详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取企业管理详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<EnterpriseDictionary> Detail([FromQuery] QueryByIdEnterpriseDictionaryInput input)
    {
        return await _enterpriseDictionaryRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加企业管理 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加企业管理")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddEnterpriseDictionaryInput input)
    {
        var entity = input.Adapt<EnterpriseDictionary>();
        entity.EnterpriseNamePinyin =  TextUtil.GetFirstPinyin( entity.EnterpriseName);

        if (await _enterpriseDictionaryRep.AsQueryable().AnyAsync(
                u => u.EnterpriseCode == entity.EnterpriseCode || u.EnterpriseName == entity.EnterpriseName
            ))
        {
            throw Oops.Oh("存在相同的企业编码或企业名称");
        }

        return await _enterpriseDictionaryRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新企业管理 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新企业管理")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateEnterpriseDictionaryInput input)
    {
        var entity = input.Adapt<EnterpriseDictionary>();
        
        if (await _enterpriseDictionaryRep.AsQueryable().AnyAsync(
                u => (u.EnterpriseCode == entity.EnterpriseCode || u.EnterpriseName == entity.EnterpriseName)
                     && u.Id != entity.Id

            ))
        {
            throw Oops.Oh("存在相同的企业编码或企业名称");
        }
        entity.EnterpriseNamePinyin =  TextUtil.GetFirstPinyin( entity.EnterpriseName);
        await _enterpriseDictionaryRep.AsUpdateable(entity)
            
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除企业管理 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除企业管理")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteEnterpriseDictionaryInput input)
    {
        var entity = await _enterpriseDictionaryRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _enterpriseDictionaryRep.FakeDeleteAsync(entity);   //假删除
        //await _enterpriseDictionaryRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除企业管理 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除企业管理")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteEnterpriseDictionaryInput> input)
    {
        var exp = Expressionable.Create<EnterpriseDictionary>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _enterpriseDictionaryRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _enterpriseDictionaryRep.FakeDeleteAsync(list);   //假删除
        //return await _enterpriseDictionaryRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置企业管理状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置企业管理状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetEnterpriseDictionaryStatus(SetEnterpriseDictionaryStatusInput input)
    {
        await _enterpriseDictionaryRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 导出企业管理记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出企业管理记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageEnterpriseDictionaryInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportEnterpriseDictionaryOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var enterpriseTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "enterprise_type" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.EnterpriseTypeDictLabel = enterpriseTypeDictMap.GetValueOrDefault(e.EnterpriseType ?? "", e.EnterpriseType);
        });
        return ExcelHelper.ExportTemplate(list, "企业管理导出记录");
    }
    
    /// <summary>
    /// 下载企业管理数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载企业管理数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportEnterpriseDictionaryOutput>(), "企业管理导入模板");
    }
    
    /// <summary>
    /// 导入企业管理记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入企业管理记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var enterpriseTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "enterprise_type" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportEnterpriseDictionaryInput, EnterpriseDictionary>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 映射字典值
                    foreach(var item in pageItems) {
                        if (string.IsNullOrWhiteSpace(item.EnterpriseTypeDictLabel)) continue;
                        item.EnterpriseType = enterpriseTypeDictMap.GetValueOrDefault(item.EnterpriseTypeDictLabel);
                        if (item.EnterpriseType == null) item.Error = "企业类型字典映射失败";
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<EnterpriseDictionary>>();
                    
                    var storageable = _enterpriseDictionaryRep.Context.Storageable(rows)
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.EnterpriseCode), "企业编码不能为空")
                        .SplitError(it => it.Item.EnterpriseCode?.Length > 100, "企业编码长度不能超过100个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.EnterpriseName), "企业名称不能为空")
                        .SplitError(it => it.Item.EnterpriseName?.Length > 100, "企业名称长度不能超过100个字符")
                        .SplitError(it => it.Item.EnterpriseNamePinyin?.Length > 100, "企业名称拼音长度不能超过100个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.EnterpriseType), "企业类型不能为空")
                        .SplitError(it => it.Item.EnterpriseType?.Length > 50, "企业类型长度不能超过50个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
