namespace His.Module.OutpatientDoctor.Api.Prescription.Dto;

public class PrescriptionRefundDrugDto
{
    /// <summary>
    /// 处方id
    /// </summary>
    public long? PrescriptionId { get; set; }

    /// <summary>
    /// 明细id
    /// </summary>
    public long? PrescriptionDetailId { get; set; }
 
    /// <summary>
    /// 退药药人
    /// </summary>
    public long? RefundUserId { get; set; }
    
    
 
    /// <summary>
    /// 执行科室
    /// </summary>
    public long? ExecuteDeptId { get; set; }
    
    // 
    
    /// <summary>
    /// 原因
    /// </summary>
    public string? Reason { get; set; }
    
}
 