﻿namespace His.Module.Shared.Entity;

/// <summary>
/// 给药途径表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("medication_routes", "给药途径表")]
public class MedicationRoutes : EntityTenant
{
    /// <summary>
    /// 途径编码
    /// </summary>
    [SugarColumn(ColumnName = "route_code", ColumnDescription = "途径编码", Length = 64)]
    public virtual string? RouteCode { get; set; }

    /// <summary>
    /// 途径名称
    /// </summary>
    [SugarColumn(ColumnName = "route_name", ColumnDescription = "途径名称", Length = 64)]
    public virtual string? RouteName { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 32)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 32)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 缩写
    /// </summary>
    [SugarColumn(ColumnName = "abbreviation", ColumnDescription = "缩写", Length = 64)]
    public virtual string? Abbreviation { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    [SugarColumn(ColumnName = "route_category", ColumnDescription = "分类", Length = 64)]
    public virtual string? RouteCategory { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }
}