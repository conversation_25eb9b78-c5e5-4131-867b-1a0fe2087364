﻿using Furion.DatabaseAccessor;
using His.Module.MedicalTech.Enum;
using His.Module.OutpatientDoctor.Service.Dto;
using His.Module.Shared.Api.Api.ChargeItem;
using Yitter.IdGenerator;


namespace His.Module.MedicalTech.Service;

/// <summary>
//检查模板服务 🧩
/// </summary>
[ApiDescriptionSettings(MedicalTechConst.GroupName, Order = 100)]
public class TemplateExaminationService(
    UserManager userManager,
    IChargeItemApi _chargeItemApi,
    SqlSugarRepository<TemplateExamination> templateExaminationRep,
    SqlSugarRepository<TemplateExaminationDetails> templateExaminationDetailsRep) : IDynamicApiController, ITransient
{
    /// <summary>
    /// 查询模板列表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询模板列表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    [UnitOfWork]
    public async Task<SqlSugarPagedList<TemplateExamination>> Page(QueryTemplateExaminationInput input)
    {
        var list = await templateExaminationRep.AsQueryable()
            .ToPagedListAsync(input.Page, input.PageSize);

        return list;
    }

    /// <summary>
    /// 查询模板列表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询模板列表")]
    [ApiDescriptionSettings(Name = "List"), HttpPost]
    [UnitOfWork]
    public async Task<List<TemplateExamination>> GetList(QueryTemplateExaminationInput input)
    {
        var list = await templateExaminationRep.AsQueryable()
            .ToListAsync();

        return list;
    }

    /// <summary>
    /// 增加处方表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询处方模板明细")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    [UnitOfWork]
    public async Task<List<TemplateExaminationDetails>> GetDetail([FromQuery] long id)
    {
        var details = await templateExaminationDetailsRep
            .AsQueryable().Where(u => u.TemplateId == id).ToListAsync();

        return details;
    }

    /// <summary>
    /// 增加 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("保存模板")]
    [ApiDescriptionSettings(Name = "Save"), HttpPost]
    [UnitOfWork]
    public async Task<long> Save(TemplateExaminationInput input)
    {
        // 将输入参数转换为检查实体
        var examination = input.Adapt<TemplateExamination>();
        // 将输入的检查明细转换为实体列表
        var detailsList = input.ExaminationDetails.Adapt<List<TemplateExaminationDetails>>();

        // 验证检查明细中执行科室是否一致
        if (detailsList.Count > 1)
        {
            var firstDeptId = detailsList.First().ExecuteDeptId; // 获取第一个明细的执行科室ID
            if (detailsList.Any(detail => detail.ExecuteDeptId != firstDeptId))
            {
                throw Oops.Oh(MedicalTechErrorCodeEnum.MT0003);
            }
        }

        // 设置检查的基本信息
        if (examination.Id == 0)
        {
            examination.Id = YitIdHelper.NextId();
            await templateExaminationRep.InsertAsync(examination);
        }
        else
        {
            await templateExaminationDetailsRep.AsDeleteable()
              .Where(u => u.TemplateId == examination.Id).ExecuteCommandAsync();
            await templateExaminationRep.UpdateAsync(examination);
        }


        examination.Status = 1; // 设置状态为未收费
        examination.ExecuteDeptId = detailsList.First().ExecuteDeptId;
        examination.ExecuteDeptName = detailsList.First().ExecuteDeptName;

        // 批量获取所有关联的收费项目详细信息，优化数据库查询，避免N+1问题
        var chargeItemDetails = await _chargeItemApi.GetDetails([.. detailsList.Select(detail => (long)detail.ItemId)]);

        foreach (var detail in detailsList)
        {
            // 根据 ItemId 匹配对应的收费项目
            var chargeItem = chargeItemDetails.FirstOrDefault(item => item.Id == detail.ItemId);
            if (chargeItem == null) continue;

            // 填充收费项目基础信息
            detail.Id = YitIdHelper.NextId();
            detail.TemplateId = examination.Id;
            detail.ItemId = chargeItem.Id;
            detail.ItemCode = chargeItem.Code;
            detail.ItemName = chargeItem.Name;
            detail.Unit = chargeItem.Unit;
            detail.Price = chargeItem.Price;
            detail.Amount = detail.Quantity * chargeItem.Price;
            detail.ChargeCategoryId = chargeItem.ChargeCategoryId;
            detail.IsPackage = (int?)chargeItem.Package;
        }

        // 批量插入检查明细和检查记录
        await templateExaminationDetailsRep.InsertRangeAsync(detailsList);

        return examination.Id;
        // var entity = input.Adapt<TemplateExamination>();
        //
        // entity.Status = 1; // 默认为1
        // if (input.Id is > 0)
        // {
        //     await templateExaminationRep.UpdateAsync(entity) ;
        //     await templateExaminationDetailsRep.AsDeleteable().Where(u => u.TemplateId == entity.Id)
        //         .ExecuteCommandAsync();
        // }
        // else
        // {  entity.Id = 0; //修改时重新插入
        //     await templateExaminationRep.InsertAsync(entity) ;
        // }
        //
        // var details = input.ExaminationDetails.Adapt<List<TemplateExaminationDetails>>();
        // foreach (var item in details)
        // {
        //     item.Id = 0; //修改时重新插入
        //     item.TemplateId = entity.Id;
        //     await templateExaminationDetailsRep.InsertAsync(item);
        // }
        // return entity.Id;
    }


    /// <summary>
    /// 删除处方主表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除模板主表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeleteExaminationInput input)
    {
        var main = await templateExaminationRep.GetFirstAsync(u => u.Id == input.Id);


        if (main.TemplateScope == 3 && main.CreateUserId != userManager.UserId)
        {
            throw Oops.Oh("只能删除自己创建的模板");
        }

        await templateExaminationRep.FakeDeleteAsync(main);
        await templateExaminationDetailsRep.AsDeleteable()
            .Where(u => u.TemplateId == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除处方明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除模板明细表")]
    [ApiDescriptionSettings(Name = "DeleteDetail"), HttpPost]
    public async Task DeleteDetail(DeleteExaminationInput input)
    {
        var detail = await templateExaminationDetailsRep.GetFirstAsync(u => u.Id == input.Id);
        var main = await templateExaminationRep.GetFirstAsync(u => u.Id == detail.TemplateId);


        if (main.TemplateScope == 3 && main.CreateUserId != userManager.UserId)
        {
            throw Oops.Oh("只能删除自己创建的模板");
        }

        await templateExaminationDetailsRep.DeleteByIdAsync(input.Id);

        //await _prescriptionDetailRep.DeleteAsync(entity);   //真删除
    }
}