﻿namespace His.Module.Shared.Service;

/// <summary>
/// 单项套餐分页查询输入参数
/// </summary>
public class PageChargeItemPackInput : BasePageInput
{
    /// <summary>
    /// 套餐Id
    /// </summary>
    public long? PackId { get; set; }
}

/// <summary>
/// 单项套餐增加输入参数
/// </summary>
public class AddChargeItemPackInput : ChargeItemPack
{
}

/// <summary>
/// 单项套餐删除输入参数
/// </summary>
public class DeleteChargeItemPackInput : BaseIdInput
{
}

/// <summary>
/// 单项套餐更新输入参数
/// </summary>
public class UpdateChargeItemPackInput : AddChargeItemPackInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public override long Id { get; set; }
}

/// <summary>
/// 单项套餐主键查询输入参数
/// </summary>
public class QueryByIdChargeItemPackInput : DeleteChargeItemPackInput
{
}