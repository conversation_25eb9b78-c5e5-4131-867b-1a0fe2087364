﻿namespace His.Module.Pharmacy;

/// <summary>
/// 药理分类维护输出参数
/// </summary>
public class PharmacologicalClassDto
{
    /// <summary>
    /// 父级分类ID
    /// </summary>
    public string ParentIdFkColumn { get; set; }
    
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 药理分类编码
    /// </summary>
    public string? ClassCode { get; set; }
    
    /// <summary>
    /// 药理分类名称
    /// </summary>
    public string? ClassName { get; set; }
    
    /// <summary>
    /// 父级分类ID
    /// </summary>
    public long? ParentId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
