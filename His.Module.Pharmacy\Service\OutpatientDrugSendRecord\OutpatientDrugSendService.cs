﻿using Admin.NET.Core.Service;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.OutpatientDoctor.Api.Prescription;
using His.Module.OutpatientDoctor.Api.Prescription.Dto;
using His.Module.Shared.Entity;
using His.Module.Shared.Service.BasicInfo;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 门诊发药服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName,
    // Module=PharmacyConst.GroupName,
    Order = 100)]
public class OutpatientDrugSendService(
    SysUserService sysUserService,
    BasicInfoService basicInfoService,
    SqlSugarRepository<OutpatientDrugSendRecord> outpatientDrugSendRecordRep,
    SqlSugarRepository<DrugInventory> drugInventoryRep,
    SqlSugarRepository<MedicationRoutes> medicationRoutesRep,
    InventoryService inventoryService,
    SqlSugarRepository<OutpatientPrescriptionInventoryAllocation> outpatientPrescriptionInventoryAllocationRep,
    IPrescriptionApi prescriptionApi)
    : IDynamicApiController, ITransient
{
    // private readonly ISqlSugarClient _sqlSugarClient = sqlSugarClient;
    // private readonly SysDictTypeService _sysDictTypeService = sysDictTypeService;

    /// <summary>
    /// 查询现有库存 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询现有库存")]
    [ApiDescriptionSettings(Name = "CurrentDrugInventory"), HttpPost]
    public async Task<List<QueryCurrentDrugInventoryDetail>> CurrentDrugInventory(QueryCurrentDrugInventoryInput input)
    {
        // var tasks = input.Details.Select(detail =>
        // {
        //     return _drugInventoryRep.AsQueryable()
        //         .Where(x => x.DrugId == detail.DrugId && x.SalePrice == detail.Price)
        //         .SumAsync(u => u.Quantity)
        //         .ContinueWith(task => detail.CurrentQuantity = task.Result ?? 0);
        // }).ToArray();
        //
        // await Task.WhenAll(tasks);
        //
        // return input.Details;

        foreach (QueryCurrentDrugInventoryDetail detail in input.Details)
        {
            detail.CurrentQuantity = await drugInventoryRep.AsQueryable()
                .Where(x => x.DrugId == detail.DrugId
                            && x.SalePrice == detail.Price
                ).SumAsync(u => u.Quantity - u.PendingQuantity) ?? 0;
        }

        return input.Details;
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataOutpatientDrugSendRecordInput input)
    {
        var storageIdData = await outpatientDrugSendRecordRep.Context.Queryable<DrugStorage>()
            .InnerJoinIF<OutpatientDrugSendRecord>(input.FromPage, (u, r) => u.Id == r.StorageId)
            .Select(u => new { Value = u.Id, Label = $"{u.StorageName}" }).ToListAsync();
        var manufacturerIdData = await outpatientDrugSendRecordRep.Context.Queryable<EnterpriseDictionary>()
            .InnerJoinIF<OutpatientDrugSendRecord>(input.FromPage, (u, r) => u.Id == r.ManufacturerId)
            .Select(u => new { Value = u.Id, Label = $"{u.EnterpriseName}" }).ToListAsync();
        var freqList = await medicationRoutesRep.Context.Queryable<Frequency>()
            .Select(u => new { Value = u.Id, Label = $"{u.Name}" }).ToListAsync();
        var routeList = await medicationRoutesRep.Context.Queryable<MedicationRoutes>()
            .Select(u => new { Value = u.Id, Label = $"{u.RouteName}" }).ToListAsync();


        var users = await basicInfoService.GetUsers(
            new His.Module.Shared.Service.BasicInfo.Dto.UserInput()
            {
                OrgId = long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value)
            });
        // var pharmacyUsers = new List<Dictionary<string, object>>()
        // {
        //     new Dictionary<string, object>() { { "value", 1300000000101 }, { "label", "药师1" } },
        //     new Dictionary<string, object>() { { "value", 2 }, { "label", "药师2" } },
        //     new Dictionary<string, object>() { { "value", 3 }, { "label", "药师3" } }
        // };
        var pharmacyUsers = users.Select(u => new Dictionary<string, object>()
        {
            { "value", u.Id }, { "label", u.RealName }
        }).ToList();
        long currentUserId = long.Parse(App.User.FindFirst(ClaimConst.UserId).Value);
        var defaultPharmacy = new
            Dictionary<string, object>()
            {
                { "auditUserId", currentUserId },
                { "pickUserId", currentUserId },
                { "checkUserId", currentUserId },
                { "sendUserId", currentUserId }
            };

        return new Dictionary<string, dynamic>
        {
            { "storageId", storageIdData },
            { "manufacturerId", manufacturerIdData },
            { "pharmacyUsers", pharmacyUsers },
            { "defaultPharmacy", defaultPharmacy },
            { "routeList", routeList },
            { "freqList", freqList }
        };
    }

    /// <summary>
    /// 处方计费
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
    /// <summary>
    /// 发药 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("发药")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task Add(List<AddOutpatientDrugSendInput> input)
    {
        /*
         *
      "sendTime": "发药时间",
      "storageId": "药房",
      "storageCode": "药房编码",
         *
         *
         *
         */
        long prescriptionId = input.First().PrescriptionId ?? 0L;
        long sendUserId = input.First().SendUserId ?? 0L;
        var storageCode = App.User.FindFirst(ClaimConst.OrgCode);
        //var storageId = App.User.FindFirst(ClaimConst.OrgId);
        var now = DateTime.Now;
        var list = input.Adapt<List<OutpatientDrugSendRecord>>();
        var allocationList = await outpatientPrescriptionInventoryAllocationRep.AsQueryable()
            .Where(u => u.PrescriptionId == prescriptionId)
            .ToListAsync();
        if (list.Count == 0)
        {
            throw Oops.Oh("请选择要发药的药品");
        }

        var prescriptionMain = await prescriptionApi.GetPrescription(prescriptionId);
        if (prescriptionMain is null)
            throw Oops.Oh("当前处方不存在！");
        if (prescriptionMain.ChargeMainId is null or 0L)
            throw Oops.Oh("当前处方未计费！");

        //处理库存
        foreach (var detail in list)
        {
            detail.SendTime = now;
            detail.StorageCode = storageCode?.Value;
            detail.AuditTime = now;
            detail.PickTime = now;
            detail.CheckTime = now;
            detail.ChargeId = prescriptionMain.ChargeMainId;
            detail.ChargeStaffId = prescriptionMain.ChargeStaffId;
            detail.ChargeTime = prescriptionMain.ChargeTime;
            detail.DeptId = prescriptionMain.CreateOrgId;
            detail.DeptName = prescriptionMain.CreateOrgName;
            detail.DoctorId = prescriptionMain.CreateUserId;
            detail.DoctorName = prescriptionMain.CreateUserName;

            var allocation =
                allocationList?.Where(u =>
                        u.PrescriptionId == detail.PrescriptionId
                        && u.PrescriptionDetailId == detail.PrescriptionDetailId
                        && u.DrugId == detail.DrugId
                    )
                    .FirstOrDefault();

            if (allocation == null)
            {
                throw Oops.Oh("未获取到锁定的库存记录");
                //根据id 和价格
                //定期清理

                // _drugInventoryRep.AsQueryable().Where(
                //     u => u.DrugId == detail.DrugId && u.SalePrice == detail.Price
                //                                    && u.StorageId == detail.StorageId && u.Quantity >= detail.Quantity
                // ).OrderByDescending(u => u.ExpirationDate);
            }

            detail.InventoryId = allocation.InventoryId;
            detail.StorageId = allocation.StorageId;
            var inventory = await drugInventoryRep.GetFirstAsync(p => p.Id == detail.InventoryId);
            detail.DrugType = inventory.DrugType;
            detail.ManufacturerId = inventory.ManufacturerId;
            detail.ManufacturerName = inventory.ManufacturerName;
            detail.Spec = inventory.Spec;
            detail.BatchNo = inventory.BatchNo;
            detail.ProductionDate = inventory.ProductionDate;
            detail.ExpirationDate = inventory.ExpirationDate;
            await inventoryService.UpdateInventoryAsync(detail);

            // 发药记录
            var id = await outpatientDrugSendRecordRep.InsertAsync(detail) ? detail.Id : 0;
            // 分配记录会写id
            await outpatientPrescriptionInventoryAllocationRep.UpdateAsync(u =>
                    new OutpatientPrescriptionInventoryAllocation()
                    {
                        Status = 1, // 已发药
                        SendRecordId = id // 发药记录 
                    }, u =>
                    u.PrescriptionId == detail.PrescriptionId &&
                    u.PrescriptionDetailId == detail.PrescriptionDetailId
            );
        }


        // 修改处方状态
        await prescriptionApi.PrescriptionSendDrug(
            new PrescriptionSendDrugDto()
            {
                PrescriptionId = prescriptionMain.Id,
                ExecuteDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId)?.Value ?? "0"),
                SendUserId = sendUserId
            });
    }
}