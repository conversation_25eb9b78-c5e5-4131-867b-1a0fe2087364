﻿namespace His.Module.MedicalTech.Enum;

/// <summary>
/// 医技管理错误码
/// </summary>
[ErrorCodeType]
[Description("医技管理错误码")]
public enum MedicalTechErrorCodeEnum
{
    /// <summary>
    /// 当前状态无法修改申请单
    /// </summary>
    [ErrorCodeItemMetadata("当前状态无法修改申请单")]
    MT0001,

    /// <summary>
    /// 当前状态禁止删除
    /// </summary>
    [ErrorCodeItemMetadata("当前状态禁止删除")]
    MT0002,

    /// <summary>
    /// 明细的执行科室不一致
    /// </summary>
    [ErrorCodeItemMetadata("明细的执行科室不一致")]
    MT0003,

    /// <summary>
    /// 状态转换无效
    /// </summary>
    [ErrorCodeItemMetadata("状态转换无效")]
    MT0004,

    /// <summary>
    /// 无效的申请单类型
    /// </summary>
    [ErrorCodeItemMetadata("无效的申请单类型")]
    MT0005,

    /// <summary>
    /// 当前申请单无法收费，请重新查询再进行操作
    /// </summary>
    [ErrorCodeItemMetadata("当前申请单无法收费，请重新查询再进行操作")]
    MT0006,

    /// <summary>
    /// 申请单不属于同一患者，无法一起收费
    /// </summary>
    [ErrorCodeItemMetadata("申请单不属于同一患者，无法一起收费")]
    MT0007
}