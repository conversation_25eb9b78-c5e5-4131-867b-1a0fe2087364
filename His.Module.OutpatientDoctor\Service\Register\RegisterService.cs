﻿using Furion.DatabaseAccessor;
using His.Module.OutpatientDoctor.Api.Register;
using His.Module.OutpatientDoctor.Api.Register.Dto;
using His.Module.OutpatientDoctor.OtherModelEntity;
using His.Module.Patient.Api.Api;
using His.Module.Patient.Api.Api.Dto;
using His.Module.Shared.Api.Api.ChargeItem;
using His.Module.Shared.Api.Api.RegCategory;
using Yitter.IdGenerator;
namespace His.Module.OutpatientDoctor.Service;

/// <summary>
/// 门诊挂号服务
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class RegisterService : IDynamicApiController, IOutpatientRegisterApi, ITransient
{
    private readonly SqlSugarRepository<Register> _mzRegisterRep;
    private readonly SqlSugarRepository<ChargeMain> _chargeMainRep;
    private readonly SqlSugarRepository<ChargeDetail> _chargeDetailRep;
    private readonly IRegCategoryApi _regCategoryApi;
    private readonly IChargeItemApi _chargeItemApi;
    private readonly ICardInfoApi _cardInfoApi;
    private readonly UserManager _userManager;

    public RegisterService(SqlSugarRepository<Register> mzRegisterRep,
        SqlSugarRepository<ChargeMain> chargeMainRep,
        SqlSugarRepository<ChargeDetail> chargeDetailRep,
        IRegCategoryApi regCategoryApi,
        IChargeItemApi chargeItemApi,
        ICardInfoApi cardInfoApi, UserManager userManager)
    {
        _mzRegisterRep = mzRegisterRep;
        _regCategoryApi = regCategoryApi;
        _chargeItemApi = chargeItemApi;
        _chargeMainRep = chargeMainRep;
        _chargeDetailRep = chargeDetailRep;
        _cardInfoApi = cardInfoApi;
        _userManager = userManager;
    }

    /// <summary>
    /// 分页查询就诊记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page"), SkipPermission]
    [DisplayName("分页查询就诊记录")]
    public async Task<SqlSugarPagedList<RegisterOutput>> Page(RegisterInput input)
    {
        input.PatientName = input.PatientName?.ToLower();
        return await _mzRegisterRep.AsTenant().QueryableWithAttr<Register>()
            .LeftJoin<SysOrg>((u, a) => u.DeptId == a.Id)
            .LeftJoin<SysUser>((u, a, b) => u.DoctorId == b.Id)
            .LeftJoin<FeeCategory>((u, a, b, c) => u.FeeId == c.Id)
            .LeftJoin<RegCategory>((u, a, b, c, d) => u.RegCategoryId == d.Id)
            .LeftJoin<MedicalCardInfo>((u, a, b, c, d, e) => u.CardId == e.Id)
            .WhereIF(input.Id > 0, u => u.Id == input.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName)
                                                                         || u.PinyinCode.Contains(input.PatientName)
                                                                         || u.WubiCode.Contains(input.PatientName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo),
                u => u.VisitNo.Equals(input.VisitNo, StringComparison.CurrentCultureIgnoreCase))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardNo), u => u.IdCardNo.ToLower() == input.IdCardNo.ToLower())
            .WhereIF(input.StartTime != null, u => u.CreateTime >= input.StartTime)
            .WhereIF(input.EndTime != null, u => u.CreateTime <= input.EndTime)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.PatientName.Contains(input.Keyword) ||
                                                                     u.CardNo.Contains(input.Keyword) ||
                                                                     u.IdCardNo.Contains(input.Keyword) ||
                                                                     u.OutpatientNo.Contains(input.Keyword)
            )
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.DoctorId != null, u => u.DoctorId == input.DoctorId)
            .Where(u => u.CreateUserId == _userManager.UserId)
            .Where(u => u.CreateOrgId == _userManager.OrgId)
            .Select((u, a, b, c, d, e) => new RegisterOutput()
            {
                DeptName = a.Name,
                DoctorName = b.RealName,
                FeeName = c.Name,
                RegCategory = d.Name,
                CardNo = e.CardNo,
                IdCardNo = u.IdCardNo,
            }, true)
            .OrderByDescending(u => u.Id)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 挂号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    [DisplayName("挂号")]
    [UnitOfWork]
    public async Task<long> Add(AddMzRegisterInput input)
    {
        //获取卡信息实体
        var cardInfoEntity = await _cardInfoApi.Detail(input.CardId);
        //检查卡余额是否足够
        if (cardInfoEntity.Balance < input.PersonalPayment)
        {
            throw Oops.Oh("就诊卡余额不足！");
        }

        //将输入适配为Register实体
        var entity = input.Adapt<Register>();
        //为实体分配唯一ID
        entity.Id = YitIdHelper.NextId();
        // 清空创建时间 否则会使用历史记录时间
        entity.CreateTime = DateTime.Now;
        entity.CreateUserId = null;
        entity.UpdateTime = null;

        //构造收费主表实体
        var chargeMainEntity = new ChargeMain()
        {
            Id = YitIdHelper.NextId(),
            PatientId = entity.PatientId,
            RegisterId = entity.Id,
            //从数据库获取发票号码
            InvoiceNumber =
                await _mzRegisterRep.Context.Ado.GetStringAsync(
                    "SELECT LPAD(CAST(NEXTVAL('charge_main_in_num_seq')As varchar),8,'0')"),
            TotalAmount = input.TotalAmount,
            PayMethod1Id = -1,
            PayAmount1 = 0,
            PayMethod2Id = input.PaymentMethod,
            PayAmount2 = input.PersonalPayment,
            Status = ChargeStatusEnum.NotCharged, // 默认未收费
            Type = 0,
            CardNo = cardInfoEntity.CardNo,
            VisitNo = input.VisitNo,
            OutpatientNo = input.OutpatientNo,
        };
        //插入收费主表
        await _chargeMainRep.InsertAsync(chargeMainEntity);
        //获取号别实体
        var regCategoryEntity = await _regCategoryApi.Detail(entity.RegCategoryId);
        //获取号别绑定的收费收费项目
        var chargeItemEntities = await _chargeItemApi.GetDetails([(long)regCategoryEntity.ChargeItemId]);
        if (chargeItemEntities is null || chargeItemEntities.Count == 0)
        {
            throw Oops.Oh("当前号别没有绑定收费项目！");
        }

        var chargeItemEntity = chargeItemEntities.First();
        //构造收费明细
        var chargeDetailEntity = new ChargeDetail()
        {
            ChargeId = chargeMainEntity.Id,
            ItemId = chargeItemEntity.Id,
            ItemCode = chargeItemEntity.Code,
            ItemName = chargeItemEntity.Name,
            Quantity = 1,
            Price = chargeItemEntity.Price,
            Amount = chargeItemEntity.Price,
            ExecuteStatus = YesNoEnum.N,
            Withdrawal = YesNoEnum.N,
            PayMethod1Id = -1,
            PayAmount1 = 0,
            PayMethod2Id = chargeMainEntity.PayMethod2Id,
            PayAmount2 = chargeMainEntity.PayAmount2,
            TotalPayAmount = chargeMainEntity.TotalAmount,
            ChargeCategoryId = chargeItemEntity.ChargeCategoryId,
            ExecuteDeptId = entity.DeptId,
            BillingDeptId = entity.DeptId,
            BillingDoctorId = entity.DoctorId,
            Status = ChargeStatusEnum.NotCharged,
        };
        //插入收费明细表
        await _chargeDetailRep.InsertAsync(chargeDetailEntity);
        //获取患者第一次就诊记录
        var firstVisit = await _mzRegisterRep.AsQueryable()
            .Where(u => u.PatientId == input.PatientId && u.VisitNum == 1).FirstAsync();

        //为实体分配就诊号码
        entity.VisitNo =
            await _mzRegisterRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('register_visit_no_seq')As varchar),8,'0')");
        //生成拼音码和五笔码
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.PatientName);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.PatientName);
        //获取最大的就诊次数
        var maxVisitNum = await _mzRegisterRep.AsQueryable().Where(u => u.PatientId == input.PatientId)
            .MaxAsync(u => u.VisitNum);
        // 更新就诊次数
        entity.VisitNum = (maxVisitNum ?? 0) + 1;

        //设置挂号状态
        entity.Status = RegStatusEnum.NotCharged; // RegStatusEnum.Register;
        //关联收费主表ID
        entity.ChargeMainId = chargeMainEntity.Id;
        //设置其他费用和实际收费金额
        entity.OtherFee = 0;
        entity.ActualChargeFee = entity.ConsultationFee;
        //处理首次就诊情况
        if (firstVisit == null)
        {
            entity.FirstDeptId = entity.DeptId;
            entity.FirstDoctorId = entity.DoctorId;
            entity.OutpatientNo = entity.VisitNo;
        }
        else
        {
            entity.FirstDeptId = firstVisit.FirstDeptId;
            entity.FirstDoctorId = firstVisit.FirstDoctorId;
            entity.OutpatientNo = firstVisit.OutpatientNo;
        }

        entity.CardNo = cardInfoEntity.CardNo;
        entity.TriageStatus = 0;
        //插入挂号表
        // 挂号时间 
        if (entity.RegTime == null)
        {
            entity.RegTime = DateTime.Now;
        }

        await _mzRegisterRep.InsertAsync(entity);
        //处理卡扣款情况
        if (input.PaymentMethod == 658657048305733)
        {
            //卡扣款
            await _cardInfoApi.CardPay(new CardPayInput()
            {
                Id = cardInfoEntity.Id,
                InvoiceNumber = chargeMainEntity.InvoiceNumber,
                PayAmount = chargeMainEntity.PayAmount2,
                PayMethodId = input.PaymentMethod,
            });
            //设置挂号状态
            await Charging(new RegisterChargeDto()
            {
                RegisterId = entity.Id,
                InsurancePayAmount = 0,
                SelfPayAmount = chargeMainEntity.PayAmount2,
                TotalAmount = chargeMainEntity.PayAmount2,
            });
        }

        return entity.Id;
    }

    [HttpPost]
    [ApiDescriptionSettings(Name = "charging"), UnitOfWork]
    [DisplayName("收费")]
    public async Task<bool> Charging(RegisterChargeDto input)
    {
        Register register = null;
        ChargeMain chargeMain = null;
        if (input.RegisterId != null)
        {
            register = await _mzRegisterRep.GetByIdAsync(input.RegisterId);
            if (register == null)
                throw Oops.Oh(ErrorCodeEnum.D1002);
            if (!(await _chargeMainRep.IsAnyAsync(u => u.Id == register.ChargeMainId)))
                throw Oops.Oh(ErrorCodeEnum.D1002);
        }

        if (input.ChargeId != null)
        {
            chargeMain = await _chargeMainRep.GetByIdAsync(input.ChargeId);
            register= await _mzRegisterRep.GetByIdAsync(chargeMain.RegisterId);
        }
        
        // 判断医保支付和自费支付是否和挂号金额一致
        if (input.SelfPayAmount + input.InsurancePayAmount != register.RegistrationFee)
        {
            // 没有完全支付 
            // 按项目明细
            // input.Items
        }

        var r = await _mzRegisterRep.UpdateAsync(u => new Register() { Status = RegStatusEnum.Register, }
            , u => u.Id == input.RegisterId);

        await _chargeMainRep.UpdateAsync(u => new ChargeMain() { Status = ChargeStatusEnum.Charged, }
            , u => u.Id == register.ChargeMainId);

        await _chargeDetailRep.UpdateAsync(u => new ChargeDetail() { ExecuteStatus = YesNoEnum.Y, }
            , u => u.ChargeId == register.ChargeMainId);
        return r;
    }

    /// <summary>
    /// 退号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Refund")]
    [DisplayName("退号")]
    public async Task Refund(RefundMzRegisterInput input)
    {
        var entity = await _mzRegisterRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        entity.RefundNumTime = DateTime.Now;
        entity.RefundNumId = _userManager.UserId;
        await _mzRegisterRep.FakeDeleteAsync(entity);
    }

    /// <summary>
    /// 更新挂号信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    [DisplayName("更新挂号信息")]
    public async Task Update(UpdateMzRegisterInput input)
    {
        await _mzRegisterRep.UpdateAsync(
            u => new Register()
            {
                PatientName = input.PatientName,
                Sex = input.Sex,
                Age = input.Age,
                AgeUnit = input.AgeUnit,
                FeeId = input.FeeId,
                IdCardNo = input.IdCardNo,
                CardType = input.CardType,
                ContactName = input.ContactName,
                ContactPhone = input.ContactPhone,
            }
            , u => u.Id == input.Id);
        // 患者主索引
        // 就诊卡表
        // await _mzRegisterRep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 医生看诊
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "SeeVisit")]
    [DisplayName("医生看诊")]
    public async Task SeeVisit(SeeVisitInput input)
    {
  
        await _mzRegisterRep.UpdateAsync(
            u=> new Register  { SeeTime = DateTime.Now,
                Status = RegStatusEnum.Visit },
            u => u.Id == input.Id && u.Status == RegStatusEnum.Register
        );
      
    }
    /// <summary>
    /// 结束就诊
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "EndVisit")]
    [DisplayName("结束就诊")]
    public async Task OverVisit(EndVisitInput input)
    {
        var entity = input.Adapt<Register>();


        await _mzRegisterRep.UpdateAsync(
           u=> new Register  { Status = RegStatusEnum.End },
            u => u.Id == input.Id
        );

    }
    

    /// <summary>
    /// 获取门诊挂号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    [DisplayName("获取门诊挂号")]
    public async Task<Register> Detail([FromQuery] QueryByIdMzRegisterInput input)
    {
        return await _mzRegisterRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 根据挂号状态查询挂号列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "ListByStatus")]
    [DisplayName("根据挂号状态查询挂号列表")]
    public async Task<List<RegisterOutput>> ListByStatus([FromQuery] QueryByStatusMzRegisterInput input)
    {
        return await _mzRegisterRep.AsTenant().QueryableWithAttr<Register>()
            .LeftJoin<SysOrg>((u, a) => u.DeptId == a.Id)
            .LeftJoin<SysUser>((u, a, b) => u.DoctorId == b.Id)
            .LeftJoin<FeeCategory>((u, a, b, c) => u.FeeId == c.Id)
            .LeftJoin<RegCategory>((u, a, b, c, d) => u.RegCategoryId == d.Id)
            .LeftJoin<MedicalCardInfo>((u, a, b, c, d, e) => u.CardId == e.Id)
            .Where(u => u.Status == input.Status && u.CreateTime >= DateTime.Now.AddHours(-48)) //只查询最近24小时的数据
            .Where(u => u.DeptId == _userManager.OrgId)
            .Where(u => u.DoctorId == _userManager.UserId)
            .Select((u, a, b, c, d, e) => new RegisterOutput()
            {
                DeptName = a.Name,
                DoctorName = b.RealName,
                FeeName = c.Name,
                RegCategory = d.Name,
                CardNo = e.CardNo,
            }, true)
            .OrderByDescending(u => u.Id)
            .ToListAsync();
    }

    // 获取未分诊挂号。预约列表 

    [HttpPost]
    [ApiDescriptionSettings(Name = "GetUnTriageRegister")]
    [DisplayName("获取未分诊挂号。预约列表")]
    public async Task<List<RegisterOutput>> GetUnTriageRegister(UnTriageRegisterDto dto)
    {
        return await _mzRegisterRep.AsTenant().QueryableWithAttr<Register>()
            .LeftJoin<SysOrg>((u, a) => u.DeptId == a.Id)
            .LeftJoin<SysUser>((u, a, b) => u.DoctorId == b.Id)
            .LeftJoin<FeeCategory>((u, a, b, c) => u.FeeId == c.Id)
            .LeftJoin<RegCategory>((u, a, b, c, d) => u.RegCategoryId == d.Id)
            .LeftJoin<MedicalCardInfo>((u, a, b, c, d, e) => u.CardId == e.Id)
            .WhereIF(dto.DeptId.HasValue, u => u.DeptId == dto.DeptId)
            .Where(u => u.TriageStatus == 0 && u.CreateTime >= DateTime.Today && u.Status == 0)
            .Select((u, a, b, c, d, e) => new RegisterOutput()
            {
                DeptName = a.Name,
                DoctorName = b.RealName,
                FeeName = c.Name,
                RegCategory = d.Name,
                CardNo = e.CardNo,
            }, true).ToListAsync();
    }


    [HttpGet]
    [ApiDescriptionSettings(Name = "GetRegisterByOutpatientNo")]
    [DisplayName("根据门诊号获取挂号列表")]
    public async Task<List<RegisterOutput>> GetRegisterByOutpatientNo([FromQuery] string outpatientNo)
    {
        return await _mzRegisterRep.AsTenant().QueryableWithAttr<Register>()
            .LeftJoin<SysOrg>((u, a) => u.DeptId == a.Id)
            .LeftJoin<SysUser>((u, a, b) => u.DoctorId == b.Id)
            .LeftJoin<FeeCategory>((u, a, b, c) => u.FeeId == c.Id)
            .LeftJoin<RegCategory>((u, a, b, c, d) => u.RegCategoryId == d.Id)
            .LeftJoin<MedicalCardInfo>((u, a, b, c, d, e) => u.CardId == e.Id)
            .Where(u => u.OutpatientNo == outpatientNo)
            .OrderBy(u => u.Id)
            .Select(
                (u, a, b, c, d, e) => new RegisterOutput
                {
                    DeptName = a.Name,
                    DoctorName = b.RealName,
                    FeeName = c.Name,
                    RegCategory = d.Name,
                    CardNo = e.CardNo
                }, true).ToListAsync();
    }

    /// <summary>
    /// 删除未收费的挂号记录 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除退费审核表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteRegisterInput input)
    {
        var entity = await _mzRegisterRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity == null)
        {
            throw Oops.Oh("挂号记录不存在");
        }

        if (entity.Status == RegStatusEnum.NotCharged)
        {
            await _mzRegisterRep.FakeDeleteAsync(entity); //假删除
        }
        else
        {
            throw Oops.Oh("已收费的挂号记录不能删除");
        }


        //await _refundAuditRep.DeleteAsync(entity);   //真删除
    }
}