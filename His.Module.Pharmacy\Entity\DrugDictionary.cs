﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品字典表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_dictionary", "药品字典表")]
public class DrugDictionary : EntityTenant
{
    /// <summary>
    /// 药品编码
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 50)]
    public virtual string DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 200)]
    public virtual string DrugName { get; set; }
    
    /// <summary>
    /// 药品名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "drug_name_pinyin", ColumnDescription = "药品名称拼音", Length = 200)]
    public virtual string? DrugNamePinyin { get; set; }
    
    /// <summary>
    /// 通用名称
    /// </summary>
    [SugarColumn(ColumnName = "generic_name", ColumnDescription = "通用名称", Length = 200)]
    public virtual string? GenericName { get; set; }
    
    /// <summary>
    /// 通用名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "generic_name_pinyin", ColumnDescription = "通用名称拼音", Length = 200)]
    public virtual string? GenericNamePinyin { get; set; }
    
    /// <summary>
    /// 产品名称
    /// </summary>
    [SugarColumn(ColumnName = "product_name", ColumnDescription = "产品名称", Length = 200)]
    public virtual string? ProductName { get; set; }
    
    /// <summary>
    /// 产品名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "product_name_pinyin", ColumnDescription = "产品名称拼音", Length = 200)]
    public virtual string? ProductNamePinyin { get; set; }
    
    /// <summary>
    /// 药品类型（西药、中成药、中药饮片等）
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类型（西药、中成药、中药饮片等）", Length = 100)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 药品分类（普通药品、精神类药品、抗菌药物等）
    /// </summary>
    [SugarColumn(ColumnName = "drug_category", ColumnDescription = "药品分类（普通药品、精神类药品、抗菌药物等）", Length = 100)]
    public virtual long? DrugCategory { get; set; }
    [SugarColumn(ColumnName = "drug_category_code", ColumnDescription = "药品分类（普通药品、精神类药品、抗菌药物等）编号", Length = 100)]

    public virtual string? DrugCategoryCode { get; set; }
    [SugarColumn(ColumnName = "drug_category_name", ColumnDescription = "药品分类（普通药品、精神类药品、抗菌药物等） 名称", Length = 100)]

    public virtual string? DrugCategoryName { get; set; }
    
    /// <summary>
    /// 药理分类
    /// </summary>
    [SugarColumn(ColumnName = "pharmacological_class", ColumnDescription = "药理分类", Length = 100)]
    public virtual long? PharmacologicalClass { get; set; }
    
    /// <summary>
    /// 抗生素级别
    /// </summary>
    [SugarColumn(ColumnName = "antibacterial_level", ColumnDescription = "抗生素级别", Length = 100)]
    public virtual string? AntibacterialLevel { get; set; }
    
    /// <summary>
    /// 剂型
    /// </summary>
    [SugarColumn(ColumnName = "drug_form", ColumnDescription = "剂型", Length = 100)]
    public virtual long? DrugForm { get; set; }
    
    /// <summary>
    /// 用药途径
    /// </summary>
    [SugarColumn(ColumnName = "drug_route", ColumnDescription = "用药途径", Length = 100)]
    public virtual long? DrugRoute { get; set; }
    
    /// <summary>
    /// 用药频次
    /// </summary>
    [SugarColumn(ColumnName = "drug_freq", ColumnDescription = "用药频次", Length = 100)]
    public virtual long? DrugFreq { get; set; }
    
    /// <summary>
    /// ICD10编码
    /// </summary>
    [SugarColumn(ColumnName = "icd10",IsJson = true,ColumnDescription = "ICD10编码")]
    public virtual List<String>? Icd10 { get; set; }
    
    /// <summary>
    /// 生产企业ID
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_id", ColumnDescription = "生产企业ID")]
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产企业名称
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_name", ColumnDescription = "生产企业名称", Length = 200)]
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 产地
    /// </summary>
    [SugarColumn(ColumnName = "place_of_origin", ColumnDescription = "产地", Length = 200)]
    public virtual string? PlaceOfOrigin { get; set; }
    
    /// <summary>
    /// 入库单位
    /// </summary>
    [SugarColumn(ColumnName = "storage_unit", ColumnDescription = "入库单位", Length = 100)]
    public virtual string? StorageUnit { get; set; }
    
    /// <summary>
    /// 包装规格
    /// </summary>
    [SugarColumn(ColumnName = "package_spec", ColumnDescription = "包装规格", Length = 100)]
    public virtual string? PackageSpec { get; set; }
    
    /// <summary>
    /// 包装数量
    /// </summary>
    [SugarColumn(ColumnName = "package_quantity", ColumnDescription = "包装数量")]
    public virtual int? PackageQuantity { get; set; }
    
    /// <summary>
    /// 最小包装单位
    /// </summary>
    [SugarColumn(ColumnName = "min_package_unit", ColumnDescription = "最小包装单位", Length = 100)]
    public virtual string? MinPackageUnit { get; set; }
    
    /// <summary>
    /// 剂量单位
    /// </summary>
    [SugarColumn(ColumnName = "dosage_unit", ColumnDescription = "剂量单位", Length = 100)]
    public virtual string? DosageUnit { get; set; }
    
    /// <summary>
    /// 剂量值
    /// </summary>
    [SugarColumn(ColumnName = "dosage_value", ColumnDescription = "剂量值", Length = 0, DecimalDigits=0)]
    public virtual decimal? DosageValue { get; set; }
    
    /// <summary>
    /// 含量
    /// </summary>
    [SugarColumn(ColumnName = "content_value", ColumnDescription = "含量", Length = 0, DecimalDigits=0)]
    public virtual decimal? ContentValue { get; set; }
    
    /// <summary>
    /// 含量单位
    /// </summary>
    [SugarColumn(ColumnName = "content_unit", ColumnDescription = "含量单位", Length = 100)]
    public virtual string? ContentUnit { get; set; }
    
    /// <summary>
    /// 门诊规格
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_spec", ColumnDescription = "门诊规格", Length = 100)]
    public virtual string? OutpatientSpec { get; set; }
    
    /// <summary>
    /// 门诊单位
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_unit", ColumnDescription = "门诊单位", Length = 100)]
    public virtual string? OutpatientUnit { get; set; }
    
    /// <summary>
    /// 门诊包装数量
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_package_quantity", ColumnDescription = "门诊包装数量")]
    public virtual int? OutpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 住院规格
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_spec", ColumnDescription = "住院规格", Length = 100)]
    public virtual string? InpatientSpec { get; set; }
    
    /// <summary>
    /// 住院单位
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_unit", ColumnDescription = "住院单位", Length = 100)]
    public virtual string? InpatientUnit { get; set; }
    
    /// <summary>
    /// 住院包装数量
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_package_quantity", ColumnDescription = "住院包装数量")]
    public virtual int? InpatientPackageQuantity { get; set; }
    
    /// <summary>
    /// 采购类型
    /// </summary>
    [SugarColumn(ColumnName = "purchase_type", ColumnDescription = "采购类型", Length = 100)]
    public virtual string? PurchaseType { get; set; }
    
    /// <summary>
    /// 上市许可持有人
    /// </summary>
    [SugarColumn(ColumnName = "holder", ColumnDescription = "上市许可持有人", Length = 100)]
    public virtual long? Holder { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    [SugarColumn(ColumnName = "purchase_price", ColumnDescription = "进价", Length = 0, DecimalDigits=0)]
    public virtual decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [SugarColumn(ColumnName = "sale_price", ColumnDescription = "零售价", Length = 0, DecimalDigits=0)]
    public virtual decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 每公斤进价
    /// </summary>
    [SugarColumn(ColumnName = "purchase_price_of_kg", ColumnDescription = "每公斤进价", Length = 0, DecimalDigits=0)]
    public virtual decimal? PurchasePriceOfKg { get; set; }
    
    /// <summary>
    /// 每公斤零售价
    /// </summary>
    [SugarColumn(ColumnName = "sale_price_of_kg", ColumnDescription = "每公斤零售价", Length = 0, DecimalDigits=0)]
    public virtual decimal? SalePriceOfKg { get; set; }
    
    /// <summary>
    /// 电子监管码
    /// </summary>
    [SugarColumn(ColumnName = "regulation_code", ColumnDescription = "电子监管码", Length = 100)]
    public virtual string? RegulationCode { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [SugarColumn(ColumnName = "approval_number", ColumnDescription = "批准文号", Length = 100)]
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 优先使用
    /// </summary>
    [SugarColumn(ColumnName = "priority_use", ColumnDescription = "优先使用", Length = 100)]
    public virtual string? PriorityUse { get; set; }
    
    /// <summary>
    /// 药房货位
    /// </summary>
    [SugarColumn(ColumnName = "pharmacy_location", ColumnDescription = "药房货位", Length = 100)]
    public virtual string? PharmacyLocation { get; set; }
    
    /// <summary>
    /// 药库货位
    /// </summary>
    [SugarColumn(ColumnName = "storehouse_location", ColumnDescription = "药库货位", Length = 100)]
    public virtual string? StorehouseLocation { get; set; }
    
    /// <summary>
    /// YPID
    /// </summary>
    [SugarColumn(ColumnName = "ypid", ColumnDescription = "YPID", Length = 100)]
    public virtual string? Ypid { get; set; }
    
    /// <summary>
    /// 是否拆零
    /// </summary>
    [SugarColumn(ColumnName = "is_split", ColumnDescription = "是否拆零")]
    public virtual int? IsSplit { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [SugarColumn(ColumnName = "medicine_code", ColumnDescription = "国家医保编码", Length = 100)]
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 是否医保药品
    /// </summary>
    [SugarColumn(ColumnName = "is_medicare", ColumnDescription = "是否医保药品")]
    public virtual int? IsMedicare { get; set; }
    
    /// <summary>
    /// 是否自制药
    /// </summary>
    [SugarColumn(ColumnName = "is_self", ColumnDescription = "是否自制药")]
    public virtual int? IsSelf { get; set; }
    
    /// <summary>
    /// 是否基本药物
    /// </summary>
    [SugarColumn(ColumnName = "is_basic", ColumnDescription = "是否基本药物")]
    public virtual int? IsBasic { get; set; }
    
    /// <summary>
    /// 是否皮试药品
    /// </summary>
    [SugarColumn(ColumnName = "is_skin_test", ColumnDescription = "是否皮试药品")]
    public virtual int? IsSkinTest { get; set; }
    
    /// <summary>
    /// 是否国谈药
    /// </summary>
    [SugarColumn(ColumnName = "is_country", ColumnDescription = "是否国谈药")]
    public virtual int? IsCountry { get; set; }
    
    /// <summary>
    /// 是否辅助药品
    /// </summary>
    [SugarColumn(ColumnName = "is_assist", ColumnDescription = "是否辅助药品")]
    public virtual int? IsAssist { get; set; }
    
    /// <summary>
    /// 是否临采药品
    /// </summary>
    [SugarColumn(ColumnName = "is_temporary", ColumnDescription = "是否临采药品")]
    public virtual int? IsTemporary { get; set; }
    
    /// <summary>
    /// 是否溶媒
    /// </summary>
    [SugarColumn(ColumnName = "is_solvent", ColumnDescription = "是否溶媒")]
    public virtual int? IsSolvent { get; set; }
    
    /// <summary>
    /// 是否新冠门诊药品
    /// </summary>
    [SugarColumn(ColumnName = "is_covid", ColumnDescription = "是否新冠门诊药品")]
    public virtual int? IsCovid { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "1 启用 2 停用")]
    public virtual int? Status { get; set; }
    [SugarColumn(ColumnName = "lower_limit", ColumnDescription = "库存下限")]
    public virtual int? LowerLimit { get; set; }
    [SugarColumn(ColumnName = "upper_limit", ColumnDescription = "库存上限")]
    public virtual int? UpperLimit { get; set; }
    
    
        
    
}
