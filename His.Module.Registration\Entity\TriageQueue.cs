﻿using Admin.NET.Core;
namespace His.Module.Registration.Entity;

/// <summary>
/// 分诊队列表，用于记录患者排队信息
/// </summary>
[Tenant("1300000000004")]
[SugarTable("triage_queue", "分诊队列表，用于记录患者排队信息")]
public class TriageQueue : EntityTenant
{
    /// <summary>
    /// 排班计划ID
    /// </summary>
    [SugarColumn(ColumnName = "scheduling_plan_id", ColumnDescription = "排班计划ID")]
    public virtual long? SchedulingPlanId { get; set; }
      
    /// <summary>
    /// 诊室ID
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室ID")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "科室名称", Length = 255)]
    public virtual string? DeptName { get; set; }
    /// <summary>
    /// 诊室ID
    /// </summary>
    [SugarColumn(ColumnName = "room_id", ColumnDescription = "诊室ID")]
    public virtual long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    [SugarColumn(ColumnName = "room_name", ColumnDescription = "诊室名称", Length = 255)]
    public virtual string? RoomName { get; set; }
    
    /// <summary>
    /// 分诊台ID
    /// </summary>
    [SugarColumn(ColumnName = "console_id", ColumnDescription = "分诊台ID")]
    public virtual long? ConsoleId { get; set; }
    
    /// <summary>
    /// 分诊台名称
    /// </summary>
    [SugarColumn(ColumnName = "console_name", ColumnDescription = "分诊台名称", Length = 255)]
    public virtual string? ConsoleName { get; set; }
    
    /// <summary>
    /// 排队号
    /// </summary>
    [SugarColumn(ColumnName = "queue_number", ColumnDescription = "排队号")]
    public virtual int? QueueNumber { get; set; }
    
    /// <summary>
    /// 状态（如排队中、已就诊等）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态（如排队中、已就诊等）")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 时间段ID
    /// </summary>
    [SugarColumn(ColumnName = "time_period_id", ColumnDescription = "时间段ID")]
    public virtual long? TimePeriodId { get; set; }
    
    /// <summary>
    /// 时间段编码
    /// </summary>
    [SugarColumn(ColumnName = "time_period_code", ColumnDescription = "时间段编码", Length = 64)]
    public virtual string? TimePeriodCode { get; set; }
    
    /// <summary>
    /// 时间段名称
    /// </summary>
    [SugarColumn(ColumnName = "time_period_name", ColumnDescription = "时间段名称", Length = 64)]
    public virtual string? TimePeriodName { get; set; }
    
    /// <summary>
    /// 时间段开始时间
    /// </summary>
    [SugarColumn(ColumnName = "time_period_start_time", ColumnDescription = "时间段开始时间")]
    public virtual DateTime? TimePeriodStartTime { get; set; }
    
    /// <summary>
    /// 时间段结束时间
    /// </summary>
    [SugarColumn(ColumnName = "time_period_end_time", ColumnDescription = "时间段结束时间")]
    public virtual DateTime? TimePeriodEndTime { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
