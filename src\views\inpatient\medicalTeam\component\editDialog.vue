﻿<script lang="ts" name="medicalTeam" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useMedicalTeamApi } from '/@/api/inpatient/medicalTeam';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
//父级传递来的函数，用于回调
import PinyinSelect from '/@/components/pinyinSelect/index.vue';
const basicInfoApi = useBasicInfoApi();
const emit = defineEmits(["reloadTable"]);
const medicalTeamApi = useMedicalTeamApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
	teamName: [{ required: true, message: '请选择医疗组名称！', trigger: 'blur', },],
	deptId: [{ required: true, message: '请选择所属科室ID！', trigger: 'blur', },],
	deptName: [{ required: true, message: '请选择组长！', trigger: 'blur', },],
	//teamType: [{ required: true, message: '请选择医疗组类型 字典医疗组！', trigger: 'blur', },],
});

// 页面加载时
onMounted(async () => {
	await basicInfoApi.getDepartments({ orgTypes: ['InpatientDept'] }).then(res => {
		state.dropdownData.deptList = res.data.result;
	});

});
const deptChange = async (value: any) => {
	if (value) {
		state.ruleForm.deptName = state.dropdownData.deptList.find((e: any) => e.id === value)?.name;
		await basicInfoApi.getUsers({ orgId: value }).then(res => {
			state.dropdownData.userList = res.data.result;
		});

	} else {
		state.ruleForm.deptName = '';
	}
};
const teamLeaderChange = async (value: any) => {
	if (value) {
		state.ruleForm.teamLeaderName = state.dropdownData.userList.find((e: any) => e.id === value)?.name;
	} else {
		state.ruleForm.teamLeaderName = '';
	}
}

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {};
	state.ruleForm = row.id ? await medicalTeamApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
	state.dropdownData.userList = [{ id: state.ruleForm.teamLeaderId, name: state.ruleForm.teamLeaderName }];

};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};
const changeTeamStatus = async (data: any) => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			const res = await medicalTeamApi.update(state.ruleForm);
			if (res.data.code === 200) {
				ElMessage.success(res.data.message);
				closeDialog();
			} else {
				ElMessage.error(res.data.message);
			}
		}
	})
}
// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await medicalTeamApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="medicalTeam-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="所属科室" prop="deptId">
							<PinyinSelect v-model="state.ruleForm.deptId" placeholder="请选择科室" @change="deptChange"
								:options="state.dropdownData.deptList" />

						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="医疗组名称" prop="teamName">
							<el-input v-model="state.ruleForm.teamName" placeholder="请输入医疗组名称" maxlength="100"
								show-word-limit clearable />
						</el-form-item>
					</el-col>


					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="医疗组类型 字典医疗组" prop="teamType">
							<el-input v-model="state.ruleForm.teamType" placeholder="请输入医疗组类型 字典医疗组" maxlength="100"
								show-word-limit clearable />
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="组长" prop="teamLeaderId">
							<PinyinSelect v-model="state.ruleForm.teamLeaderId" placeholder="请选择组长"
								@change="teamLeaderChange" :options="state.dropdownData.userList" />
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="组长" prop="teamLeaderName">
							<el-input v-model="state.ruleForm.teamLeaderName" placeholder="请输入组长" maxlength="20"
								show-word-limit clearable />
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="成立日期" prop="establishDate">
							<el-date-picker v-model="state.ruleForm.establishDate" type="date" placeholder="成立日期" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="状态" prop="status">

							<el-switch v-model="state.ruleForm.status" :active-value="1" :inactive-value="2"
								size="small" @change="changeTeamStatus(state.ruleForm)" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input type="textarea" v-model="state.ruleForm.remark" placeholder="请输入备注"
								maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>