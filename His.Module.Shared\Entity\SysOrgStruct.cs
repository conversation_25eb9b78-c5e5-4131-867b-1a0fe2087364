﻿using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 科室结构维护
/// </summary>
[Tenant("1300000000014")]
[SugarTable("sys_org_struct", "科室结构维护")]
public class SysOrgStruct : EntityTenant
{    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编号", Length = 255)]
    public virtual string? Code { get; set; }
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 255)]
    public virtual string? Name { get; set; }
    
    /// <summary>
    /// 父级id
    /// </summary>
    [SugarColumn(ColumnName = "parent_id", ColumnDescription = "父级id")]
    public virtual long? ParentId { get; set; }
    
    /// <summary>
    /// 父级名称
    /// </summary>
    [SugarColumn(ColumnName = "parent_name", ColumnDescription = "父级名称", Length = 255)]
    public virtual string? ParentName { get; set; }
    
    /// <summary>
    /// 层级
    /// </summary>
    [SugarColumn(ColumnName = "level", ColumnDescription = "层级")]
    public virtual int? Level { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
