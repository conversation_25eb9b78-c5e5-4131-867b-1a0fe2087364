﻿using Microsoft.AspNetCore.Http;

namespace His.Module.Shared.Service;

/// <summary>
/// 检查类别服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class CheckCategoryService : IDynamic<PERSON><PERSON><PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<CheckCategory> _checkCategoryRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public CheckCategoryService(SqlSugarRepository<CheckCategory> checkCategoryRep, ISqlSugarClient sqlSugarClient)
    {
        _checkCategoryRep = checkCategoryRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询检查类别 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询检查类别")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<CheckCategoryOutput>> Page(PageCheckCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.Code = input.Code?.Trim();
        input.Name = input.Name?.Trim().ToLower();
        var query = _checkCategoryRep.AsQueryable()
            .LeftJoin<ChargeCategory>((u, chargeCategory) => u.ChargeCategoryId == chargeCategory.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
            || u.Name.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select((u, chargeCategory) => new CheckCategoryOutput
            {
                Id = u.Id,
                Code = u.Code,
                Name = u.Name,
                PinyinCode = u.PinyinCode,
                WubiCode = u.WubiCode,
                ChargeCategoryId = u.ChargeCategoryId,
                ChargeCategoryFkDisplayName = $"{chargeCategory.Name}",
                Status = u.Status,
                OrderNo = u.OrderNo,
                CreateTime = u.CreateTime,
                UpdateTime = u.UpdateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
                Remark = u.Remark,
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取检查类别详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取检查类别详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<CheckCategory> Detail([FromQuery] QueryByIdCheckCategoryInput input)
    {
        return await _checkCategoryRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取检查类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    [DisplayName("获取检查类别列表")]
    public async Task<List<CheckCategory>> List()
    {
        return await _checkCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 增加检查类别 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加检查类别")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddCheckCategoryInput input)
    {
        var entity = input.Adapt<CheckCategory>();
        entity.Code = await _checkCategoryRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('check_category_code_seq')As varchar),3,'0')");
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        return await _checkCategoryRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新检查类别 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新检查类别")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateCheckCategoryInput input)
    {
        var entity = input.Adapt<CheckCategory>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _checkCategoryRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除检查类别 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除检查类别")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteCheckCategoryInput input)
    {
        var entity = await _checkCategoryRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _checkCategoryRep.FakeDeleteAsync(entity);   //假删除
        //await _checkCategoryRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除检查类别 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除检查类别")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteCheckCategoryInput> input)
    {
        var exp = Expressionable.Create<CheckCategory>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _checkCategoryRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _checkCategoryRep.FakeDeleteAsync(list);   //假删除
        //return await _checkCategoryRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置检查类别状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置检查类别状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetCheckCategoryStatus(SetCheckCategoryStatusInput input)
    {
        await _checkCategoryRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataCheckCategoryInput input)
    {
        var chargeCategoryIdData = await _checkCategoryRep.Context.Queryable<ChargeCategory>()
            .InnerJoinIF<CheckCategory>(input.FromPage, (u, r) => u.Id == r.ChargeCategoryId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.Name}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "chargeCategoryId", chargeCategoryIdData },
        };
    }

    /// <summary>
    /// 导出检查类别记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出检查类别记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageCheckCategoryInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportCheckCategoryOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "检查类别导出记录");
    }

    /// <summary>
    /// 下载检查类别数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载检查类别数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportCheckCategoryOutput>(), "检查类别导入模板", (_, info) =>
        {
            if (nameof(ExportCheckCategoryOutput.ChargeCategoryFkDisplayName) == info.Name) return _checkCategoryRep.Context.Queryable<ChargeCategory>().Select(u => $"{u.Name}").Distinct().ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入检查类别记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入检查类别记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportCheckCategoryInput, CheckCategory>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 收费类别
                    var chargeCategoryIdLabelList = pageItems.Where(x => x.ChargeCategoryFkDisplayName != null).Select(x => x.ChargeCategoryFkDisplayName).Distinct().ToList();
                    if (chargeCategoryIdLabelList.Any())
                    {
                        var chargeCategoryIdLinkMap = _checkCategoryRep.Context.Queryable<ChargeCategory>().Where(u => chargeCategoryIdLabelList.Contains($"{u.Name}")).ToList().ToDictionary(u => $"{u.Name}", u => u.Id);
                        pageItems.ForEach(e =>
                        {
                            e.ChargeCategoryId = chargeCategoryIdLinkMap.GetValueOrDefault(e.ChargeCategoryFkDisplayName ?? "");
                            if (e.ChargeCategoryId == null) e.Error = "收费类别链接失败";
                        });
                    }

                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.ChargeCategoryId == null)
                        {
                            x.Error = "收费类别不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<CheckCategory>>();

                    var storageable = _checkCategoryRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Code?.Length > 32, "编码长度不能超过32个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.Name), "名称不能为空")
                        .SplitError(it => it.Item.Name?.Length > 32, "名称长度不能超过32个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 20, "拼音码长度不能超过20个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 20, "五笔码长度不能超过20个字符")
                        .SplitError(it => it.Item.ChargeCategoryId == null, "收费类别不能为空")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}