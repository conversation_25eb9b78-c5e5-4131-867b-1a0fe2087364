﻿namespace His.Module.Registration;

/// <summary>
/// 诊台维护输出参数
/// </summary>
public class ClinicConsoleDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 诊台名称
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// 诊台编码
    /// </summary>
    public string? Code { get; set; }
    
    /// <summary>
    /// 诊室id
    /// </summary>
    public long? RoomId { get; set; }
    
    /// <summary>
    /// 诊室名称
    /// </summary>
    public string? RoomName { get; set; }
    
    /// <summary>
    /// 当前人数
    /// </summary>
    public int? CurrentCount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
