﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品大剂维护基础输入参数
/// </summary>
public class DrugBigFormBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 大剂型名称
    /// </summary>
    public virtual string? BigFormName { get; set; }
    
    /// <summary>
    /// 大剂型名称拼音
    /// </summary>
    public virtual string? BigFormNamePinyin { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 药品大剂维护分页查询输入参数
/// </summary>
public class PageDrugBigFormInput : BasePageInput
{
    /// <summary>
    /// 大剂型名称
    /// </summary>
    public string? BigFormName { get; set; }
    
    /// <summary>
    /// 大剂型名称拼音
    /// </summary>
    public string? BigFormNamePinyin { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品大剂维护增加输入参数
/// </summary>
public class AddDrugBigFormInput
{
    /// <summary>
    /// 大剂型名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "大剂型名称字符长度不能超过100")]
    public string? BigFormName { get; set; }
    
    /// <summary>
    /// 大剂型名称拼音
    /// </summary>
    [MaxLength(100, ErrorMessage = "大剂型名称拼音字符长度不能超过100")]
    public string? BigFormNamePinyin { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    public int? Status { get; set; }
    
}

/// <summary>
/// 药品大剂维护删除输入参数
/// </summary>
public class DeleteDrugBigFormInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品大剂维护更新输入参数
/// </summary>
public class UpdateDrugBigFormInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 大剂型名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "大剂型名称字符长度不能超过100")]
    public string? BigFormName { get; set; }
    
    /// <summary>
    /// 大剂型名称拼音
    /// </summary>    
    [MaxLength(100, ErrorMessage = "大剂型名称拼音字符长度不能超过100")]
    public string? BigFormNamePinyin { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>    
    public int? Status { get; set; }
    
}

/// <summary>
/// 药品大剂维护主键查询输入参数
/// </summary>
public class QueryByIdDrugBigFormInput : DeleteDrugBigFormInput
{
}

/// <summary>
/// 药品大剂维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugBigFormInput : BaseImportInput
{
    /// <summary>
    /// 大剂型名称
    /// </summary>
    [ImporterHeader(Name = "大剂型名称")]
    [ExporterHeader("大剂型名称", Format = "", Width = 25, IsBold = true)]
    public string? BigFormName { get; set; }
    
    /// <summary>
    /// 大剂型名称拼音
    /// </summary>
    [ImporterHeader(Name = "大剂型名称拼音")]
    [ExporterHeader("大剂型名称拼音", Format = "", Width = 25, IsBold = true)]
    public string? BigFormNamePinyin { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    [ImporterHeader(Name = "1 启用 2 停用")]
    [ExporterHeader("1 启用 2 停用", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
}
