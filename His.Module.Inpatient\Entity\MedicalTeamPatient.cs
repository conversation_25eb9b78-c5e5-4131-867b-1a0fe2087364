﻿using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 医疗组患者关联表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("medical_team_patient", "医疗组患者关联表")]
public class MedicalTeamPatient : EntityTenant
{
    /// <summary>
    /// 医疗组ID
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "team_id", ColumnDescription = "医疗组ID")]
    public virtual long TeamId { get; set; }
    
    /// <summary>
    /// 医疗组名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "team_name", ColumnDescription = "医疗组名称", Length = 255)]
    public virtual string TeamName { get; set; }
    
    /// <summary>
    /// 科室ID
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室ID")]
    public virtual long DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "科室名称", Length = 255)]
    public virtual string DeptName { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者名称", Length = 255)]
    public virtual string PatientName { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "inpatient_no", ColumnDescription = "住院号", Length = 255)]
    public virtual string InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "inpatient_serial_no", ColumnDescription = "住院流水号", Length = 255)]
    public virtual string InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 开始时间
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "start_time", ColumnDescription = "开始时间")]
    public virtual DateTime StartTime { get; set; }
    
    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(ColumnName = "end_time", ColumnDescription = "结束时间")]
    public virtual DateTime? EndTime { get; set; }
    
    /// <summary>
    /// 状态(0:结束,1:进行中)
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态(0:结束,1:进行中)")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
     
    
}
