﻿namespace His.Module.OutpatientDoctor;

/// <summary>
/// 处方主表基础输入参数
/// </summary>
public class PrescriptionMainBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 处方号
    /// </summary>
    public virtual string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    public virtual DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 处方类型
    /// </summary>
    public virtual string? PrescriptionType { get; set; }

    /// <summary>
    /// 处方名称
    /// </summary>
    public virtual string? PrescriptionName { get; set; }

    /// <summary>
    /// 门诊处方类型
    /// </summary>
    public virtual string? OutpatientPrescriptionType { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public virtual long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string? PatientName { get; set; }

    /// <summary>
    /// 挂号Id
    /// </summary>
    public virtual long? RegisterId { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public virtual long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public virtual long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生签名
    /// </summary>
    public virtual string? BillingDoctorSign { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public virtual long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public virtual DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 退费人员Id
    /// </summary>
    public virtual long? RefundStaffId { get; set; }

    /// <summary>
    /// 退费时间
    /// </summary>
    public virtual DateTime? RefundTime { get; set; }

    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    public virtual int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 诊断编码
    /// </summary>
    public virtual string? DiagnosticCode { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    public virtual string? DiagnosticName { get; set; }

    /// <summary>
    /// 次诊断1编码
    /// </summary>
    public virtual string? Diagnostic1Code { get; set; }

    /// <summary>
    /// 次诊断1名称
    /// </summary>
    public virtual string? Diagnostic1Name { get; set; }

    /// <summary>
    /// 次诊断2编码
    /// </summary>
    public virtual string? Diagnostic2Code { get; set; }

    /// <summary>
    /// 次诊断2名称
    /// </summary>
    public virtual string? Diagnostic2Name { get; set; }

    /// <summary>
    /// 中医诊断编码
    /// </summary>
    public virtual string? TcmDiagnosticCode { get; set; }

    /// <summary>
    /// 中医诊断名称
    /// </summary>
    public virtual string? TcmDiagnosticName { get; set; }

    /// <summary>
    /// 是否打印
    /// </summary>
    public virtual int? IsPrint { get; set; }

    /// <summary>
    /// 中药付数
    /// </summary>
    public virtual int? HerbsQuantity { get; set; }

    /// <summary>
    /// 中药煎法
    /// </summary>
    public virtual string? HerbsDecoction { get; set; }

    /// <summary>
    /// 是否代煎
    /// </summary>
    public virtual int? IsDecoction { get; set; }

    /// <summary>
    /// 打印时间
    /// </summary>
    public virtual DateTime? PrintTime { get; set; }

    /// <summary>
    /// 收费主表Id
    /// </summary>
    public virtual long? ChargeMainId { get; set; }

    /// <summary>
    /// 退费发票号
    /// </summary>
    public virtual string? RefundInvoiceNumber { get; set; }
}

/// <summary>
/// 处方主表分页查询输入参数
/// </summary>
public class PagePrescriptionMainInput : BasePageInput
{
    /// <summary>
    /// 处方号
    /// </summary>
    public string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间范围
    /// </summary>
    public DateTime?[] PrescriptionTimeRange { get; set; }

    /// <summary>
    /// 处方类型
    /// </summary>
    public string? PrescriptionType { get; set; }

    /// <summary>
    /// 处方名称
    /// </summary>
    public string? PrescriptionName { get; set; }

    /// <summary>
    /// 西药处方类型
    /// </summary>
    public string? WstrnMdcnPrescriptionType { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 挂号Id
    /// </summary>
    public long? RegisterId { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生签名
    /// </summary>
    public string? BillingDoctorSign { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费时间范围
    /// </summary>
    public DateTime?[] ChargeTimeRange { get; set; }

    /// <summary>
    /// 退费人员Id
    /// </summary>
    public long? RefundStaffId { get; set; }

    /// <summary>
    /// 退费时间范围
    /// </summary>
    public DateTime?[] RefundTimeRange { get; set; }

    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 诊断编码
    /// </summary>
    public string? DiagnosticCode { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosticName { get; set; }

    /// <summary>
    /// 次诊断1编码
    /// </summary>
    public string? Diagnostic1Code { get; set; }

    /// <summary>
    /// 次诊断1名称
    /// </summary>
    public string? Diagnostic1Name { get; set; }

    /// <summary>
    /// 次诊断2编码
    /// </summary>
    public string? Diagnostic2Code { get; set; }

    /// <summary>
    /// 次诊断2名称
    /// </summary>
    public string? Diagnostic2Name { get; set; }

    /// <summary>
    /// 中医诊断编码
    /// </summary>
    public string? TcmDiagnosticCode { get; set; }

    /// <summary>
    /// 中医诊断名称
    /// </summary>
    public string? TcmDiagnosticName { get; set; }

    /// <summary>
    /// 是否打印
    /// </summary>
    public int? IsPrint { get; set; }

    /// <summary>
    /// 中药付数
    /// </summary>
    public int? HerbsQuantity { get; set; }

    /// <summary>
    /// 中药煎法
    /// </summary>
    public string? HerbsDecoction { get; set; }

    /// <summary>
    /// 是否代煎
    /// </summary>
    public int? IsDecoction { get; set; }

    /// <summary>
    /// 打印时间范围
    /// </summary>
    public DateTime?[] PrintTimeRange { get; set; }

    /// <summary>
    /// 收费主表Id
    /// </summary>
    public long? ChargeMainId { get; set; }

    /// <summary>
    /// 退费发票号
    /// </summary>
    public string? RefundInvoiceNumber { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 处方主表增加输入参数
/// </summary>
public class AddPrescriptionMainInput
{
    public virtual long? Id { get; set; }
    /// <summary>
    /// 处方号
    /// </summary>
    [MaxLength(64, ErrorMessage = "处方号字符长度不能超过64")]
    public string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    public DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 处方类型
    /// </summary>
    [MaxLength(64, ErrorMessage = "处方类型字符长度不能超过64")]
    public string? PrescriptionType { get; set; }

    /// <summary>
    /// 处方名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "处方名称字符长度不能超过64")]
    public string? PrescriptionName { get; set; }

    // /// <summary>
    // /// 西药处方类型
    // /// </summary>
    // [MaxLength(64, ErrorMessage = "西药处方类型字符长度不能超过64")]
    // public string? WstrnMdcnPrescriptionType { get; set; }
    /// <summary>
    /// 处方类型
    /// </summary>
    [MaxLength(64, ErrorMessage = "处方类型字符长度不能超过64")]
    [Required(ErrorMessage = "处方类型不能为空")]
    public string? OutpatientPrescriptionType { get; set; }
    /// <summary>
    /// 就诊号
    /// </summary>
    [Required(ErrorMessage = "就诊号不能为空")]
    public string? VisitNo { get; set; }
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [Required(ErrorMessage = "就诊卡号不能为空")]
    public string?  CardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
    public string?  OutpatientNo { get; set; }
    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string? PatientName { get; set; }

    /// <summary>
    /// 挂号Id
    /// </summary>
    public long? RegisterId { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生签名
    /// </summary>
    public string? BillingDoctorSign { get; set; }
    //
    // /// <summary>
    // /// 收费人员Id
    // /// </summary>
    // public long? ChargeStaffId { get; set; }
    //
    // /// <summary>
    // /// 收费时间
    // /// </summary>
    // public DateTime? ChargeTime { get; set; }
    //
    // /// <summary>
    // /// 退费人员Id
    // /// </summary>
    // public long? RefundStaffId { get; set; }
    //
    // /// <summary>
    // /// 退费时间
    // /// </summary>
    // public DateTime? RefundTime { get; set; }
    //
    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }

    /// <summary>
    /// 诊断编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "诊断编码字符长度不能超过128")]
    public string? DiagnosticCode { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "诊断名称字符长度不能超过128")]
    public string? DiagnosticName { get; set; }

    /// <summary>
    /// 次诊断1编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "次诊断1编码字符长度不能超过128")]
    public string? Diagnostic1Code { get; set; }

    /// <summary>
    /// 次诊断1名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "次诊断1名称字符长度不能超过128")]
    public string? Diagnostic1Name { get; set; }

    /// <summary>
    /// 次诊断2编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "次诊断2编码字符长度不能超过128")]
    public string? Diagnostic2Code { get; set; }

    /// <summary>
    /// 次诊断2名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "次诊断2名称字符长度不能超过128")]
    public string? Diagnostic2Name { get; set; }

    /// <summary>
    /// 中医诊断编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "中医诊断编码字符长度不能超过128")]
    public string? TcmDiagnosticCode { get; set; }

    /// <summary>
    /// 中医诊断名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "中医诊断名称字符长度不能超过128")]
    public string? TcmDiagnosticName { get; set; }

    /// <summary>
    /// 中医证型编号
    /// </summary> 
    public virtual string? TcmSyndromeCode { get; set; }

    /// <summary>
    /// 中医证型名称
    /// </summary> 
    public virtual string? TcmSyndromeName { get; set; }
    /// <summary>
    /// 是否打印
    /// </summary>
    public int? IsPrint { get; set; }

    /// <summary>
    /// 中药付数
    /// </summary>
    public int? HerbsQuantity { get; set; }

    /// <summary>
    /// 中药煎法
    /// </summary>
    [MaxLength(128, ErrorMessage = "中药煎法字符长度不能超过128")]
    public string? HerbsDecoction { get; set; }

    /// <summary>
    /// 是否代煎
    /// </summary>
    public int? IsDecoction { get; set; }
    //
    // /// <summary>
    // /// 打印时间
    // /// </summary>
    // public DateTime? PrintTime { get; set; }
    //
    // /// <summary>
    // /// 收费主表Id
    // /// </summary>
    // public long? ChargeMainId { get; set; }
    //
    // /// <summary>
    // /// 退费发票号
    // /// </summary>
    // [MaxLength(32, ErrorMessage = "退费发票号字符长度不能超过32")]
    // public string? RefundInvoiceNumber { get; set; }
    /// <summary>
    /// 药房Id
    /// </summary>
    public long? StorageId { get; set; }

    /// <summary>
    /// 药房名称
    /// </summary>
    public string? StorageName { get; set; }
}

/// <summary>
/// 处方主表更新输入参数
/// </summary>
public class UpdatePrescriptionMainInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 处方号
    /// </summary>
    [MaxLength(64, ErrorMessage = "处方号字符长度不能超过64")]
    public string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    public DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 处方类型
    /// </summary>
    [MaxLength(64, ErrorMessage = "处方类型字符长度不能超过64")]
    public string? PrescriptionType { get; set; }

    /// <summary>
    /// 处方名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "处方名称字符长度不能超过64")]
    public string? PrescriptionName { get; set; }

    /// <summary>
    /// 西药处方类型
    /// </summary>
    [MaxLength(64, ErrorMessage = "西药处方类型字符长度不能超过64")]
    public string? WstrnMdcnPrescriptionType { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string? PatientName { get; set; }

    /// <summary>
    /// 挂号Id
    /// </summary>
    public long? RegisterId { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生签名
    /// </summary>
    public string? BillingDoctorSign { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 退费人员Id
    /// </summary>
    public long? RefundStaffId { get; set; }

    /// <summary>
    /// 退费时间
    /// </summary>
    public DateTime? RefundTime { get; set; }

    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }

    /// <summary>
    /// 诊断编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "诊断编码字符长度不能超过128")]
    public string? DiagnosticCode { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "诊断名称字符长度不能超过128")]
    public string? DiagnosticName { get; set; }

    /// <summary>
    /// 次诊断1编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "次诊断1编码字符长度不能超过128")]
    public string? Diagnostic1Code { get; set; }

    /// <summary>
    /// 次诊断1名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "次诊断1名称字符长度不能超过128")]
    public string? Diagnostic1Name { get; set; }

    /// <summary>
    /// 次诊断2编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "次诊断2编码字符长度不能超过128")]
    public string? Diagnostic2Code { get; set; }

    /// <summary>
    /// 次诊断2名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "次诊断2名称字符长度不能超过128")]
    public string? Diagnostic2Name { get; set; }

    /// <summary>
    /// 中医诊断编码
    /// </summary>
    [MaxLength(128, ErrorMessage = "中医诊断编码字符长度不能超过128")]
    public string? TcmDiagnosticCode { get; set; }

    /// <summary>
    /// 中医诊断名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "中医诊断名称字符长度不能超过128")]
    public string? TcmDiagnosticName { get; set; }

    /// <summary>
    /// 是否打印
    /// </summary>
    public int? IsPrint { get; set; }

    /// <summary>
    /// 中药付数
    /// </summary>
    public int? HerbsQuantity { get; set; }

    /// <summary>
    /// 中药煎法
    /// </summary>
    [MaxLength(128, ErrorMessage = "中药煎法字符长度不能超过128")]
    public string? HerbsDecoction { get; set; }

    /// <summary>
    /// 是否代煎
    /// </summary>
    public int? IsDecoction { get; set; }

    /// <summary>
    /// 打印时间
    /// </summary>
    public DateTime? PrintTime { get; set; }

    /// <summary>
    /// 收费主表Id
    /// </summary>
    public long? ChargeMainId { get; set; }

    /// <summary>
    /// 退费发票号
    /// </summary>
    [MaxLength(32, ErrorMessage = "退费发票号字符长度不能超过32")]
    public string? RefundInvoiceNumber { get; set; }
    /// <summary>
    /// 药房Id
    /// </summary>
    public long? StorageId { get; set; }

    /// <summary>
    /// 药房名称
    /// </summary>
    public string? StorageName { get; set; }
}

/// <summary>
/// 处方主表删除输入参数
/// </summary>
public class DeletePrescriptionMainInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 处方主表主键查询输入参数
/// </summary>
public class QueryByIdPrescriptionMainInput : DeletePrescriptionMainInput
{
}
/// <summary>
/// 处方结算
/// </summary>
public class PrescriptionChargeInput  
{    /// <summary>
    /// 处方Id
    /// </summary>
    [Required(ErrorMessage = "处方Id不能为空")]
    public long? PrescriptionId { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }
  
    /// <summary>
    /// 是否使用共济账户
    /// </summary>
    public bool EnableMutualAid { get; set; }
    /// <summary>
    /// 共济账户余额
    /// </summary>
    public decimal? MutualAidAccountBalance { get; set; }
    /// <summary>
    /// 共济账户支付
    /// </summary>
    public decimal?   MutualAidAccountBalancePayment { get; set; }
    
    /// <summary>
    /// 医保账户余额
    /// </summary>
    public decimal? MedicalInsuranceAccountBalance { get; set; }
 
    /// <summary>
    /// 医保账号支付
    /// </summary>
    public decimal? MedicalInsuranceAccountPayment { get; set; }
    /// <summary>
    /// 病人负担
    /// </summary>
    public decimal? PatientPayment { get; set; }
    /// <summary>
    /// 个人支付
    /// </summary> 
    public decimal? PersonalPayment { get; set; }

    /// <summary>
    /// 现金支付
    /// </summary>
    public long? PaymentMethod { get; set; }
    
    



}