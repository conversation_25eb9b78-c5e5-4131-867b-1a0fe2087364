﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 采购计划基础输入参数
/// </summary>
public class DrugPurchasePlanBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 采购计划号
    /// </summary>
    public virtual string? PlanNo { get; set; }
    
    /// <summary>
    /// 采购计划时间
    /// </summary>
    public virtual DateTime? PlanTime { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 采购计划分页查询输入参数
/// </summary>
public class PageDrugPurchasePlanInput : BasePageInput
{
    /// <summary>
    /// 采购计划号
    /// </summary>
    public string? PlanNo { get; set; }
    
    /// <summary>
    /// 采购计划时间范围
    /// </summary>
     public DateTime?[] PlanTimeRange { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 采购计划增加输入参数
/// </summary>
public class AddDrugPurchasePlanInput
{
    /// <summary>
    /// 采购计划号
    /// </summary>
    [MaxLength(100, ErrorMessage = "采购计划号字符长度不能超过100")]
    public string? PlanNo { get; set; }
    
    /// <summary>
    /// 采购计划时间
    /// </summary>
    public DateTime? PlanTime { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
}

/// <summary>
/// 采购计划删除输入参数
/// </summary>
public class DeleteDrugPurchasePlanInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 采购计划更新输入参数
/// </summary>
public class UpdateDrugPurchasePlanInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 采购计划号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "采购计划号字符长度不能超过100")]
    public string? PlanNo { get; set; }
    
    /// <summary>
    /// 采购计划时间
    /// </summary>    
    public DateTime? PlanTime { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>    
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
}

/// <summary>
/// 采购计划主键查询输入参数
/// </summary>
public class QueryByIdDrugPurchasePlanInput : DeleteDrugPurchasePlanInput
{
}

/// <summary>
/// 采购计划数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugPurchasePlanInput : BaseImportInput
{
    /// <summary>
    /// 采购计划号
    /// </summary>
    [ImporterHeader(Name = "采购计划号")]
    [ExporterHeader("采购计划号", Format = "", Width = 25, IsBold = true)]
    public string? PlanNo { get; set; }
    
    /// <summary>
    /// 采购计划时间
    /// </summary>
    [ImporterHeader(Name = "采购计划时间")]
    [ExporterHeader("采购计划时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? PlanTime { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等） 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等） 文本
    /// </summary>
    [Dict("StorageInOutStatus")]
    [ImporterHeader(Name = "状态（0 未处理 1 处理中 2 已完成等）")]
    [ExporterHeader("状态（0 未处理 1 处理中 2 已完成等）", Format = "", Width = 25, IsBold = true)]
    public string StatusDictLabel { get; set; }
    
}
