﻿using Admin.NET.Core.Service;
using DocumentFormat.OpenXml.Wordprocessing;
using His.Module.Pharmacy.Api.DrugInventory;
using His.Module.Pharmacy.Api.DrugInventory.Dto;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品库存表服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugInventoryService(
    SqlSugarRepository<DrugInventory> drugInventoryRep,
    SqlSugarRepository<SysOrgStorage> sysOrgStorageRep,
    ISqlSugarClient sqlSugarClient,
    SysDictTypeService sysDictTypeService,
    InventoryService inventoryService,
    SqlSugarRepository<OutpatientPrescriptionInventoryAllocation>
        outpatientPrescriptionInventoryAllocationRep)
    : IDynamicApiController, ITransient, IDrugInventoryApi
{
 

    /// <summary>
    /// 开方库存查询 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("开方库存查询")]
    [ApiDescriptionSettings(Name = "List"), HttpPost]
    public async Task<List<DrugInventoryOfPrescriptionOutput>> GetList(SendDrugInventoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToUpper();
        
        if(input.StorageId== null || input.StorageId.Count == 0)
        {
            var storage = await sysOrgStorageRep.AsQueryable()
                .Where(u => u.OrgId ==long.Parse( App.User.FindFirst(ClaimConst.OrgId).Value))
                .Select(u => u.StorageId).ToListAsync();
            input.StorageId =storage.Select(x => (long?)x).ToList(); ;
            if(storage.Count == 0)
                throw Oops.Oh("当前科室未设置药房");
        }

        var query = drugInventoryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.DrugCode.Contains(input.Keyword)
                     || u.DrugName.Contains(input.Keyword) ||
                     u.DrugType.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            //.Where(u => u.Quantity - u.PendingQuantity > 0)
            .Where(u => u.AvailableQuantity > 0)
            .InnerJoin<DrugDictionary>((u, dict) => u.DrugId == dict.Id
            )
            .Where((u, dict) =>
                dict.DrugNamePinyin.Contains(input.Keyword) || dict.GenericName.Contains(input.Keyword)
                                                            || dict.GenericNamePinyin.Contains(input.Keyword)
                                                            || dict.ProductName.Contains(input.Keyword) ||
                                                            dict.ProductNamePinyin.Contains(input.Keyword)
            )
            .InnerJoin<DrugStorage>((u, dict, storage) => u.StorageId == storage.Id)
            .WhereIF(input.StorageId != null && input.StorageId.Count != 0,
                (u, dict, storage) => input.StorageId.Contains(storage.Id))

            // .Where  ((u, dict, storage) =>storage.Id==1 )//input.StorageIds.Contains(storage.Id))
            // 库存大于0
            // .LeftJoin<EnterpriseDictionary>((u, storage, manufacturer) => u.ManufacturerId == manufacturer.Id)
            // .LeftJoin<EnterpriseDictionary>((u, storage, manufacturer, lastSupplier) => u.LastSupplierId == lastSupplier.Id)
            .Select((u, dict, storage) => new DrugInventoryOfPrescriptionOutput
            {
                InventoryId = u.Id,
                DrugId = u.DrugId,
                DrugCode = u.DrugCode,
                DrugName = u.DrugName,
                DrugType = u.DrugType,
                StorageId = u.StorageId,
                StorageName = storage.StorageName,
                ManufacturerId = u.ManufacturerId,
                ManufacturerName = u.ManufacturerName,
                Manufacturer = u.ManufacturerName,
               // InventoryQuantity = u.Quantity - u.PendingQuantity,
                InventoryQuantity=u.AvailableQuantity,
                Unit = u.Unit,
                Price = u.SalePrice,
                BatchNo = u.BatchNo,
                ProductionDate = u.ProductionDate,
                ExpirationDate = u.ExpirationDate,
                ApprovalNumber = u.ApprovalNumber,
                MedicineCode = dict.MedicineCode,
                TenantId = u.TenantId,
                DosageValue = dict.DosageValue,
                PackageQuantity = dict.PackageQuantity,
                OutpatientPackageQuantity = dict.OutpatientPackageQuantity == 0
                    ? dict.PackageQuantity
                    : dict.OutpatientPackageQuantity,
                InpatientPackageQuantity = dict.InpatientPackageQuantity == 0
                    ? dict.PackageQuantity
                    : dict.InpatientPackageQuantity,
                DosageUnit = dict.DosageUnit,
                Spec = dict.OutpatientSpec, // 门诊规格
                DrugFreq = dict.DrugFreq,
                DrugRoute = dict.DrugRoute,
                DrugForm = dict.DrugForm,
                IsSkinTest = dict.IsSkinTest,
                DrugCategoryCode = dict.DrugCategoryCode, 
                AntibacterialLevel = dict.AntibacterialLevel,
            });
        return await query.OrderBy(u => u.ExpirationDate).Take(input.Limit ?? 20).ToListAsync();
    }

    [DisplayName("开方锁定库存")]
    [ApiDescriptionSettings(Name = "Lock"), HttpPost]
    [UnitOfWork]
    public async Task<bool> LockInventory(List<LockDrugInventoryInput> list)
    {
        var deptCode = App.User.FindFirst(ClaimConst.OrgName);
        // 方式一 ： 开药查询库存数据不合并。 直接使用库存id处理
        foreach (var item in list)
        {
            var result = await inventoryService.UpdateInventoryAsync(item);
            if (result)
            {
                //库存分配记录
                var allocation = item.Adapt<OutpatientPrescriptionInventoryAllocation>();
                allocation.Status = 0;
                allocation.AllocatedQuantity = item.Quantity;
                // var allocation = new OutpatientPrescriptionInventoryAllocation()
                // {
                //     InventoryId = item.InventoryId,
                //     DrugId = item.DrugId,
                //     PrescriptionId = item.PrescriptionId,
                //     PrescriptionDetailId = item.PrescriptionDetailId,
                //     AllocatedQuantity = item.Quantity,
                //     StorageId=item.StorageId,
                //     Status = 0,
                // };
                await outpatientPrescriptionInventoryAllocationRep.InsertAsync(allocation);
            }
        }

        // 过于复杂后期实现  wangkai 2025年3月15日23:50
        // 方式二： 合并库存数据，一个药品存在多个批次 ,新增处方批次分配表，发药时根据该表扣减库存 
        // 查找数量符合的
        // foreach (var item in list)
        // {
        //     var result = true;
        //     //拆分开药数量
        //     var unLockQuantity= item.Quantity;
        //     var batchQuantity= item.Quantity;
        //     while  (result)
        //     {
        //         // 查找数量符合的
        //         result=   await _drugInventoryRep.UpdateAsync(u =>
        //                 new DrugInventory()
        //                 {
        //                     PendingQuantity = u.PendingQuantity+  batchQuantity,
        //                 }, u => u.Id == item.InventoryId && u.Quantity >=u.PendingQuantity+ batchQuantity
        //         );
        //         if (!result)
        //         {
        //             batchQuantity -= 1;
        //             result = true;//减一 后继续
        //         }
        //         else //分批扣减库存
        //         { //未扣除数量
        //             unLockQuantity-= batchQuantity;
        //            if(unLockQuantity==0)
        //                break;
        //             batchQuantity=unLockQuantity;
        //         }
        //     }
        //     // 根据处方明细id保存锁定的库存记录，
        //     /*
        //      *处方批次分配表 (prescription_batch_allocation)
        //         id (主键)
        //         prescription_detail_id (处方明细 ID)
        //         inventory_id (库存 ID)
        //         batch_number (批次号)
        //         allocated_quantity (分配数量)
        //                      * 
        //      */
        //     
        // }
        return true;
    }

    /// <summary>
    /// 单条处理 事务在调用方
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    [DisplayName("开方锁定库存")]
    [ApiDescriptionSettings(Name = "Lock",IgnoreApi=true), HttpPost]
    public async Task<bool> LockInventory(LockDrugInventoryInput item)
    {
        // 方式一 ： 开药查询库存数据不合并。 直接使用库存id处理
        var result = await inventoryService.UpdateInventoryAsync(item);
        if (result)
        {
            //库存分配记录
            var allocation = item.Adapt<OutpatientPrescriptionInventoryAllocation>();
            allocation.Status = 0;
            allocation.AllocatedQuantity = item.Quantity;
            await outpatientPrescriptionInventoryAllocationRep.InsertAsync(allocation);
        }

        return true;
    }
    /// <summary>
    /// 移除库存锁定
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    [ApiDescriptionSettings(Name = "RemoveLock",IgnoreApi=true), HttpPost]
    public async Task<bool> RemoveLockInventory(LockDrugInventoryInput item)
    {
        // 设置为付数，修改分配记录状态
    
 
         item.Quantity = -item.Quantity;
         await outpatientPrescriptionInventoryAllocationRep
            .UpdateAsync(u => new OutpatientPrescriptionInventoryAllocation()
                {
                    IsDelete = true
                },
                u => u.PrescriptionId == item.PrescriptionId &&
                     u.PrescriptionDetailId == item.PrescriptionDetailId
            );
 
        // 更新锁定库存
         return await inventoryService.UpdateInventoryAsync(item);
    }

    /// <summary>
    /// 分页查询药品库存表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品库存表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugInventoryOutput>> Page(PageDrugInventoryInput input)
    {
        if (input.ZeroQuantity == null)
            input.ZeroQuantity = 0; // 只查非0库存

        input.Keyword = input.Keyword?.Trim();
        var query = drugInventoryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) ||
                     u.DrugType.Contains(input.Keyword) || u.ManufacturerName.Contains(input.Keyword) ||
                     u.ApprovalNumber.Contains(input.Keyword) || u.MedicineCode.Contains(input.Keyword) ||
                     u.LastSupplierName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ManufacturerName),
                u => u.ManufacturerName.Contains(input.ManufacturerName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApprovalNumber),
                u => u.ApprovalNumber.Contains(input.ApprovalNumber.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicineCode),
                u => u.MedicineCode.Contains(input.MedicineCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.LastSupplierName),
                u => u.LastSupplierName.Contains(input.LastSupplierName.Trim()))
            .Where(
                u =>
                    (input.ZeroQuantity == 1 && u.Quantity == 0) // 只查0库存
                    || (input.ZeroQuantity == 0 && u.Quantity > 0) // 只查非0库存
                    || (input.ZeroQuantity == 2 && u.Quantity >= 0) // 全部
            )
            .WhereIF(input.ManufacturerId != null, u => u.ManufacturerId == input.ManufacturerId)
            .WhereIF(input.ExpirationDateRange?.Length == 2,
                u => u.ExpirationDate >= input.ExpirationDateRange[0] &&
                     u.ExpirationDate <= input.ExpirationDateRange[1])
            .WhereIF(input.LastSupplierId != null, u => u.LastSupplierId == input.LastSupplierId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .WhereIF(!input.StorageId.Equals(0L), u => u.StorageId == input.StorageId)
            .InnerJoin<DrugStorage>((u, storage) => u.StorageId == storage.Id)
            .LeftJoin<EnterpriseDictionary>((u, storage, manufacturer) => u.ManufacturerId == manufacturer.Id)
            .LeftJoin<EnterpriseDictionary>((u, storage, manufacturer, lastSupplier) =>
                u.LastSupplierId == lastSupplier.Id)
            .Select((u, storage, manufacturer, lastSupplier) => new DrugInventoryOutput
            {
                Id = u.Id,
                DrugId = u.DrugId,
                DrugCode = u.DrugCode,
                DrugName = u.DrugName,
                DrugType = u.DrugType,
                StorageId = u.StorageId,
                StorageFkDisplayName = $"{storage.StorageName}",
                StorageCode = u.StorageCode,
                StorageName = u.StorageName,
                Spec = u.Spec,
                ManufacturerId = u.ManufacturerId,
                ManufacturerFkDisplayName = $"{manufacturer.EnterpriseName}",
                ManufacturerName = u.ManufacturerName,
                Quantity = u.Quantity,
                Unit = u.Unit,
                PendingQuantity = u.PendingQuantity,
                SalePrice = u.SalePrice,
                TotalSalePrice = u.TotalSalePrice,
                PurchasePrice = u.PurchasePrice,
                TotalPurchasePrice = u.TotalPurchasePrice,
                BatchNo = u.BatchNo,
                ProductionDate = u.ProductionDate,
                ExpirationDate = u.ExpirationDate,
                ApprovalNumber = u.ApprovalNumber,
                MedicineCode = u.MedicineCode,
                LastSupplierId = u.LastSupplierId,
                LastSupplierFkDisplayName = $"{lastSupplier.EnterpriseName}",
                LastSupplierName = u.LastSupplierName,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品库存表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品库存表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugInventory> Detail([FromQuery] QueryByIdDrugInventoryInput input)
    {
        return await drugInventoryRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品库存表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品库存表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugInventoryInput input)
    {
        var entity = input.Adapt<DrugInventory>();
        return await drugInventoryRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品库存表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品库存表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugInventoryInput input)
    {
        var entity = input.Adapt<DrugInventory>();
        await drugInventoryRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }
    //
    // /// <summary>
    // /// 删除药品库存表 ❌
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("删除药品库存表")]
    // [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    // public async Task Delete(DeleteDrugInventoryInput input)
    // {
    //     var entity = await _drugInventoryRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
    //     await _drugInventoryRep.FakeDeleteAsync(entity);   //假删除
    //     //await _drugInventoryRep.DeleteAsync(entity);   //真删除
    // }

    /// <summary>
    /// 批量删除药品库存表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品库存表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteDrugInventoryInput> input)
    {
        var exp = Expressionable.Create<DrugInventory>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await drugInventoryRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await drugInventoryRep.FakeDeleteAsync(list); //假删除
        //return await _drugInventoryRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataDrugInventoryInput input)
    {
        var storageIdData = await drugInventoryRep.Context.Queryable<DrugStorage>()
            //  .InnerJoinIF<DrugInventory>(input.FromPage, (u, r) => u.Id == r.StorageId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.StorageName}"
            }).ToListAsync();
        var manufacturerIdData = await drugInventoryRep.Context.Queryable<EnterpriseDictionary>()
            .InnerJoinIF<DrugInventory>(input.FromPage, (u, r) => u.Id == r.ManufacturerId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
        var lastSupplierIdData = await drugInventoryRep.Context.Queryable<EnterpriseDictionary>()
            .InnerJoinIF<DrugInventory>(input.FromPage, (u, r) => u.Id == r.LastSupplierId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "storageId", storageIdData },
            { "manufacturerId", manufacturerIdData },
            { "lastSupplierId", lastSupplierIdData },
        };
    }

    /// <summary>
    /// 导出药品库存表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品库存表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugInventoryInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugInventoryOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var drugTypeDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result
            .ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => { e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType); });
        return ExcelHelper.ExportTemplate(list, "药品库存表导出记录");
    }

    /// <summary>
    /// 下载药品库存表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品库存表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugInventoryOutput>(), "药品库存表导入模板", (_, info) =>
        {
            if (nameof(ExportDrugInventoryOutput.StorageFkDisplayName) == info.Name)
                return drugInventoryRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}").Distinct()
                    .ToList();
            if (nameof(ExportDrugInventoryOutput.ManufacturerFkDisplayName) == info.Name)
                return drugInventoryRep.Context.Queryable<EnterpriseDictionary>().Select(u => $"{u.EnterpriseName}")
                    .Distinct().ToList();
            if (nameof(ExportDrugInventoryOutput.LastSupplierFkDisplayName) == info.Name)
                return drugInventoryRep.Context.Queryable<EnterpriseDictionary>().Select(u => $"{u.EnterpriseName}")
                    .Distinct().ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入药品库存表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品库存表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var drugTypeDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportDrugInventoryInput, DrugInventory>(file,
                (list, markerErrorAction) =>
                {
                    sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                    {
                        // 链接 药房ID
                        var storageIdLabelList = pageItems.Where(x => x.StorageFkDisplayName != null)
                            .Select(x => x.StorageFkDisplayName).Distinct().ToList();
                        if (storageIdLabelList.Any())
                        {
                            var storageIdLinkMap = drugInventoryRep.Context.Queryable<DrugStorage>()
                                .Where(u => storageIdLabelList.Contains($"{u.StorageName}")).ToList()
                                .ToDictionary(u => $"{u.StorageName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.StorageId = storageIdLinkMap.GetValueOrDefault(e.StorageFkDisplayName ?? "");
                                if (e.StorageId == null) e.Error = "药房ID链接失败";
                            });
                        }

                        // 链接 生产厂家ID
                        var manufacturerIdLabelList = pageItems.Where(x => x.ManufacturerFkDisplayName != null)
                            .Select(x => x.ManufacturerFkDisplayName).Distinct().ToList();
                        if (manufacturerIdLabelList.Any())
                        {
                            var manufacturerIdLinkMap = drugInventoryRep.Context.Queryable<EnterpriseDictionary>()
                                .Where(u => manufacturerIdLabelList.Contains($"{u.EnterpriseName}")).ToList()
                                .ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.ManufacturerId =
                                    manufacturerIdLinkMap.GetValueOrDefault(e.ManufacturerFkDisplayName ?? "");
                                if (e.ManufacturerId == null) e.Error = "生产厂家ID链接失败";
                            });
                        }

                        // 链接 最后一次供应商ID
                        var lastSupplierIdLabelList = pageItems.Where(x => x.LastSupplierFkDisplayName != null)
                            .Select(x => x.LastSupplierFkDisplayName).Distinct().ToList();
                        if (lastSupplierIdLabelList.Any())
                        {
                            var lastSupplierIdLinkMap = drugInventoryRep.Context.Queryable<EnterpriseDictionary>()
                                .Where(u => lastSupplierIdLabelList.Contains($"{u.EnterpriseName}")).ToList()
                                .ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.LastSupplierId =
                                    lastSupplierIdLinkMap.GetValueOrDefault(e.LastSupplierFkDisplayName ?? "");
                                if (e.LastSupplierId == null) e.Error = "最后一次供应商ID链接失败";
                            });
                        }

                        // 映射字典值
                        foreach (var item in pageItems)
                        {
                            if (string.IsNullOrWhiteSpace(item.DrugTypeDictLabel)) continue;
                            item.DrugType = drugTypeDictMap.GetValueOrDefault(item.DrugTypeDictLabel);
                            if (item.DrugType == null) item.Error = "药品类型字典映射失败";
                        }

                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x => { return true; }).Adapt<List<DrugInventory>>();

                        var storageable = drugInventoryRep.Context.Storageable(rows)
                            .SplitError(it => it.Item.DrugCode?.Length > 100, "药品编码长度不能超过100个字符")
                            .SplitError(it => it.Item.DrugName?.Length > 100, "药品名称长度不能超过100个字符")
                            .SplitError(it => it.Item.DrugType?.Length > 100, "药品类型长度不能超过100个字符")
                            .SplitError(it => it.Item.StorageCode?.Length > 100, "药房编码长度不能超过100个字符")
                            .SplitError(it => it.Item.StorageName?.Length > 100, "药房名称长度不能超过100个字符")
                            .SplitError(it => it.Item.Spec?.Length > 100, "规格长度不能超过100个字符")
                            .SplitError(it => it.Item.ManufacturerName?.Length > 100, "生产厂家名称长度不能超过100个字符")
                            .SplitError(it => it.Item.Unit?.Length > 100, "单位长度不能超过100个字符")
                            .SplitError(it => it.Item.BatchNo?.Length > 100, "批号长度不能超过100个字符")
                            .SplitError(it => it.Item.ApprovalNumber?.Length > 100, "批准文号长度不能超过100个字符")
                            .SplitError(it => it.Item.MedicineCode?.Length > 100, "国家医保编码长度不能超过100个字符")
                            .SplitError(it => it.Item.LastSupplierName?.Length > 100, "最后一次供应商名称长度不能超过100个字符")
                            .SplitInsert(_ => true)
                            .ToStorage();

                        storageable.BulkCopy();
                        storageable.BulkUpdate();

                        // 标记错误信息
                        markerErrorAction.Invoke(storageable, pageItems, rows);
                    });
                });

            return stream;
        }
    }
}