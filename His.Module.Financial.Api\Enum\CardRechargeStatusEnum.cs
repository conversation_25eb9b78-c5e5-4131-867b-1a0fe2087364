﻿using System.ComponentModel;

namespace His.Module.Financial.Enum;

/// <summary>
/// 卡充值状态枚举
/// </summary>
[Description("卡充值状态枚举")]
public enum CardRechargeStatusEnum
{
    /// <summary>
    /// 充值
    /// </summary>
    [Description("充值")]
    Recharge = 0,

    /// <summary>
    /// 红冲
    /// </summary>
    [Description("红冲")]
    HongChong = 1,

    /// <summary>
    /// 扣款
    /// </summary>
    [Description("扣款")]
    Deduction = 2,

    /// <summary>
    /// 退费
    /// </summary>
    [Description("退费")]
    Refund = 3,

    /// <summary>
    /// 退卡余额
    /// </summary>
    [Description("退卡余额")]
    CardRefundBalance = 4,
}