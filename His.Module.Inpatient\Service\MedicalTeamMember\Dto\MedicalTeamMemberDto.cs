﻿namespace His.Module.Inpatient;

/// <summary>
/// 医疗组成员表输出参数
/// </summary>
public class MedicalTeamMemberDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 医疗组ID
    /// </summary>
    public long TeamId { get; set; }
    
    /// <summary>
    /// 成员ID
    /// </summary>
    public long StaffId { get; set; }
    
    /// <summary>
    /// 成员名称
    /// </summary>
    public string StaffName { get; set; }
    
    /// <summary>
    /// 角色类型(1:组长,2:副组长,3:主治医师,4:住院医师,5:护士等)
    /// </summary>
    public string? RoleType { get; set; }
    
    /// <summary>
    /// 加入日期
    /// </summary>
    public DateTime? JoinDate { get; set; }
    
    /// <summary>
    /// 离开日期
    /// </summary>
    public DateTime? LeaveDate { get; set; }
    
    /// <summary>
    /// 状态(1:启用 2:停用,)
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建用户ID
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建用户名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 更新用户ID
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 更新用户名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 是否删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
