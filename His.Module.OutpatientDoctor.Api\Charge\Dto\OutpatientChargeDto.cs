using System.ComponentModel.DataAnnotations;
namespace His.Module.OutpatientDoctor.Api.Charge.Dto;

public class OutpatientChargeDto
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [Required(ErrorMessage = "患者Id不能为空")]
    public long? PatientId { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary> 
    [Required(ErrorMessage = "就诊Id不能为空")]
    public long? RegisterId { get; set; }

    /// <summary>
    /// 就诊卡Id
    /// </summary> 
    [Required(ErrorMessage = "就诊卡Id不能为空")]
    public long? CardId { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// 总金额
    /// </summary> 
    [Required(ErrorMessage = "总金额不能为空")]
    public decimal? TotalAmount { get; set; }

    /// <summary>
    /// 支付渠道
    /// </summary> 
    [Required(ErrorMessage = "支付渠道不能为空")]
    public int PayChannel { get; set; }

    /// <summary>
    /// 医保支付 金额
    /// </summary> 
    [Required(ErrorMessage = "医保支付金额不能为空")]
    public decimal PayAmount1 { get; set; }

    /// <summary>
    /// 其他支付方式2金额
    /// </summary> 
    [Required(ErrorMessage = "其他支付方式2金额不能为空")]
    public decimal PayAmount2 { get; set; }

    /// <summary>
    /// 支付方式
    /// </summary>
    [Required(ErrorMessage = "支付方式不能为空")]
    public long? PaymentMethod { get; set; }

    /// <summary>
    /// 卡号
    /// </summary>
    [Required(ErrorMessage = "卡号不能为空")]
    public string? CardNo { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [Required(ErrorMessage = "就诊号不能为空")]
    public string? VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [Required(ErrorMessage = "门诊号不能为空")]
    public string? OutpatientNo { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
 //   [Required(ErrorMessage = "执行科室Id不能为空")]
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
  //  [Required(ErrorMessage = "执行医生Id不能为空")]
    public long? ExecuteDoctorId { get; set; }

    
    /// <summary>
    /// 执行时间
    /// </summary>
    [Required(ErrorMessage = "开单时间不能为空")]
    public DateTime? ExecuteTime { get; set; }
    /// <summary>
    /// 开单科室Id
    /// </summary>
    [Required(ErrorMessage = "开单科室Id不能为空")]
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    [Required(ErrorMessage = "开单医生Id不能为空")]
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单类型  处方，处置，检验，检查 
    /// </summary>
    [Required(ErrorMessage = "开单类型不能为空")]
    public string? BillingType { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    [Required(ErrorMessage = "开单时间不能为空")]
    public DateTime? BillingTime { get; set; }

        
    /// <summary>
    /// 单据Id(处方id)
    /// </summary> 
    public   long? BillingId { get; set; }
    
    /// <summary>
    /// 单据号
    /// </summary> 
    public   string? BillingNo { get; set; }
    
    /// <summary>
    /// 明细列表
    /// </summary>
    [Required(ErrorMessage = "明细列表不能为空")]
    public List<OutpatientChargeDetailDto> Details { get; set; }
}
