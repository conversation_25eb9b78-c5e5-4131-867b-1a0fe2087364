namespace His.Module.Registration;

public class UsePlanTempInput
{
    
    /// <summary>
    /// 科室id
    /// </summary>
    [Required(ErrorMessage = "科室id不能为空")]
    public long? DeptId { get; set; }
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 星期几
    /// </summary>
    [Required( ErrorMessage = "星期不能为空")]
    public string? WeekDay { get; set; }
    
    /// <summary>
    /// 门诊日期
    /// </summary>
    [Required( ErrorMessage = "日期不能为空")]
    public DateTime? OutpatientDate { get; set; }
}

