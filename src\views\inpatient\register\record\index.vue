﻿<script lang="ts" setup name="inpatientRegister">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useInpatientRegisterApi } from '/@/api/inpatient/inpatientRegister';
import editDialog from '/@/views/inpatient/register/record/component/editDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from '/@/components/table/importData.vue';

const inpatientRegisterApi = useInpatientRegisterApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
	exportLoading: false,
	tableLoading: false,
	stores: {},
	showAdvanceQueryUI: false,
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'createTime', // 默认的排序字段
		order: 'descending', // 排序方向
		descStr: 'descending', // 降序排序的关键字符
	},
	tableData: [],
});

// 页面加载时
onMounted(async () => {
	const data = (await inpatientRegisterApi.getDropdownData(true).then((res) => res.data.result)) ?? {};
	state.dropdownData.patientId = data.patientId;
});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	const result = await inpatientRegisterApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
	state.tableParams.total = result?.total;
	state.tableData = result?.items ?? [];
	state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

// 删除
const delInpatientRegister = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await inpatientRegisterApi.delete({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 批量删除
const batchDelInpatientRegister = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await inpatientRegisterApi.batchDelete(state.selectData.map((u) => ({ id: u.id }))).then((res) => {
				ElMessage.success(`成功批量删除${res.data.result}条记录`);
				handleQuery();
			});
		})
		.catch(() => {});
};

// 导出数据
const exportInpatientRegisterCommand = async (command: string) => {
	try {
		state.exportLoading = true;
		if (command === 'select') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams, { selectKeyList: state.selectData.map((u) => u.id) });
			await inpatientRegisterApi.exportData(params).then((res) => downloadStreamFile(res));
		} else if (command === 'current') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams);
			await inpatientRegisterApi.exportData(params).then((res) => downloadStreamFile(res));
		} else if (command === 'all') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams, { page: 1, pageSize: 99999999 });
			await inpatientRegisterApi.exportData(params).then((res) => downloadStreamFile(res));
		}
	} finally {
		state.exportLoading = false;
	}
};

handleQuery();
</script>
<template>
	<div class="inpatientRegister-container" v-loading="state.exportLoading">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="患者ID">
							<el-select clearable filterable v-model="state.tableQueryParams.patientId" placeholder="请选择患者ID">
								<el-option v-for="(item, index) in state.dropdownData.patientId ?? []" :key="index" :value="item.value" :label="item.label" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="患者编号">
							<el-input v-model="state.tableQueryParams.patientNo" clearable placeholder="请输入患者编号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="患者姓名">
							<el-input v-model="state.tableQueryParams.patientName" clearable placeholder="请输入患者姓名" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="证件类型">
							<el-input v-model="state.tableQueryParams.cardType" clearable placeholder="请输入证件类型" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="证件类型">
							<g-sys-dict v-model="state.tableQueryParams.idCardType" code="CardTypeEnum" render-as="select" placeholder="请选择证件类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="证件号码">
							<el-input v-model="state.tableQueryParams.idCardNo" clearable placeholder="请输入证件号码" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="保险号">
							<el-input v-model="state.tableQueryParams.insuranceNo" clearable placeholder="请输入保险号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="就诊卡ID">
							<el-input v-model="state.tableQueryParams.medicalCardId" clearable placeholder="请输入就诊卡ID" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="就诊卡号">
							<el-input v-model="state.tableQueryParams.medicalCardNo" clearable placeholder="请输入就诊卡号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="门诊号">
							<el-input v-model="state.tableQueryParams.outpatientNo" clearable placeholder="请输入门诊号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="住院号">
							<el-input v-model="state.tableQueryParams.inpatientNo" clearable placeholder="请输入住院号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="住院流水号">
							<el-input v-model="state.tableQueryParams.inpatientSerialNo" clearable placeholder="请输入住院流水号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="住院次数">
							<el-input-number v-model="state.tableQueryParams.inpatientTimes" clearable placeholder="请输入住院次数" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="病案号">
							<el-input v-model="state.tableQueryParams.medicalRecordNo" clearable placeholder="请输入病案号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="费别ID">
							<g-sys-dict v-model="state.tableQueryParams.feeId" code="" render-as="select" placeholder="请选择费别ID" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="医生ID">
							<el-input v-model="state.tableQueryParams.doctorId" clearable placeholder="请输入医生ID" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="医生姓名">
							<el-input v-model="state.tableQueryParams.doctorName" clearable placeholder="请输入医生姓名" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="科室ID">
							<el-input v-model="state.tableQueryParams.deptId" clearable placeholder="请输入科室ID" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="科室名称">
							<el-input v-model="state.tableQueryParams.deptName" clearable placeholder="请输入科室名称" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="主治医生ID">
							<el-input v-model="state.tableQueryParams.mainDoctorId" clearable placeholder="请输入主治医生ID" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="主治医生姓名">
							<el-input v-model="state.tableQueryParams.mainDoctorName" clearable placeholder="请输入主治医生姓名" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="入院诊断代码">
							<el-input v-model="state.tableQueryParams.inpatientDiagnosticCode" clearable placeholder="请输入入院诊断代码" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="入院诊断名称">
							<el-input v-model="state.tableQueryParams.inpatientDiagnosticName" clearable placeholder="请输入入院诊断名称" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="次要诊断代码">
							<el-input v-model="state.tableQueryParams.secondaryDiagnosticCode" clearable placeholder="请输入次要诊断代码" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="次要诊断名称">
							<el-input v-model="state.tableQueryParams.secondaryDiagnosticName" clearable placeholder="请输入次要诊断名称" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="入院途径">
							<el-input v-model="state.tableQueryParams.inpatientWay" clearable placeholder="请输入入院途径" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="入院时间">
							<el-date-picker
								type="daterange"
								v-model="state.tableQueryParams.inpatientTimeRange"
								value-format="YYYY-MM-DD HH:mm:ss"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								:default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
							/>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="是否允许欠费">
							<el-input v-model="state.tableQueryParams.allowArrears" clearable placeholder="请输入是否允许欠费" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="欠费上限">
							<el-input-number v-model="state.tableQueryParams.arrearsLimit" clearable placeholder="请输入欠费上限" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="担保人">
							<el-input v-model="state.tableQueryParams.guaranteePerson" clearable placeholder="请输入担保人" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="新生儿出生体重g">
							<el-input-number v-model="state.tableQueryParams.newbornBirthWeight1" clearable placeholder="请输入新生儿出生体重g" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="新生儿出生体重单位g">
							<el-input-number v-model="state.tableQueryParams.newbornBirthWeight2" clearable placeholder="请输入新生儿出生体重单位g" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="新生儿出生体重单位g">
							<el-input-number v-model="state.tableQueryParams.newbornBirthWeight3" clearable placeholder="请输入新生儿出生体重单位g" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="新生儿入院体重g">
							<el-input-number v-model="state.tableQueryParams.newbornInpatientWeight" clearable placeholder="请输入新生儿入院体重g" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="是否有医保卡">
							<el-input-number v-model="state.tableQueryParams.hasMedicalInsurance" clearable placeholder="请输入是否有医保卡" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="状态">
							<el-input-number v-model="state.tableQueryParams.status" clearable placeholder="请输入状态" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'inpatientRegister:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" v-if="!state.showAdvanceQueryUI" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" v-if="state.showAdvanceQueryUI" style="margin-left: 5px"> 隐藏 </el-button>
								<el-button
									type="danger"
									style="margin-left: 5px"
									icon="ele-Delete"
									@click="batchDelInpatientRegister"
									:disabled="state.selectData.length == 0"
									v-auth="'inpatientRegister:batchDelete'"
								>
									删除
								</el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef.openDialog(null, '新增住院登记')" v-auth="'inpatientRegister:add'"> 新增 </el-button>
								<el-dropdown :show-timeout="70" :hide-timeout="50" @command="exportInpatientRegisterCommand">
									<el-button type="primary" style="margin-left: 5px" icon="ele-FolderOpened" v-reclick="20000" v-auth="'inpatientRegister:export'"> 导出 </el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item command="select" :disabled="state.selectData.length == 0">导出选中</el-dropdown-item>
											<el-dropdown-item command="current">导出本页</el-dropdown-item>
											<el-dropdown-item command="all">导出全部</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
								<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'inpatientRegister:import'"> 导入 </el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" v-if="auth('inpatientRegister:batchDelete') || auth('inpatientRegister:export')" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<!-- <el-table-column prop='patientNo' label='患者编号' show-overflow-tooltip /> -->
				<el-table-column prop="patientName" label="患者姓名" show-overflow-tooltip />
				<el-table-column prop="inpatientWay" label="入院途径" show-overflow-tooltip width="100">
					<template #default="scope">
						<g-sys-dict v-model="scope.row.inpatientWay" code="InpatientWayType" />
					</template>
				</el-table-column>
				<el-table-column prop="inpatientTime" label="入院时间" show-overflow-tooltip width="140" />
				<el-table-column prop="inpatientNo" label="住院号" show-overflow-tooltip width="100">
					<template #default="scope">
						<el-link type="primary" style="font-size: 12px" @click="viewRow(scope.row)">{{ scope.row.inpatientNo }}</el-link>
					</template>
				</el-table-column>
				<el-table-column prop="inpatientSerialNo" label="住院流水号" show-overflow-tooltip width="100" />
				<el-table-column prop="inpatientTimes" label="住院次数" show-overflow-tooltip />
				<el-table-column prop="medicalRecordNo" label="病案号" show-overflow-tooltip width="100" />
				<el-table-column prop="idCardType" label="证件类型" show-overflow-tooltip width="100">
					<template #default="scope">
						<g-sys-dict v-model="scope.row.idCardType" code="CardTypeEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="idCardNo" label="证件号码" show-overflow-tooltip width="140" />
				<el-table-column prop="insuranceNo" label="保险号" show-overflow-tooltip width="140" />
				<el-table-column prop="medicalCardNo" label="就诊卡号" show-overflow-tooltip />
				<el-table-column prop="outpatientNo" label="门诊号" show-overflow-tooltip />

				<el-table-column prop="feeCategory" label="费用类别" width="100">
					<template #default="scope">
						<g-sys-dict v-model="scope.row.feeCategory" code="InpatientFeeCategory" />
					</template>
				</el-table-column>
				<el-table-column prop="doctorName" label="医生姓名" show-overflow-tooltip />
				<el-table-column prop="deptName" label="科室名称" show-overflow-tooltip />
				<el-table-column prop="mainDoctorName" label="主治医生姓名" show-overflow-tooltip width="120" />
				<el-table-column prop="inpatientDiagnosticCode" label="入院诊断代码" show-overflow-tooltip width="120" />
				<el-table-column prop="inpatientDiagnosticName" label="入院诊断名称" show-overflow-tooltip width="120" />
				<el-table-column prop="secondaryDiagnosticCode" label="次要诊断代码" show-overflow-tooltip width="120" />
				<el-table-column prop="secondaryDiagnosticName" label="次要诊断名称" show-overflow-tooltip width="120" />

				<el-table-column prop="allowArrears" label="允许欠费" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.allowArrears"> 是 </el-tag>
						<el-tag type="danger" v-else> 否 </el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="arrearsLimit" label="欠费上限" show-overflow-tooltip />
				<el-table-column prop="guaranteePerson" label="担保人" show-overflow-tooltip />
				<el-table-column prop="newbornBirthWeight1" label="新生儿出生体重g" show-overflow-tooltip width="120" />
				<el-table-column prop="newbornBirthWeight2" label="新生儿出生体重g" show-overflow-tooltip width="120" />
				<el-table-column prop="newbornBirthWeight3" label="新生儿出生体重g" show-overflow-tooltip width="120" />
				<el-table-column prop="newbornInpatientWeight" label="新生儿入院体重g" show-overflow-tooltip width="120" />
				<el-table-column prop="hasMedicalInsurance" label="医保卡" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.allowArrears"> 有 </el-tag>
						<el-tag type="danger" v-else> 无 </el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="status" label="状态" show-overflow-tooltip />
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('inpatientRegister:update') || auth('inpatientRegister:delete')">
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="editDialogRef.openDialog(scope.row, '编辑住院登记')" v-auth="'inpatientRegister:update'"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="primary" @click="delInpatientRegister(scope.row)" v-auth="'inpatientRegister:delete'"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
			<ImportData ref="importDataRef" :import="inpatientRegisterApi.importData" :download="inpatientRegisterApi.downloadTemplate" v-auth="'inpatientRegister:import'" @refresh="handleQuery" />
			<printDialog ref="printDialogRef" :title="'打印住院登记'" @reloadTable="handleQuery" />
			<editDialog ref="editDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
