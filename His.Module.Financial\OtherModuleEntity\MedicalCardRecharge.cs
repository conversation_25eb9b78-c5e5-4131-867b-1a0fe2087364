﻿using His.Module.Financial.Enum;
namespace His.Module.Financial.OtherModuleEntity;

/// <summary>
/// 卡费用记录表
/// </summary>
[SugarTable(null, "卡费用记录表")]
[Tenant("1300000000010")]
public class MedicalCardRecharge : EntityTenant
{
    /// <summary>
    /// 患者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "患者Id")]
    public long PatientId { get; set; }

    /// <summary>
    /// 就诊卡Id
    /// </summary>
    [SugarColumn(ColumnDescription = "就诊卡Id")]
    public long CardId { get; set; }
    /// <summary>
    /// 就诊卡号
    /// </summary>
    [SugarColumn(ColumnDescription = "就诊卡号")]
    public string CardNo { get; set; }
    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnDescription = "就诊号")]
    public string VisitNo { get; set; }
 
    

    /// <summary>
    /// 支付方式Id
    /// </summary>
    [SugarColumn(ColumnDescription = "支付方式Id")]
    public long? PayMethodId { get; set; }

    /// <summary>
    /// 支付渠道
    /// </summary>
    [SugarColumn(ColumnDescription = "支付渠道")]
    public long? PayChannel { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    [SugarColumn(ColumnDescription = "支付金额")]
    public decimal? PayAmount { get; set; }

    /// <summary>
    /// 卡余额
    /// </summary>
    [SugarColumn(ColumnDescription = "卡余额")]
    public decimal? CardBalance { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    [SugarColumn(ColumnDescription = "发票号")]
    public string InvoiceNumber { get; set; }

    /// <summary>
    /// 状态 0充值 1红冲 2扣款 3退费 4退卡余额
    /// </summary>
    [SugarColumn(ColumnDescription = "状态 0充值 1红冲 2扣款 3退费 4退卡余额")]
    public CardRechargeStatusEnum Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注")]
    public string Remark { get; set; }

    /// <summary>
    /// 红冲Id
    /// </summary>
    [SugarColumn(ColumnDescription = "红冲Id")]
    public long? RedInvoiceId { get; set; }

    /// <summary>
    /// 是否日结
    /// </summary>
    [SugarColumn(ColumnDescription = "是否日结")]
    public int? IsDailySettle { get; set; }

    /// <summary>
    /// 日结Id
    /// </summary>
    [SugarColumn(ColumnDescription = "日结Id")]
    public long? DailySettleId { get; set; }
}