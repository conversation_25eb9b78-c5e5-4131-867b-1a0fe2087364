namespace His.Module.OutpatientDoctor.Dto;

public class PrescriptionMainForSendDrugOutput:PrescriptionMainOutput
{
    
    public string? IdCardNo { get; set; }
    public long? FeeId { get; set; }
    public string? FeeName { get; set; }

    
    /// <summary>
    /// 医疗类别 0 自费 1 医保
    /// </summary>
    public virtual Int16? MedCategory { get; set; }

 
 
 
    
    /// <summary>
    /// 医疗统筹类别
    /// </summary> 
    public virtual string? MedicalPoolingCategory { get; set; }
    /// <summary>
    /// 医疗统筹类别名称
    /// </summary>险种标志
    public virtual string? MedicalPoolingCategoryName { get; set; }
    
    /// <summary>
    /// 险种标志
    /// </summary> 
    public virtual string? MedicalInsuranceFlag { get; set; }
    /// <summary>
    /// 险种标志名称
    /// </summary> 
    public virtual string? MedicalInsuranceFlagName { get; set; }
}