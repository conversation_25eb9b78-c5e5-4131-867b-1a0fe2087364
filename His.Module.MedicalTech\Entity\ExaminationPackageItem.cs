﻿namespace His.Module.MedicalTech.Entity;

/// <summary>
/// 检查套餐项目表
/// </summary>
[Tenant("1300000000009")]
[SugarTable("examination_package_item", "检查套餐项目表")]
public class ExaminationPackageItem : EntityTenantBaseData
{
    /// <summary>
    /// 检查Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "examination_id", ColumnDescription = "检查Id")]
    public virtual long ExaminationId { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "apply_no", ColumnDescription = "申请单号", Length = 64)]
    public virtual string ApplyNo { get; set; }

    /// <summary>
    /// 检查明细Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "examination_details_id", ColumnDescription = "检查明细Id")]
    public virtual long ExaminationDetailsId { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    [SugarColumn(ColumnName = "item_id", ColumnDescription = "项目Id")]
    public virtual long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [SugarColumn(ColumnName = "item_code", ColumnDescription = "项目编码", Length = 64)]
    public virtual string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    [SugarColumn(ColumnName = "item_name", ColumnDescription = "项目名称", Length = 64)]
    public virtual string? ItemName { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 64)]
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [SugarColumn(ColumnName = "price", ColumnDescription = "单价", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal? Amount { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    [SugarColumn(ColumnName = "charge_category_id", ColumnDescription = "收费类别Id")]
    public virtual long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    [SugarColumn(ColumnName = "self_pay_ratio", ColumnDescription = "自付比例", Length = 4, DecimalDigits = 4)]
    public virtual decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    [SugarColumn(ColumnName = "is_ratio_audit", ColumnDescription = "自付比例是否审核 1审核 2不审核")]
    public virtual int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_time", ColumnDescription = "自付比例审核时间")]
    public virtual DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人员Id
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_staff_id", ColumnDescription = "自付比例审核人员Id")]
    public virtual long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人员名称
    /// </summary>
    [SugarColumn(ColumnName = "ratio_audit_staff_name", ColumnDescription = "自付比例审核人员名称", Length = 64)]
    public virtual string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }
}