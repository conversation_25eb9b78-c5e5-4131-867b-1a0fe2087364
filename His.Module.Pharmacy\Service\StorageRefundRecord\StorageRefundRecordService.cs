﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品退货服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class StorageRefundRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<StorageRefundRecord> _storageRefundRecordRep;
    private readonly SqlSugarRepository<StorageRefundDetail> _storageRefundDetailRep;
    private readonly SqlSugarRepository<DrugStorage> _drugStorageRep;
    private readonly InventoryService _inventoryService;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public StorageRefundRecordService(
        SqlSugarRepository<StorageRefundRecord> storageRefundRecordRep,
        SqlSugarRepository<StorageRefundDetail> storageRefundDetailRep,
        SqlSugarRepository<DrugStorage>  drugStorageRep,
        InventoryService inventoryService,
        ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _storageRefundRecordRep = storageRefundRecordRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
        _storageRefundDetailRep= storageRefundDetailRep;
        _drugStorageRep= drugStorageRep;
        _inventoryService= inventoryService;
    }

    /// <summary>
    /// 分页查询药品退货 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品退货")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<StorageRefundRecordOutput>> Page(PageStorageRefundRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _storageRefundRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.RefundNo.Contains(input.Keyword) || u.RefundType.Contains(input.Keyword) || u.RefundReason.Contains(input.Keyword) || u.ApplyDeptCode.Contains(input.Keyword) || u.ApplyDeptName.Contains(input.Keyword) || u.TargetStorageCode.Contains(input.Keyword) || u.TargetStorageName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RefundNo), u => u.RefundNo.Contains(input.RefundNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RefundType), u => u.RefundType.Contains(input.RefundType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RefundReason), u => u.RefundReason.Contains(input.RefundReason.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApplyDeptCode), u => u.ApplyDeptCode.Contains(input.ApplyDeptCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApplyDeptName), u => u.ApplyDeptName.Contains(input.ApplyDeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TargetStorageCode), u => u.TargetStorageCode.Contains(input.TargetStorageCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TargetStorageName), u => u.TargetStorageName.Contains(input.TargetStorageName.Trim()))
            .WhereIF(input.RefundTimeRange?.Length == 2, u => u.RefundTime >= input.RefundTimeRange[0] && u.RefundTime <= input.RefundTimeRange[1])
            .WhereIF(input.ApplyDeptId != null, u => u.ApplyDeptId == input.ApplyDeptId)
            .WhereIF(input.TargetStorageId != null, u => u.TargetStorageId == input.TargetStorageId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            //排查供应商退药 09
            .Where(u=>u.RefundType!="09")
            .LeftJoin<DrugStorage>((u, applyDept) => u.ApplyDeptId == applyDept.Id)
            .LeftJoin<DrugStorage>((u, applyDept, targetStorage) => u.TargetStorageId == targetStorage.Id)
            .Select((u, applyDept, targetStorage) => new StorageRefundRecordOutput
            {
                Id = u.Id,
                RefundNo = u.RefundNo,
                RefundTime = u.RefundTime,
                RefundType = u.RefundType,
                RefundReason = u.RefundReason,
                ApplyDeptId = u.ApplyDeptId,
                ApplyDeptFkDisplayName = $"{applyDept.StorageName}",
                ApplyDeptCode = u.ApplyDeptCode,
                ApplyDeptName = u.ApplyDeptName,
                TargetStorageId = u.TargetStorageId,
                TargetStorageFkDisplayName = $"{targetStorage.StorageName}",
                TargetStorageCode = u.TargetStorageCode,
                TargetStorageName = u.TargetStorageName,
                TotalSalePrice = u.TotalSalePrice,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }
 /// <summary>
    /// 供应商退药 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询供应商退药")]
    [ApiDescriptionSettings(Name = "Page4Supplier"), HttpPost]
    public async Task<SqlSugarPagedList<StorageRefundRecordOutput>> Page4Supplier(PageStorageRefundRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
       
        var query = _storageRefundRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.RefundNo.Contains(input.Keyword) || u.RefundType.Contains(input.Keyword) || u.RefundReason.Contains(input.Keyword) || u.ApplyDeptCode.Contains(input.Keyword) || u.ApplyDeptName.Contains(input.Keyword) || u.TargetStorageCode.Contains(input.Keyword) || u.TargetStorageName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RefundNo), u => u.RefundNo.Contains(input.RefundNo.Trim()))
            .Where(u => u.RefundType=="09")//   只包含供应商 09
            .WhereIF(!string.IsNullOrWhiteSpace(input.RefundReason), u => u.RefundReason.Contains(input.RefundReason.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApplyDeptCode), u => u.ApplyDeptCode.Contains(input.ApplyDeptCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApplyDeptName), u => u.ApplyDeptName.Contains(input.ApplyDeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TargetStorageCode), u => u.TargetStorageCode.Contains(input.TargetStorageCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TargetStorageName), u => u.TargetStorageName.Contains(input.TargetStorageName.Trim()))
            .WhereIF(input.RefundTimeRange?.Length == 2, u => u.RefundTime >= input.RefundTimeRange[0] && u.RefundTime <= input.RefundTimeRange[1])
            .WhereIF(input.ApplyDeptId != null, u => u.ApplyDeptId == input.ApplyDeptId)
            .WhereIF(input.TargetStorageId != null, u => u.TargetStorageId == input.TargetStorageId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            
            
            .LeftJoin<DrugStorage>((u, applyDept) => u.ApplyDeptId == applyDept.Id)
          //  .LeftJoin<DrugStorage>((u, applyDept, targetStorage) => u.TargetStorageId == targetStorage.Id)
            .LeftJoin<EnterpriseDictionary>((u, applyDept, supplier)=> u.TargetStorageId == supplier.Id)
            .Select((u, applyDept, supplier) => new StorageRefundRecordOutput
            {
                Id = u.Id,
                RefundNo = u.RefundNo,
                RefundTime = u.RefundTime,
                RefundType = u.RefundType,
                RefundReason = u.RefundReason,
                ApplyDeptId = u.ApplyDeptId,
                ApplyDeptFkDisplayName = $"{applyDept.StorageName}",
                ApplyDeptCode = u.ApplyDeptCode,
                ApplyDeptName = u.ApplyDeptName,
                TargetStorageId = u.TargetStorageId,
                TargetStorageFkDisplayName = $"{supplier.EnterpriseName}",
                TargetStorageCode = u.TargetStorageCode,
                TargetStorageName = u.TargetStorageName,
                TotalSalePrice = u.TotalSalePrice,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }
    /// <summary>
    /// 获取药品退货详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品退货详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<StorageRefundRecord> Detail([FromQuery] QueryByIdStorageRefundRecordInput input)
    {
        return await _storageRefundRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品退货 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品退货")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<long> Add(AddStorageRefundRecordInput input)
    {
        var entity = input.Adapt<StorageRefundRecord>();
        
        entity.RefundNo =  await _storageRefundRecordRep.Context.Ado.GetStringAsync(
                "SELECT LPAD(CAST(NEXTVAL('storage_refund_no_seq')As varchar),7,'0')");

        entity.Status = 0;
        var details = input.Details.Adapt<List<StorageRefundDetail>>();
        entity.TotalSalePrice = details.Sum(p => p.TotalSalePrice); 
        var record= await _storageRefundRecordRep.InsertAsync(entity) ? entity.Id : 0;
        await SaveDetail(details, entity);
       
        return record;
    }
    async Task SaveDetail(List<StorageRefundDetail> details, StorageRefundRecord entity)
    {
        await _storageRefundDetailRep.DeleteAsync(u => u.RefundRecordId == entity.Id);
        foreach (var item in details)
        {
            item.RefundRecordId =  entity.Id;
            item.Id = 0;
            if (item.Quantity == null || item.Quantity == 0)
            {
                throw Oops.Oh("出库数量不能为0");
            }
        }

   
        await _storageRefundDetailRep.InsertRangeAsync(details);
    }
    /// <summary>
    /// 更新药品退货 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品退货")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [UnitOfWork]
    public async Task Update(UpdateStorageRefundRecordInput input)
    {

        var record = await _storageRefundRecordRep.GetFirstAsync(u => u.Id == input.Id);
        if(record==null)
            throw Oops.Oh(ErrorCodeEnum.D1002);
        if (record.Status != 0)
        {
            throw Oops.Oh("当前状态禁止修改");
        }

        var entity = input.Adapt<StorageRefundRecord>();
        var details = input.Details.Adapt<List<StorageRefundDetail>>();
        entity.TotalSalePrice = details.Sum(p => p.TotalSalePrice); 
        await SaveDetail(details, entity); 
        await _storageRefundRecordRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 提交 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("提交")]
    [ApiDescriptionSettings(Name = "Submit"), HttpPost]
    [UnitOfWork]
    public async Task<bool> Submit(SubmitStorageRefundRecordInput input)
    {
        var result = await _storageRefundRecordRep.GetFirstAsync(u => u.Id == input.Id);

        if (result.Status == 0)
        {
            
  
            
            // 是否需要审核
            var storage = await _drugStorageRep.GetFirstAsync(u => u.Id == result.ApplyDeptId);
                var details = await _storageRefundDetailRep.GetListAsync(u => 
                    u.RefundRecordId == input.Id);
                foreach (var detail in details)
                {
                    await _inventoryService.UpdateInventoryAsync(detail, result);
                }
            return await _storageRefundRecordRep.UpdateAsync(u
                => new StorageRefundRecord() { Status = 1, RefundTime = DateTime.Now }, u => u.Id == input.Id);
        }
        else
            throw Oops.Oh("当前状态禁止提交");
    }
    /// <summary>
    /// 删除药品退货 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品退货")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeleteStorageRefundRecordInput input)
    {
        var entity = await _storageRefundRecordRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status != 0)
        {
            throw Oops.Oh("当前状态禁止修改");
        }
        
        await _storageRefundRecordRep.FakeDeleteAsync(entity);   //假删除
        var details = await _storageRefundDetailRep.GetListAsync(
            u => u.RefundRecordId == input.Id);
        await _storageRefundDetailRep.FakeDeleteAsync(details);
        //await _storageRefundRecordRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品退货 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品退货")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteStorageRefundRecordInput> input)
    {
        var exp = Expressionable.Create<StorageRefundRecord>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _storageRefundRecordRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _storageRefundRecordRep.FakeDeleteAsync(list);   //假删除
        //return await _storageRefundRecordRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataStorageRefundRecordInput input)
    {
        var applyDeptIdData = await _storageRefundRecordRep.Context.Queryable<DrugStorage>()
            .InnerJoinIF<StorageRefundRecord>(input.FromPage, (u, r) => u.Id == r.ApplyDeptId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.StorageName}"
            }).ToListAsync();
        var targetStorageIdData = await _storageRefundRecordRep.Context.Queryable<DrugStorage>()
            .InnerJoinIF<StorageRefundRecord>(input.FromPage, (u, r) => u.Id == r.TargetStorageId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.StorageName}"
            }).ToListAsync();
        var supplierIdData = await _storageRefundRecordRep.Context.Queryable<EnterpriseDictionary>()
           // .InnerJoinIF<StorageRefundRecord>(input.FromPage, (u, r) => u.Id == r.TargetStorageId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
        var manufacturerIdData = await _storageRefundDetailRep.Context.Queryable<EnterpriseDictionary>()
            .InnerJoinIF<StorageRefundDetail>(input.FromPage, (u, r) => u.Id == r.ManufacturerId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
   
        return new Dictionary<string, dynamic>
        {
            { "applyDeptId", applyDeptIdData },
            { "targetStorageId", targetStorageIdData },
            {"supplierId", supplierIdData},
            { "manufacturerId", manufacturerIdData },
        };
    }
    
    /// <summary>
    /// 导出药品退货记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品退货记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageStorageRefundRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportStorageRefundRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var refundTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "" }).Result.ToDictionary(x => x.Value, x => x.Label);
        var statusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.RefundTypeDictLabel = refundTypeDictMap.GetValueOrDefault(e.RefundType ?? "", e.RefundType);
            e.StatusDictLabel = statusDictMap.GetValueOrDefault(e.Status==null ? "" : e.Status.ToString());
        });
        return ExcelHelper.ExportTemplate(list, "药品退货导出记录");
    }
    
    /// <summary>
    /// 下载药品退货数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品退货数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportStorageRefundRecordOutput>(), "药品退货导入模板", (_, info) =>
        {
            if (nameof(ExportStorageRefundRecordOutput.ApplyDeptFkDisplayName) == info.Name) return _storageRefundRecordRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}").Distinct().ToList();
            if (nameof(ExportStorageRefundRecordOutput.TargetStorageFkDisplayName) == info.Name) return _storageRefundRecordRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入药品退货记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品退货记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var refundTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var statusDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportStorageRefundRecordInput, StorageRefundRecord>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 申请科室
                    var applyDeptIdLabelList = pageItems.Where(x => x.ApplyDeptFkDisplayName != null).Select(x => x.ApplyDeptFkDisplayName).Distinct().ToList();
                    if (applyDeptIdLabelList.Any()) {
                        var applyDeptIdLinkMap = _storageRefundRecordRep.Context.Queryable<DrugStorage>().Where(u => applyDeptIdLabelList.Contains($"{u.StorageName}")).ToList().ToDictionary(u => $"{u.StorageName}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.ApplyDeptId = applyDeptIdLinkMap.GetValueOrDefault(e.ApplyDeptFkDisplayName ?? "");
                            if (e.ApplyDeptId == null) e.Error = "申请科室链接失败";
                        });
                    }
                    // 链接 目标库房
                    var targetStorageIdLabelList = pageItems.Where(x => x.TargetStorageFkDisplayName != null).Select(x => x.TargetStorageFkDisplayName).Distinct().ToList();
                    if (targetStorageIdLabelList.Any()) {
                        var targetStorageIdLinkMap = _storageRefundRecordRep.Context.Queryable<DrugStorage>().Where(u => targetStorageIdLabelList.Contains($"{u.StorageName}")).ToList().ToDictionary(u => $"{u.StorageName}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.TargetStorageId = targetStorageIdLinkMap.GetValueOrDefault(e.TargetStorageFkDisplayName ?? "");
                            if (e.TargetStorageId == null) e.Error = "目标库房链接失败";
                        });
                    }
                    
                    // 映射字典值
                    foreach(var item in pageItems) {
                        if (string.IsNullOrWhiteSpace(item.RefundTypeDictLabel)) continue;
                        item.RefundType = refundTypeDictMap.GetValueOrDefault(item.RefundTypeDictLabel);
                        if (item.RefundType == null) item.Error = "退货类型字典映射失败";
                        // if (string.IsNullOrWhiteSpace(item.StatusDictLabel)) continue;
                        // item.Status = statusDictMap.GetValueOrDefault(item.StatusDictLabel);
                        if (item.Status == null) item.Error = "状态字典映射失败";
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<StorageRefundRecord>>();
                    
                    var storageable = _storageRefundRecordRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.RefundNo?.Length > 100, "退货单号长度不能超过100个字符")
                        .SplitError(it => it.Item.RefundType?.Length > 100, "退货类型长度不能超过100个字符")
                        .SplitError(it => it.Item.RefundReason?.Length > 100, "退货原因长度不能超过100个字符")
                        .SplitError(it => it.Item.ApplyDeptCode?.Length > 100, "申请部门编码长度不能超过100个字符")
                        .SplitError(it => it.Item.ApplyDeptName?.Length > 100, "申请部门名称长度不能超过100个字符")
                        .SplitError(it => it.Item.TargetStorageCode?.Length > 100, "目标库房编码长度不能超过100个字符")
                        .SplitError(it => it.Item.TargetStorageName?.Length > 100, "目标库房名称长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
