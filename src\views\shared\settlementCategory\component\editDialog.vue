﻿<script lang="ts" name="settlementCategory" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { formatDate } from '/@/utils/formatTime';
import { useSettlementCategoryApi } from '/@/api/shared/settlementCategory';

//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);
const settlementCategoryApi = useSettlementCategoryApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
	code: [{ required: true, message: '请输入编号！', trigger: 'blur' }],
	name: [{ required: true, message: '请选择名称！', trigger: 'blur' }],
	medCategory: [{ required: true, message: '请选择医疗类别！', trigger: 'change' }],
	usageScope: [{ required: true, message: '请选择使用范围！', trigger: 'change' }],
});

// 页面加载时
onMounted(async () => {});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? { status: 1, orderNo: 100 };
	state.ruleForm = row.id ? await settlementCategoryApi.detail(row.id).then((res) => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit('reloadTable');
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await settlementCategoryApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="settlementCategory-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="编号" prop="code">
							<el-input v-model="state.ruleForm.code" placeholder="请输入编号" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="名称" prop="name">
							<el-input v-model="state.ruleForm.name" placeholder="请输入名称" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="医疗类别" prop="medCategory">
							<g-sys-dict v-model="state.ruleForm.medCategory" code="MedCategoryEnum" render-as="select" placeholder="请选择医疗类别" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="使用范围" prop="usageScope">
							<g-sys-dict v-model="state.ruleForm.usageScope" code="MedServiceCategoryEnum" render-as="select" placeholder="请选择使用范围" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="医保类型" prop="medInsType">
							<g-sys-dict v-model="state.ruleForm.medInsType" code="MedInsTypeEnum" render-as="select" placeholder="请选择医保类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="医疗统筹类别" prop="medicalPoolingCategory">
							<el-input v-model="state.ruleForm.medicalPoolingCategory" placeholder="请输入医疗统筹类别" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="医疗统筹类别名称" prop="medicalPoolingCategoryName">
							<el-input v-model="state.ruleForm.medicalPoolingCategoryName" placeholder="请输入医疗统筹类别名称" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="险种标志" prop="medicalInsuranceFlag">
							<el-input v-model="state.ruleForm.medicalInsuranceFlag" placeholder="请输入险种标志" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="险种标志名称" prop="medicalInsuranceFlagName">
							<el-input v-model="state.ruleForm.medicalInsuranceFlagName" placeholder="请输入险种标志名称" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="住院是否允许欠费" prop="isInpatientArrearsAllowed">
							<g-sys-dict v-model="state.ruleForm.isInpatientArrearsAllowed" code="YesNoEnum" render-as="select" placeholder="请选择住院是否允许欠费" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="住院允许欠费金额" prop="inpatientAllowedArrearsAmount">
							<el-input-number v-model="state.ruleForm.inpatientAllowedArrearsAmount" placeholder="请输入住院允许欠费金额" :min="1" :step="100" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="住院允许欠费比例" prop="inpatientAllowedArrearsRatio">
							<el-input-number v-model="state.ruleForm.inpatientAllowedArrearsRatio" placeholder="请输入住院允许欠费比例" :precision="2" :step="0.1" :min="0" :max="1" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="state.ruleForm.id">
						<el-form-item label="状态" prop="status">
							<g-sys-dict v-model="state.ruleForm.status" code="StatusEnum" render-as="select" placeholder="请选择状态" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="排序" prop="orderNo">
							<el-input-number v-model="state.ruleForm.orderNo" placeholder="请输入排序" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => (state.showDialog = false)">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
