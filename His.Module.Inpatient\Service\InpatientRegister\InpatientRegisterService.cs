using His.Module.Inpatient.Service.Deposit;
using His.Module.Inpatient.Service.Deposit.Dto;
using Yitter.IdGenerator;
namespace His.Module.Inpatient;

/// <summary>
/// 入院登记记录服务 🧩
/// </summary>
[ApiDescriptionSettings(InpatientConst.GroupName, Order = 100)]
public class InpatientRegisterService(
    SqlSugarRepository<InpatientRegister> inpatientRegisterRep,
    AppointmentRecordService appointmentRecordService,
    DepositService depositService,
    ISqlSugarClient sqlSugarClient) : IDynamicApiController, ITransient
{

    /// <summary>
    /// 分页查询入院登记记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询入院登记记录")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<InpatientRegisterOutput>> Page(PageInpatientRegisterInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = inpatientRegisterRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.InpatientSerialNo.Contains(input.Keyword) || u.InpatientNo.Contains(input.Keyword) || u.MedicalCardNo.Contains(input.Keyword) || u.PatientName.Contains(input.Keyword) || u.AgeUnit.Contains(input.Keyword) || u.IdCardNo.Contains(input.Keyword) || u.Phone.Contains(input.Keyword) || u.ContactName.Contains(input.Keyword) || u.ContactRelationship.Contains(input.Keyword) || u.ContactAddress.Contains(input.Keyword) || u.ContactPhone.Contains(input.Keyword) || u.ResidenceAddress.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.WardName.Contains(input.Keyword) || u.TeamName.Contains(input.Keyword) || u.DoctorName.Contains(input.Keyword) || u.ReceivingDoctorName.Contains(input.Keyword) || u.InpatientWay.Contains(input.Keyword) || u.SettlementCategory.Contains(input.Keyword) || u.AdmissionDiagnosisCode.Contains(input.Keyword) || u.AdmissionDiagnosisName.Contains(input.Keyword) || u.PregnancyRiskLevel.Contains(input.Keyword) || u.HighRiskFactors.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientSerialNo), u => u.InpatientSerialNo.Contains(input.InpatientSerialNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientNo), u => u.InpatientNo.Contains(input.InpatientNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicalCardNo), u => u.MedicalCardNo.Contains(input.MedicalCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AgeUnit), u => u.AgeUnit.Contains(input.AgeUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardNo), u => u.IdCardNo.Contains(input.IdCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), u => u.Phone.Contains(input.Phone.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactName), u => u.ContactName.Contains(input.ContactName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactRelationship), u => u.ContactRelationship.Contains(input.ContactRelationship.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactAddress), u => u.ContactAddress.Contains(input.ContactAddress.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ContactPhone), u => u.ContactPhone.Contains(input.ContactPhone.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ResidenceAddress), u => u.ResidenceAddress.Contains(input.ResidenceAddress.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeptName), u => u.DeptName.Contains(input.DeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.WardName), u => u.WardName.Contains(input.WardName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TeamName), u => u.TeamName.Contains(input.TeamName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DoctorName), u => u.DoctorName.Contains(input.DoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ReceivingDoctorName), u => u.ReceivingDoctorName.Contains(input.ReceivingDoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InpatientWay), u => u.InpatientWay.Contains(input.InpatientWay.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SettlementCategory), u => u.SettlementCategory.Contains(input.SettlementCategory.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AdmissionDiagnosisCode), u => u.AdmissionDiagnosisCode.Contains(input.AdmissionDiagnosisCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AdmissionDiagnosisName), u => u.AdmissionDiagnosisName.Contains(input.AdmissionDiagnosisName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PregnancyRiskLevel), u => u.PregnancyRiskLevel.Contains(input.PregnancyRiskLevel.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.HighRiskFactors), u => u.HighRiskFactors.Contains(input.HighRiskFactors.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.InpatientCount != null, u => u.InpatientCount == input.InpatientCount)
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.Sex != null, u => u.Sex == input.Sex)
            .WhereIF(input.Age != null, u => u.Age == input.Age)
            .WhereIF(input.BirthdayRange?.Length == 2, u => u.Birthday >= input.BirthdayRange[0] && u.Birthday <= input.BirthdayRange[1])
            .WhereIF(input.CardType != null, u => u.CardType == input.CardType)
            .WhereIF(input.ResidenceProvince != null, u => u.ResidenceProvince == input.ResidenceProvince)
            .WhereIF(input.ResidenceCity != null, u => u.ResidenceCity == input.ResidenceCity)
            .WhereIF(input.ResidenceCounty != null, u => u.ResidenceCounty == input.ResidenceCounty)
            .WhereIF(input.InpatientTimeRange?.Length == 2, u => u.InpatientTime >= input.InpatientTimeRange[0] && u.InpatientTime <= input.InpatientTimeRange[1])
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.WardId != null, u => u.WardId == input.WardId)
            .WhereIF(input.TeamId != null, u => u.TeamId == input.TeamId)
            .WhereIF(input.DoctorId != null, u => u.DoctorId == input.DoctorId)
            .WhereIF(input.ReceivingDoctorId != null, u => u.ReceivingDoctorId == input.ReceivingDoctorId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<InpatientRegisterOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取入院登记记录详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取入院登记记录详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<InpatientRegister> Detail([FromQuery] QueryByIdInpatientRegisterInput input)
    {
        return await inpatientRegisterRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加入院登记记录 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加入院登记记录")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<long> Add(AddInpatientRegisterInput input)
    {
        var entity = input.Adapt<InpatientRegister>();
        entity.Id = YitIdHelper.NextId();
        entity.InpatientNo = await GenerateInpatientNo();
        entity.InpatientCount = await GenerateInpatientCount(entity.PatientId);
        entity.InpatientSerialNo = GenerateInpatientSerialNo(entity.InpatientCount, entity.InpatientNo);
        entity.Status = 0;
        //预约记录确认
        if (input.AppointmentId != null)
            await appointmentRecordService.Confirm(new ConfirmAppointmentRecordInput
            {
                Id = input.AppointmentId
            });
        //创建押金账户
        await depositService.Add(new AddDepositInput
        {
            InpatientRegisterId = entity.Id, InpatientNo = entity.InpatientNo, PatientId = entity.PatientId
        });
        return await inpatientRegisterRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新入院登记记录 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新入院登记记录")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateInpatientRegisterInput input)
    {
        var entity = input.Adapt<InpatientRegister>();
        await inpatientRegisterRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除入院登记记录 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除入院登记记录")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteInpatientRegisterInput input)
    {
        var entity = await inpatientRegisterRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await inpatientRegisterRep.FakeDeleteAsync(entity); //假删除
        //await _inpatientRegisterRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除入院登记记录 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除入院登记记录")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteInpatientRegisterInput> input)
    {
        var exp = Expressionable.Create<InpatientRegister>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await inpatientRegisterRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await inpatientRegisterRep.FakeDeleteAsync(list); //假删除
        //return await _inpatientRegisterRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出入院登记记录记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出入院登记记录记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageInpatientRegisterInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportInpatientRegisterOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "入院登记记录导出记录");
    }
    
    /// <summary>
    /// 下载入院登记记录数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载入院登记记录数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportInpatientRegisterOutput>(), "入院登记记录导入模板");
    }
    
    /// <summary>
    /// 导入入院登记记录记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入入院登记记录记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportInpatientRegisterInput, InpatientRegister>(file, (list, markerErrorAction) =>
            {
                sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<InpatientRegister>>();

                    var storageable = inpatientRegisterRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.InpatientSerialNo?.Length > 100, "住院流水号长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientNo?.Length > 100, "住院号长度不能超过100个字符")
                        .SplitError(it => it.Item.MedicalCardNo?.Length > 100, "就诊卡号长度不能超过100个字符")
                        .SplitError(it => it.Item.PatientName?.Length > 100, "患者姓名长度不能超过100个字符")
                        .SplitError(it => it.Item.AgeUnit?.Length > 32, "年龄单位长度不能超过32个字符")
                        .SplitError(it => it.Item.IdCardNo?.Length > 32, "身份证号长度不能超过32个字符")
                        .SplitError(it => it.Item.Phone?.Length > 16, "电话号码长度不能超过16个字符")
                        .SplitError(it => it.Item.ContactName?.Length > 32, "联系人姓名长度不能超过32个字符")
                        .SplitError(it => it.Item.ContactRelationship?.Length > 16, "联系人关系长度不能超过16个字符")
                        .SplitError(it => it.Item.ContactAddress?.Length > 64, "联系人地址长度不能超过64个字符")
                        .SplitError(it => it.Item.ContactPhone?.Length > 16, "联系人电话号码长度不能超过16个字符")
                        .SplitError(it => it.Item.ResidenceAddress?.Length > 128, "详细现居住地长度不能超过128个字符")
                        .SplitError(it => it.Item.DeptName?.Length > 100, "科室名称长度不能超过100个字符")
                        .SplitError(it => it.Item.WardName?.Length > 100, "病区名称长度不能超过100个字符")
                        .SplitError(it => it.Item.TeamName?.Length > 100, "诊疗组名称长度不能超过100个字符")
                        .SplitError(it => it.Item.DoctorName?.Length > 100, "医生姓名长度不能超过100个字符")
                        .SplitError(it => it.Item.ReceivingDoctorName?.Length > 100, "接诊医生名称长度不能超过100个字符")
                        .SplitError(it => it.Item.InpatientWay?.Length > 32, "入院途径长度不能超过32个字符")
                        .SplitError(it => it.Item.SettlementCategory?.Length > 100, "结算类别长度不能超过100个字符")
                        .SplitError(it => it.Item.AdmissionDiagnosisCode?.Length > 100, "入院诊断编号长度不能超过100个字符")
                        .SplitError(it => it.Item.AdmissionDiagnosisName?.Length > 100, "入院诊断名称长度不能超过100个字符")
                        .SplitError(it => it.Item.PregnancyRiskLevel?.Length > 32, "妊娠风险评估长度不能超过32个字符")
                        .SplitError(it => it.Item.HighRiskFactors?.Length > 256, "高危因素长度不能超过256个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }

    /// <summary>
    /// 生成住院号
    /// </summary>
    /// <returns></returns>
    private async Task<string> GenerateInpatientNo()
    {
        var inpatientNo = await inpatientRegisterRep.Context.Ado.GetStringAsync(
            "SELECT LPAD(CAST(NEXTVAL('inpatient_register_no_seq')As varchar),8,'0')");
        return inpatientNo;
    }

    /// <summary>
    /// 生成住院流水号
    /// 规则：Z + 两位住院次数 + 住院号
    /// </summary>
    /// <param name="inpatientCount">住院次数</param>
    /// <param name="inpatientNo">住院号</param>
    /// <returns>住院流水号</returns>
    private string GenerateInpatientSerialNo(int inpatientCount, string inpatientNo)
    {
        return $"Z{inpatientCount:D2}{inpatientNo}";
    }

    /// <summary>
    /// 根据患者Id查询生成住院次数
    /// </summary>
    /// <returns></returns>
    private async Task<int> GenerateInpatientCount(long patientId)
    {
        var maxInpatientCount = await inpatientRegisterRep.AsQueryable()
            .Where(u => u.PatientId == patientId)
            .MaxAsync(u => u.InpatientCount);
        return maxInpatientCount + 1;
    }
}
