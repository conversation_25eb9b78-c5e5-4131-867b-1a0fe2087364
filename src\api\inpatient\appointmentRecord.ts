﻿import {useBaseApi} from '/@/api/base';

// 住院预约管理接口服务
export const useAppointmentRecordApi = () => {
	const baseApi = useBaseApi("appointmentRecord");
	return {
		// 分页查询住院预约管理
		page: baseApi.page,
		// 查看住院预约管理详细
		detail: baseApi.detail,
		// 新增住院预约管理
		add: baseApi.add,
		// 更新住院预约管理
		update: baseApi.update,
		// 删除住院预约管理
		delete: baseApi.delete,
		// 批量删除住院预约管理
		batchDelete: baseApi.batchDelete,
		// 导出住院预约管理数据
		exportData: baseApi.exportData,
		// 导入住院预约管理数据
		importData: baseApi.importData,
		// 下载住院预约管理数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 住院预约管理实体
export interface AppointmentRecord {
	// 主键Id
	id: number;
	// 患者ID
	patientId: number;
	// 患者姓名
	patientName: string;
	// 性别
	sex?: number;
	// 年龄
	age?: number;
	// 年龄单位
	ageUnit: string;
	// 出生日期
	birthday: string;
	// 证件类型
	cardType?: number;
	// 身份证号
	idCardNo: string;
	// 民族
	nation: string;
	// 电话号码
	phone: string;
	// 联系人姓名
	contactName: string;
	// 联系人关系
	contactRelationship: string;
	// 联系人地址
	contactAddress: string;
	// 联系人电话号码
	contactPhone: string;
	// 国籍
	nationality: string;
	// 职业
	occupation: string;
	// 婚姻
	marriage: string;
	// 籍贯省
	nativePlaceProvince: number;
	// 籍贯市
	nativePlaceCity: number;
	// 籍贯县
	nativePlaceCounty: number;
	// 出生地省
	birthplaceProvince: number;
	// 出生地市
	birthplaceCity: number;
	// 出生地县
	birthplaceCounty: number;
	// 现居住地省
	residenceProvince?: number;
	// 现居住地市
	residenceCity?: number;
	// 现居住地县
	residenceCounty?: number;
	// 详细现居住地
	residenceAddress: string;
	// 工作地址省
	workProvince: number;
	// 工作地址市
	workCity: number;
	// 工作地址县
	workCounty: number;
	// 详细工作地址
	workAddress: string;
	// 工作单位
	workPlace: string;
	// 单位电话
	workPlacePhone: string;
	// 就诊卡号
	medicalCardNo: string;
	// 门诊号
	outpatientNo: string;
	// 就诊号
	visitNo: string;
	// 预约时间
	appointmentTime: string;
	// 预约科室ID
	deptId: number;
	// 预约科室名称
	deptName: string;
	// 预约病区ID
	wardId: number;
	// 预约病区名称
	wardName: string;
	// 预约诊疗组ID
	teamId: number;
	// 预约诊疗组名称
	teamName: string;
	// 预约医生ID
	doctorId: number;
	// 预约医生姓名
	doctorName: string;
	// 接诊医生id
	receivingDoctorId: number;
	// 接诊医生名称
	receivingDoctorName: string;
	// 入院途径InpatientWayType
	inpatientWay: string;
	// 结算类别
	settlementCategory: string;
	// 入院诊断
	admissionDiagnosis: string;
	// 预交金
	advancePayment: number;
	// 妊娠风险评估
	pregnancyRiskLevel: string;
	// 高危因素
	highRiskFactors: string;
	// 状态
	status: number;
	// 备注
	remark: string;
	// 租户Id
	tenantId: number;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
	// 软删除
	isDelete?: boolean;
}