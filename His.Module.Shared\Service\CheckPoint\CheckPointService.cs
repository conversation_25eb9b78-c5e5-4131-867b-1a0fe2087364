﻿using System.Data;
using Microsoft.AspNetCore.Http;

namespace His.Module.Shared.Service;

/// <summary>
/// 检查部位服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class CheckPointService : IDynamic<PERSON>pi<PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<CheckPoint> _checkPointRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public CheckPointService(SqlSugarRepository<CheckPoint> checkPointRep, ISqlSugarClient sqlSugarClient)
    {
        _checkPointRep = checkPointRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询检查部位 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询检查部位")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<CheckPointOutput>> Page(PageCheckPointInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.Code = input.Code?.Trim();
        input.Name = input.Name?.Trim().ToLower();
        var query = _checkPointRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
            || u.Name.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .LeftJoin<CheckCategory>((u, checkCategory) => u.CheckCategoryId == checkCategory.Id)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select((u, checkCategory) => new CheckPointOutput
            {
                Id = u.Id,
                Code = u.Code,
                Name = u.Name,
                PinyinCode = u.PinyinCode,
                WubiCode = u.WubiCode,
                CheckCategoryId = u.CheckCategoryId,
                CheckCategoryFkDisplayName = $"{checkCategory.Name}",
                Status = u.Status,
                OrderNo = u.OrderNo,
                CreateTime = u.CreateTime,
                UpdateTime = u.UpdateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
                Remark = u.Remark,
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取检查部位详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取检查部位详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<CheckPoint> Detail([FromQuery] QueryByIdCheckPointInput input)
    {
        return await _checkPointRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取检查部位列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    [DisplayName("获取检查部位列表")]
    public async Task<List<CheckPoint>> List()
    {
        return await _checkPointRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 增加检查部位 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加检查部位")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddCheckPointInput input)
    {
        var entity = input.Adapt<CheckPoint>();
        entity.Code = await _checkPointRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('check_point_code_seq')As varchar),3,'0')");
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        return await _checkPointRep.InsertAsync(entity) ? entity.Id : 0;
    }


    /// <summary>
    /// 添加检查部位数据
    /// </summary>
    /// <returns></returns>
    [DisplayName("添加检查部位数据")]
    [ApiDescriptionSettings(Name = "AddCheckPoint"), HttpPost]
    public async Task<bool> AddCheckPoint()
    {
        var dataTable = await _sqlSugarClient.Ado.GetDataTableAsync("SELECT * FROM \"shared\".\"XT_DICEXAMPOS\"");
        var list = new List<CheckPoint>();
        foreach (DataRow item in dataTable.Rows)
        {
            var entity = new CheckPoint
            {
                Code = await _checkPointRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('check_point_code_seq')As varchar),3,'0')"),
                Name = item["POSNAME"].ToString(),
                Status = StatusEnum.Enable,
                OrderNo = 100
            };
            entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
            entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
            list.Add(entity);
        }

        return await _checkPointRep.InsertRangeAsync(list);
    }

    /// <summary>
    /// 更新检查部位 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新检查部位")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateCheckPointInput input)
    {
        var entity = input.Adapt<CheckPoint>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _checkPointRep.AsUpdateable(entity)
        .IgnoreColumns(u => new
        {
            u.Code,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除检查部位 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除检查部位")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteCheckPointInput input)
    {
        var entity = await _checkPointRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _checkPointRep.FakeDeleteAsync(entity);   //假删除
        //await _checkPointRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除检查部位 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除检查部位")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteCheckPointInput> input)
    {
        var exp = Expressionable.Create<CheckPoint>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _checkPointRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _checkPointRep.FakeDeleteAsync(list);   //假删除
        //return await _checkPointRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置检查部位状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置检查部位状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetCheckPointStatus(SetCheckPointStatusInput input)
    {
        await _checkPointRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataCheckPointInput input)
    {
        var checkCategoryIdData = await _checkPointRep.Context.Queryable<CheckCategory>()
            .InnerJoinIF<CheckPoint>(input.FromPage, (u, r) => u.Id == r.CheckCategoryId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.Name}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "checkCategoryId", checkCategoryIdData },
        };
    }

    /// <summary>
    /// 导出检查部位记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出检查部位记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageCheckPointInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportCheckPointOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "检查部位导出记录");
    }

    /// <summary>
    /// 下载检查部位数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载检查部位数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportCheckPointOutput>(), "检查部位导入模板", (_, info) =>
        {
            if (nameof(ExportCheckPointOutput.CheckCategoryFkDisplayName) == info.Name) return _checkPointRep.Context.Queryable<CheckCategory>().Select(u => $"{u.Name}").Distinct().ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入检查部位记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入检查部位记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportCheckPointInput, CheckPoint>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 检查类别
                    var checkCategoryIdLabelList = pageItems.Where(x => x.CheckCategoryFkDisplayName != null).Select(x => x.CheckCategoryFkDisplayName).Distinct().ToList();
                    if (checkCategoryIdLabelList.Any())
                    {
                        var checkCategoryIdLinkMap = _checkPointRep.Context.Queryable<CheckCategory>().Where(u => checkCategoryIdLabelList.Contains($"{u.Name}")).ToList().ToDictionary(u => $"{u.Name}", u => u.Id);
                        pageItems.ForEach(e =>
                        {
                            e.CheckCategoryId = checkCategoryIdLinkMap.GetValueOrDefault(e.CheckCategoryFkDisplayName ?? "");
                            if (e.CheckCategoryId == null) e.Error = "检查类别链接失败";
                        });
                    }

                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        return true;
                    }).Adapt<List<CheckPoint>>();

                    var storageable = _checkPointRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Code?.Length > 32, "编码长度不能超过32个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.Name), "名称不能为空")
                        .SplitError(it => it.Item.Name?.Length > 32, "名称长度不能超过32个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 32, "拼音码长度不能超过32个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 32, "五笔码长度不能超过32个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}