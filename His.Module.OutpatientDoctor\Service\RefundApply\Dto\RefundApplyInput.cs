﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 门诊退费申请基础输入参数
/// </summary>
public class RefundApplyBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 退费申请ID
    /// </summary>
    public virtual long? ChargeId { get; set; }
    
    /// <summary>
    /// 退费申请类型
    /// </summary>
    public virtual string? BillingType { get; set; }
    
    /// <summary>
    /// 退费申请单号
    /// </summary>
    public virtual string? ApplyNo { get; set; }
    
    /// <summary>
    /// 退费申请时间
    /// </summary>
    public virtual DateTime? ApplyTime { get; set; }
     
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public virtual string? CardNo { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public virtual string? VisitNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 挂号ID
    /// </summary>
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public virtual string? PatientName { get; set; }
    
    /// <summary>
    /// 退费原因
    /// </summary>
    public virtual string? ApplyReason { get; set; }
    
    /// <summary>
    /// 状态 0 新增待审核 1 审核中 2 审核完成
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 审核状态 表refund_audit 状态
    /// </summary>
    public virtual int? AuditStatus { get; set; }
    
}

/// <summary>
/// 门诊退费申请分页查询输入参数
/// </summary>
public class PageRefundApplyInput : BasePageInput
{
    /// <summary>
    /// 退费申请ID
    /// </summary>
    public long? ChargeId { get; set; }
    
    /// <summary>
    /// 退费申请类型
    /// </summary>
    public string? BillingType { get; set; }
    
    /// <summary>
    /// 退费申请单号
    /// </summary>
    public string? ApplyNo { get; set; }
    
    /// <summary>
    /// 退费申请时间范围
    /// </summary>
     public DateTime?[] ApplyTimeRange { get; set; }
    
    /// <summary>
    /// 申请部门ID
    /// </summary>
    public long? ApplyDeptId { get; set; }
    
    /// <summary>
    /// 申请部门编码
    /// </summary>
    public string? ApplyDeptCode { get; set; }
    
    /// <summary>
    /// 申请部门名称
    /// </summary>
    public string? ApplyDeptName { get; set; }
    
    /// <summary>
    /// 申请人ID
    /// </summary>
    public long? ApplyUserId { get; set; }
    
    /// <summary>
    /// 申请人编码
    /// </summary>
    public string? ApplyUserCode { get; set; }
    
    /// <summary>
    /// 申请人名称
    /// </summary>
    public string? ApplyUserName { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? CardNo { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 挂号ID
    /// </summary>
    public long? RegisterId { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 退费原因
    /// </summary>
    public string? ApplyReason { get; set; }
    
    /// <summary>
    /// 状态 0 新增待审核 1 审核中 2 审核完成
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 审核状态 表refund_audit 状态
    /// </summary>
    public int? AuditStatus { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 门诊退费申请增加输入参数
/// </summary>
public class AddRefundApplyInput
{
    /// <summary>
    /// 费用记录ID
    /// </summary>
    [Required(ErrorMessage = "费用记录ID不能为空")]
    public long ChargeId { get; set; }
    
    /// <summary>
    ///  患者姓名
    /// </summary>
    public string PatientName { get; set; }
    
    
    /// <summary>
    /// 退费原因
    /// </summary>
    [MaxLength(200, ErrorMessage = "退费原因字符长度不能超过200")]
    [Required(ErrorMessage = "退费原因不能为空")]
    public string ApplyReason { get; set; }
    
 
    
}

/// <summary>
/// 门诊退费申请删除输入参数
/// </summary>
public class DeleteRefundApplyInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 门诊退费申请更新输入参数
/// </summary>
public class UpdateRefundApplyInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 退费申请ID
    /// </summary>    
    public long? ChargeId { get; set; }
     
    
    /// <summary>
    /// 退费原因
    /// </summary>    
    [MaxLength(200, ErrorMessage = "退费原因字符长度不能超过200")]
    public string? ApplyReason { get; set; }
    
    /// <summary>
    /// 状态 0 新增待审核 1 审核中 2 审核完成
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 审核状态 表refund_audit 状态
    /// </summary>    
    public int? AuditStatus { get; set; }
    
}

/// <summary>
/// 门诊退费申请主键查询输入参数
/// </summary>
public class QueryByIdRefundApplyInput : DeleteRefundApplyInput
{
}

