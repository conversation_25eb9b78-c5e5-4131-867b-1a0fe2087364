﻿namespace His.Module.Inpatient;

/// <summary>
/// 预交金输出参数
/// </summary>
public class AdvancePaymentOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 收据号
    /// </summary>
    public string? VoucherNo { get; set; }    
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }    
    
    /// <summary>
    /// 患者ID 描述
    /// </summary>
    public string PatientFkDisplayName { get; set; } 
    
    /// <summary>
    /// 住院登记号
    /// </summary>
    public long? RegisterId { get; set; }    
    
    /// <summary>
    /// 住院登记号 描述
    /// </summary>
    public string RegisterFkDisplayName { get; set; } 
    
    /// <summary>
    /// 住院号
    /// </summary>
    public string? InpatientNo { get; set; }    
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    public string? InpatientSerialNo { get; set; }    
    
    /// <summary>
    /// 病案号
    /// </summary>
    public string? MedicalRecordNo { get; set; }    
    
    /// <summary>
    /// 保险号码
    /// </summary>
    public string? InsuranceNo { get; set; }    
    
    /// <summary>
    /// 预缴金额
    /// </summary>
    public decimal? AdvanceAmount { get; set; }    
    
    /// <summary>
    /// 大写金额
    /// </summary>
    public string? AdvanceAmountChinese { get; set; }    
    
    /// <summary>
    /// 缴费时间
    /// </summary>
    public DateTime? AdvanceTime { get; set; }    
    
    /// <summary>
    /// 付款方式
    /// </summary>
    public string? PaymentMethod { get; set; }    
    
    /// <summary>
    /// 付款记录ID
    /// </summary>
    public long? PaymentRecordId { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 创建组织ID
    /// </summary>
    public long? CreateOrgId { get; set; }    
    
    /// <summary>
    /// 创建组织名称
    /// </summary>
    public string? CreateOrgName { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 预交金数据导入模板实体
/// </summary>
public class ExportAdvancePaymentOutput : ImportAdvancePaymentInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
