using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace His.Module.OutpatientDoctor.Api.Charge.Dto;

public class OutpatientChargeExecuteDto
{
    /// <summary>
    /// 计费id
    /// </summary>
    [Required(ErrorMessage = "计费id不能为空")]
    public long? ChargeId { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
     [Required(ErrorMessage = "执行科室Id不能为空")]
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
     [Required(ErrorMessage = "执行医生Id不能为空")]
    public long? ExecuteDoctorId { get; set; }

    
    /// <summary>
    /// 执行时间
    /// </summary>
    [Required(ErrorMessage = "执行时间不能为空")]
    public DateTime? ExecuteTime { get; set; }
 
}
