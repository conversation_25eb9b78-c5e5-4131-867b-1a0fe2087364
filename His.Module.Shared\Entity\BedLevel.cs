﻿using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 床位等级
/// </summary>
[Tenant("1300000000014")]
[SugarTable("bed_level", "床位等级")]
public class BedLevel : EntityTenant
{
    /// <summary>
    /// 床位等级名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "level_name", ColumnDescription = "床位等级名称", Length = 100)]
    public virtual string LevelName { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 10, DecimalDigits=2)]
    public virtual decimal Amount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
