﻿using His.Module.Shared.Api.Enum;

namespace His.Module.MedicalTech.OtherModelEntity;

/// <summary>
/// 费用类别表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("fee_category", "费用类别表")]
public class FeeCategory : EntityTenant
{
    /// <summary>
    /// 编号
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编号", Length = 32)]
    public virtual string Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 32)]
    public virtual string Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 20)]
    public virtual string PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 20)]
    public virtual string WubiCode { get; set; }

    /// <summary>
    /// 医疗类别
    /// </summary>
    [SugarColumn(ColumnName = "med_category", ColumnDescription = "医疗类别")]
    public virtual MedCategoryEnum? MedCategory { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [SugarColumn(ColumnName = "usage_scope", ColumnDescription = "使用范围")]
    public virtual MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 医保id
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_id", ColumnDescription = "医保id")]
    public virtual short? MedInsId { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_type", ColumnDescription = "医保类型")]
    public virtual MedInsTypeEnum? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string Remark { get; set; }
}