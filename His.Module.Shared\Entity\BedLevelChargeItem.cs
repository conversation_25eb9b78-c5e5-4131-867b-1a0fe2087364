﻿using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 床位等级收费项目
/// </summary>
[Tenant("1300000000014")]
[SugarTable("bed_level_charge_item", "床位等级收费项目")]
public class BedLevelChargeItem : EntityTenant
{
    
    /// <summary>
    /// 床位等级名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "level_id", ColumnDescription = "床位等级id" )]
    public virtual long LevelId { get; set; }
    
    /// <summary>
    /// 床位等级名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "level_name", ColumnDescription = "床位等级名称", Length = 100)]
    public virtual string LevelName { get; set; }
    
    /// <summary>
    /// 收费项目id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "charge_item_id", ColumnDescription = "收费项目id")]
    public virtual long ChargeItemId { get; set; }
    
    /// <summary>
    /// 收费项目编码
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "charge_item_code", ColumnDescription = "收费项目编码", Length = 100)]
    public virtual string ChargeItemCode { get; set; }
    
    /// <summary>
    /// 收费项目名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "charge_item_name", ColumnDescription = "收费项目名称", Length = 100)]
    public virtual string ChargeItemName { get; set; }
    
    /// <summary>
    /// 价格
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "price", ColumnDescription = "价格", Length = 10, DecimalDigits=2)]
    public virtual decimal Price { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "数量")]
    public virtual int Quantity { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "amount", ColumnDescription = "金额", Length = 10, DecimalDigits=2)]
    public virtual decimal Amount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
