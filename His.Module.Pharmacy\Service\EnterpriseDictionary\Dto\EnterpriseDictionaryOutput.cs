﻿namespace His.Module.Pharmacy;

/// <summary>
/// 企业管理输出参数
/// </summary>
public class EnterpriseDictionaryOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 企业编码
    /// </summary>
    public string? EnterpriseCode { get; set; }    
    
    /// <summary>
    /// 企业名称
    /// </summary>
    public string? EnterpriseName { get; set; }    
    
    /// <summary>
    /// 企业名称拼音
    /// </summary>
    public string? EnterpriseNamePinyin { get; set; }    
    
    /// <summary>
    /// 企业类型
    /// </summary>
    public string? EnterpriseType { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }    
    /// <summary>
    ///  联系人
    /// </summary> 
    public   string? ContactName { get; set; }
    /// <summary>
    ///  联系人
    /// </summary> 
    public   string? ContactPhone{ get; set; }
    
    /// <summary>
    ///  企业地址
    /// </summary> 
    public   string? EnterpriseAddress { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 企业管理数据导入模板实体
/// </summary>
public class ExportEnterpriseDictionaryOutput : ImportEnterpriseDictionaryInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
