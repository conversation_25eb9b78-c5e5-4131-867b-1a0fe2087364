﻿using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 门诊处方库存分配表，锁定库存时保存分配信息
/// </summary>
[Tenant("1300000000008")]
[SugarTable("outpatient_prescription_inventory_allocation", "门诊处方库存分配表，锁定库存时保存分配信息")]
public class OutpatientPrescriptionInventoryAllocation : EntityTenant
{
    /// <summary>
    /// 药房id
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "药房id")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 库存ID
    /// </summary>
    [SugarColumn(ColumnName = "inventory_id", ColumnDescription = "库存ID")]
    public virtual long? InventoryId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品ID")]
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 处方ID
    /// </summary>
    [SugarColumn(ColumnName = "prescription_id", ColumnDescription = "处方ID")]
    public virtual long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    [SugarColumn(ColumnName = "prescription_detail_id", ColumnDescription = "处方明细ID")]
    public virtual long? PrescriptionDetailId { get; set; }
    
    /// <summary>
    /// 发药记录id
    /// </summary>
    [SugarColumn(ColumnName = "send_record_id", ColumnDescription = "发药记录id")]
    public virtual long? SendRecordId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 100)]
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "药品规格", Length = 100)]
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 分配数量
    /// </summary>
    [SugarColumn(ColumnName = "allocated_quantity", ColumnDescription = "分配数量")]
    public virtual int? AllocatedQuantity { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "药品单位", Length = 100)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 批次号
    /// </summary>
    [SugarColumn(ColumnName = "batch_no", ColumnDescription = "批次号", Length = 100)]
    public virtual string? BatchNo { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 创建机构ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构ID")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
