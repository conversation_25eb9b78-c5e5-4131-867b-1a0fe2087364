using Admin.NET.Core;
using Furion.DependencyInjection;
using His.Module.Insurance.Service.Settlement;
using His.Module.Insurance.Service.Settlement.Dto;
using His.Module.Insurance.Service.Settlement.Dto.Patient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
 
using System.Text;
using System.Text.Json;
using System.Xml.Linq;
using Yitter.IdGenerator;

namespace His.Module.InsuranceSettlementForLiaocheng.Infrastructure;

public class InsuranceSettlementBasicApi( 
    UserManager userManager,
    ILogger<InsuranceSettlementBasicApi> _logger,
    IOptions<InsuranceOptions> insuranceOptions,
    HttpClient httpClient)
    : ITransient
{
    private HttpClient _httpClient = httpClient; 
    public readonly XNamespace XNamespace = "http://service.communication.service.dareway.com/";

    public Task<BaseSettlementResponse> Request<T>(T inParam, string methodName)
    {
     
        throw new NotImplementedException();
    }

     

    /// <summary>
    /// 初始化
    /// </summary>
    /// <returns></returns>

    public Task<bool> Init()
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// 登录获取用户key
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<string> GetUserKey()
    {
        var settlement = insuranceOptions.Value.Settlement;
 
       var doc=await    SendSoapRequestAsync("loginByYybm",
           userManager.NickName,
           settlement.DefaultPwd,
           settlement.HosCode);
        
 
        var loginResult1 = doc.Descendants(XNamespace + "loginByYybmResponse")
            .Select(e => e.Element("loginResult1")?.Value)
            .FirstOrDefault();

        if (string.IsNullOrEmpty(loginResult1)) throw new Exception("解析 XML 失败:" + doc);
        // 使用 System.Text.Json 或 Newtonsoft.Json 反序列化为字典
        var basicResponse = JsonSerializer.Deserialize<BaseSettlementResponse>(loginResult1);

        if (basicResponse is { IsSuccess: true })
            return basicResponse.resulttext;
        else
            throw new Exception(basicResponse?.resulttext??"解析 XML 失败:"+doc);
    }
    /// <summary>
    /// 请求webservice 服务
    /// </summary>
    /// <param name="actionName"></param>
    /// <param name="array"></param>
    /// <returns></returns>
    public async Task<XDocument> SendSoapRequestAsync(string method,string jsonParam)
    {
        var parasm = new string[]
        {
            
            
        };
     return   await  SendSoapRequestAsync("invoke", parasm);

    }
    // /// <summary>
    // /// 请求 invoke 服务
    // /// </summary>
    // /// <param name="userKey"></param>
    // /// <param name="sbjgbh">社保机构编码</param>
    // /// <param name="method"></param>
    // /// <param name="jsonObj"></param>
    // /// <returns></returns>
    // public async Task<XDocument> SendSoapInvokeRequestAsync<T>(string userKey,string sbjgbh,string method,T jsonObj)
    // {
    //     var parasm = new string[]
    //     {
    //         sbjgbh,
    //         userKey,
    //         method,
    //         JsonSerializer.Serialize(jsonObj),
    //     };
    //     return  await  SendSoapRequestAsync("invoke", parasm);
    //   
    //
    // }
    /// <summary>
    /// 
    /// </summary> 
    /// <param name="method"></param>
    /// <param name="jsonObj"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<BaseSettlementResponse> SendSoapInvokeRequestAsync( string method,
        BaseSettlementRequest jsonObj)
    {
        return await SendSoapInvokeRequestAsync(jsonObj.UserKey,"37000000", method, jsonObj);
    }

    /// <summary>
    /// 请求 invoke 服务
    /// </summary>
    /// <param name="userKey"></param>
    /// <param name="sbjgbh">社保机构编码</param>
    /// <param name="method"></param>
    /// <param name="jsonObj"></param>
    /// <returns></returns>
    public async Task<BaseSettlementResponse> SendSoapInvokeRequestAsync<T>(string userKey,string sbjgbh,string method,T jsonObj)
    {
        var businessNo=DateTime.Now.ToString("yyyyMMddHHmmss")+"_"+ YitIdHelper.NextId();
        if (string.IsNullOrEmpty(sbjgbh))
        {
            sbjgbh = insuranceOptions.Value.Settlement.HosCode;
        }

        var parasm = new string[]
        {
            sbjgbh,
            userKey,
            businessNo,
            method,
            JsonSerializer.Serialize(jsonObj),
        };
        var args = new StringBuilder();
        for (var i = 0; i < parasm.Length; i++)
        {
            args.Append($"<arg{i}>{parasm[i]}</arg{i}>");
        }

        var inputXml = $@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:ser=""http://service.communication.service.dareway.com/""> 
                       <soapenv:Header/>
                       <soapenv:Body>
                           <ser:invoke>
                               {args}
                           </ser:invoke>
                       </soapenv:Body>
                    </soapenv:Envelope>";

        // 发送 SOAP 请求并返回响应的 XML 文档
        
        var doc=    await SendSoapRequestAsync(inputXml);
        var basicResponse = this.ParseResponseDoc(doc);

        if (basicResponse is { IsSuccess: true })
            return basicResponse;      
        else
            throw new Exception($"[{method}]请求失败 入参：{inputXml} 出参：code={basicResponse.resultcode};text={basicResponse?.resulttext}");

    }
    /// <summary>
    /// 请求webservice 服务
    /// </summary>
    /// <param name="actionName"></param>
    /// <param name="array"></param>
    /// <returns></returns>
    public async Task<XDocument> SendSoapRequestAsync(string actionName,params string[] array)
    {
        var args = new StringBuilder();
        for (var i = 0; i < array.Length; i++)
        {
            args.Append($"<arg{i}>{array[i]}</arg{i}>");
        }

        var inputXml = $@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:ser=""http://service.communication.service.dareway.com/""> 
                       <soapenv:Header/>
                       <soapenv:Body>
                           <ser:{actionName}>
                               {args}
                           </ser:{actionName}>
                       </soapenv:Body>
                    </soapenv:Envelope>";

        // 发送 SOAP 请求并返回响应的 XML 文档
        return await SendSoapRequestAsync(inputXml);
    }

    /// <summary>
    /// 请求webservice 服务
    /// </summary>
    /// <param name="inputXml"></param>
    /// <returns>未解析的xml</returns>
    /// <exception cref="Exception"></exception>
   public async Task<XDocument> SendSoapRequestAsync(string inputXml)
   {
       var url = insuranceOptions.Value.Settlement.Url;

       using var client = new HttpClient();
       var content = new StringContent(inputXml, Encoding.UTF8, "text/xml");
       content.Headers.Add("SOAPAction", "http://service.communication.service.dareway.com/invoke");
       var result = "";
       try
       {
           _logger.LogInformation("发送 SOAP 请求: {InputXml}", inputXml);

           var response = await client.PostAsync(url, content);
 
           using (var stream = await response.Content.ReadAsStreamAsync())
           using (var reader = new StreamReader(stream, Encoding.UTF8))
           {
               result = await reader.ReadToEndAsync();
           }

           _logger.LogInformation("接收 SOAP 响应: {ResponseXml}", result);

           var doc = XDocument.Parse(result);
           if (doc == null)
               throw new Exception("解析 XML 失败");

           return doc;
       }
       catch (Exception ex)
       {
           _logger.LogError(ex, "SOAP 请求失败，请求内容: {InputXml}, 错误详情: {ErrorMessage}", inputXml, ex.Message);
           throw new Exception("SOAP 请求失败"+ $"请求内容: {inputXml};响应内容：{result}; 错误详情: {ex.Message}");
       }
   }
   
   
    /// <summary>
    /// 解析xml
    /// </summary>
    /// <param name="doc"></param>
    /// <param name="actionName"></param>
    /// <param name="elementName"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public BaseSettlementResponse ParseResponseDoc(XDocument doc,string  actionName,string elementName)
    {
        
        // var loginResult1 = doc.Descendants(insuranceSettlementApi.XNamespace + "loginByYybmResponse")
        //     .Select(e => e.Element("loginResult1")?.Value)
        //     .FirstOrDefault();

 
        var loginResult1 = doc.Descendants($"{this.XNamespace }{actionName}Response")
            .Select(e => e.Element(elementName)?.Value)
            .FirstOrDefault();

        if (string.IsNullOrEmpty(loginResult1)) throw new Exception("解析 XML 失败:" + doc);
        // 使用 System.Text.Json 或 Newtonsoft.Json 反序列化为字典
        var basicResponse = JsonSerializer.Deserialize<BaseSettlementResponse>(loginResult1);

        if (basicResponse!=null)
            return  basicResponse;
        else
            throw new Exception(basicResponse?.resulttext??"解析 XML 失败:"+doc);
    }
    /// <summary>
    /// 解析xml
    /// </summary>
    /// <param name="doc"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public BaseSettlementResponse ParseResponseDoc(XDocument doc)
    {
        
        // var loginResult1 = doc.Descendants(insuranceSettlementApi.XNamespace + "loginByYybmResponse")
        //     .Select(e => e.Element("loginResult1")?.Value)
        //     .FirstOrDefault();

 
        var loginResult1 = doc.Descendants($"{this.XNamespace }invokeResponse")
            .Select(e => e.Element("resultContent")?.Value)
            .FirstOrDefault();

        if (string.IsNullOrEmpty(loginResult1)) throw new Exception("解析 XML 失败:" + doc);
        // 使用 System.Text.Json 或 Newtonsoft.Json 反序列化为字典
        var basicResponse = JsonSerializer.Deserialize<BaseSettlementResponse>(loginResult1);

        if (basicResponse!=null)
            return  basicResponse;
        else
            throw new Exception(basicResponse?.resulttext??"解析 XML 失败:"+doc);
    }
}