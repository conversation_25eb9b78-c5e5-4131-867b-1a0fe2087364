﻿using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;


namespace His.Module.Shared;

/// <summary>
/// 床位等级基础输入参数
/// </summary>
public class BedLevelBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 床位等级名称
    /// </summary>
    [Required(ErrorMessage = "床位等级名称不能为空")]
    public virtual string LevelName { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    [Required(ErrorMessage = "金额不能为空")]
    public virtual decimal? Amount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
}

/// <summary>
/// 床位等级分页查询输入参数
/// </summary>
public class PageBedLevelInput : BasePageInput
{
    /// <summary>
    /// 床位等级名称
    /// </summary>
    public string LevelName { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 床位等级增加输入参数
/// </summary>
public class AddBedLevelInput
{
    /// <summary>
    /// 床位等级名称
    /// </summary>
    [Required(ErrorMessage = "床位等级名称不能为空")]
    [MaxLength(100, ErrorMessage = "床位等级名称字符长度不能超过100")]
    public string LevelName { get; set; }
    
 
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    
   /// <summary>
   /// 收费项目
   /// </summary>
    public List<AddBedLevelChargeItemInput> ChargeItems { get; set; }
    
}
public class AddBedLevelChargeItemInput  
{
    
    
    
    /// <summary>
    /// 收费项目id
    /// </summary> 
    [Required]
    public virtual long ChargeItemId { get; set; }
    
    /// <summary>
    /// 收费项目编码
    /// </summary> 
    [Required]
    public virtual string ChargeItemCode { get; set; }
    
    /// <summary>
    /// 收费项目名称
    /// </summary>
    [Required]
    public virtual string ChargeItemName { get; set; }
    
    /// <summary>
    /// 价格
    /// </summary>
    [Required]
    public virtual decimal Price { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [Required] 
    public virtual int Quantity { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    [Required] 
    public virtual decimal Amount { get; set; }
    
    
}

/// <summary>
/// 床位等级删除输入参数
/// </summary>
public class DeleteBedLevelInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 床位等级更新输入参数
/// </summary>
public class UpdateBedLevelInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 床位等级名称
    /// </summary>    
    [Required(ErrorMessage = "床位等级名称不能为空")]
    [MaxLength(100, ErrorMessage = "床位等级名称字符长度不能超过100")]
    public string LevelName { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>    
    [Required(ErrorMessage = "金额不能为空")]
    public decimal? Amount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(255, ErrorMessage = "备注字符长度不能超过255")]
    public string? Remark { get; set; }
    /// 收费项目
    /// </summary>
    public List<AddBedLevelChargeItemInput> ChargeItems { get; set; }
}
/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetBedLevelStatusInput : BaseStatusInput
{
}
/// <summary>
/// 床位等级主键查询输入参数
/// </summary>
public class QueryByIdBedLevelInput : DeleteBedLevelInput
{
}

/// <summary>
/// 床位等级数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportBedLevelInput : BaseImportInput
{
    /// <summary>
    /// 床位等级名称
    /// </summary>
    [ImporterHeader(Name = "*床位等级名称")]
    [ExporterHeader("*床位等级名称", Format = "", Width = 25, IsBold = true)]
    public string LevelName { get; set; }
    
    /// <summary>
    /// 金额
    /// </summary>
    [ImporterHeader(Name = "*金额")]
    [ExporterHeader("*金额", Format = "", Width = 25, IsBold = true)]
    public decimal? Amount { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
}
