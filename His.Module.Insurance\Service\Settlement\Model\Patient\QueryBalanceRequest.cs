using His.Module.Insurance.Service.Settlement.Dto;

namespace His.Module.Insurance.Service.Settlement.Model.Patient;

public class QueryBalanceRequest :   PatientBaseSettlementDto
{
    /// <summary>
    /// 卡号。若由地纬DLL控制读卡器，p_kh可不传；若由his控制读卡器，p_kh为必传。
    /// </summary>
    public string p_kh { get; set; }

    /// <summary>
    /// 个人编号（社会保障号码或身份证号）
    /// </summary>
    public string p_grbh { get; set; }
}