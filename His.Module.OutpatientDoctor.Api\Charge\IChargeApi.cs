using His.Module.OutpatientDoctor.Api.Charge.Dto;
namespace His.Module.OutpatientDoctor.Api.Charge;

public interface IChargeApi
{
    // 门诊计费
    Task<OutpatientChargeResult> Add(OutpatientChargeDto dto);

    /// <summary>
    /// 批量计费 ➕
    /// </summary>
    /// <param name="dtos"></param>
    /// <returns></returns>
    Task BatchAdd(List<OutpatientChargeDto> dtos);
    
    // 退费
     Task<bool> Refund(OutpatientChargeRefundDto dto);

   /// <summary>
   /// 项目执行状态
   /// </summary>
   /// <param name="dto"></param>
   /// <returns></returns>
    Task<bool> ExecuteStatus(OutpatientChargeExecuteDto dto);
   /// <summary>
   /// 项目收费状态
   /// </summary>
   /// <param name="dto"></param>
   /// <returns></returns>
    Task<OutpatientChargeResult> ConfirmStatus(OutpatientChargeConfirmDto dto);

    /// <summary>
    /// 批量确认收费
    /// </summary>
    /// <param name="dtos"></param>
    /// <returns></returns>
    Task BatchConfirmStatus(List<OutpatientChargeConfirmDto> dtos);
   
   
    /// <summary>
    /// 获取门诊收费明细
    /// </summary>
    /// <param name="chargeIds"></param>
    /// <returns></returns>
    Task<List<OutpatientChargeDetailDto>> GetChargeList(long?[] chargeIds);

    /// <summary>
    /// 获取发票号
    /// </summary>
    /// <returns></returns>
    Task<string> GetInvoiceNumber();
    
}