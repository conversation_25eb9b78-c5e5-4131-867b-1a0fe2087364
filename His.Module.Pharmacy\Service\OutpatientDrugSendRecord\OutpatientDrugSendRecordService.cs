﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 门诊发药记录表服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class OutpatientDrugSendRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<OutpatientDrugSendRecord> _outpatientDrugSendRecordRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public OutpatientDrugSendRecordService(SqlSugarRepository<OutpatientDrugSendRecord> outpatientDrugSendRecordRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _outpatientDrugSendRecordRep = outpatientDrugSendRecordRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询门诊发药记录表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询门诊发药记录表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<OutpatientDrugSendRecordOutput>> Page(PageOutpatientDrugSendRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _outpatientDrugSendRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.SendNo.Contains(input.Keyword) || u.SendUserName.Contains(input.Keyword) || u.AuditUserName.Contains(input.Keyword) || u.PickUserName.Contains(input.Keyword) || u.CheckUserName.Contains(input.Keyword) || u.StorageCode.Contains(input.Keyword) || u.PatientName.Contains(input.Keyword) || u.VisitNo.Contains(input.Keyword) || u.PrescriptionNo.Contains(input.Keyword) || u.PrescriptionType.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.DoctorName.Contains(input.Keyword) || u.InvoiceNumber.Contains(input.Keyword) || u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) || u.DrugType.Contains(input.Keyword) || u.Spec.Contains(input.Keyword) || u.Unit.Contains(input.Keyword) || u.SingleDoseUnit.Contains(input.Keyword) || u.DecoctionMethod.Contains(input.Keyword) || u.BatchNo.Contains(input.Keyword) || u.ApprovalNumber.Contains(input.Keyword) || u.MedicineCode.Contains(input.Keyword) || u.ManufacturerName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SendNo), u => u.SendNo.Contains(input.SendNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SendUserName), u => u.SendUserName.Contains(input.SendUserName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.AuditUserName), u => u.AuditUserName.Contains(input.AuditUserName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PickUserName), u => u.PickUserName.Contains(input.PickUserName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.CheckUserName), u => u.CheckUserName.Contains(input.CheckUserName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageCode), u => u.StorageCode.Contains(input.StorageCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName), u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.VisitNo), u => u.VisitNo.Contains(input.VisitNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrescriptionNo), u => u.PrescriptionNo.Contains(input.PrescriptionNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrescriptionType), u => u.PrescriptionType.Contains(input.PrescriptionType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeptName), u => u.DeptName.Contains(input.DeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DoctorName), u => u.DoctorName.Contains(input.DoctorName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InvoiceNumber), u => u.InvoiceNumber.Contains(input.InvoiceNumber.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Spec), u => u.Spec.Contains(input.Spec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SingleDoseUnit), u => u.SingleDoseUnit.Contains(input.SingleDoseUnit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DecoctionMethod), u => u.DecoctionMethod.Contains(input.DecoctionMethod.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BatchNo), u => u.BatchNo.Contains(input.BatchNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApprovalNumber), u => u.ApprovalNumber.Contains(input.ApprovalNumber.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicineCode), u => u.MedicineCode.Contains(input.MedicineCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ManufacturerName), u => u.ManufacturerName.Contains(input.ManufacturerName.Trim()))
            .WhereIF(input.SendUserId != null, u => u.SendUserId == input.SendUserId)
            .WhereIF(input.SendTimeRange?.Length == 2, u => u.SendTime >= input.SendTimeRange[0] && u.SendTime <= input.SendTimeRange[1])
            .WhereIF(input.AuditUserId != null, u => u.AuditUserId == input.AuditUserId)
            .WhereIF(input.AuditTimeRange?.Length == 2, u => u.AuditTime >= input.AuditTimeRange[0] && u.AuditTime <= input.AuditTimeRange[1])
            .WhereIF(input.PickUserId != null, u => u.PickUserId == input.PickUserId)
            .WhereIF(input.PickTimeRange?.Length == 2, u => u.PickTime >= input.PickTimeRange[0] && u.PickTime <= input.PickTimeRange[1])
            .WhereIF(input.CheckUserId != null, u => u.CheckUserId == input.CheckUserId)
            .WhereIF(input.CheckTimeRange?.Length == 2, u => u.CheckTime >= input.CheckTimeRange[0] && u.CheckTime <= input.CheckTimeRange[1])
            .WhereIF(input.StorageId != null, u => u.StorageId == input.StorageId)
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.RegisterId != null, u => u.RegisterId == input.RegisterId)
            .WhereIF(input.PrescriptionId != null, u => u.PrescriptionId == input.PrescriptionId)
            .WhereIF(input.PrescriptionTimeRange?.Length == 2, u => u.PrescriptionTime >= input.PrescriptionTimeRange[0] && u.PrescriptionTime <= input.PrescriptionTimeRange[1])
            .WhereIF(input.InventoryId != null, u => u.InventoryId == input.InventoryId)
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.DoctorId != null, u => u.DoctorId == input.DoctorId)
            .WhereIF(input.ChargeStaffId != null, u => u.ChargeStaffId == input.ChargeStaffId)
            .WhereIF(input.ChargeId != null, u => u.ChargeId == input.ChargeId)
            .WhereIF(input.ChargeTimeRange?.Length == 2, u => u.ChargeTime >= input.ChargeTimeRange[0] && u.ChargeTime <= input.ChargeTimeRange[1])
            .WhereIF(input.DrugId != null, u => u.DrugId == input.DrugId)
            .WhereIF(input.Quantity != null, u => u.Quantity == input.Quantity)
            .WhereIF(input.MedicationRoutesId != null, u => u.MedicationRoutesId == input.MedicationRoutesId)
            .WhereIF(input.FrequencyId != null, u => u.FrequencyId == input.FrequencyId)
            .WhereIF(input.HerbsQuantity != null, u => u.HerbsQuantity == input.HerbsQuantity)
            .WhereIF(input.IsDecoction.HasValue, u => u.IsDecoction == (int)input.IsDecoction)
            .WhereIF(input.RefundQuantity != null, u => u.RefundQuantity == input.RefundQuantity)
            .WhereIF(input.ProductionDateRange?.Length == 2, u => u.ProductionDate >= input.ProductionDateRange[0] && u.ProductionDate <= input.ProductionDateRange[1])
            .WhereIF(input.ExpirationDateRange?.Length == 2, u => u.ExpirationDate >= input.ExpirationDateRange[0] && u.ExpirationDate <= input.ExpirationDateRange[1])
            .WhereIF(input.ManufacturerId != null, u => u.ManufacturerId == input.ManufacturerId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .InnerJoin<DrugStorage>((u, storage) => u.StorageId == storage.Id && storage.OrgId
                ==long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value) )
            
            .LeftJoin<EnterpriseDictionary>((u, storage, manufacturer) => u.ManufacturerId == manufacturer.Id)
            .Select((u, storage, manufacturer) => new OutpatientDrugSendRecordOutput
            {
                Id = u.Id,
                SendNo = u.SendNo,
                SendUserId = u.SendUserId,
                SendUserName = u.SendUserName,
                SendTime = u.SendTime,
                AuditUserId = u.AuditUserId,
                AuditUserName = u.AuditUserName,
                AuditTime = u.AuditTime,
                PickUserId = u.PickUserId,
                PickUserName = u.PickUserName,
                PickTime = u.PickTime,
                CheckUserId = u.CheckUserId,
                CheckUserName = u.CheckUserName,
                CheckTime = u.CheckTime,
                StorageId = u.StorageId,
                StorageFkDisplayName = $"{storage.StorageName}",
                StorageCode = u.StorageCode,
                PatientId = u.PatientId,
                PatientName = u.PatientName,
                RegisterId = u.RegisterId,
                VisitNo = u.VisitNo,
                CardNo = u.CardNo,
                OutpatientNo = u.OutpatientNo,
                PrescriptionId = u.PrescriptionId,
                PrescriptionNo = u.PrescriptionNo,
                PrescriptionTime = u.PrescriptionTime,
                PrescriptionType = u.PrescriptionType,
                InventoryId = u.InventoryId,
                DeptId = u.DeptId,
                DeptName = u.DeptName,
                DoctorId = u.DoctorId,
                DoctorName = u.DoctorName,
                InvoiceNumber = u.InvoiceNumber,
                ChargeStaffId = u.ChargeStaffId,
                ChargeId = u.ChargeId,
                ChargeTime = u.ChargeTime,
                DrugId = u.DrugId,
                DrugCode = u.DrugCode,
                DrugName = u.DrugName,
                DrugType = u.DrugType,
                GroupNo = u.GroupNo,
                GroupFlag = u.GroupFlag,
                Spec = u.Spec,
                Unit = u.Unit,
                Quantity = u.Quantity,
                SingleDose = u.SingleDose,
                SingleDoseUnit = u.SingleDoseUnit,
                MedicationRoutesId = u.MedicationRoutesId,
                MedicationRoutesName = u.MedicationRoutesName,
                FrequencyName = u.FrequencyName,
                FrequencyId = u.FrequencyId,
                MedicationDays = u.MedicationDays,
                Price = u.Price,
                Amount = u.Amount,
                HerbsQuantity = u.HerbsQuantity,
                DecoctionMethod = u.DecoctionMethod,
                IsDecoction =(YesNoEnum) u.IsDecoction,
                RefundQuantity = u.RefundQuantity,
                RefundAmount = u.RefundAmount,
                BatchNo = u.BatchNo,
                ProductionDate = u.ProductionDate,
                ExpirationDate = u.ExpirationDate,
                ApprovalNumber = u.ApprovalNumber,
                MedicineCode = u.MedicineCode,
                ManufacturerId = u.ManufacturerId,
                ManufacturerFkDisplayName = $"{manufacturer.EnterpriseName}",
                ManufacturerName = u.ManufacturerName,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取门诊发药记录表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取门诊发药记录表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<OutpatientDrugSendRecord> Detail([FromQuery] QueryByIdOutpatientDrugSendRecordInput input)
    {
        return await _outpatientDrugSendRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }
 

    
    
    /// <summary>
    /// 导出门诊发药记录表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出门诊发药记录表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageOutpatientDrugSendRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportOutpatientDrugSendRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Value, x => x.Label);
        var decoctionMethodDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DecoctionMethod" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType);
            e.DecoctionMethodDictLabel = decoctionMethodDictMap.GetValueOrDefault(e.DecoctionMethod ?? "", e.DecoctionMethod);
        });
        return ExcelHelper.ExportTemplate(list, "门诊发药记录表导出记录");
    }
     
}
