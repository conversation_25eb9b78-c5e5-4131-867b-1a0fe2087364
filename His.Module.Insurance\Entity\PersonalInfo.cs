﻿using Admin.NET.Core;
namespace His.Module.Insurance.Entity;

/// <summary>
/// 参保个人信息
/// </summary>
[Tenant("1300000000013")]
[SugarTable("personal_info", "参保个人信息")]
public class PersonalInfo : EntityTenant
{
    /// <summary>
    /// 挂号id
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "挂号id")]
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 就诊唯一号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊唯一号", Length = 50)]
    public virtual string? VisitNo { get; set; }
    
    /// <summary>
    /// 门诊号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号", Length = 50)]
    public virtual string? OutpatientNo { get; set; }
    
    /// <summary>
    /// 患者id
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者id")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 姓名
    /// </summary>
    [SugarColumn(ColumnName = "xm", ColumnDescription = "姓名", Length = 100)]
    public virtual string? Xm { get; set; }
    
    /// <summary>
    /// 性别：1男，2女，9不确定
    /// </summary>
    [SugarColumn(ColumnName = "xb", ColumnDescription = "性别：1男，2女，9不确定", Length = 10)]
    public virtual string? Xb { get; set; }
    
    /// <summary>
    /// 个人编号或社会保障号码
    /// </summary>
    [SugarColumn(ColumnName = "grbh", ColumnDescription = "个人编号或社会保障号码", Length = 50)]
    public virtual string? Grbh { get; set; }
    
    /// <summary>
    /// 卡号（阳煤或二维码读卡时为空）
    /// </summary>
    [SugarColumn(ColumnName = "kh", ColumnDescription = "卡号（阳煤或二维码读卡时为空）", Length = 50)]
    public virtual string? Kh { get; set; }
    
    /// <summary>
    /// 身份证号码
    /// </summary>
    [SugarColumn(ColumnName = "sfzhm", ColumnDescription = "身份证号码", Length = 18)]
    public virtual string? Sfzhm { get; set; }
    
    /// <summary>
    /// 灰名单标志：0灰名单，1白名单
    /// </summary>
    [SugarColumn(ColumnName = "zfbz", ColumnDescription = "灰名单标志：0灰名单，1白名单", Length = 1)]
    public virtual string? Zfbz { get; set; }
    
    /// <summary>
    /// 灰名单原因（白名单为空）
    /// </summary>
    [SugarColumn(ColumnName = "zfsm", ColumnDescription = "灰名单原因（白名单为空）", Length = 0)]
    public virtual string? Zfsm { get; set; }
    
    /// <summary>
    /// 单位名称
    /// </summary>
    [SugarColumn(ColumnName = "dwmc", ColumnDescription = "单位名称", Length = 200)]
    public virtual string? Dwmc { get; set; }
    
    /// <summary>
    /// 医疗人员类别（汉字）
    /// </summary>
    [SugarColumn(ColumnName = "ylrylb", ColumnDescription = "医疗人员类别（汉字）", Length = 100)]
    public virtual string? Ylrylb { get; set; }
    
    /// <summary>
    /// 异地标志：1是，0否
    /// </summary>
    [SugarColumn(ColumnName = "ydbz", ColumnDescription = "异地标志：1是，0否", Length = 1)]
    public virtual string? Ydbz { get; set; }
    
    /// <summary>
    /// 疾病编码（格式：名称#m编码/...）
    /// </summary>
    [SugarColumn(ColumnName = "mzdbjbs", ColumnDescription = "疾病编码（格式：名称#m编码/...）", Length = 0)]
    public virtual string? Mzdbjbs { get; set; }
    
    /// <summary>
    /// 门诊大病备注
    /// </summary>
    [SugarColumn(ColumnName = "mzdbbz", ColumnDescription = "门诊大病备注", Length = 0)]
    public virtual string? Mzdbbz { get; set; }
    
    /// <summary>
    /// 社保局或社保机构编号
    /// </summary>
    [SugarColumn(ColumnName = "sbjgbh", ColumnDescription = "社保局或社保机构编号", Length = 20)]
    public virtual string? Sbjgbh { get; set; }
    
    /// <summary>
    /// 人群类别：A职工，B居民，其他见字典
    /// </summary>
    [SugarColumn(ColumnName = "rqlb", ColumnDescription = "人群类别：A职工，B居民，其他见字典", Length = 10)]
    public virtual string? Rqlb { get; set; }
    
    /// <summary>
    /// 出生日期
    /// </summary>
    [SugarColumn(ColumnName = "csrq", ColumnDescription = "出生日期")]
    public virtual DateTime? Csrq { get; set; }
    
    /// <summary>
    /// 参保地市编号（省异地用）
    /// </summary>
    [SugarColumn(ColumnName = "cbdsbh", ColumnDescription = "参保地市编号（省异地用）", Length = 20)]
    public virtual string? Cbdsbh { get; set; }
    
    /// <summary>
    /// 参保机构名称（省异地用）
    /// </summary>
    [SugarColumn(ColumnName = "cbjgmc", ColumnDescription = "参保机构名称（省异地用）", Length = 200)]
    public virtual string? Cbjgmc { get; set; }
    
    /// <summary>
    /// 门诊定点标志：1为当前医院是指定门诊统筹医院（异地不返回）
    /// </summary>
    [SugarColumn(ColumnName = "mzddbz", ColumnDescription = "门诊定点标志：1为当前医院是指定门诊统筹医院（异地不返回）", Length = 1)]
    public virtual string? Mzddbz { get; set; }
    
    /// <summary>
    /// 余额
    /// </summary>
    [SugarColumn(ColumnName = "ye", ColumnDescription = "余额", Length = 12, DecimalDigits=2)]
    public virtual decimal? Ye { get; set; }
    
    /// <summary>
    /// 账户余额
    /// </summary>
    [SugarColumn(ColumnName = "zhye", ColumnDescription = "账户余额", Length = 12, DecimalDigits=2)]
    public virtual decimal? Zhye { get; set; }
    
    /// <summary>
    /// 共济账户余额（有共济绑定关系时返回）
    /// </summary>
    [SugarColumn(ColumnName = "gjzhye", ColumnDescription = "共济账户余额（有共济绑定关系时返回）", Length = 12, DecimalDigits=2)]
    public virtual decimal? Gjzhye { get; set; }
    
    /// <summary>
    /// 门慢二级代码（聊城使用）
    /// </summary>
    [SugarColumn(ColumnName = "mzmxm_ejjbbm", ColumnDescription = "门慢二级代码（聊城使用）", Length = 50)]
    public virtual string? MzmxmEjjbbm { get; set; }
    
    /// <summary>
    /// 门慢二级名称（聊城使用）
    /// </summary>
    [SugarColumn(ColumnName = "mzmxm_ejjbmc", ColumnDescription = "门慢二级名称（聊城使用）", Length = 100)]
    public virtual string? MzmxmEjjbmc { get; set; }
    
    /// <summary>
    /// 人员ID
    /// </summary>
    [SugarColumn(ColumnName = "ryid", ColumnDescription = "人员ID", Length = 50)]
    public virtual string? Ryid { get; set; }
    
    /// <summary>
    /// 行政区划（山东省内异地刷卡使用）
    /// </summary>
    [SugarColumn(ColumnName = "xzqh", ColumnDescription = "行政区划（山东省内异地刷卡使用）", Length = 20)]
    public virtual string? Xzqh { get; set; }
    
    /// <summary>
    /// 社保卡异地标志：1是，0否（山东）
    /// </summary>
    [SugarColumn(ColumnName = "sbkydbz", ColumnDescription = "社保卡异地标志：1是，0否（山东）", Length = 1)]
    public virtual string? Sbkydbz { get; set; }
    
    /// <summary>
    /// 其他字段（json格式）
    /// </summary>
    [SugarColumn(ColumnName = "other", ColumnDescription = "其他字段（json格式）")]
    public virtual object? Other { get; set; }
    
    /// <summary>
    /// 创建机构ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建机构ID")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建机构名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
