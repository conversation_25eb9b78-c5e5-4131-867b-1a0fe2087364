﻿namespace His.Module.Shared.Service;

/// <summary>
/// 费用类别输出参数
/// </summary>
public class FeeCategoryOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 编号
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    public string? WubiCode { get; set; }

    /// <summary>
    /// 医疗类别
    /// </summary>
    public MedCategoryEnum? MedCategory { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 医保id
    /// </summary>
    public Int16? MedInsId { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    public MedInsTypeEnum? MedInsType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    public string? MedicalPoolingCategory { get; set; }
    /// <summary>
    /// 医疗统筹类别名称
    /// </summary>
    public string? MedicalPoolingCategoryName { get; set; }

    /// <summary>
    /// 医疗统筹类别
    /// </summary>
    public string? MedicalInsuranceFlag { get; set; }
    /// <summary>
    /// 医疗统筹类别名称
    /// </summary>
    public string? MedicalInsuranceFlagName { get; set; }
}

/// <summary>
/// 费用类别数据导入模板实体
/// </summary>
public class ExportFeeCategoryOutput : ImportFeeCategoryInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}