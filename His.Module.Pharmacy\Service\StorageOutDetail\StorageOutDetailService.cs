﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
namespace His.Module.Pharmacy;

/// <summary>
/// 药品出库明细表服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class StorageOutDetailService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<StorageOutDetail> _storageOutDetailRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public StorageOutDetailService(SqlSugarRepository<StorageOutDetail> storageOutDetailRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _storageOutDetailRep = storageOutDetailRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询药品出库明细表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品出库明细表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<StorageOutDetailOutput>> Page(PageStorageOutDetailInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _storageOutDetailRep.AsQueryable()
            .WhereIF(input.StorageInId.HasValue, u => u.StorageOutId == input.StorageInId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.DrugType.Contains(input.Keyword) || u.StorageOutNo.Contains(input.Keyword) || u.DrugCode.Contains(input.Keyword) || u.DrugName.Contains(input.Keyword) || u.Spec.Contains(input.Keyword) || u.Unit.Contains(input.Keyword) || u.BatchNo.Contains(input.Keyword) || u.ApprovalNumber.Contains(input.Keyword) || u.MedicineCode.Contains(input.Keyword) || u.ManufacturerName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageOutNo), u => u.StorageOutNo.Contains(input.StorageOutNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugCode), u => u.DrugCode.Contains(input.DrugCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugName), u => u.DrugName.Contains(input.DrugName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Spec), u => u.Spec.Contains(input.Spec.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Unit), u => u.Unit.Contains(input.Unit.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BatchNo), u => u.BatchNo.Contains(input.BatchNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ApprovalNumber), u => u.ApprovalNumber.Contains(input.ApprovalNumber.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MedicineCode), u => u.MedicineCode.Contains(input.MedicineCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ManufacturerName), u => u.ManufacturerName.Contains(input.ManufacturerName.Trim()))
            .WhereIF(input.DrugId != null, u => u.DrugId == input.DrugId)
            .WhereIF(input.Quantity != null, u => u.Quantity == input.Quantity)
            .WhereIF(input.ProductionDateRange?.Length == 2, u => u.ProductionDate >= input.ProductionDateRange[0] && u.ProductionDate <= input.ProductionDateRange[1])
            .WhereIF(input.ExpirationDateRange?.Length == 2, u => u.ExpirationDate >= input.ExpirationDateRange[0] && u.ExpirationDate <= input.ExpirationDateRange[1])
            .WhereIF(input.ManufacturerId != null, u => u.ManufacturerId == input.ManufacturerId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<EnterpriseDictionary>((u, manufacturer) => u.ManufacturerId == manufacturer.Id)
            .Select((u, manufacturer) => new StorageOutDetailOutput
            {
                Id = u.Id,
                DrugType = u.DrugType,
                StorageOutNo = u.StorageOutNo,
                DrugId = u.DrugId,
                DrugCode = u.DrugCode,
                DrugName = u.DrugName,
                Spec = u.Spec,
                Unit = u.Unit,
                PurchasePrice = u.PurchasePrice,
                SalePrice = u.SalePrice,
                TotalPurchasePrice = u.TotalPurchasePrice,
                TotalSalePrice = u.TotalSalePrice,
                Quantity = u.Quantity,
                BatchNo = u.BatchNo,
                ProductionDate = u.ProductionDate,
                ExpirationDate = u.ExpirationDate,
                ApprovalNumber = u.ApprovalNumber,
                MedicineCode = u.MedicineCode,
                ManufacturerId = u.ManufacturerId,
                ManufacturerFkDisplayName = $"{manufacturer.EnterpriseName}",
                ManufacturerName = u.ManufacturerName,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品出库明细表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品出库明细表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<StorageOutDetail> Detail([FromQuery] QueryByIdStorageOutDetailInput input)
    {
        return await _storageOutDetailRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品出库明细表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品出库明细表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddStorageOutDetailInput input)
    {
        var entity = input.Adapt<StorageOutDetail>();
        return await _storageOutDetailRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品出库明细表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品出库明细表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateStorageOutDetailInput input)
    {
        var entity = input.Adapt<StorageOutDetail>();
        await _storageOutDetailRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品出库明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品出库明细表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteStorageOutDetailInput input)
    {
        var entity = await _storageOutDetailRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _storageOutDetailRep.FakeDeleteAsync(entity);   //假删除
        //await _storageOutDetailRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品出库明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品出库明细表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteStorageOutDetailInput> input)
    {
        var exp = Expressionable.Create<StorageOutDetail>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _storageOutDetailRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _storageOutDetailRep.FakeDeleteAsync(list);   //假删除
        //return await _storageOutDetailRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataStorageOutDetailInput input)
    {
        var manufacturerIdData = await _storageOutDetailRep.Context.Queryable<EnterpriseDictionary>()
             //      .InnerJoinIF<StorageOutDetail>(input.FromPage, (u, r) => u.Id == r.ManufacturerId)
             .Where(u => u.EnterpriseType == "manufacturer")
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.EnterpriseName}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "manufacturerId", manufacturerIdData },
        };
    }
    
    /// <summary>
    /// 导出药品出库明细表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品出库明细表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageStorageOutDetailInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportStorageOutDetailOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e => {
            e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType);
        });
        return ExcelHelper.ExportTemplate(list, "药品出库明细表导出记录");
    }
    
    /// <summary>
    /// 下载药品出库明细表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品出库明细表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportStorageOutDetailOutput>(), "药品出库明细表导入模板", (_, info) =>
        {
            if (nameof(ExportStorageOutDetailOutput.ManufacturerFkDisplayName) == info.Name) return _storageOutDetailRep.Context.Queryable<EnterpriseDictionary>().Select(u => $"{u.EnterpriseName}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入药品出库明细表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品出库明细表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var drugTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "DrugType" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportStorageOutDetailInput, StorageOutDetail>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 生产厂商ID
                    var manufacturerIdLabelList = pageItems.Where(x => x.ManufacturerFkDisplayName != null).Select(x => x.ManufacturerFkDisplayName).Distinct().ToList();
                    if (manufacturerIdLabelList.Any()) {
                        var manufacturerIdLinkMap = _storageOutDetailRep.Context.Queryable<EnterpriseDictionary>().Where(u => manufacturerIdLabelList.Contains($"{u.EnterpriseName}")).ToList().ToDictionary(u => $"{u.EnterpriseName}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.ManufacturerId = manufacturerIdLinkMap.GetValueOrDefault(e.ManufacturerFkDisplayName ?? "");
                            if (e.ManufacturerId == null) e.Error = "生产厂商ID链接失败";
                        });
                    }
                    
                    // 映射字典值
                    foreach(var item in pageItems) {
                        if (string.IsNullOrWhiteSpace(item.DrugTypeDictLabel)) continue;
                        item.DrugType = drugTypeDictMap.GetValueOrDefault(item.DrugTypeDictLabel);
                        if (item.DrugType == null) item.Error = "药品类型字典映射失败";
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<StorageOutDetail>>();
                    
                    var storageable = _storageOutDetailRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.DrugType?.Length > 100, "药品类型长度不能超过100个字符")
                        .SplitError(it => it.Item.StorageOutNo?.Length > 100, "出库单号长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugCode?.Length > 100, "药品编码长度不能超过100个字符")
                        .SplitError(it => it.Item.DrugName?.Length > 100, "药品名称长度不能超过100个字符")
                        .SplitError(it => it.Item.Spec?.Length > 100, "规格长度不能超过100个字符")
                        .SplitError(it => it.Item.Unit?.Length > 100, "单位长度不能超过100个字符")
                        .SplitError(it => it.Item.BatchNo?.Length > 100, "批号长度不能超过100个字符")
                        .SplitError(it => it.Item.ApprovalNumber?.Length > 100, "批准文号长度不能超过100个字符")
                        .SplitError(it => it.Item.MedicineCode?.Length > 100, "国家医保编码长度不能超过100个字符")
                        .SplitError(it => it.Item.ManufacturerName?.Length > 100, "生产厂商名称长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
