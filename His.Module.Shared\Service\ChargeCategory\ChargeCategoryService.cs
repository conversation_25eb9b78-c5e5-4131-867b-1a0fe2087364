﻿using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Shared.Service;

/// <summary>
/// 收费类别服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class ChargeCategoryService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ChargeCategory> _chargeCategoryRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public ChargeCategoryService(SqlSugarRepository<ChargeCategory> chargeCategoryRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _chargeCategoryRep = chargeCategoryRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询收费类别 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询收费类别")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<ChargeCategoryOutput>> Page(PageChargeCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.Code = input.Code?.Trim();
        input.Name = input.Name?.Trim().ToLower();
        var query = _chargeCategoryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
            || u.Name.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select<ChargeCategoryOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取收费类别详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取收费类别详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<ChargeCategory> Detail([FromQuery] QueryByIdChargeCategoryInput input)
    {
        return await _chargeCategoryRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取收费类别列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    [DisplayName("获取收费类别列表")]
    public async Task<List<ChargeCategory>> List()
    {
        return await _chargeCategoryRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 增加收费类别 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加收费类别")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddChargeCategoryInput input)
    {
        var entity = input.Adapt<ChargeCategory>();
        entity.Code = await _chargeCategoryRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('charge_category_code_seq')As varchar),3,'0')");
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        return await _chargeCategoryRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新收费类别 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新收费类别")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateChargeCategoryInput input)
    {
        var entity = input.Adapt<ChargeCategory>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _chargeCategoryRep.AsUpdateable(entity)
        .IgnoreColumns(u => new
        {
            u.Code
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除收费类别 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除收费类别")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteChargeCategoryInput input)
    {
        var entity = await _chargeCategoryRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _chargeCategoryRep.FakeDeleteAsync(entity);   //假删除
        //await _chargeCategoryRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除收费类别 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除收费类别")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteChargeCategoryInput> input)
    {
        var exp = Expressionable.Create<ChargeCategory>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _chargeCategoryRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _chargeCategoryRep.FakeDeleteAsync(list);   //假删除
        //return await _chargeCategoryRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置收费类别状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置收费类别状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetChargeCategoryStatus(SetChargeCategoryStatusInput input)
    {
        await _chargeCategoryRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 导出收费类别记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出收费类别记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageChargeCategoryInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportChargeCategoryOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var medInsTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "MedInsChargeType" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e =>
        {
            e.MedInsTypeDictLabel = medInsTypeDictMap.GetValueOrDefault(e.MedInsType ?? "", e.MedInsType);
        });
        return ExcelHelper.ExportTemplate(list, "收费类别导出记录");
    }

    /// <summary>
    /// 下载收费类别数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载收费类别数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportChargeCategoryOutput>(), "收费类别导入模板");
    }

    /// <summary>
    /// 导入收费类别记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入收费类别记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var medInsTypeDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "MedInsChargeType" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportChargeCategoryInput, ChargeCategory>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 映射字典值
                    foreach (var item in pageItems)
                    {
                        if (string.IsNullOrWhiteSpace(item.MedInsTypeDictLabel)) continue;
                        item.MedInsType = medInsTypeDictMap.GetValueOrDefault(item.MedInsTypeDictLabel);
                        if (item.MedInsType == null) item.Error = "医保类型字典映射失败";
                    }

                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        return true;
                    }).Adapt<List<ChargeCategory>>();

                    var storageable = _chargeCategoryRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Code?.Length > 32, "编码长度不能超过32个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.Name), "名称不能为空")
                        .SplitError(it => it.Item.Name?.Length > 32, "名称长度不能超过32个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 20, "拼音码长度不能超过20个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 20, "五笔码长度不能超过20个字符")
                        .SplitError(it => it.Item.AccountAttribute == null, "记账属性不能为空")
                        .SplitError(it => it.Item.Type == null, "类型不能为空")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.MedInsType), "医保类型不能为空")
                        .SplitError(it => it.Item.MedInsType?.Length > 16, "医保类型长度不能超过16个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}