﻿namespace His.Module.Patient.Enum
{
    /// <summary>
    /// 患者管理错误码
    /// </summary>
    [ErrorCodeType]
    [Description("患者管理错误码")]
    public enum PatientErrorCodeEnum
    {
        /// <summary>
        /// 没有查询到身份证号为[{0}]的患者信息
        /// </summary>
        [ErrorCodeItemMetadata("没有查询到身份证号为[{0}]的患者信息")]
        P0001,

        /// <summary>
        /// 没有查询到患者[{0}]的有效就诊卡信息
        /// </summary>
        [ErrorCodeItemMetadata("没有查询到患者[{0}]的有效就诊卡信息")]
        P0002,

        /// <summary>
        /// 没有查询到卡号[{0}]的有效就诊卡信息
        /// </summary>
        [ErrorCodeItemMetadata("没有查询到卡号[{0}]的有效就诊卡信息")]
        P0003,

        /// <summary>
        /// 身份证号[{0}]的患者信息已经存在，请核对身份证号
        /// </summary>
        [ErrorCodeItemMetadata("身份证号[{0}]的患者信息已经存在，请核对身份证号")]
        P0004,

        /// <summary>
        /// 该卡[{0}]已挂失
        /// </summary>
        [ErrorCodeItemMetadata("该卡[{0}]已挂失")]
        P0005,

        /// <summary>
        /// 该卡[{0}]已退
        /// </summary>
        [ErrorCodeItemMetadata("该卡[{0}]已退")]
        P0006,

        /// <summary>
        /// 该卡[{0}]不是挂失状态
        /// </summary>
        [ErrorCodeItemMetadata("该卡[{0}]不是挂失状态")]
        P0007,
    }
}